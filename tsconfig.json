{
	"compilerOptions": {
		"target": "ES2022",
		"lib": ["ES2022", "DOM", "DOM.Iterable"],
		"module": "ESNext",
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"verbatimModuleSyntax": false,
		"noEmit": true,
		"jsx": "react-jsx",

		/* Type Checking */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedSideEffectImports": true,

		/* Interop Constraints */
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,

		/* Completeness */
		"skipLibCheck": true
	},
	"references": [
		{ "path": "./packages/backend" },
		{ "path": "./packages/admin" },
		{ "path": "./packages/frontend" },
		{ "path": "./packages/ui" }
	],
	"files": []
}