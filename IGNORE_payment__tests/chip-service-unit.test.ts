import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { AppError, ErrorCode } from "../../lib/error-handler"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "../payments.schema"

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe("ChipService Unit Tests", () => {
	beforeEach(() => {
		vi.clearAllMocks()
		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test_secret_key"
		process.env.CHIP_BRAND_ID = "test_brand_id"
		process.env.CHIP_WEBHOOK_SECRET = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef
-----<PERSON><PERSON> PUBLIC KEY-----`
		process.env.FRONTEND_URL = "https://test-frontend.com"
		process.env.BACKEND_URL = "https://test-backend.com"
	})

	afterEach(() => {
		vi.restoreAllMocks()
	})

	// Helper function to create valid payment parameters
	const createValidPaymentParams = (
		overrides: Partial<CreatePaymentParams> = {},
	): CreatePaymentParams => ({
		amount: 50.0,
		currency: "MYR",
		email: "<EMAIL>",
		phone: "+***********",
		fullName: "John Doe",
		products: [
			{
				name: "Test Donation",
				price: 50.0,
				category: "donation",
				quantity: "1",
			},
		],
		successUrl: "https://test-backend.com/api/webhooks/chip",
		failureUrl: "https://test-backend.com/api/webhooks/chip",
		successRedirectUrl: "https://test-frontend.com/donation/success",
		failureRedirectUrl: "https://test-frontend.com/donation/failed",
		cancelRedirectUrl: "https://test-frontend.com/campaigns/test-campaign",
		reference: "donation-123",
		notes: "Test donation payment",
		...overrides,
	})

	// Helper function to create mock Chip API response
	const createMockChipResponse = (
		overrides: Partial<ChipPaymentResponse> = {},
	): ChipPaymentResponse => ({
		id: "chip_payment_123",
		checkout_url: "https://test.chip-in.asia/checkout/chip_payment_123",
		status: "created",
		due: 0,
		type: "purchase",
		client: {
			email: "<EMAIL>",
			phone: "+***********",
			full_name: "John Doe",
			cc: [],
			bcc: [],
			city: "",
			state: "",
			country: "",
			zip_code: "",
			bank_code: "",
			brand_name: "",
			legal_name: "",
			tax_number: "",
			client_type: null,
			bank_account: "",
			personal_code: "",
			shipping_city: "",
			shipping_state: "",
			street_address: "",
			delivery_methods: [],
			shipping_country: "",
			shipping_zip_code: "",
			registration_number: "",
			shipping_street_address: "",
		},
		issued: "2024-01-01T00:00:00Z",
		is_test: true,
		payment: null,
		product: "test",
		user_id: null,
		brand_id: "test_brand_id",
		order_id: null,
		platform: "web",
		purchase: {
			debt: 0,
			notes: "Test donation payment",
			total: 5000, // 50.00 in cents
			currency: "MYR",
			language: "en",
			products: [
				{
					name: "Test Donation",
					price: 5000, // 50.00 in cents
					category: "donation",
					quantity: "1",
				},
			],
			timezone: "Asia/Kuala_Lumpur",
			due_strict: false,
			email_message: "",
			total_override: null,
			shipping_options: [],
			subtotal_override: null,
			total_tax_override: null,
			has_upsell_products: false,
			payment_method_details: {},
			request_client_details: [],
			total_discount_override: null,
		},
		client_id: "test_client_id",
		reference: "donation-123",
		viewed_on: 0,
		company_id: "test_company_id",
		created_on: Date.now(),
		event_type: "purchase.created",
		updated_on: Date.now(),
		invoice_url: null,
		can_retrieve: true,
		send_receipt: false,
		skip_capture: false,
		creator_agent: "test",
		referral_code: null,
		can_chargeback: false,
		issuer_details: {
			website: "",
			brand_name: "",
			legal_city: "",
			legal_name: "",
			tax_number: "",
			bank_accounts: [],
			legal_country: "",
			legal_zip_code: "",
			registration_number: "",
			legal_street_address: "",
		},
		marked_as_paid: false,
		status_history: [
			{
				status: "created",
				timestamp: Date.now(),
			},
		],
		cancel_redirect: "https://test-frontend.com/campaigns/test-campaign",
		created_from_ip: "127.0.0.1",
		direct_post_url: "https://test.chip-in.asia/direct",
		force_recurring: false,
		recurring_token: null,
		failure_redirect: "https://test-frontend.com/donation/failed",
		success_callback: "https://test-backend.com/api/webhooks/chip",
		success_redirect: "https://test-frontend.com/donation/success",
		transaction_data: {
			flow: "web",
			extra: {},
			country: "MY",
			attempts: [],
			payment_method: "",
			processing_tx_id: "",
		},
		upsell_campaigns: [],
		refundable_amount: 0,
		is_recurring_token: false,
		billing_template_id: null,
		currency_conversion: null,
		reference_generated: "donation-123-gen",
		refund_availability: "none",
		referral_campaign_id: null,
		retain_level_details: null,
		referral_code_details: null,
		referral_code_generated: null,
		payment_method_whitelist: null,
		...overrides,
	})

	describe("Payment Creation", () => {
		it("should create payment successfully with valid parameters", async () => {
			const { ChipService } = await import("../payments.service")

			const mockResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockResponse),
			})

			const params = createValidPaymentParams()
			const result = await ChipService.createPayment(params)

			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith(
				"https://test.chip-in.asia/api/v1/purchases/",
				expect.objectContaining({
					method: "POST",
					headers: expect.objectContaining({
						"Content-Type": "application/json",
						Authorization: "Bearer test_secret_key",
						"X-Brand-ID": "test_brand_id",
					}),
				}),
			)
		})

		it("should convert amounts to cents correctly", async () => {
			const { ChipService } = await import("../payments.service")

			const mockResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockResponse),
			})

			const params = createValidPaymentParams({
				amount: 123.45,
				products: [
					{
						name: "Test Product",
						price: 123.45,
						category: "donation",
						quantity: "1",
					},
				],
			})

			await ChipService.createPayment(params)

			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body)

			expect(requestBody.purchase.amount).toBe(12345) // 123.45 in cents
			expect(requestBody.purchase.products[0].price).toBe(12345) // 123.45 in cents
		})

		it("should validate payment parameters", async () => {
			const { ChipService } = await import("../payments.service")

			const invalidParams = createValidPaymentParams({
				amount: 0, // Invalid amount
				email: "invalid-email", // Invalid email
				fullName: "", // Empty name
				products: [], // Empty products
			})

			await expect(ChipService.createPayment(invalidParams)).rejects.toThrow(
				AppError,
			)
		})

		it("should handle Chip API errors", async () => {
			const { ChipService } = await import("../payments.service")

			const chipError = {
				error: "Invalid brand ID",
				code: "INVALID_BRAND",
				details: { brand_id: "invalid_brand" },
			}

			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 400,
				statusText: "Bad Request",
				json: () => Promise.resolve(chipError),
			})

			const params = createValidPaymentParams()

			await expect(ChipService.createPayment(params)).rejects.toThrow(AppError)
		})

		it("should handle network errors", async () => {
			const { ChipService } = await import("../payments.service")

			mockFetch.mockRejectedValueOnce(new Error("Network timeout"))

			const params = createValidPaymentParams()

			await expect(ChipService.createPayment(params)).rejects.toThrow(AppError)
		})
	})

	describe("Donation Payment Creation", () => {
		const mockDonation = {
			id: "donation-123",
			campaignId: "campaign-456",
			donorId: "donor-789",
			amount: "50.00",
			currency: "MYR",
			chipPaymentId: null,
			status: "pending" as const,
			donorName: "John Doe",
			donorEmail: "<EMAIL>",
			donorPhone: "+***********",
			donorMessage: "Keep up the good work!",
			internalNotes: null,
			createdAt: new Date(),
			updatedAt: new Date(),
		}

		const mockCampaign = {
			id: "campaign-456",
			name: "Test Campaign",
			slug: "test-campaign",
			description: "A test campaign",
			goalAmount: "1000.00",
			currentAmount: "0.00",
			currency: "MYR",
			isActive: true,
			organizerId: "organizer-123",
			createdAt: new Date(),
			updatedAt: new Date(),
		}

		it("should create donation payment with proper context", async () => {
			const { ChipService } = await import("../payments.service")

			const mockResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockResponse),
			})

			const params = createValidPaymentParams()
			const result = await ChipService.createDonationPayment(
				mockDonation,
				mockCampaign,
				params,
			)

			expect(result).toEqual(mockResponse)

			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body)

			// Verify donation-specific product details
			expect(requestBody.purchase.products[0].name).toBe(
				"Donation to Test Campaign",
			)
			expect(requestBody.purchase.products[0].category).toBe("donation")
			expect(requestBody.reference).toBe("donation-123")
		})

		it("should reject payments for inactive campaigns", async () => {
			const { ChipService } = await import("../payments.service")

			const inactiveCampaign = { ...mockCampaign, isActive: false }
			const params = createValidPaymentParams()

			await expect(
				ChipService.createDonationPayment(
					mockDonation,
					inactiveCampaign,
					params,
				),
			).rejects.toThrow("not currently accepting donations")
		})
	})

	describe("Payment Retrieval", () => {
		it("should retrieve payment details successfully", async () => {
			const { ChipService } = await import("../payments.service")

			const mockResponse = createMockChipResponse({ status: "paid" })
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockResponse),
			})

			const result = await ChipService.getPayment("chip_payment_123")

			expect(result).toEqual(mockResponse)
			expect(mockFetch).toHaveBeenCalledWith(
				"https://test.chip-in.asia/api/v1/purchases/chip_payment_123",
				expect.objectContaining({
					method: "GET",
					headers: expect.objectContaining({
						Authorization: "Bearer test_secret_key",
						"X-Brand-ID": "test_brand_id",
					}),
				}),
			)
		})

		it("should validate payment ID parameter", async () => {
			const { ChipService } = await import("../payments.service")

			await expect(ChipService.getPayment("")).rejects.toThrow(
				"Payment ID is required",
			)
		})

		it("should handle 404 errors", async () => {
			const { ChipService } = await import("../payments.service")

			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 404,
				statusText: "Not Found",
				json: () => Promise.resolve({ error: "Payment not found" }),
			})

			await expect(
				ChipService.getPayment("non_existent_payment"),
			).rejects.toThrow(AppError)
		})
	})

	describe("Webhook Signature Verification", () => {
		it("should handle missing payload or signature", async () => {
			const { ChipService } = await import("../payments.service")

			const testCases = [
				{ payload: "", signature: "signature" },
				{ payload: "payload", signature: "" },
				{ payload: null, signature: "signature" },
				{ payload: "payload", signature: null },
			]

			for (const testCase of testCases) {
				const result = await ChipService.verifyWebhookSignature(
					testCase.payload as any,
					testCase.signature as any,
				)
				expect(result).toBe(false)
			}
		})

		it("should handle missing environment configuration", async () => {
			delete process.env.CHIP_WEBHOOK_SECRET

			const { ChipService } = await import("../payments.service")

			const payload = JSON.stringify({ id: "payment_123", status: "paid" })
			const signature = "signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
		})
	})

	describe("Environment Configuration", () => {
		it("should validate required environment variables", async () => {
			const requiredVars = [
				"CHIP_BASE_URL",
				"CHIP_SECRET_KEY",
				"CHIP_BRAND_ID",
				"CHIP_WEBHOOK_SECRET",
			]

			for (const varName of requiredVars) {
				// Clear the specific environment variable
				const originalValue = process.env[varName]
				delete process.env[varName]

				const { ChipService } = await import("../payments.service")

				const params = createValidPaymentParams()

				try {
					await ChipService.createPayment(params)
					expect.fail(`Should have thrown an error for missing ${varName}`)
				} catch (error) {
					expect(error).toBeInstanceOf(AppError)
					expect((error as AppError).code).toBe(
						ErrorCode.MISSING_ENVIRONMENT_VARIABLES,
					)
				}

				// Restore the environment variable
				if (originalValue) {
					process.env[varName] = originalValue
				}
			}
		})
	})

	describe("Parameter Validation", () => {
		const validationTestCases = [
			{
				name: "negative amount",
				params: { amount: -10 },
				expectedError: "Payment amount must be greater than zero",
			},
			{
				name: "zero amount",
				params: { amount: 0 },
				expectedError: "Payment amount must be a valid number",
			},
			{
				name: "invalid email format",
				params: { email: "invalid-email" },
				expectedError: "Valid email address is required",
			},
			{
				name: "empty full name",
				params: { fullName: "" },
				expectedError: "Full name is required",
			},
			{
				name: "empty products array",
				params: { products: [] },
				expectedError: "At least one product is required",
			},
		]

		validationTestCases.forEach(({ name, params, expectedError }) => {
			it(`should validate ${name}`, async () => {
				const { ChipService } = await import("../payments.service")

				const invalidParams = createValidPaymentParams(params)

				try {
					await ChipService.createPayment(invalidParams)
					expect.fail(`Should have thrown an error for ${name}`)
				} catch (error) {
					expect(error).toBeInstanceOf(AppError)
					expect((error as AppError).message).toContain(expectedError)
				}
			})
		})
	})
})
