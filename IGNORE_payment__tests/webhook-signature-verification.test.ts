import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { ChipService } from "../payments.service"

// Mock crypto module
const mockVerify = vi.fn()
const mockUpdate = vi.fn()
const mockCreateVerify = vi.fn(() => ({
	update: mockUpdate,
	verify: mockVerify,
}))

vi.mock("node:crypto", async () => {
	const actual = await vi.importActual("node:crypto")
	return {
		...actual,
		createVerify: mockCreateVerify,
	}
})

describe("Webhook Signature Verification Tests", () => {
	beforeEach(() => {
		vi.clearAllMocks()

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test_secret_key"
		process.env.CHIP_BRAND_ID = "test_brand_id"
		process.env.CHIP_WEBHOOK_SECRET = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyVdQGzHZ8rVexVXnFqtN
8qJKqKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
vKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvKvK
QIDAQAB
-----END PUBLIC KEY-----`
	})

	afterEach(() => {
		vi.restoreAllMocks()
	})

	// Helper function to create test webhook payloads
	const createTestWebhookPayload = (overrides = {}) => ({
		id: "payment_test_123",
		status: "paid",
		reference: "donation_test_456",
		purchase: {
			total: 5000, // 50.00 in cents
			currency: "MYR",
			products: [
				{
					name: "Test Donation",
					price: 5000,
					category: "donation",
					quantity: "1",
				},
			],
		},
		client: {
			email: "<EMAIL>",
			phone: "+60123456789",
			full_name: "Test User",
		},
		created_on: Date.now(),
		updated_on: Date.now(),
		...overrides,
	})

	describe("Valid Signature Verification", () => {
		it("should verify valid webhook signatures successfully", async () => {
			mockVerify.mockReturnValueOnce(true)

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "valid_signature_base64_encoded"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(true)
			expect(mockCreateVerify).toHaveBeenCalledWith("sha256WithRSAEncryption")
			expect(mockUpdate).toHaveBeenCalledWith(payload)
			expect(mockVerify).toHaveBeenCalledWith(
				process.env.CHIP_WEBHOOK_SECRET,
				Buffer.from(signature, "base64"),
			)
		})

		it("should handle different payload structures correctly", async () => {
			mockVerify.mockReturnValue(true)

			const testPayloads = [
				// Minimal payload
				{ id: "payment_1", status: "paid" },
				// Complex payload with nested objects
				createTestWebhookPayload({
					transaction_data: {
						flow: "web",
						attempts: [
							{
								successful: true,
								payment_method: "fpx",
								processing_time: 1500,
							},
						],
					},
				}),
				// Payload with special characters
				createTestWebhookPayload({
					purchase: {
						notes: "Special chars: àáâãäåæçèéêë & symbols: !@#$%^&*()",
					},
				}),
				// Large payload
				createTestWebhookPayload({
					status_history: Array.from({ length: 10 }, (_, i) => ({
						status: `status_${i}`,
						timestamp: Date.now() + i * 1000,
					})),
				}),
			]

			for (const testPayload of testPayloads) {
				const payload = JSON.stringify(testPayload)
				const signature = "test_signature"

				const result = await ChipService.verifyWebhookSignature(
					payload,
					signature,
				)
				expect(result).toBe(true)
			}
		})

		it("should handle Unicode characters in payload", async () => {
			mockVerify.mockReturnValueOnce(true)

			const payloadWithUnicode = createTestWebhookPayload({
				client: {
					full_name: "测试用户 José María Ñoño",
					email: "<EMAIL>",
				},
				purchase: {
					notes: "Donation with emoji: 💰🎉 and unicode: ñáéíóú",
				},
			})

			const payload = JSON.stringify(payloadWithUnicode)
			const signature = "unicode_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(true)
			expect(mockUpdate).toHaveBeenCalledWith(payload)
		})
	})

	describe("Invalid Signature Verification", () => {
		it("should reject invalid webhook signatures", async () => {
			mockVerify.mockReturnValueOnce(false)

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "invalid_signature_base64"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
			expect(mockCreateVerify).toHaveBeenCalledWith("sha256WithRSAEncryption")
			expect(mockUpdate).toHaveBeenCalledWith(payload)
			expect(mockVerify).toHaveBeenCalledWith(
				process.env.CHIP_WEBHOOK_SECRET,
				Buffer.from(signature, "base64"),
			)
		})

		it("should handle tampered payloads", async () => {
			mockVerify.mockReturnValueOnce(false)

			const originalPayload = createTestWebhookPayload()
			const tamperedPayload = {
				...originalPayload,
				purchase: {
					...originalPayload.purchase,
					total: 999999, // Tampered amount
				},
			}

			const payload = JSON.stringify(tamperedPayload)
			const signature = "original_signature_for_different_payload"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
		})

		it("should handle malformed signatures", async () => {
			mockVerify.mockImplementationOnce(() => {
				throw new Error("Invalid signature format")
			})

			const payload = JSON.stringify(createTestWebhookPayload())
			const malformedSignature = "not_base64_encoded!"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				malformedSignature,
			)

			expect(result).toBe(false)
		})
	})

	describe("Parameter Validation", () => {
		it("should reject empty or null payloads", async () => {
			const testCases = [
				{ payload: "", signature: "valid_signature" },
				{ payload: null, signature: "valid_signature" },
				{ payload: undefined, signature: "valid_signature" },
				{ payload: "   ", signature: "valid_signature" }, // Whitespace only
			]

			for (const testCase of testCases) {
				const result = await ChipService.verifyWebhookSignature(
					testCase.payload as any,
					testCase.signature,
				)
				expect(result).toBe(false)
			}

			// Verify crypto functions were not called for invalid inputs
			expect(mockCreateVerify).not.toHaveBeenCalled()
			expect(mockUpdate).not.toHaveBeenCalled()
			expect(mockVerify).not.toHaveBeenCalled()
		})

		it("should reject empty or null signatures", async () => {
			const payload = JSON.stringify(createTestWebhookPayload())

			const testCases = [
				{ payload, signature: "" },
				{ payload, signature: null },
				{ payload, signature: undefined },
				{ payload, signature: "   " }, // Whitespace only
			]

			for (const testCase of testCases) {
				const result = await ChipService.verifyWebhookSignature(
					testCase.payload,
					testCase.signature as any,
				)
				expect(result).toBe(false)
			}
		})

		it("should handle very large payloads", async () => {
			mockVerify.mockReturnValueOnce(true)

			// Create a large payload (simulate a webhook with lots of data)
			const largePayload = createTestWebhookPayload({
				large_data: "x".repeat(10000), // 10KB of data
				status_history: Array.from({ length: 100 }, (_, i) => ({
					status: `status_${i}`,
					timestamp: Date.now() + i * 1000,
					details: "x".repeat(100),
				})),
			})

			const payload = JSON.stringify(largePayload)
			const signature = "large_payload_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(true)
			expect(mockUpdate).toHaveBeenCalledWith(payload)
		})
	})

	describe("Environment Configuration", () => {
		it("should handle missing webhook secret configuration", async () => {
			delete process.env.CHIP_WEBHOOK_SECRET

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "valid_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
			// Should not attempt crypto operations with missing config
			expect(mockCreateVerify).not.toHaveBeenCalled()
		})

		it("should handle invalid public key format", async () => {
			process.env.CHIP_WEBHOOK_SECRET = "invalid_public_key_format"

			mockVerify.mockImplementationOnce(() => {
				throw new Error("Invalid key format")
			})

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "valid_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
		})

		it("should validate public key format on startup", async () => {
			// Test with various invalid public key formats
			const invalidKeys = [
				"", // Empty
				"not_a_key", // Plain text
				"-----BEGIN CERTIFICATE-----\nInvalidCert\n-----END CERTIFICATE-----", // Wrong type
				"-----BEGIN PUBLIC KEY-----\nInvalidKey", // Missing end marker
			]

			for (const invalidKey of invalidKeys) {
				process.env.CHIP_WEBHOOK_SECRET = invalidKey

				mockVerify.mockImplementationOnce(() => {
					throw new Error("Invalid key")
				})

				const payload = JSON.stringify(createTestWebhookPayload())
				const signature = "signature"

				const result = await ChipService.verifyWebhookSignature(
					payload,
					signature,
				)
				expect(result).toBe(false)
			}
		})
	})

	describe("Crypto Error Handling", () => {
		it("should handle crypto.createVerify errors", async () => {
			mockCreateVerify.mockImplementationOnce(() => {
				throw new Error("Crypto algorithm not supported")
			})

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "valid_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
		})

		it("should handle verifier.update errors", async () => {
			mockUpdate.mockImplementationOnce(() => {
				throw new Error("Update failed")
			})

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "valid_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
		})

		it("should handle verifier.verify errors", async () => {
			mockVerify.mockImplementationOnce(() => {
				throw new Error("Verification failed")
			})

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "valid_signature"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				signature,
			)

			expect(result).toBe(false)
		})
	})

	describe("Signature Format Validation", () => {
		it("should handle different base64 signature formats", async () => {
			mockVerify.mockReturnValue(true)

			const payload = JSON.stringify(createTestWebhookPayload())

			const validBase64Signatures = [
				"SGVsbG8gV29ybGQ=", // Standard base64
				"SGVsbG8gV29ybGQ", // Base64 without padding
				"SGVsbG8_V29ybGQ=", // URL-safe base64 with padding
				"SGVsbG8_V29ybGQ", // URL-safe base64 without padding
			]

			for (const signature of validBase64Signatures) {
				const result = await ChipService.verifyWebhookSignature(
					payload,
					signature,
				)
				expect(result).toBe(true)
				expect(mockVerify).toHaveBeenCalledWith(
					process.env.CHIP_WEBHOOK_SECRET,
					Buffer.from(signature, "base64"),
				)
			}
		})

		it("should handle invalid base64 signatures gracefully", async () => {
			mockVerify.mockImplementationOnce(() => {
				throw new Error("Invalid base64")
			})

			const payload = JSON.stringify(createTestWebhookPayload())
			const invalidSignature = "not!valid@base64#"

			const result = await ChipService.verifyWebhookSignature(
				payload,
				invalidSignature,
			)

			expect(result).toBe(false)
		})
	})

	describe("Logging and Monitoring", () => {
		it("should log verification attempts", async () => {
			const consoleSpy = vi.spyOn(console, "info").mockImplementation(() => {})

			mockVerify.mockReturnValueOnce(true)

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "test_signature"

			await ChipService.verifyWebhookSignature(payload, signature)

			expect(consoleSpy).toHaveBeenCalledWith(
				"Verifying Chip webhook signature",
				expect.objectContaining({
					payloadLength: payload.length,
					signatureLength: signature.length,
				}),
			)

			expect(consoleSpy).toHaveBeenCalledWith(
				"Webhook signature verification completed",
				expect.objectContaining({
					result: "valid",
					payloadLength: payload.length,
				}),
			)

			consoleSpy.mockRestore()
		})

		it("should log verification failures with appropriate level", async () => {
			const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})

			mockVerify.mockReturnValueOnce(false)

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "invalid_signature"

			await ChipService.verifyWebhookSignature(payload, signature)

			// Should log the failure as a warning, not error (since it's expected behavior)
			expect(consoleSpy).toHaveBeenCalled()

			consoleSpy.mockRestore()
		})

		it("should not log sensitive information", async () => {
			const consoleSpy = vi.spyOn(console, "info").mockImplementation(() => {})

			mockVerify.mockReturnValueOnce(true)

			const sensitivePayload = createTestWebhookPayload({
				client: {
					email: "<EMAIL>",
					phone: "+60123456789",
					full_name: "Sensitive User",
				},
			})

			const payload = JSON.stringify(sensitivePayload)
			const signature = "test_signature"

			await ChipService.verifyWebhookSignature(payload, signature)

			// Verify that sensitive data is not logged
			const logCalls = consoleSpy.mock.calls
			const loggedData = logCalls.map((call) => JSON.stringify(call))

			expect(loggedData.join("")).not.toContain("<EMAIL>")
			expect(loggedData.join("")).not.toContain("+60123456789")
			expect(loggedData.join("")).not.toContain("Sensitive User")

			consoleSpy.mockRestore()
		})
	})

	describe("Performance Considerations", () => {
		it("should handle signature verification efficiently", async () => {
			mockVerify.mockReturnValue(true)

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "performance_test_signature"

			const startTime = Date.now()

			// Run multiple verifications
			const promises = Array.from({ length: 10 }, () =>
				ChipService.verifyWebhookSignature(payload, signature),
			)

			const results = await Promise.all(promises)

			const endTime = Date.now()
			const totalTime = endTime - startTime

			// All verifications should succeed
			expect(results.every((result) => result === true)).toBe(true)

			// Should complete reasonably quickly (less than 1 second for 10 verifications)
			expect(totalTime).toBeLessThan(1000)
		})

		it("should not leak memory during repeated verifications", async () => {
			mockVerify.mockReturnValue(true)

			const payload = JSON.stringify(createTestWebhookPayload())
			const signature = "memory_test_signature"

			// Simulate many webhook verifications
			for (let i = 0; i < 100; i++) {
				await ChipService.verifyWebhookSignature(payload, signature)
			}

			// If we reach here without memory issues, the test passes
			expect(true).toBe(true)
		})
	})
})
