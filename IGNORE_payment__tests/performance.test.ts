import { eq } from "drizzle-orm"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { campaigns } from "../../campaigns/campaigns.schema"
import { db } from "../../db"
import { donations } from "../../donations/donations.schema"
import { users } from "../../users/users.schema"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "../payments.schema"
import { ChipService } from "../payments.service"
import { ChipMockResponseFactory } from "./mock-chip-responses.test"

// Mock fetch for Chip API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

describe("Payment Processing Performance Tests", () => {
	let testUserId: string
	let testCampaignId: string
	let testDonationIds: string[] = []

	beforeEach(async () => {
		vi.clearAllMocks()

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test_secret_key"
		process.env.CHIP_BRAND_ID = "test_brand_id"
		process.env.CHIP_WEBHOOK_SECRET = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef
-----END PUBLIC KEY-----`
		process.env.FRONTEND_URL = "https://test-frontend.com"
		process.env.BACKEND_URL = "https://test-backend.com"

		// Create test user
		const [user] = await db
			.insert(users)
			.values({
				id: `test-user-perf-${Date.now()}`,
				name: "Performance Test User",
				email: `perf-test-${Date.now()}@example.com`,
				emailVerified: false,
				image: null,
				createdAt: new Date(),
				updatedAt: new Date(),
			})
			.returning()

		testUserId = user.id

		// Create test campaign
		const [campaign] = await db
			.insert(campaigns)
			.values({
				name: "Performance Test Campaign",
				slug: `perf-test-campaign-${Date.now()}`,
				description: "A test campaign for performance testing",
				goalAmount: "10000.00",
				currentAmount: "0.00",
				currency: "MYR",
				isActive: true,
				organizerId: testUserId,
			})
			.returning()

		testCampaignId = campaign.id
	})

	afterEach(async () => {
		// Clean up test data
		if (testDonationIds.length > 0) {
			for (const donationId of testDonationIds) {
				await db.delete(donations).where(eq(donations.id, donationId))
			}
			testDonationIds = []
		}
		if (testCampaignId) {
			await db.delete(campaigns).where(eq(campaigns.id, testCampaignId))
		}
		if (testUserId) {
			await db.delete(users).where(eq(users.id, testUserId))
		}
	})

	// Helper function to create test donations
	const createTestDonations = async (count: number) => {
		const donations = []
		for (let i = 0; i < count; i++) {
			const [donation] = await db
				.insert(donations)
				.values({
					campaignId: testCampaignId,
					donorId: testUserId,
					amount: (Math.random() * 100 + 10).toFixed(2), // Random amount between 10-110
					currency: "MYR",
					status: "pending",
					donorName: `Donor ${i + 1}`,
					donorEmail: `donor${i + 1}@example.com`,
					donorPhone: `+6012345${String(i).padStart(4, "0")}`,
					donorMessage: `Performance test donation ${i + 1}`,
				})
				.returning()

			donations.push(donation)
			testDonationIds.push(donation.id)
		}
		return donations
	}

	// Helper function to create payment parameters
	const createPaymentParams = (donation: any): CreatePaymentParams => ({
		amount: parseFloat(donation.amount),
		currency: "MYR",
		email: donation.donorEmail,
		phone: donation.donorPhone,
		fullName: donation.donorName,
		products: [
			{
				name: `Donation to Performance Test Campaign`,
				price: parseFloat(donation.amount),
				category: "donation",
				quantity: "1",
			},
		],
		successUrl: "https://test-backend.com/api/webhooks/chip",
		failureUrl: "https://test-backend.com/api/webhooks/chip",
		successRedirectUrl: "https://test-frontend.com/donation/success",
		failureRedirectUrl: "https://test-frontend.com/donation/failed",
		cancelRedirectUrl: "https://test-frontend.com/campaigns/perf-test-campaign",
		reference: donation.id,
		notes: donation.donorMessage,
	})

	describe("Payment Creation Performance", () => {
		it("should create single payment within acceptable time", async () => {
			const [donation] = await createTestDonations(1)
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const mockResponse = ChipMockResponseFactory.createPaymentCreatedResponse(
				{
					reference: donation.id,
				},
			)

			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockResponse),
			})

			const params = createPaymentParams(donation)

			const startTime = Date.now()
			const result = await ChipService.createDonationPayment(
				donation,
				campaign,
				params,
			)
			const endTime = Date.now()

			const processingTime = endTime - startTime

			expect(result.id).toBeTruthy()
			expect(processingTime).toBeLessThan(1000) // Should complete within 1 second
		})

		it("should handle multiple concurrent payment creations efficiently", async () => {
			const donationCount = 10
			const testDonations = await createTestDonations(donationCount)
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Mock responses for all payments
			testDonations.forEach((donation, index) => {
				mockFetch.mockResolvedValueOnce({
					ok: true,
					json: () =>
						Promise.resolve(
							ChipMockResponseFactory.createPaymentCreatedResponse({
								id: `chip_payment_concurrent_${index}`,
								reference: donation.id,
							}),
						),
				})
			})

			const startTime = Date.now()

			// Create all payments concurrently
			const paymentPromises = testDonations.map((donation) => {
				const params = createPaymentParams(donation)
				return ChipService.createDonationPayment(donation, campaign, params)
			})

			const results = await Promise.all(paymentPromises)
			const endTime = Date.now()

			const totalProcessingTime = endTime - startTime
			const averageProcessingTime = totalProcessingTime / donationCount

			expect(results).toHaveLength(donationCount)
			expect(totalProcessingTime).toBeLessThan(5000) // All should complete within 5 seconds
			expect(averageProcessingTime).toBeLessThan(1000) // Average should be under 1 second per payment

			// Verify all payments were created successfully
			results.forEach((result, index) => {
				expect(result.id).toBe(`chip_payment_concurrent_${index}`)
				expect(result.reference).toBe(testDonations[index].id)
			})
		})

		it("should maintain performance under load", async () => {
			const donationCount = 50
			const testDonations = await createTestDonations(donationCount)
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Mock responses for all payments
			testDonations.forEach((donation, index) => {
				mockFetch.mockResolvedValueOnce({
					ok: true,
					json: () =>
						Promise.resolve(
							ChipMockResponseFactory.createPaymentCreatedResponse({
								id: `chip_payment_load_${index}`,
								reference: donation.id,
							}),
						),
				})
			})

			const startTime = Date.now()

			// Process payments in batches to simulate real-world load
			const batchSize = 10
			const batches = []
			for (let i = 0; i < testDonations.length; i += batchSize) {
				const batch = testDonations.slice(i, i + batchSize)
				batches.push(batch)
			}

			const allResults = []
			for (const batch of batches) {
				const batchPromises = batch.map((donation) => {
					const params = createPaymentParams(donation)
					return ChipService.createDonationPayment(donation, campaign, params)
				})

				const batchResults = await Promise.all(batchPromises)
				allResults.push(...batchResults)
			}

			const endTime = Date.now()
			const totalProcessingTime = endTime - startTime

			expect(allResults).toHaveLength(donationCount)
			expect(totalProcessingTime).toBeLessThan(15000) // Should complete within 15 seconds

			// Check that processing time scales reasonably
			const averageTimePerPayment = totalProcessingTime / donationCount
			expect(averageTimePerPayment).toBeLessThan(500) // Should average under 500ms per payment
		})
	})

	describe("Webhook Processing Performance", () => {
		it("should process single webhook quickly", async () => {
			const [donation] = await createTestDonations(1)

			const webhookPayload =
				ChipMockResponseFactory.createPaymentSuccessResponse({
					reference: donation.id,
				})

			const startTime = Date.now()
			await ChipService.handleDonationCompleted(webhookPayload)
			const endTime = Date.now()

			const processingTime = endTime - startTime

			expect(processingTime).toBeLessThan(500) // Should complete within 500ms

			// Verify donation was updated
			const [updatedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, donation.id))
				.limit(1)

			expect(updatedDonation.status).toBe("completed")
		})

		it("should handle multiple concurrent webhooks efficiently", async () => {
			const donationCount = 20
			const testDonations = await createTestDonations(donationCount)

			const startTime = Date.now()

			// Process webhooks concurrently
			const webhookPromises = testDonations.map((donation, index) => {
				const webhookPayload =
					ChipMockResponseFactory.createPaymentSuccessResponse({
						id: `chip_payment_webhook_${index}`,
						reference: donation.id,
					})
				return ChipService.handleDonationCompleted(webhookPayload)
			})

			await Promise.all(webhookPromises)
			const endTime = Date.now()

			const totalProcessingTime = endTime - startTime
			const averageProcessingTime = totalProcessingTime / donationCount

			expect(totalProcessingTime).toBeLessThan(3000) // All should complete within 3 seconds
			expect(averageProcessingTime).toBeLessThan(200) // Average should be under 200ms per webhook

			// Verify all donations were updated
			for (const donation of testDonations) {
				const [updatedDonation] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, donation.id))
					.limit(1)

				expect(updatedDonation.status).toBe("completed")
			}
		})

		it("should handle mixed webhook types efficiently", async () => {
			const donationCount = 30
			const testDonations = await createTestDonations(donationCount)

			const webhookTypes = ["completed", "failed", "canceled"]
			const startTime = Date.now()

			// Process mixed webhook types
			const webhookPromises = testDonations.map((donation, index) => {
				const webhookType = webhookTypes[index % webhookTypes.length]

				let webhookPayload: ChipPaymentResponse
				let handler: (payload: ChipPaymentResponse) => Promise<void>

				switch (webhookType) {
					case "completed":
						webhookPayload =
							ChipMockResponseFactory.createPaymentSuccessResponse({
								id: `chip_payment_mixed_${index}`,
								reference: donation.id,
							})
						handler = ChipService.handleDonationCompleted
						break
					case "failed":
						webhookPayload =
							ChipMockResponseFactory.createPaymentFailedResponse({
								id: `chip_payment_mixed_${index}`,
								reference: donation.id,
							})
						handler = ChipService.handleDonationFailed
						break
					case "canceled":
						webhookPayload =
							ChipMockResponseFactory.createPaymentCanceledResponse({
								id: `chip_payment_mixed_${index}`,
								reference: donation.id,
							})
						handler = ChipService.handleDonationCanceled
						break
					default:
						throw new Error("Invalid webhook type")
				}

				return handler.call(ChipService, webhookPayload)
			})

			await Promise.all(webhookPromises)
			const endTime = Date.now()

			const totalProcessingTime = endTime - startTime
			const averageProcessingTime = totalProcessingTime / donationCount

			expect(totalProcessingTime).toBeLessThan(5000) // All should complete within 5 seconds
			expect(averageProcessingTime).toBeLessThan(200) // Average should be under 200ms per webhook

			// Verify all donations were updated with correct statuses
			for (let i = 0; i < testDonations.length; i++) {
				const donation = testDonations[i]
				const expectedStatus = webhookTypes[i % webhookTypes.length]

				const [updatedDonation] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, donation.id))
					.limit(1)

				expect(updatedDonation.status).toBe(expectedStatus)
			}
		})
	})

	describe("Memory Usage and Resource Management", () => {
		it("should not leak memory during repeated operations", async () => {
			const iterations = 100
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Get initial memory usage
			const initialMemory = process.memoryUsage()

			for (let i = 0; i < iterations; i++) {
				const [donation] = await createTestDonations(1)

				// Mock payment creation
				mockFetch.mockResolvedValueOnce({
					ok: true,
					json: () =>
						Promise.resolve(
							ChipMockResponseFactory.createPaymentCreatedResponse({
								id: `chip_payment_memory_${i}`,
								reference: donation.id,
							}),
						),
				})

				const params = createPaymentParams(donation)
				await ChipService.createDonationPayment(donation, campaign, params)

				// Process webhook
				const webhookPayload =
					ChipMockResponseFactory.createPaymentSuccessResponse({
						id: `chip_payment_memory_${i}`,
						reference: donation.id,
					})
				await ChipService.handleDonationCompleted(webhookPayload)

				// Clean up donation immediately to prevent database bloat
				await db.delete(donations).where(eq(donations.id, donation.id))
				testDonationIds = testDonationIds.filter((id) => id !== donation.id)
			}

			// Get final memory usage
			const finalMemory = process.memoryUsage()

			// Memory increase should be reasonable (less than 50MB)
			const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
			expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024) // 50MB
		})

		it("should handle database connection efficiently", async () => {
			const donationCount = 25
			const testDonations = await createTestDonations(donationCount)

			// Measure database operation performance
			const startTime = Date.now()

			// Perform multiple database operations
			const dbOperations = testDonations.map(async (donation) => {
				// Simulate payment creation database update
				await db
					.update(donations)
					.set({ chipPaymentId: `chip_payment_db_${donation.id}` })
					.where(eq(donations.id, donation.id))

				// Simulate webhook processing database update
				await db
					.update(donations)
					.set({ status: "completed" })
					.where(eq(donations.id, donation.id))

				// Read back the donation
				const [updatedDonation] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, donation.id))
					.limit(1)

				return updatedDonation
			})

			const results = await Promise.all(dbOperations)
			const endTime = Date.now()

			const totalDbTime = endTime - startTime
			const averageDbTime = totalDbTime / donationCount

			expect(results).toHaveLength(donationCount)
			expect(totalDbTime).toBeLessThan(2000) // All DB operations within 2 seconds
			expect(averageDbTime).toBeLessThan(100) // Average under 100ms per donation
		})
	})

	describe("Error Handling Performance", () => {
		it("should handle API errors quickly without blocking", async () => {
			const donationCount = 10
			const testDonations = await createTestDonations(donationCount)
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Mock API errors for all requests
			testDonations.forEach(() => {
				mockFetch.mockResolvedValueOnce({
					ok: false,
					status: 500,
					statusText: "Internal Server Error",
					json: () => Promise.resolve({ error: "Server error" }),
				})
			})

			const startTime = Date.now()

			// Attempt to create payments (all will fail)
			const paymentPromises = testDonations.map(async (donation) => {
				const params = createPaymentParams(donation)
				try {
					await ChipService.createDonationPayment(donation, campaign, params)
					return { success: true }
				} catch (error) {
					return { success: false, error }
				}
			})

			const results = await Promise.all(paymentPromises)
			const endTime = Date.now()

			const totalErrorHandlingTime = endTime - startTime
			const averageErrorHandlingTime = totalErrorHandlingTime / donationCount

			// All should fail quickly
			expect(results.every((r) => !r.success)).toBe(true)
			expect(totalErrorHandlingTime).toBeLessThan(2000) // All errors handled within 2 seconds
			expect(averageErrorHandlingTime).toBeLessThan(300) // Average under 300ms per error
		})

		it("should handle timeout scenarios efficiently", async () => {
			const [donation] = await createTestDonations(1)
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Mock network timeout
			mockFetch.mockRejectedValueOnce(new Error("Request timeout"))

			const params = createPaymentParams(donation)

			const startTime = Date.now()

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown timeout error")
			} catch (error) {
				const endTime = Date.now()
				const errorHandlingTime = endTime - startTime

				expect(error.message).toContain(
					"Failed to connect to Chip payment gateway",
				)
				expect(errorHandlingTime).toBeLessThan(1000) // Error should be handled quickly
			}
		})
	})

	describe("Scalability Tests", () => {
		it("should maintain consistent performance as load increases", async () => {
			const loadLevels = [5, 10, 20, 30]
			const performanceResults = []

			for (const loadLevel of loadLevels) {
				const testDonations = await createTestDonations(loadLevel)
				const [campaign] = await db
					.select()
					.from(campaigns)
					.where(eq(campaigns.id, testCampaignId))
					.limit(1)

				// Mock responses
				testDonations.forEach((donation, index) => {
					mockFetch.mockResolvedValueOnce({
						ok: true,
						json: () =>
							Promise.resolve(
								ChipMockResponseFactory.createPaymentCreatedResponse({
									id: `chip_payment_scale_${loadLevel}_${index}`,
									reference: donation.id,
								}),
							),
					})
				})

				const startTime = Date.now()

				// Process payments
				const paymentPromises = testDonations.map((donation) => {
					const params = createPaymentParams(donation)
					return ChipService.createDonationPayment(donation, campaign, params)
				})

				await Promise.all(paymentPromises)
				const endTime = Date.now()

				const totalTime = endTime - startTime
				const averageTime = totalTime / loadLevel

				performanceResults.push({
					loadLevel,
					totalTime,
					averageTime,
				})

				// Clean up donations for this load level
				for (const donation of testDonations) {
					await db.delete(donations).where(eq(donations.id, donation.id))
					testDonationIds = testDonationIds.filter((id) => id !== donation.id)
				}
			}

			// Verify performance doesn't degrade significantly with increased load
			for (let i = 1; i < performanceResults.length; i++) {
				const current = performanceResults[i]
				const previous = performanceResults[i - 1]

				// Average time per payment shouldn't increase by more than 50%
				const performanceDegradation =
					(current.averageTime - previous.averageTime) / previous.averageTime
				expect(performanceDegradation).toBeLessThan(0.5)
			}

			// All load levels should complete within reasonable time
			performanceResults.forEach((result) => {
				expect(result.averageTime).toBeLessThan(1000) // Under 1 second per payment
			})
		})
	})
})
