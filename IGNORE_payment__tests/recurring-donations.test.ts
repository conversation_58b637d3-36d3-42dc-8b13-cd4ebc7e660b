import { eq } from "drizzle-orm"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import type { Campaign } from "../../campaigns/campaigns.schema"
import { campaigns } from "../../campaigns/campaigns.schema"
import { db } from "../../db"
import { donations } from "../../donations/donations.schema"
import {
	type RecurringDonation,
	recurringDonations,
	recurringPaymentAttempts,
} from "../../donations/recurring-donations.schema"
import { users } from "../../users/users.schema"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "../payments.schema"
import { ChipService } from "../payments.service"

// Mock fetch globally
global.fetch = vi.fn()

describe("ChipService - Recurring Donations", () => {
	let testCampaign: Campaign
	let testUser: any
	let testRecurringDonation: RecurringDonation

	beforeEach(async () => {
		// Clean up test data
		await db.delete(recurringPaymentAttempts)
		await db.delete(recurringDonations)
		await db.delete(donations)
		await db.delete(campaigns)
		await db.delete(users)

		// Create test user
		const [user] = await db
			.insert(users)
			.values({
				id: "test-user-id",
				name: "Test User",
				email: "<EMAIL>",
				role: "user",
			})
			.returning()
		testUser = user

		// Create test campaign
		const [campaign] = await db
			.insert(campaigns)
			.values({
				id: "test-campaign-id",
				organizerId: testUser.id,
				name: "Test Campaign",
				description: "Test Description",
				slug: "test-campaign",
				isActive: true,
			})
			.returning()
		testCampaign = campaign

		// Create test recurring donation
		const [recurringDonation] = await db
			.insert(recurringDonations)
			.values({
				id: "test-recurring-id",
				campaignId: testCampaign.id,
				donorId: testUser.id,
				amount: "50.00",
				currency: "MYR",
				frequency: "monthly",
				status: "active",
				chipRecurringToken: "test-token-123",
				donorName: "Test User",
				donorEmail: "<EMAIL>",
				donorPhone: "+60123456789",
				nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
				failedAttempts: 0,
				maxRetries: 3,
			})
			.returning()
		testRecurringDonation = recurringDonation

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://gate.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test-secret-key"
		process.env.CHIP_BRAND_ID = "test-brand-id"
		process.env.CHIP_WEBHOOK_SECRET = "test-webhook-secret"
		process.env.FRONTEND_URL = "https://test-frontend.com"
	})

	afterEach(() => {
		vi.clearAllMocks()
	})

	describe("createRecurringDonationPayment", () => {
		it("should create recurring donation payment with token setup", async () => {
			const mockChipResponse: ChipPaymentResponse = {
				id: "chip-payment-123",
				checkout_url: "https://chip.com/checkout/123",
				is_recurring_token: true,
				recurring_token: null,
				reference: testRecurringDonation.id,
				status: "created",
				// Add other required fields with mock values
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "web",
				purchase: {
					debt: 0,
					notes: "",
					total: 5000,
					currency: "MYR",
					language: "en",
					products: [],
					timezone: "UTC",
					due_strict: false,
					email_message: "",
					total_override: null,
					shipping_options: [],
					subtotal_override: null,
					total_tax_override: null,
					has_upsell_products: false,
					payment_method_details: {},
					request_client_details: [],
					total_discount_override: null,
				},
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.created",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: false,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => mockChipResponse,
			} as Response)

			const params: CreatePaymentParams = {
				amount: 50.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+60123456789",
				fullName: "Test User",
				products: [],
				successUrl: "https://test.com/success",
				failureUrl: "https://test.com/failure",
				successRedirectUrl: "https://test.com/success-redirect",
				failureRedirectUrl: "https://test.com/failure-redirect",
				cancelRedirectUrl: "https://test.com/cancel-redirect",
			}

			const result = await ChipService.createRecurringDonationPayment(
				testRecurringDonation,
				testCampaign,
				params,
			)

			expect(result).toEqual(mockChipResponse)
			expect(fetch).toHaveBeenCalledWith(
				"https://gate.chip-in.asia/api/v1/purchases/",
				expect.objectContaining({
					method: "POST",
					headers: expect.objectContaining({
						"Content-Type": "application/json",
						Authorization: "Bearer test-secret-key",
						"X-Brand-ID": "test-brand-id",
					}),
					body: expect.stringContaining('"is_recurring_token":true'),
				}),
			)
		})

		it("should throw error for inactive campaign", async () => {
			const inactiveCampaign = { ...testCampaign, isActive: false }

			const params: CreatePaymentParams = {
				amount: 50.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+60123456789",
				fullName: "Test User",
				products: [],
				successUrl: "https://test.com/success",
				failureUrl: "https://test.com/failure",
				successRedirectUrl: "https://test.com/success-redirect",
				failureRedirectUrl: "https://test.com/failure-redirect",
				cancelRedirectUrl: "https://test.com/cancel-redirect",
			}

			await expect(
				ChipService.createRecurringDonationPayment(
					testRecurringDonation,
					inactiveCampaign,
					params,
				),
			).rejects.toThrow(
				'Campaign "Test Campaign" is not currently accepting donations',
			)
		})
	})

	describe("processRecurringPayment", () => {
		it("should process recurring payment using stored token", async () => {
			const mockChipResponse: ChipPaymentResponse = {
				id: "recurring-payment-123",
				status: "paid",
				reference: `recurring_${testRecurringDonation.id}_${Date.now()}`,
				// Add other required fields with mock values
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "api",
				purchase: {
					debt: 0,
					notes: "",
					total: 5000,
					currency: "MYR",
					language: "en",
					products: [],
					timezone: "UTC",
					due_strict: false,
					email_message: "",
					total_override: null,
					shipping_options: [],
					subtotal_override: null,
					total_tax_override: null,
					has_upsell_products: false,
					payment_method_details: {},
					request_client_details: [],
					total_discount_override: null,
				},
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.paid",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				checkout_url: "",
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: true,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				recurring_token: "test-token-123",
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				is_recurring_token: false,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => mockChipResponse,
			} as Response)

			const result = await ChipService.processRecurringPayment(
				testRecurringDonation,
			)

			expect(result).toEqual(mockChipResponse)
			expect(fetch).toHaveBeenCalledWith(
				"https://gate.chip-in.asia/api/v1/purchases/",
				expect.objectContaining({
					method: "POST",
					headers: expect.objectContaining({
						"Content-Type": "application/json",
						Authorization: "Bearer test-secret-key",
						"X-Brand-ID": "test-brand-id",
					}),
					body: expect.stringContaining('"recurring_token":"test-token-123"'),
				}),
			)
		})

		it("should throw error for inactive recurring donation", async () => {
			const inactiveRecurringDonation = {
				...testRecurringDonation,
				status: "paused" as const,
			}

			await expect(
				ChipService.processRecurringPayment(inactiveRecurringDonation),
			).rejects.toThrow("Recurring donation is not active")
		})

		it("should throw error for expired token", async () => {
			const expiredRecurringDonation = {
				...testRecurringDonation,
				chipTokenExpiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
			}

			await expect(
				ChipService.processRecurringPayment(expiredRecurringDonation),
			).rejects.toThrow("Recurring payment token has expired")
		})
	})

	describe("handleRecurringTokenSetup", () => {
		it("should handle successful token setup", async () => {
			const mockWebhookData: ChipPaymentResponse = {
				id: "token-setup-payment-123",
				status: "paid",
				reference: testRecurringDonation.id,
				recurring_token: "new-token-456",
				is_recurring_token: true,
				// Add other required fields
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "web",
				purchase: {} as any,
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.paid",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				checkout_url: "",
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: true,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			await ChipService.handleRecurringTokenSetup(mockWebhookData)

			// Verify recurring donation was updated with token
			const [updatedRecurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, testRecurringDonation.id))

			expect(updatedRecurringDonation.chipRecurringToken).toBe("new-token-456")
			expect(updatedRecurringDonation.status).toBe("active")
			expect(updatedRecurringDonation.nextPaymentDate).toBeDefined()

			// Verify initial donation was created
			const donationRecords = await db
				.select()
				.from(donations)
				.where(eq(donations.chipPaymentId, "token-setup-payment-123"))

			expect(donationRecords).toHaveLength(1)
			expect(donationRecords[0].status).toBe("completed")
			expect(donationRecords[0].amount).toBe("50.00")
		})

		it("should throw error when recurring token is missing", async () => {
			const mockWebhookData: ChipPaymentResponse = {
				id: "token-setup-payment-123",
				status: "paid",
				reference: testRecurringDonation.id,
				recurring_token: null,
				is_recurring_token: true,
				// Add minimal required fields
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "web",
				purchase: {} as any,
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.paid",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				checkout_url: "",
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: true,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			await expect(
				ChipService.handleRecurringTokenSetup(mockWebhookData),
			).rejects.toThrow("Recurring token missing from successful payment")
		})
	})

	describe("handleRecurringDonationCompleted", () => {
		beforeEach(async () => {
			// Update recurring donation to have a token
			await db
				.update(recurringDonations)
				.set({
					chipRecurringToken: "active-token-123",
					status: "active",
				})
				.where(eq(recurringDonations.id, testRecurringDonation.id))
		})

		it("should handle completed recurring payment", async () => {
			const mockWebhookData: ChipPaymentResponse = {
				id: "recurring-payment-456",
				status: "paid",
				reference: `recurring_${testRecurringDonation.id}_${Date.now()}`,
				recurring_token: "active-token-123",
				is_recurring_token: false,
				// Add minimal required fields
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "api",
				purchase: {} as any,
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.paid",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				checkout_url: "",
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: true,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			await ChipService.handleRecurringDonationCompleted(mockWebhookData)

			// Verify donation was created
			const donationRecords = await db
				.select()
				.from(donations)
				.where(eq(donations.chipPaymentId, "recurring-payment-456"))

			expect(donationRecords).toHaveLength(1)
			expect(donationRecords[0].status).toBe("completed")
			expect(donationRecords[0].amount).toBe("50.00")
			expect(donationRecords[0].donorMessage).toContain(
				"Recurring monthly donation",
			)

			// Verify recurring donation was updated
			const [updatedRecurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, testRecurringDonation.id))

			expect(updatedRecurringDonation.lastPaymentDate).toBeDefined()
			expect(updatedRecurringDonation.nextPaymentDate).toBeDefined()
			expect(updatedRecurringDonation.failedAttempts).toBe(0)
		})
	})

	describe("handleRecurringDonationFailed", () => {
		beforeEach(async () => {
			// Update recurring donation to have a token and some failed attempts
			await db
				.update(recurringDonations)
				.set({
					chipRecurringToken: "active-token-123",
					status: "active",
					failedAttempts: 1,
				})
				.where(eq(recurringDonations.id, testRecurringDonation.id))
		})

		it("should handle failed recurring payment with retry", async () => {
			const mockWebhookData: ChipPaymentResponse = {
				id: "failed-payment-789",
				status: "failed",
				reference: `recurring_${testRecurringDonation.id}_${Date.now()}`,
				error_description: "Insufficient funds",
				// Add minimal required fields
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "api",
				purchase: {} as any,
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.failed",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				checkout_url: "",
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: false,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				recurring_token: null,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				is_recurring_token: false,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			await ChipService.handleRecurringDonationFailed(mockWebhookData)

			// Verify recurring donation failed attempts were incremented
			const [updatedRecurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, testRecurringDonation.id))

			expect(updatedRecurringDonation.failedAttempts).toBe(2)
			expect(updatedRecurringDonation.status).toBe("active") // Should still be active (not at max retries)
		})

		it("should pause recurring donation after max retries", async () => {
			// Set failed attempts to max - 1
			await db
				.update(recurringDonations)
				.set({ failedAttempts: 2 }) // maxRetries is 3, so this will be the 3rd failure
				.where(eq(recurringDonations.id, testRecurringDonation.id))

			const mockWebhookData: ChipPaymentResponse = {
				id: "failed-payment-final",
				status: "failed",
				reference: `recurring_${testRecurringDonation.id}_${Date.now()}`,
				error_description: "Card expired",
				// Add minimal required fields
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "api",
				purchase: {} as any,
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.failed",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				checkout_url: "",
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: false,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				recurring_token: null,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				is_recurring_token: false,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			await ChipService.handleRecurringDonationFailed(mockWebhookData)

			// Verify recurring donation was paused
			const [updatedRecurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, testRecurringDonation.id))

			expect(updatedRecurringDonation.failedAttempts).toBe(3)
			expect(updatedRecurringDonation.status).toBe("paused")
		})
	})

	describe("calculateNextPaymentDate", () => {
		it("should calculate next monthly payment date", () => {
			const currentDate = new Date("2024-01-15")
			const nextDate = ChipService.calculateNextPaymentDate(
				currentDate,
				"monthly",
			)

			expect(nextDate.getMonth()).toBe(1) // February (0-indexed)
			expect(nextDate.getDate()).toBe(15)
			expect(nextDate.getFullYear()).toBe(2024)
		})

		it("should calculate next quarterly payment date", () => {
			const currentDate = new Date("2024-01-15")
			const nextDate = ChipService.calculateNextPaymentDate(
				currentDate,
				"quarterly",
			)

			expect(nextDate.getMonth()).toBe(3) // April (0-indexed)
			expect(nextDate.getDate()).toBe(15)
			expect(nextDate.getFullYear()).toBe(2024)
		})

		it("should calculate next yearly payment date", () => {
			const currentDate = new Date("2024-01-15")
			const nextDate = ChipService.calculateNextPaymentDate(
				currentDate,
				"yearly",
			)

			expect(nextDate.getMonth()).toBe(0) // January (0-indexed)
			expect(nextDate.getDate()).toBe(15)
			expect(nextDate.getFullYear()).toBe(2025)
		})

		it("should throw error for invalid frequency", () => {
			const currentDate = new Date("2024-01-15")

			expect(() => {
				ChipService.calculateNextPaymentDate(currentDate, "invalid")
			}).toThrow("Invalid frequency: invalid")
		})
	})

	describe("fallbackToOneTimePayment", () => {
		it("should create one-time payment when recurring setup fails", async () => {
			const mockChipResponse: ChipPaymentResponse = {
				id: "fallback-payment-123",
				checkout_url: "https://chip.com/checkout/fallback",
				is_recurring_token: false,
				recurring_token: null,
				reference: "fallback-donation-id",
				status: "created",
				// Add other required fields
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01",
				is_test: false,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test-brand",
				order_id: null,
				platform: "web",
				purchase: {} as any,
				client_id: "test-client",
				viewed_on: 0,
				company_id: "test-company",
				created_on: Date.now(),
				event_type: "purchase.created",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: false,
				status_history: [],
				cancel_redirect: "",
				created_from_ip: "",
				direct_post_url: "",
				force_recurring: false,
				failure_redirect: "",
				success_callback: "",
				success_redirect: "",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "",
				refund_availability: "",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => mockChipResponse,
			} as Response)

			const params: CreatePaymentParams = {
				amount: 50.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+60123456789",
				fullName: "Test User",
				products: [],
				successUrl: "https://test.com/success",
				failureUrl: "https://test.com/failure",
				successRedirectUrl: "https://test.com/success-redirect",
				failureRedirectUrl: "https://test.com/failure-redirect",
				cancelRedirectUrl: "https://test.com/cancel-redirect",
			}

			const result = await ChipService.fallbackToOneTimePayment(
				testRecurringDonation,
				testCampaign,
				params,
			)

			expect(result).toEqual(mockChipResponse)

			// Verify recurring donation was canceled
			const [updatedRecurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, testRecurringDonation.id))

			expect(updatedRecurringDonation.status).toBe("canceled")

			// Verify fallback donation was created
			const fallbackDonations = await db
				.select()
				.from(donations)
				.where(
					eq(
						donations.donorMessage,
						`Fallback from recurring ${testRecurringDonation.frequency} donation`,
					),
				)

			expect(fallbackDonations).toHaveLength(1)
			expect(fallbackDonations[0].status).toBe("pending")
		})
	})
})
