import { describe, expect, it, vi } from "vitest"
import { ChipService } from "../payments.service"

// Mock fetch globally
global.fetch = vi.fn()

describe("ChipService - Recurring Donations - Simple Tests", () => {
	it("should calculate next monthly payment date correctly", () => {
		const currentDate = new Date("2024-01-15")
		const nextDate = ChipService.calculateNextPaymentDate(
			currentDate,
			"monthly",
		)

		expect(nextDate.getMonth()).toBe(1) // February (0-indexed)
		expect(nextDate.getDate()).toBe(15)
		expect(nextDate.getFullYear()).toBe(2024)
	})

	it("should calculate next quarterly payment date correctly", () => {
		const currentDate = new Date("2024-01-15")
		const nextDate = ChipService.calculateNextPaymentDate(
			currentDate,
			"quarterly",
		)

		expect(nextDate.getMonth()).toBe(3) // April (0-indexed)
		expect(nextDate.getDate()).toBe(15)
		expect(nextDate.getFullYear()).toBe(2024)
	})

	it("should calculate next yearly payment date correctly", () => {
		const currentDate = new Date("2024-01-15")
		const nextDate = ChipService.calculateNextPaymentDate(currentDate, "yearly")

		expect(nextDate.getMonth()).toBe(0) // January (0-indexed)
		expect(nextDate.getDate()).toBe(15)
		expect(nextDate.getFullYear()).toBe(2025)
	})

	it("should throw error for invalid frequency", () => {
		const currentDate = new Date("2024-01-15")

		expect(() => {
			ChipService.calculateNextPaymentDate(currentDate, "invalid")
		}).toThrow("Invalid frequency: invalid")
	})
})
