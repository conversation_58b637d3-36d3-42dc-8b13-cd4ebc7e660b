import { eq } from "drizzle-orm"
import { afterEach, beforeEach, describe, expect, it } from "vitest"
import { campaigns } from "../../campaigns/campaigns.schema"
import { db } from "../../db"
import { donations } from "../../donations/donations.schema"
import { users } from "../../users/users.schema"
import type { ChipPaymentResponse } from "../payments.schema"
import { ChipService } from "../payments.service"

describe("Donation Record Lifecycle Integration Tests", () => {
	let testUserId: string
	let testCampaignId: string
	let testDonationId: string

	beforeEach(async () => {
		// Create test user
		const [user] = await db
			.insert(users)
			.values({
				id: `test-user-${Date.now()}`,
				name: "Test User",
				email: `test-${Date.now()}@example.com`,
				emailVerified: false,
				image: null,
				createdAt: new Date(),
				updatedAt: new Date(),
			})
			.returning()

		testUserId = user.id

		// Create test campaign
		const [campaign] = await db
			.insert(campaigns)
			.values({
				name: "Test Campaign",
				slug: `test-campaign-${Date.now()}`,
				description: "A test campaign for integration tests",
				goalAmount: "1000.00",
				currentAmount: "0.00",
				currency: "MYR",
				isActive: true,
				organizerId: testUserId,
			})
			.returning()

		testCampaignId = campaign.id

		// Create test donation
		const [donation] = await db
			.insert(donations)
			.values({
				campaignId: testCampaignId,
				donorId: testUserId,
				amount: "50.00",
				currency: "MYR",
				status: "pending",
				donorName: "John Doe",
				donorEmail: "<EMAIL>",
				donorPhone: "+60123456789",
				donorMessage: "Keep up the good work!",
			})
			.returning()

		testDonationId = donation.id
	})

	afterEach(async () => {
		// Clean up test data
		if (testDonationId) {
			await db.delete(donations).where(eq(donations.id, testDonationId))
		}
		if (testCampaignId) {
			await db.delete(campaigns).where(eq(campaigns.id, testCampaignId))
		}
		if (testUserId) {
			await db.delete(users).where(eq(users.id, testUserId))
		}
	})

	describe("handleDonationCompleted", () => {
		it("should update donation status to completed when payment succeeds", async () => {
			const mockPaymentResponse: ChipPaymentResponse = {
				id: "chip_payment_123",
				status: "paid",
				reference: testDonationId,
				checkout_url: "https://test.chip-in.asia/checkout/chip_payment_123",
				due: 0,
				type: "purchase",
				client: {} as any,
				issued: "2024-01-01T00:00:00Z",
				is_test: true,
				payment: null,
				product: "test",
				user_id: null,
				brand_id: "test_brand_id",
				order_id: null,
				platform: "web",
				purchase: {} as any,
				client_id: "test_client_id",
				viewed_on: 0,
				company_id: "test_company_id",
				created_on: Date.now(),
				event_type: "purchase.paid",
				updated_on: Date.now(),
				invoice_url: null,
				can_retrieve: true,
				send_receipt: false,
				skip_capture: false,
				creator_agent: "test",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {} as any,
				marked_as_paid: false,
				status_history: [],
				cancel_redirect: "https://example.com/cancel",
				created_from_ip: "127.0.0.1",
				direct_post_url: "https://test.chip-in.asia/direct",
				force_recurring: false,
				recurring_token: null,
				failure_redirect: "https://example.com/failure",
				success_callback: "https://example.com/success",
				success_redirect: "https://example.com/success",
				transaction_data: {} as any,
				upsell_campaigns: [],
				refundable_amount: 0,
				is_recurring_token: false,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "test_ref_gen",
				refund_availability: "none",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
			}

			await ChipService.handleDonationCompleted(mockPaymentResponse)

			// Verify donation status was updated
			const [updatedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(updatedDonation.status).toBe("completed")
			expect(updatedDonation.chipPaymentId).toBe("chip_payment_123")
		})

		it("should handle missing donation gracefully", async () => {
			const mockPaymentResponse: ChipPaymentResponse = {
				id: "chip_payment_456",
				status: "paid",
				reference: "00000000-0000-0000-0000-000000000000", // Valid UUID format but non-existent
			} as ChipPaymentResponse

			// Should not throw error
			await expect(
				ChipService.handleDonationCompleted(mockPaymentResponse),
			).resolves.toBeUndefined()
		})
	})

	describe("handleDonationFailed", () => {
		it("should update donation status to failed when payment fails", async () => {
			const mockPaymentResponse: ChipPaymentResponse = {
				id: "chip_payment_789",
				status: "failed",
				reference: testDonationId,
			} as ChipPaymentResponse

			await ChipService.handleDonationFailed(mockPaymentResponse)

			// Verify donation status was updated
			const [updatedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(updatedDonation.status).toBe("failed")
			expect(updatedDonation.chipPaymentId).toBe("chip_payment_789")
		})
	})

	describe("handleDonationCanceled", () => {
		it("should update donation status to canceled when payment is canceled", async () => {
			const mockPaymentResponse: ChipPaymentResponse = {
				id: "chip_payment_999",
				status: "canceled",
				reference: testDonationId,
			} as ChipPaymentResponse

			await ChipService.handleDonationCanceled(mockPaymentResponse)

			// Verify donation status was updated
			const [updatedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(updatedDonation.status).toBe("canceled")
			expect(updatedDonation.chipPaymentId).toBe("chip_payment_999")
		})
	})

	describe("Chip payment ID storage and tracking", () => {
		it("should find donation by chip payment ID when reference is not available", async () => {
			// First, set a chip payment ID on the donation
			await db
				.update(donations)
				.set({ chipPaymentId: "existing_chip_payment_id" })
				.where(eq(donations.id, testDonationId))

			const mockPaymentResponse: ChipPaymentResponse = {
				id: "existing_chip_payment_id",
				status: "paid",
				reference: null, // No reference, should find by chip payment ID
			} as ChipPaymentResponse

			await ChipService.handleDonationCompleted(mockPaymentResponse)

			// Verify donation status was updated
			const [updatedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(updatedDonation.status).toBe("completed")
		})
	})

	describe("Error handling for invalid campaign IDs", () => {
		it("should handle missing campaign gracefully in logs", async () => {
			// This test verifies that the error handling logic exists in the code
			// The actual database constraint prevents us from creating invalid references
			// but the code handles the case where a campaign is not found

			const mockPaymentResponse: ChipPaymentResponse = {
				id: "chip_payment_invalid_campaign",
				status: "paid",
				reference: testDonationId,
			} as ChipPaymentResponse

			// The method should complete without throwing errors
			// Even if the campaign is missing, it should handle it gracefully
			await expect(
				ChipService.handleDonationCompleted(mockPaymentResponse),
			).resolves.toBeUndefined()

			// Verify donation status was updated to completed (since campaign exists in this test)
			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(donation.status).toBe("completed")
		})
	})
})
