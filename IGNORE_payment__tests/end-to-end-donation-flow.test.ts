import { eq } from "drizzle-orm"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { campaigns } from "../../campaigns/campaigns.schema"
import { db } from "../../db"
import { donations } from "../../donations/donations.schema"
import { users } from "../../users/users.schema"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "../payments.schema"
import { ChipService } from "../payments.service"

// Mock fetch for Chip API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

describe("End-to-End Donation Flow Integration Tests", () => {
	let testUserId: string
	let testCampaignId: string
	let testDonationId: string
	let testCampaign: any
	let testDonation: any

	beforeEach(async () => {
		vi.clearAllMocks()

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test_secret_key"
		process.env.CHIP_BRAND_ID = "test_brand_id"
		process.env.CHIP_WEBHOOK_SECRET = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef
-----END PUBLIC KEY-----`
		process.env.FRONTEND_URL = "https://test-frontend.com"
		process.env.BACKEND_URL = "https://test-backend.com"

		// Create test user (organizer)
		const [user] = await db
			.insert(users)
			.values({
				id: `test-user-${Date.now()}`,
				name: "Test Organizer",
				email: `organizer-${Date.now()}@example.com`,
				emailVerified: false,
				image: null,
				role: "organizer",
				createdAt: new Date(),
				updatedAt: new Date(),
			})
			.returning()

		testUserId = user.id

		// Create test campaign
		const [campaign] = await db
			.insert(campaigns)
			.values({
				name: "Test Campaign for E2E",
				slug: `test-campaign-e2e-${Date.now()}`,
				description: "A test campaign for end-to-end integration tests",
				goalAmount: "1000.00",
				currentAmount: "0.00",
				currency: "MYR",
				isActive: true,
				organizerId: testUserId,
			})
			.returning()

		testCampaignId = campaign.id
		testCampaign = campaign

		// Create test donation
		const [donation] = await db
			.insert(donations)
			.values({
				campaignId: testCampaignId,
				donorId: testUserId, // Using same user as donor for simplicity
				amount: "100.00",
				currency: "MYR",
				status: "pending",
				donorName: "John Doe",
				donorEmail: "<EMAIL>",
				donorPhone: "+***********",
				donorMessage: "Great cause! Keep it up!",
			})
			.returning()

		testDonationId = donation.id
		testDonation = donation
	})

	afterEach(async () => {
		// Clean up test data in reverse order of creation
		if (testDonationId) {
			await db.delete(donations).where(eq(donations.id, testDonationId))
		}
		if (testCampaignId) {
			await db.delete(campaigns).where(eq(campaigns.id, testCampaignId))
		}
		if (testUserId) {
			await db.delete(users).where(eq(users.id, testUserId))
		}
	})

	// Helper function to create mock Chip API response
	const createMockChipResponse = (
		overrides: Partial<ChipPaymentResponse> = {},
	): ChipPaymentResponse => ({
		id: `chip_payment_e2e_${Date.now()}`,
		checkout_url: "https://test.chip-in.asia/checkout/chip_payment_e2e",
		status: "created",
		due: 0,
		type: "purchase",
		client: {
			email: "<EMAIL>",
			phone: "+***********",
			full_name: "John Doe",
			cc: [],
			bcc: [],
			city: "",
			state: "",
			country: "",
			zip_code: "",
			bank_code: "",
			brand_name: "",
			legal_name: "",
			tax_number: "",
			client_type: null,
			bank_account: "",
			personal_code: "",
			shipping_city: "",
			shipping_state: "",
			street_address: "",
			delivery_methods: [],
			shipping_country: "",
			shipping_zip_code: "",
			registration_number: "",
			shipping_street_address: "",
		},
		issued: new Date().toISOString(),
		is_test: true,
		payment: null,
		product: "donation",
		user_id: null,
		brand_id: "test_brand_id",
		order_id: null,
		platform: "web",
		purchase: {
			debt: 0,
			notes: "Great cause! Keep it up!",
			total: 10000, // 100.00 in cents
			currency: "MYR",
			language: "en",
			products: [
				{
					name: "Donation to Test Campaign for E2E",
					price: 10000, // 100.00 in cents
					category: "donation",
					quantity: "1",
				},
			],
			timezone: "Asia/Kuala_Lumpur",
			due_strict: false,
			email_message: "",
			total_override: null,
			shipping_options: [],
			subtotal_override: null,
			total_tax_override: null,
			has_upsell_products: false,
			payment_method_details: {},
			request_client_details: [],
			total_discount_override: null,
		},
		client_id: "test_client_id",
		reference: testDonationId,
		viewed_on: 0,
		company_id: "test_company_id",
		created_on: Date.now(),
		event_type: "purchase.created",
		updated_on: Date.now(),
		invoice_url: null,
		can_retrieve: true,
		send_receipt: false,
		skip_capture: false,
		creator_agent: "test",
		referral_code: null,
		can_chargeback: false,
		issuer_details: {
			website: "",
			brand_name: "",
			legal_city: "",
			legal_name: "",
			tax_number: "",
			bank_accounts: [],
			legal_country: "",
			legal_zip_code: "",
			registration_number: "",
			legal_street_address: "",
		},
		marked_as_paid: false,
		status_history: [
			{
				status: "created",
				timestamp: Date.now(),
			},
		],
		cancel_redirect: `https://test-frontend.com/campaigns/${testCampaign.slug}?status=canceled`,
		created_from_ip: "127.0.0.1",
		direct_post_url: "https://test.chip-in.asia/direct",
		force_recurring: false,
		recurring_token: null,
		failure_redirect: `https://test-frontend.com/donation/failed?campaign=${testCampaign.slug}&donation=${testDonationId}&status=failed`,
		success_callback: "https://test-backend.com/api/webhooks/chip",
		success_redirect: `https://test-frontend.com/donation/success?campaign=${testCampaign.slug}&donation=${testDonationId}&status=success`,
		transaction_data: {
			flow: "web",
			extra: {},
			country: "MY",
			attempts: [],
			payment_method: "",
			processing_tx_id: "",
		},
		upsell_campaigns: [],
		refundable_amount: 0,
		is_recurring_token: false,
		billing_template_id: null,
		currency_conversion: null,
		reference_generated: `${testDonationId}_gen`,
		refund_availability: "none",
		referral_campaign_id: null,
		retain_level_details: null,
		referral_code_details: null,
		referral_code_generated: null,
		payment_method_whitelist: null,
		...overrides,
	})

	describe("Complete Donation Payment Flow", () => {
		it("should complete full donation flow from creation to completion", async () => {
			// Step 1: Create donation payment
			const mockChipResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockChipResponse),
			})

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [], // Will be overridden by createDonationPayment
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "", // Will be generated
				failureRedirectUrl: "", // Will be generated
				cancelRedirectUrl: "", // Will be generated
				reference: testDonationId,
				notes: "Great cause! Keep it up!",
			}

			const chipResponse = await ChipService.createDonationPayment(
				testDonation,
				testCampaign,
				paymentParams,
			)

			// Verify payment creation
			expect(chipResponse.id).toBeTruthy()
			expect(chipResponse.checkout_url).toBeTruthy()
			expect(chipResponse.reference).toBe(testDonationId)

			// Verify database was updated with chip payment ID
			const [updatedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(updatedDonation.chipPaymentId).toBe(chipResponse.id)

			// Step 2: Simulate successful payment webhook
			const completedPaymentWebhook = createMockChipResponse({
				id: chipResponse.id,
				status: "paid",
				reference: testDonationId,
			})

			await ChipService.handleDonationCompleted(completedPaymentWebhook)

			// Verify donation status was updated to completed
			const [completedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(completedDonation.status).toBe("completed")
			expect(completedDonation.chipPaymentId).toBe(chipResponse.id)
		})

		it("should handle payment failure flow correctly", async () => {
			// Step 1: Create donation payment
			const mockChipResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockChipResponse),
			})

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [],
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: testDonationId,
				notes: "Great cause! Keep it up!",
			}

			const chipResponse = await ChipService.createDonationPayment(
				testDonation,
				testCampaign,
				paymentParams,
			)

			// Step 2: Simulate failed payment webhook
			const failedPaymentWebhook = createMockChipResponse({
				id: chipResponse.id,
				status: "failed",
				reference: testDonationId,
				error_description: "Payment declined by bank",
			})

			await ChipService.handleDonationFailed(failedPaymentWebhook)

			// Verify donation status was updated to failed
			const [failedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(failedDonation.status).toBe("failed")
			expect(failedDonation.chipPaymentId).toBe(chipResponse.id)
		})

		it("should handle payment cancellation flow correctly", async () => {
			// Step 1: Create donation payment
			const mockChipResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockChipResponse),
			})

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [],
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: testDonationId,
				notes: "Great cause! Keep it up!",
			}

			const chipResponse = await ChipService.createDonationPayment(
				testDonation,
				testCampaign,
				paymentParams,
			)

			// Step 2: Simulate canceled payment webhook
			const canceledPaymentWebhook = createMockChipResponse({
				id: chipResponse.id,
				status: "canceled",
				reference: testDonationId,
			})

			await ChipService.handleDonationCanceled(canceledPaymentWebhook)

			// Verify donation status was updated to canceled
			const [canceledDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(canceledDonation.status).toBe("canceled")
			expect(canceledDonation.chipPaymentId).toBe(chipResponse.id)
		})
	})

	describe("Multiple Payment Scenarios", () => {
		it("should handle multiple donations to the same campaign", async () => {
			// Create second donation
			const [secondDonation] = await db
				.insert(donations)
				.values({
					campaignId: testCampaignId,
					donorId: testUserId,
					amount: "50.00",
					currency: "MYR",
					status: "pending",
					donorName: "Jane Smith",
					donorEmail: "<EMAIL>",
					donorPhone: "+60987654321",
					donorMessage: "Supporting this great cause!",
				})
				.returning()

			try {
				// Create payments for both donations
				const mockChipResponse1 = createMockChipResponse({
					id: "chip_payment_1",
					reference: testDonationId,
				})
				const mockChipResponse2 = createMockChipResponse({
					id: "chip_payment_2",
					reference: secondDonation.id,
				})

				mockFetch
					.mockResolvedValueOnce({
						ok: true,
						json: () => Promise.resolve(mockChipResponse1),
					})
					.mockResolvedValueOnce({
						ok: true,
						json: () => Promise.resolve(mockChipResponse2),
					})

				const paymentParams1: CreatePaymentParams = {
					amount: 100.0,
					currency: "MYR",
					email: "<EMAIL>",
					phone: "+***********",
					fullName: "John Doe",
					products: [],
					successUrl: "https://test-backend.com/api/webhooks/chip",
					failureUrl: "https://test-backend.com/api/webhooks/chip",
					successRedirectUrl: "",
					failureRedirectUrl: "",
					cancelRedirectUrl: "",
					reference: testDonationId,
				}

				const paymentParams2: CreatePaymentParams = {
					amount: 50.0,
					currency: "MYR",
					email: "<EMAIL>",
					phone: "+60987654321",
					fullName: "Jane Smith",
					products: [],
					successUrl: "https://test-backend.com/api/webhooks/chip",
					failureUrl: "https://test-backend.com/api/webhooks/chip",
					successRedirectUrl: "",
					failureRedirectUrl: "",
					cancelRedirectUrl: "",
					reference: secondDonation.id,
				}

				// Create both payments
				const _chipResponse1 = await ChipService.createDonationPayment(
					testDonation,
					testCampaign,
					paymentParams1,
				)
				const _chipResponse2 = await ChipService.createDonationPayment(
					secondDonation,
					testCampaign,
					paymentParams2,
				)

				// Complete both payments
				await ChipService.handleDonationCompleted({
					...mockChipResponse1,
					status: "paid",
				})
				await ChipService.handleDonationCompleted({
					...mockChipResponse2,
					status: "paid",
				})

				// Verify both donations are completed
				const [donation1] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, testDonationId))
					.limit(1)

				const [donation2] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, secondDonation.id))
					.limit(1)

				expect(donation1.status).toBe("completed")
				expect(donation1.chipPaymentId).toBe("chip_payment_1")
				expect(donation2.status).toBe("completed")
				expect(donation2.chipPaymentId).toBe("chip_payment_2")
			} finally {
				// Clean up second donation
				await db.delete(donations).where(eq(donations.id, secondDonation.id))
			}
		})

		it("should handle concurrent payment processing", async () => {
			// Create multiple donations
			const donationPromises = []
			const donationIds = []

			for (let i = 0; i < 3; i++) {
				const donationPromise = db
					.insert(donations)
					.values({
						campaignId: testCampaignId,
						donorId: testUserId,
						amount: "25.00",
						currency: "MYR",
						status: "pending",
						donorName: `Donor ${i + 1}`,
						donorEmail: `donor${i + 1}@example.com`,
						donorPhone: `+6012345678${i}`,
						donorMessage: `Donation ${i + 1}`,
					})
					.returning()

				donationPromises.push(donationPromise)
			}

			const createdDonations = await Promise.all(donationPromises)
			createdDonations.forEach(([donation]) => donationIds.push(donation.id))

			try {
				// Mock multiple Chip API responses
				for (let i = 0; i < 3; i++) {
					mockFetch.mockResolvedValueOnce({
						ok: true,
						json: () =>
							Promise.resolve(
								createMockChipResponse({
									id: `chip_payment_concurrent_${i}`,
									reference: donationIds[i],
								}),
							),
					})
				}

				// Create payments concurrently
				const paymentPromises = createdDonations.map(([donation], index) => {
					const paymentParams: CreatePaymentParams = {
						amount: 25.0,
						currency: "MYR",
						email: `donor${index + 1}@example.com`,
						phone: `+6012345678${index}`,
						fullName: `Donor ${index + 1}`,
						products: [],
						successUrl: "https://test-backend.com/api/webhooks/chip",
						failureUrl: "https://test-backend.com/api/webhooks/chip",
						successRedirectUrl: "",
						failureRedirectUrl: "",
						cancelRedirectUrl: "",
						reference: donation.id,
					}

					return ChipService.createDonationPayment(
						donation,
						testCampaign,
						paymentParams,
					)
				})

				const chipResponses = await Promise.all(paymentPromises)

				// Verify all payments were created
				expect(chipResponses).toHaveLength(3)
				chipResponses.forEach((response, index) => {
					expect(response.id).toBe(`chip_payment_concurrent_${index}`)
					expect(response.reference).toBe(donationIds[index])
				})

				// Complete all payments concurrently
				const completionPromises = chipResponses.map((response) =>
					ChipService.handleDonationCompleted({
						...response,
						status: "paid",
					}),
				)

				await Promise.all(completionPromises)

				// Verify all donations are completed
				for (const donationId of donationIds) {
					const [donation] = await db
						.select()
						.from(donations)
						.where(eq(donations.id, donationId))
						.limit(1)

					expect(donation.status).toBe("completed")
					expect(donation.chipPaymentId).toBeTruthy()
				}
			} finally {
				// Clean up created donations
				for (const donationId of donationIds) {
					await db.delete(donations).where(eq(donations.id, donationId))
				}
			}
		})
	})

	describe("Error Recovery Scenarios", () => {
		it("should handle database update failures gracefully", async () => {
			// Create payment successfully
			const mockChipResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockChipResponse),
			})

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [],
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: testDonationId,
			}

			// This should succeed even if database update fails
			const chipResponse = await ChipService.createDonationPayment(
				testDonation,
				testCampaign,
				paymentParams,
			)

			expect(chipResponse.id).toBeTruthy()
			expect(chipResponse.checkout_url).toBeTruthy()
		})

		it("should handle webhook processing for non-existent donations", async () => {
			const nonExistentDonationId = "00000000-0000-0000-0000-000000000000"

			const webhookPayload = createMockChipResponse({
				id: "chip_payment_nonexistent",
				status: "paid",
				reference: nonExistentDonationId,
			})

			// Should not throw error for non-existent donation
			await expect(
				ChipService.handleDonationCompleted(webhookPayload),
			).resolves.toBeUndefined()
		})

		it("should handle duplicate webhook processing", async () => {
			// Create and complete a donation first
			const mockChipResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockChipResponse),
			})

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [],
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: testDonationId,
			}

			const chipResponse = await ChipService.createDonationPayment(
				testDonation,
				testCampaign,
				paymentParams,
			)

			// Complete the payment
			const completedWebhook = createMockChipResponse({
				id: chipResponse.id,
				status: "paid",
				reference: testDonationId,
			})

			await ChipService.handleDonationCompleted(completedWebhook)

			// Verify donation is completed
			const [completedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(completedDonation.status).toBe("completed")

			// Process the same webhook again (duplicate)
			await ChipService.handleDonationCompleted(completedWebhook)

			// Verify donation status remains completed (no change)
			const [stillCompletedDonation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(stillCompletedDonation.status).toBe("completed")
			expect(stillCompletedDonation.chipPaymentId).toBe(chipResponse.id)
		})
	})

	describe("Campaign State Validation", () => {
		it("should reject payments for inactive campaigns", async () => {
			// Deactivate the campaign
			await db
				.update(campaigns)
				.set({ isActive: false })
				.where(eq(campaigns.id, testCampaignId))

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [],
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: testDonationId,
			}

			// Get updated campaign
			const [inactiveCampaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			await expect(
				ChipService.createDonationPayment(
					testDonation,
					inactiveCampaign,
					paymentParams,
				),
			).rejects.toThrow("not currently accepting donations")

			// Reactivate campaign for cleanup
			await db
				.update(campaigns)
				.set({ isActive: true })
				.where(eq(campaigns.id, testCampaignId))
		})
	})

	describe("Redirect URL Generation", () => {
		it("should generate correct redirect URLs with campaign and donation context", async () => {
			const mockChipResponse = createMockChipResponse()
			mockFetch.mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockChipResponse),
			})

			const paymentParams: CreatePaymentParams = {
				amount: 100.0,
				currency: "MYR",
				email: "<EMAIL>",
				phone: "+***********",
				fullName: "John Doe",
				products: [],
				successUrl: "https://test-backend.com/api/webhooks/chip",
				failureUrl: "https://test-backend.com/api/webhooks/chip",
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: testDonationId,
			}

			await ChipService.createDonationPayment(
				testDonation,
				testCampaign,
				paymentParams,
			)

			// Verify the request was made with correct redirect URLs
			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body)

			expect(requestBody.success_redirect).toContain("test-frontend.com")
			expect(requestBody.success_redirect).toContain(
				`campaign=${testCampaign.slug}`,
			)
			expect(requestBody.success_redirect).toContain(
				`donation=${testDonationId}`,
			)
			expect(requestBody.success_redirect).toContain("status=success")

			expect(requestBody.failure_redirect).toContain("test-frontend.com")
			expect(requestBody.failure_redirect).toContain(
				`campaign=${testCampaign.slug}`,
			)
			expect(requestBody.failure_redirect).toContain(
				`donation=${testDonationId}`,
			)
			expect(requestBody.failure_redirect).toContain("status=failed")

			expect(requestBody.cancel_redirect).toContain("test-frontend.com")
			expect(requestBody.cancel_redirect).toContain(
				`/campaigns/${testCampaign.slug}`,
			)
			expect(requestBody.cancel_redirect).toContain("status=canceled")
		})
	})
})
