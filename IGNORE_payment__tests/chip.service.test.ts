import crypto from "node:crypto"
import { and, eq, lte } from "drizzle-orm"
import { StatusCodes } from "http-status-codes"
import { beforeEach, describe, expect, it, vi } from "vitest"
import type { Campaign } from "@/campaigns/campaigns.schema"
import { db } from "@/db"
import type { Donation } from "@/donations/donations.schema"
import { donations } from "@/donations/donations.schema"
import { DonationService } from "@/donations/donations.service"
import { AppError, ErrorCode } from "@/lib/error-handler"
import { RedirectUrlGenerator } from "@/lib/redirect-urls"
import type { RecurringDonation } from "@/recurring-donations/recurring-donations.schema"
import { recurringDonations, recurringPaymentAttempts } from "@/recurring-donations/recurring-donations.schema"
import { RecurringDonationService } from "@/recurring-donations/recurring-donations.service"
import { ChipService } from "../chip.service"
import type { CreatePaymentParams, ChipPaymentResponse } from "../payments.schema"

// Mock dependencies
vi.mock("../../db", () => ({
	db: {
		select: vi.fn(),
		insert: vi.fn(),
		update: vi.fn(),
		delete: vi.fn(),
	},
}))

vi.mock("../../donations/donations.service", () => ({
	DonationService: {
		handleDonationCompleted: vi.fn(),
		handleDonationCanceled: vi.fn(),
	},
}))

vi.mock("../../recurring-donations/recurring-donations.service", () => ({
	RecurringDonationService: {
		handleRecurringTokenSetup: vi.fn(),
		handleRecurringDonationCanceled: vi.fn(),
	},
}))

vi.mock("../../lib/redirect-urls", () => ({
	RedirectUrlGenerator: {
		fromEnvironment: vi.fn().mockReturnValue({
			generateRedirectUrls: vi.fn().mockReturnValue({
				successRedirectUrl: "https://example.com/success",
				failureRedirectUrl: "https://example.com/failure",
				cancelRedirectUrl: "https://example.com/cancel",
			}),
		}),
		validateEnvironmentConfiguration: vi.fn(),
	},
}))

vi.mock("../../lib/error-handler", () => ({
	AppError: class extends Error {
		constructor(message: string, public code: string, public statusCode: number, public context?: any) {
			super(message)
		}
	},
	ErrorCode: {
		PAYMENT_GATEWAY_ERROR: "PAYMENT_GATEWAY_ERROR",
		PAYMENT_VALIDATION_ERROR: "PAYMENT_VALIDATION_ERROR",
		WEBHOOK_SIGNATURE_INVALID: "WEBHOOK_SIGNATURE_INVALID",
		DATABASE_ERROR: "DATABASE_ERROR",
		CAMPAIGN_INACTIVE: "CAMPAIGN_INACTIVE",
	},
	logError: vi.fn(),
	sanitizeForLogging: vi.fn((obj) => obj),
	validateEnvironmentVariables: vi.fn(),
	validatePaymentAmount: vi.fn(),
}))

// Mock global fetch
global.fetch = vi.fn()

// Mock crypto
vi.mock("node:crypto", () => ({
	default: {
		createVerify: vi.fn(),
		randomUUID: vi.fn(),
	},
}))

// Mock Bun environment
global.Bun = {
	env: {
		CHIP_BASE_URL: "https://gate.chip.com/api/v1",
		CHIP_SECRET_KEY: "test_secret_key",
		CHIP_BRAND_ID: "test_brand_id",
		CHIP_WEBHOOK_SECRET: "test_webhook_secret",
		BACKEND_URL: "http://localhost:3000",
	},
} as any

const mockDb = vi.mocked(db)
const mockFetch = vi.mocked(fetch)
const mockCrypto = vi.mocked(crypto)
const mockDonationService = vi.mocked(DonationService)
const mockRecurringDonationService = vi.mocked(RecurringDonationService)
const mockRedirectUrlGenerator = vi.mocked(RedirectUrlGenerator)

// Mock data
const mockCampaign: Campaign = {
	id: "campaign_123",
	organizerId: "organizer_123",
	name: "Test Campaign",
	description: "Test campaign description",
	slug: "test-campaign",
	isActive: true,
	createdAt: new Date(),
}

const mockDonation: Donation = {
	id: "donation_123",
	campaignId: "campaign_123",
	donorId: "donor_123",
	amount: "100.00",
	currency: "MYR",
	chipPaymentId: null,
	status: "pending",
	donorName: "Test Donor",
	donorEmail: "<EMAIL>",
	donorPhone: "+60123456789",
	donorMessage: "Test message",
	internalNotes: null,
	createdAt: new Date(),
	updatedAt: new Date(),
}

const mockRecurringDonation: RecurringDonation = {
	id: "recurring_123",
	campaignId: "campaign_123",
	donorId: "donor_123",
	amount: "50.00",
	currency: "MYR",
	frequency: "monthly",
	status: "active",
	donorName: "Test Donor",
	donorEmail: "<EMAIL>",
	donorPhone: "+60123456789",
	donorMessage: "Recurring donation",
	chipRecurringToken: "test_token",
	chipTokenExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
	nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
	failedAttempts: 0,
	lastPaymentDate: null,
	createdAt: new Date(),
	updatedAt: new Date(),
}

const mockPaymentParams: CreatePaymentParams = {
	amount: 100.00,
	currency: "MYR",
	email: "<EMAIL>",
	phone: "+60123456789",
	fullName: "Test Donor",
	products: [{
		name: "Donation to Test Campaign",
		price: 100.00,
		category: "donation",
		quantity: "1",
	}],
	successUrl: "http://localhost:3000/api/webhooks/chip",
	failureUrl: "http://localhost:3000/api/webhooks/chip",
	successRedirectUrl: "https://example.com/success",
	failureRedirectUrl: "https://example.com/failure",
	cancelRedirectUrl: "https://example.com/cancel",
	reference: "donation_123",
	notes: "Test donation",
}

const mockChipResponse: ChipPaymentResponse = {
	id: "chip_payment_123",
	checkout_url: "https://payment.chip.com/checkout/123",
	status: "pending",
	reference: "donation_123",
	is_recurring_token: false,
	recurring_token: null,
	purchase: {
		products: [{
			name: "Donation to Test Campaign",
			price: 10000, // in cents
			category: "donation",
			quantity: "1",
		}],
	},
	// Include all required ChipPaymentResponse fields
	due: 0,
	type: "payment",
	client: {} as any,
	issued: new Date().toISOString(),
	is_test: true,
	payment: null,
	product: "donation",
	user_id: null,
	brand_id: "test_brand_id",
	order_id: null,
	platform: "web",
	client_id: "test_client_id",
	viewed_on: 0,
	company_id: "test_company_id",
	created_on: Date.now(),
	event_type: "payment.created",
	updated_on: Date.now(),
	invoice_url: null,
	can_retrieve: true,
	send_receipt: false,
	skip_capture: false,
	creator_agent: "api",
	referral_code: null,
	can_chargeback: false,
	issuer_details: {} as any,
	marked_as_paid: false,
	status_history: [],
	cancel_redirect: "https://example.com/cancel",
	created_from_ip: "127.0.0.1",
	direct_post_url: "",
	force_recurring: false,
	failure_redirect: "https://example.com/failure",
	success_callback: "http://localhost:3000/api/webhooks/chip",
	success_redirect: "https://example.com/success",
	transaction_data: {} as any,
	upsell_campaigns: [],
	refundable_amount: 10000,
	billing_template_id: null,
	currency_conversion: null,
	reference_generated: "donation_123",
	refund_availability: "available",
	referral_campaign_id: null,
	retain_level_details: null,
	referral_code_details: null,
	referral_code_generated: null,
	payment_method_whitelist: null,
}

describe("ChipService", () => {
	let chipService: ChipService

	beforeEach(() => {
		vi.clearAllMocks()
		
		// Reset singleton instance
		// @ts-ignore - Accessing private static property for testing
		ChipService.instance = null
		chipService = ChipService.getInstance()

		// Setup default mock responses
		mockDb.update = vi.fn().mockReturnValue({
			set: vi.fn().mockReturnValue({
				where: vi.fn().mockReturnValue(Promise.resolve()),
			}),
		})

		mockDb.select = vi.fn().mockReturnValue({
			from: vi.fn().mockReturnValue({
				where: vi.fn().mockReturnValue([]),
			}),
		})

		mockDb.insert = vi.fn().mockReturnValue({
			values: vi.fn().mockReturnValue({
				returning: vi.fn().mockReturnValue([{ id: "attempt_123" }]),
			}),
		})

		mockFetch.mockResolvedValue({
			ok: true,
			status: 200,
			statusText: "OK",
			json: vi.fn().mockResolvedValue(mockChipResponse),
		} as any)
	})

	describe("Singleton Pattern", () => {
		it("should return the same instance when called multiple times", () => {
			const instance1 = ChipService.getInstance()
			const instance2 = ChipService.getInstance()
			
			expect(instance1).toBe(instance2)
		})

		it("should have correct gateway name", () => {
			expect(chipService.gatewayName).toBe("Chip")
		})
	})

	describe("Configuration Validation", () => {
		it("should validate required environment variables", () => {
			expect(() => chipService.validateConfiguration()).not.toThrow()
		})

		it("should throw error if base URL is missing", () => {
			// @ts-ignore - Accessing private config for testing
			chipService.config.baseUrl = ""
			
			expect(() => chipService.validateConfiguration()).toThrow("Chip: Base URL is required")
		})

		it("should throw error if secret key is missing", () => {
			// @ts-ignore - Accessing private config for testing
			chipService.config.secretKey = ""
			
			expect(() => chipService.validateConfiguration()).toThrow("Chip: Secret key is required")
		})

		it("should throw error if brand ID is missing", () => {
			// @ts-ignore - Accessing private config for testing
			chipService.config.brandId = ""
			
			expect(() => chipService.validateConfiguration()).toThrow("Chip: Brand ID is required")
		})

		it("should throw error if webhook secret is missing", () => {
			// @ts-ignore - Accessing private config for testing
			chipService.config.webhookSecret = ""
			
			expect(() => chipService.validateConfiguration()).toThrow("Chip: Webhook secret is required")
		})
	})

	describe("Webhook Signature Verification", () => {
		it("should verify valid webhook signature", async () => {
			const payload = JSON.stringify({ test: "data" })
			const signature = "valid_signature"

			const mockVerifier = {
				update: vi.fn(),
				verify: vi.fn().mockReturnValue(true),
			}
			mockCrypto.createVerify.mockReturnValue(mockVerifier as any)

			const result = await chipService.verifyWebhookSignature(payload, signature)

			expect(result.isValid).toBe(true)
			expect(result.error).toBeUndefined()
			expect(mockCrypto.createVerify).toHaveBeenCalledWith("sha256WithRSAEncryption")
			expect(mockVerifier.update).toHaveBeenCalledWith(payload)
			expect(mockVerifier.verify).toHaveBeenCalled()
		})

		it("should reject invalid webhook signature", async () => {
			const payload = JSON.stringify({ test: "data" })
			const signature = "invalid_signature"

			const mockVerifier = {
				update: vi.fn(),
				verify: vi.fn().mockReturnValue(false),
			}
			mockCrypto.createVerify.mockReturnValue(mockVerifier as any)

			const result = await chipService.verifyWebhookSignature(payload, signature)

			expect(result.isValid).toBe(false)
			expect(result.error).toBe("Webhook signature verification failed")
		})

		it("should handle missing payload or signature", async () => {
			const result1 = await chipService.verifyWebhookSignature("", "signature")
			const result2 = await chipService.verifyWebhookSignature("payload", "")

			expect(result1.isValid).toBe(false)
			expect(result1.error).toBe("Missing required parameters for webhook signature verification")
			expect(result2.isValid).toBe(false)
			expect(result2.error).toBe("Missing required parameters for webhook signature verification")
		})

		it("should handle verification errors", async () => {
			const payload = JSON.stringify({ test: "data" })
			const signature = "signature"

			const mockVerifier = {
				update: vi.fn(),
				verify: vi.fn().mockImplementation(() => {
					throw new Error("Verification error")
				}),
			}
			mockCrypto.createVerify.mockReturnValue(mockVerifier as any)

			const result = await chipService.verifyWebhookSignature(payload, signature)

			expect(result.isValid).toBe(false)
			expect(result.error).toBe("Error during webhook signature verification")
		})
	})

	describe("Payment Creation", () => {
		it("should create payment successfully", async () => {
			const result = await chipService.createPayment(mockPaymentParams)

			expect(result).toEqual(mockChipResponse)
			expect(mockFetch).toHaveBeenCalledWith(
				"https://gate.chip.com/api/v1/purchases/",
				expect.objectContaining({
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"Authorization": "Bearer test_secret_key",
						"X-Brand-ID": "test_brand_id",
					},
					body: expect.stringContaining('"amount":10000'), // Amount in cents
				})
			)
		})

		it("should convert amount to cents correctly", async () => {
			await chipService.createPayment(mockPaymentParams)

			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body as string)
			
			expect(requestBody.purchase.amount).toBe(10000) // 100.00 * 100
			expect(requestBody.purchase.products[0].price).toBe(10000)
		})

		it("should include all required payment parameters", async () => {
			await chipService.createPayment(mockPaymentParams)

			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body as string)
			
			expect(requestBody).toMatchObject({
				brand_id: "test_brand_id",
				purchase: {
					amount: 10000,
					currency: "MYR",
					products: [{
						name: "Donation to Test Campaign",
						price: 10000,
						category: "donation",
						quantity: "1",
					}],
					notes: "Test donation",
				},
				client: {
					email: "<EMAIL>",
					phone: "+60123456789",
					full_name: "Test Donor",
				},
				success_callback: "http://localhost:3000/api/webhooks/chip",
				failure_callback: "http://localhost:3000/api/webhooks/chip",
				success_redirect: "https://example.com/success",
				failure_redirect: "https://example.com/failure",
				cancel_redirect: "https://example.com/cancel",
				platform: "web",
				reference: "donation_123",
				is_recurring_token: false,
			})
		})

		it("should handle payment gateway connection errors", async () => {
			mockFetch.mockRejectedValue(new Error("Network error"))

			await expect(chipService.createPayment(mockPaymentParams))
				.rejects.toThrow(AppError)
		})

		it("should handle payment gateway API errors", async () => {
			mockFetch.mockResolvedValue({
				ok: false,
				status: 400,
				statusText: "Bad Request",
				json: vi.fn().mockResolvedValue({ error: "Invalid parameters" }),
			} as any)

			await expect(chipService.createPayment(mockPaymentParams))
				.rejects.toThrow(AppError)
		})

		it("should validate payment parameters", async () => {
			const invalidParams = { ...mockPaymentParams, amount: -10 }

			await expect(chipService.createPayment(invalidParams))
				.rejects.toThrow("Payment amount must be greater than 0")
		})

		it("should validate redirect URLs", async () => {
			const invalidParams = { ...mockPaymentParams, successRedirectUrl: "" }

			await expect(chipService.createPayment(invalidParams))
				.rejects.toThrow(AppError)
		})

		it("should validate URL format", async () => {
			const invalidParams = { ...mockPaymentParams, successRedirectUrl: "not-a-url" }

			await expect(chipService.createPayment(invalidParams))
				.rejects.toThrow(AppError)
		})
	})

	describe("Payment Retrieval", () => {
		it("should retrieve payment details successfully", async () => {
			const paymentId = "chip_payment_123"
			
			const result = await chipService.getPayment(paymentId)

			expect(result).toEqual(mockChipResponse)
			expect(mockFetch).toHaveBeenCalledWith(
				`https://gate.chip.com/api/v1/purchases/${paymentId}`,
				expect.objectContaining({
					method: "GET",
					headers: {
						"Authorization": "Bearer test_secret_key",
						"X-Brand-ID": "test_brand_id",
					},
				})
			)
		})

		it("should validate payment ID", async () => {
			await expect(chipService.getPayment("")).rejects.toThrow(AppError)
			await expect(chipService.getPayment(null as any)).rejects.toThrow(AppError)
			await expect(chipService.getPayment("   ")).rejects.toThrow(AppError)
		})

		it("should handle payment retrieval errors", async () => {
			mockFetch.mockResolvedValue({
				ok: false,
				status: 404,
				statusText: "Not Found",
				json: vi.fn().mockResolvedValue({ error: "Payment not found" }),
			} as any)

			await expect(chipService.getPayment("nonexistent"))
				.rejects.toThrow(AppError)
		})
	})

	describe("Donation Payment Creation", () => {
		it("should create donation payment with proper context", async () => {
			const result = await chipService.createDonationPayment(
				mockDonation,
				mockCampaign,
				mockPaymentParams
			)

			expect(result).toEqual(mockChipResponse)
			expect(mockRedirectUrlGenerator.fromEnvironment).toHaveBeenCalled()
			expect(mockDb.update).toHaveBeenCalledWith(donations)
		})

		it("should reject inactive campaigns", async () => {
			const inactiveCampaign = { ...mockCampaign, isActive: false }

			await expect(
				chipService.createDonationPayment(mockDonation, inactiveCampaign, mockPaymentParams)
			).rejects.toThrow('Campaign "Test Campaign" is not currently accepting donations')
		})

		it("should update donation with payment ID", async () => {
			await chipService.createDonationPayment(mockDonation, mockCampaign, mockPaymentParams)

			expect(mockDb.update).toHaveBeenCalledWith(donations)
			const updateCall = mockDb.update.mock.results[0].value
			expect(updateCall.set).toHaveBeenCalledWith({
				chipPaymentId: "chip_payment_123",
				updatedAt: expect.any(Date),
			})
		})
	})

	describe("Recurring Payment Creation", () => {
		it("should create recurring donation payment with token setup", async () => {
			const recurringParams = { ...mockPaymentParams, isRecurringToken: true }

			const result = await chipService.createRecurringDonationPayment(
				mockRecurringDonation,
				mockCampaign,
				recurringParams
			)

			expect(result).toEqual(mockChipResponse)
			
			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body as string)
			
			expect(requestBody.is_recurring_token).toBe(true)
			expect(requestBody.reference).toBe(mockRecurringDonation.id)
			expect(requestBody.purchase.products[0].category).toBe("recurring_donation")
		})

		it("should process recurring payment with stored token", async () => {
			const result = await chipService.processRecurringPayment(mockRecurringDonation)

			expect(result).toEqual(mockChipResponse)
			
			const fetchCall = mockFetch.mock.calls[0]
			const requestBody = JSON.parse(fetchCall[1].body as string)
			
			expect(requestBody.recurring_token).toBe("test_token")
			expect(requestBody.platform).toBe("api")
		})

		it("should reject inactive recurring donations", async () => {
			const inactiveRecurring = { ...mockRecurringDonation, status: "paused" as const }

			await expect(chipService.processRecurringPayment(inactiveRecurring))
				.rejects.toThrow(AppError)
		})

		it("should reject expired tokens", async () => {
			const expiredRecurring = {
				...mockRecurringDonation,
				chipTokenExpiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
			}

			await expect(chipService.processRecurringPayment(expiredRecurring))
				.rejects.toThrow(AppError)
		})
	})

	describe("Scheduled Recurring Payments", () => {
		it("should process due recurring payments", async () => {
			const dueRecurringDonations = [mockRecurringDonation]
			
			mockDb.select.mockReturnValue({
				from: vi.fn().mockReturnValue({
					where: vi.fn().mockReturnValue(dueRecurringDonations),
				}),
			})

			await chipService.processScheduledRecurringPayments()

			expect(mockDb.select).toHaveBeenCalled()
			expect(mockDb.insert).toHaveBeenCalledWith(recurringPaymentAttempts)
			expect(mockFetch).toHaveBeenCalled()
		})

		it("should handle token expiration during scheduled processing", async () => {
			const expiredRecurring = {
				...mockRecurringDonation,
				chipTokenExpiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Expired
			}
			
			mockDb.select.mockReturnValue({
				from: vi.fn().mockReturnValue({
					where: vi.fn().mockReturnValue([expiredRecurring]),
				}),
			})

			await chipService.processScheduledRecurringPayments()

			// Should update recurring donation status to expired
			expect(mockDb.update).toHaveBeenCalledWith(recurringDonations)
		})

		it("should handle payment processing errors and increment failed attempts", async () => {
			const dueRecurringDonations = [mockRecurringDonation]
			
			mockDb.select.mockReturnValue({
				from: vi.fn().mockReturnValue({
					where: vi.fn().mockReturnValue(dueRecurringDonations),
				}),
			})

			mockFetch.mockRejectedValue(new Error("Payment processing failed"))

			await chipService.processScheduledRecurringPayments()

			// Should increment failed attempts
			expect(mockDb.update).toHaveBeenCalledWith(recurringDonations)
			const updateCall = mockDb.update.mock.results[mockDb.update.mock.results.length - 1].value
			expect(updateCall.set).toHaveBeenCalledWith({
				failedAttempts: 1,
				updatedAt: expect.any(Date),
			})
		})
	})

	describe("Token Expiration Handling", () => {
		it("should handle token expiration correctly", async () => {
			await chipService.handleTokenExpiration(mockRecurringDonation)

			expect(mockDb.update).toHaveBeenCalledWith(recurringDonations)
			const updateCall = mockDb.update.mock.results[0].value
			expect(updateCall.set).toHaveBeenCalledWith({
				status: "expired",
				updatedAt: expect.any(Date),
			})
		})
	})

	describe("Webhook Event Handling", () => {
		it("should handle payment completed for donations", async () => {
			const paymentData = { ...mockChipResponse, status: "completed" }

			await chipService.handlePaymentCompleted(paymentData)

			expect(mockDonationService.handleDonationCompleted).toHaveBeenCalledWith(paymentData)
		})

		it("should handle payment completed for recurring donations", async () => {
			const paymentData = {
				...mockChipResponse,
				status: "completed",
				purchase: {
					products: [{
						name: "Recurring Donation",
						price: 5000,
						category: "recurring_donation",
						quantity: "1",
					}],
				},
			}

			await chipService.handlePaymentCompleted(paymentData)

			// Should call the private method (testing behavior indirectly)
			expect(vi.fn()).toHaveBeenCalledTimes(0) // Placeholder - private method testing
		})

		it("should handle payment completed for recurring token setup", async () => {
			const paymentData = {
				...mockChipResponse,
				status: "completed",
				is_recurring_token: true,
				recurring_token: "new_token",
				purchase: {
					products: [{
						name: "Recurring Donation Setup",
						price: 5000,
						category: "recurring_donation",
						quantity: "1",
					}],
				},
			}

			await chipService.handlePaymentCompleted(paymentData)

			expect(mockRecurringDonationService.handleRecurringTokenSetup).toHaveBeenCalledWith(paymentData)
		})

		it("should handle payment failed", async () => {
			const paymentData = { ...mockChipResponse, status: "failed" }

			await chipService.handlePaymentFailed(paymentData)

			// Should log the failure (testing behavior indirectly)
			expect(vi.fn()).toHaveBeenCalledTimes(0) // Placeholder
		})

		it("should handle payment canceled for donations", async () => {
			const paymentData = { ...mockChipResponse, status: "canceled" }

			await chipService.handlePaymentCanceled(paymentData)

			expect(mockDonationService.handleDonationCanceled).toHaveBeenCalledWith(paymentData)
		})

		it("should handle payment canceled for recurring donations", async () => {
			const paymentData = {
				...mockChipResponse,
				status: "canceled",
				purchase: {
					products: [{
						name: "Recurring Donation",
						price: 5000,
						category: "recurring_donation",
						quantity: "1",
					}],
				},
			}

			await chipService.handlePaymentCanceled(paymentData)

			expect(mockRecurringDonationService.handleRecurringDonationCanceled).toHaveBeenCalledWith(paymentData)
		})

		it("should handle unknown product categories gracefully", async () => {
			const paymentData = {
				...mockChipResponse,
				purchase: {
					products: [{
						name: "Unknown Product",
						price: 5000,
						category: "unknown_category",
						quantity: "1",
					}],
				},
			}

			// Should not throw errors
			await expect(chipService.handlePaymentCompleted(paymentData)).resolves.toBeUndefined()
			await expect(chipService.handlePaymentFailed(paymentData)).resolves.toBeUndefined()
			await expect(chipService.handlePaymentCanceled(paymentData)).resolves.toBeUndefined()
		})

		it("should handle webhook events with missing payment ID", async () => {
			const paymentData = { ...mockChipResponse, id: "" }

			// Should handle gracefully and return early
			await expect(chipService.handlePaymentCompleted(paymentData)).resolves.toBeUndefined()
			await expect(chipService.handlePaymentFailed(paymentData)).resolves.toBeUndefined()
			await expect(chipService.handlePaymentCanceled(paymentData)).resolves.toBeUndefined()
		})
	})

	describe("Utility Methods", () => {
		it("should convert amounts to gateway format correctly", () => {
			// @ts-ignore - Testing protected method
			expect(chipService.convertAmountToGatewayFormat(100.50)).toBe(10050)
			// @ts-ignore
			expect(chipService.convertAmountToGatewayFormat(0.99)).toBe(99)
			// @ts-ignore
			expect(chipService.convertAmountToGatewayFormat(1)).toBe(100)
		})

		it("should convert amounts from gateway format correctly", () => {
			// @ts-ignore - Testing protected method
			expect(chipService.convertAmountFromGatewayFormat(10050)).toBe(100.50)
			// @ts-ignore
			expect(chipService.convertAmountFromGatewayFormat(99)).toBe(0.99)
			// @ts-ignore
			expect(chipService.convertAmountFromGatewayFormat(100)).toBe(1)
		})

		it("should generate redirect URLs correctly", () => {
			// @ts-ignore - Testing protected method
			const urls = chipService.generateDonationRedirectUrls(mockDonation, mockCampaign)
			
			expect(urls).toEqual({
				successRedirectUrl: "https://example.com/success",
				failureRedirectUrl: "https://example.com/failure",
				cancelRedirectUrl: "https://example.com/cancel",
			})
		})

		it("should generate recurring redirect URLs correctly", () => {
			// @ts-ignore - Testing protected method
			const urls = chipService.generateRecurringRedirectUrls(mockRecurringDonation, mockCampaign)
			
			expect(urls).toEqual({
				successRedirectUrl: "https://example.com/success",
				failureRedirectUrl: "https://example.com/failure",
				cancelRedirectUrl: "https://example.com/cancel",
			})
		})
	})

	describe("Error Handling", () => {
		it("should handle database errors during donation update gracefully", async () => {
			mockDb.update.mockImplementation(() => {
				throw new Error("Database connection failed")
			})

			// Should not throw error, but log warning
			await expect(
				chipService.createDonationPayment(mockDonation, mockCampaign, mockPaymentParams)
			).resolves.not.toThrow()
		})

		it("should handle configuration validation errors", () => {
			// @ts-ignore - Reset environment for testing
			global.Bun.env.CHIP_BASE_URL = ""
			
			expect(() => ChipService.getInstance()).toThrow()
		})
	})

	describe("Payment Parameter Validation", () => {
		it("should validate required payment parameters", async () => {
			const invalidParams = { ...mockPaymentParams, email: "" }

			await expect(chipService.createPayment(invalidParams))
				.rejects.toThrow("Valid email address is required")
		})

		it("should validate product requirements", async () => {
			const invalidParams = { ...mockPaymentParams, products: [] }

			await expect(chipService.createPayment(invalidParams))
				.rejects.toThrow("At least one product is required")
		})

		it("should validate full name requirement", async () => {
			const invalidParams = { ...mockPaymentParams, fullName: "" }

			await expect(chipService.createPayment(invalidParams))
				.rejects.toThrow("Full name is required")
		})
	})

	describe("Integration with External Services", () => {
		it("should handle redirect URL generation errors gracefully", async () => {
			mockRedirectUrlGenerator.fromEnvironment.mockImplementation(() => {
				throw new Error("URL generation failed")
			})

			await expect(
				chipService.createDonationPayment(mockDonation, mockCampaign, mockPaymentParams)
			).rejects.toThrow()
		})

		it("should validate environment configuration on initialization", () => {
			expect(() => ChipService.getInstance()).not.toThrow()
		})
	})
})