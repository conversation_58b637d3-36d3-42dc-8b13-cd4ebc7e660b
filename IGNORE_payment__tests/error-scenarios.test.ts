import { eq } from "drizzle-orm"
import { StatusCodes } from "http-status-codes"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { campaigns } from "../../campaigns/campaigns.schema"
import { db } from "../../db"
import { donations } from "../../donations/donations.schema"
import { AppError, ErrorCode } from "../../lib/error-handler"
import { users } from "../../users/users.schema"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "../payments.schema"
import { ChipService } from "../payments.service"

// Mock fetch for Chip API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

describe("Payment Error Scenarios Tests", () => {
	let testUserId: string
	let testCampaignId: string
	let testDonationId: string

	beforeEach(async () => {
		vi.clearAllMocks()

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test_secret_key"
		process.env.CHIP_BRAND_ID = "test_brand_id"
		process.env.CHIP_WEBHOOK_SECRET = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef
-----END PUBLIC KEY-----`
		process.env.FRONTEND_URL = "https://test-frontend.com"
		process.env.BACKEND_URL = "https://test-backend.com"

		// Create test user
		const [user] = await db
			.insert(users)
			.values({
				id: `test-user-error-${Date.now()}`,
				name: "Test User",
				email: `test-error-${Date.now()}@example.com`,
				emailVerified: false,
				image: null,
				createdAt: new Date(),
				updatedAt: new Date(),
			})
			.returning()

		testUserId = user.id

		// Create test campaign
		const [campaign] = await db
			.insert(campaigns)
			.values({
				name: "Test Campaign Error Scenarios",
				slug: `test-campaign-error-${Date.now()}`,
				description: "A test campaign for error scenario tests",
				goalAmount: "1000.00",
				currentAmount: "0.00",
				currency: "MYR",
				isActive: true,
				organizerId: testUserId,
			})
			.returning()

		testCampaignId = campaign.id

		// Create test donation
		const [donation] = await db
			.insert(donations)
			.values({
				campaignId: testCampaignId,
				donorId: testUserId,
				amount: "50.00",
				currency: "MYR",
				status: "pending",
				donorName: "John Doe",
				donorEmail: "<EMAIL>",
				donorPhone: "+60123456789",
				donorMessage: "Test donation for error scenarios",
			})
			.returning()

		testDonationId = donation.id
	})

	afterEach(async () => {
		// Clean up test data
		if (testDonationId) {
			await db.delete(donations).where(eq(donations.id, testDonationId))
		}
		if (testCampaignId) {
			await db.delete(campaigns).where(eq(campaigns.id, testCampaignId))
		}
		if (testUserId) {
			await db.delete(users).where(eq(users.id, testUserId))
		}
	})

	// Helper function to create valid payment parameters
	const createValidPaymentParams = (
		overrides: Partial<CreatePaymentParams> = {},
	): CreatePaymentParams => ({
		amount: 50.0,
		currency: "MYR",
		email: "<EMAIL>",
		phone: "+60123456789",
		fullName: "John Doe",
		products: [
			{
				name: "Test Donation",
				price: 50.0,
				category: "donation",
				quantity: "1",
			},
		],
		successUrl: "https://test-backend.com/api/webhooks/chip",
		failureUrl: "https://test-backend.com/api/webhooks/chip",
		successRedirectUrl: "https://test-frontend.com/donation/success",
		failureRedirectUrl: "https://test-frontend.com/donation/failed",
		cancelRedirectUrl: "https://test-frontend.com/campaigns/test-campaign",
		reference: testDonationId,
		notes: "Test donation payment",
		...overrides,
	})

	describe("Invalid Campaign Scenarios", () => {
		it("should reject payments for inactive campaigns", async () => {
			// Deactivate the campaign
			const [inactiveCampaign] = await db
				.update(campaigns)
				.set({ isActive: false })
				.where(eq(campaigns.id, testCampaignId))
				.returning()

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const params = createValidPaymentParams()

			try {
				await ChipService.createDonationPayment(
					donation,
					inactiveCampaign,
					params,
				)
				expect.fail("Should have thrown an error for inactive campaign")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.CAMPAIGN_INACTIVE)
				expect((error as AppError).statusCode).toBe(StatusCodes.BAD_REQUEST)
				expect((error as AppError).message).toContain(
					"not currently accepting donations",
				)
				expect((error as AppError).details).toEqual(
					expect.objectContaining({
						campaignId: inactiveCampaign.id,
						campaignName: inactiveCampaign.name,
						campaignSlug: inactiveCampaign.slug,
					}),
				)
			}

			// Reactivate for cleanup
			await db
				.update(campaigns)
				.set({ isActive: true })
				.where(eq(campaigns.id, testCampaignId))
		})

		it("should handle non-existent campaigns in webhook processing", async () => {
			// Create a donation with a non-existent campaign ID
			const [donationWithInvalidCampaign] = await db
				.insert(donations)
				.values({
					campaignId: "00000000-0000-0000-0000-000000000000", // Non-existent campaign
					donorId: testUserId,
					amount: "25.00",
					currency: "MYR",
					status: "pending",
					donorName: "Jane Doe",
					donorEmail: "<EMAIL>",
					donorPhone: "+60987654321",
					donorMessage: "Test donation with invalid campaign",
				})
				.returning()

			try {
				const mockWebhookPayload: ChipPaymentResponse = {
					id: "chip_payment_invalid_campaign",
					status: "paid",
					reference: donationWithInvalidCampaign.id,
				} as ChipPaymentResponse

				// This should handle the missing campaign gracefully
				await expect(
					ChipService.handleDonationCompleted(mockWebhookPayload),
				).rejects.toThrow(AppError)

				// Verify the error is properly structured
				try {
					await ChipService.handleDonationCompleted(mockWebhookPayload)
				} catch (error) {
					expect(error).toBeInstanceOf(AppError)
					expect((error as AppError).code).toBe(ErrorCode.CAMPAIGN_NOT_FOUND)
					expect((error as AppError).statusCode).toBe(StatusCodes.NOT_FOUND)
				}
			} finally {
				// Clean up the test donation
				await db
					.delete(donations)
					.where(eq(donations.id, donationWithInvalidCampaign.id))
			}
		})

		it("should validate campaign ownership for organizer operations", async () => {
			// Create another user (different organizer)
			const [anotherUser] = await db
				.insert(users)
				.values({
					id: `another-user-${Date.now()}`,
					name: "Another User",
					email: `another-${Date.now()}@example.com`,
					emailVerified: false,
					image: null,
					createdAt: new Date(),
					updatedAt: new Date(),
				})
				.returning()

			// Create campaign owned by another user
			const [anotherCampaign] = await db
				.insert(campaigns)
				.values({
					name: "Another User's Campaign",
					slug: `another-campaign-${Date.now()}`,
					description: "Campaign owned by different user",
					goalAmount: "500.00",
					currentAmount: "0.00",
					currency: "MYR",
					isActive: true,
					organizerId: anotherUser.id, // Different organizer
				})
				.returning()

			try {
				// Create donation for the other user's campaign
				const [donationForAnotherCampaign] = await db
					.insert(donations)
					.values({
						campaignId: anotherCampaign.id,
						donorId: testUserId,
						amount: "30.00",
						currency: "MYR",
						status: "pending",
						donorName: "Cross Campaign Donor",
						donorEmail: "<EMAIL>",
						donorPhone: "+60111222333",
						donorMessage: "Donation to another user's campaign",
					})
					.returning()

				// This should work fine - donations can be made to any active campaign
				const params = createValidPaymentParams({
					reference: donationForAnotherCampaign.id,
				})

				const mockChipResponse = {
					id: "chip_payment_cross_campaign",
					checkout_url:
						"https://test.chip-in.asia/checkout/chip_payment_cross_campaign",
					status: "created",
				} as ChipPaymentResponse

				mockFetch.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve(mockChipResponse),
				})

				const result = await ChipService.createDonationPayment(
					donationForAnotherCampaign,
					anotherCampaign,
					params,
				)

				expect(result.id).toBe("chip_payment_cross_campaign")

				// Clean up
				await db
					.delete(donations)
					.where(eq(donations.id, donationForAnotherCampaign.id))
			} finally {
				// Clean up test data
				await db.delete(campaigns).where(eq(campaigns.id, anotherCampaign.id))
				await db.delete(users).where(eq(users.id, anotherUser.id))
			}
		})
	})

	describe("Invalid Payment Scenarios", () => {
		it("should handle Chip API authentication errors", async () => {
			const chipAuthError = {
				error: "Unauthorized",
				code: "INVALID_API_KEY",
				message: "The provided API key is invalid",
			}

			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 401,
				statusText: "Unauthorized",
				json: () => Promise.resolve(chipAuthError),
			})

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown an error for authentication failure")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
				expect((error as AppError).statusCode).toBe(StatusCodes.BAD_GATEWAY)
				expect((error as AppError).details).toEqual(
					expect.objectContaining({
						chipError: chipAuthError,
					}),
				)
			}
		})

		it("should handle Chip API rate limiting", async () => {
			const rateLimitError = {
				error: "Rate limit exceeded",
				code: "RATE_LIMIT_EXCEEDED",
				message: "Too many requests. Please try again later.",
				retry_after: 60,
			}

			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 429,
				statusText: "Too Many Requests",
				json: () => Promise.resolve(rateLimitError),
			})

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown an error for rate limiting")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
				expect((error as AppError).statusCode).toBe(StatusCodes.BAD_GATEWAY)
				expect((error as AppError).details).toEqual(
					expect.objectContaining({
						chipError: rateLimitError,
					}),
				)
			}
		})

		it("should handle Chip API server errors", async () => {
			const serverError = {
				error: "Internal Server Error",
				code: "INTERNAL_ERROR",
				message: "An unexpected error occurred on our servers",
			}

			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 500,
				statusText: "Internal Server Error",
				json: () => Promise.resolve(serverError),
			})

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown an error for server error")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
				expect((error as AppError).statusCode).toBe(StatusCodes.BAD_GATEWAY)
			}
		})

		it("should handle network timeouts", async () => {
			mockFetch.mockRejectedValueOnce(new Error("Request timeout"))

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown an error for network timeout")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
				expect((error as AppError).statusCode).toBe(StatusCodes.BAD_GATEWAY)
				expect((error as AppError).message).toContain(
					"Failed to connect to Chip payment gateway",
				)
			}
		})

		it("should handle malformed JSON responses from Chip API", async () => {
			mockFetch.mockResolvedValueOnce({
				ok: false,
				status: 400,
				statusText: "Bad Request",
				json: () => Promise.reject(new Error("Invalid JSON")),
				text: () => Promise.resolve("Invalid JSON response from server"),
			})

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown an error for malformed JSON")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
				expect((error as AppError).statusCode).toBe(StatusCodes.BAD_GATEWAY)
			}
		})
	})

	describe("Invalid Donation Scenarios", () => {
		it("should handle webhook for non-existent donations", async () => {
			const nonExistentDonationId = "00000000-0000-0000-0000-000000000000"

			const mockWebhookPayload: ChipPaymentResponse = {
				id: "chip_payment_nonexistent_donation",
				status: "paid",
				reference: nonExistentDonationId,
			} as ChipPaymentResponse

			// Should handle gracefully without throwing
			try {
				await ChipService.handleDonationCompleted(mockWebhookPayload)
				expect.fail("Should have thrown an error for non-existent donation")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.DONATION_NOT_FOUND)
				expect((error as AppError).statusCode).toBe(StatusCodes.NOT_FOUND)
			}
		})

		it("should handle webhook with missing reference and chip payment ID", async () => {
			const mockWebhookPayload: ChipPaymentResponse = {
				id: "chip_payment_no_reference",
				status: "paid",
				reference: null, // No reference
			} as ChipPaymentResponse

			// Should try to find by chip payment ID, but won't find anything
			try {
				await ChipService.handleDonationCompleted(mockWebhookPayload)
				expect.fail("Should have thrown an error for missing reference")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.DONATION_NOT_FOUND)
			}
		})

		it("should handle duplicate webhook processing gracefully", async () => {
			// First, mark the donation as completed
			await db
				.update(donations)
				.set({
					status: "completed",
					chipPaymentId: "chip_payment_duplicate_test",
				})
				.where(eq(donations.id, testDonationId))

			const mockWebhookPayload: ChipPaymentResponse = {
				id: "chip_payment_duplicate_test",
				status: "paid",
				reference: testDonationId,
			} as ChipPaymentResponse

			// Processing the same webhook again should not cause errors
			await ChipService.handleDonationCompleted(mockWebhookPayload)

			// Verify donation status remains completed
			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			expect(donation.status).toBe("completed")
			expect(donation.chipPaymentId).toBe("chip_payment_duplicate_test")
		})

		it("should handle status transitions correctly", async () => {
			// Test invalid status transitions
			const statusTransitions = [
				{ from: "completed", to: "pending", shouldSucceed: false },
				{ from: "completed", to: "failed", shouldSucceed: false },
				{ from: "failed", to: "completed", shouldSucceed: true },
				{ from: "canceled", to: "completed", shouldSucceed: true },
				{ from: "pending", to: "completed", shouldSucceed: true },
			]

			for (const transition of statusTransitions) {
				// Reset donation status
				await db
					.update(donations)
					.set({
						status: transition.from as any,
						chipPaymentId: `chip_payment_transition_${transition.from}_${transition.to}`,
					})
					.where(eq(donations.id, testDonationId))

				const mockWebhookPayload: ChipPaymentResponse = {
					id: `chip_payment_transition_${transition.from}_${transition.to}`,
					status:
						transition.to === "completed"
							? "paid"
							: transition.to === "failed"
								? "failed"
								: "canceled",
					reference: testDonationId,
				} as ChipPaymentResponse

				if (transition.to === "completed") {
					await ChipService.handleDonationCompleted(mockWebhookPayload)
				} else if (transition.to === "failed") {
					await ChipService.handleDonationFailed(mockWebhookPayload)
				} else if (transition.to === "canceled") {
					await ChipService.handleDonationCanceled(mockWebhookPayload)
				}

				// Verify final status
				const [donation] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, testDonationId))
					.limit(1)

				expect(donation.status).toBe(transition.to)
			}
		})
	})

	describe("Environment Configuration Errors", () => {
		it("should handle missing required environment variables", async () => {
			const requiredVars = [
				"CHIP_BASE_URL",
				"CHIP_SECRET_KEY",
				"CHIP_BRAND_ID",
				"CHIP_WEBHOOK_SECRET",
			]

			for (const varName of requiredVars) {
				// Save original value
				const originalValue = process.env[varName]

				// Remove the environment variable
				delete process.env[varName]

				const [donation] = await db
					.select()
					.from(donations)
					.where(eq(donations.id, testDonationId))
					.limit(1)

				const [campaign] = await db
					.select()
					.from(campaigns)
					.where(eq(campaigns.id, testCampaignId))
					.limit(1)

				const params = createValidPaymentParams()

				try {
					await ChipService.createDonationPayment(donation, campaign, params)
					expect.fail(`Should have thrown an error for missing ${varName}`)
				} catch (error) {
					expect(error).toBeInstanceOf(AppError)
					expect((error as AppError).code).toBe(
						ErrorCode.MISSING_ENVIRONMENT_VARIABLES,
					)
					expect((error as AppError).statusCode).toBe(
						StatusCodes.INTERNAL_SERVER_ERROR,
					)
					expect((error as AppError).message).toContain(varName)
				}

				// Restore original value
				if (originalValue) {
					process.env[varName] = originalValue
				}
			}
		})

		it("should handle invalid environment variable formats", async () => {
			// Test with invalid URL format
			process.env.CHIP_BASE_URL = "not-a-valid-url"

			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			// Mock fetch to simulate connection failure due to invalid URL
			mockFetch.mockRejectedValueOnce(new Error("Invalid URL"))

			try {
				await ChipService.createDonationPayment(donation, campaign, params)
				expect.fail("Should have thrown an error for invalid URL")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
			}

			// Restore valid URL
			process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		})
	})

	describe("Data Validation Errors", () => {
		it("should handle extreme payment amounts", async () => {
			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Test with extremely large amount
			const extremeParams = createValidPaymentParams({
				amount: 999999999.99,
				products: [
					{
						name: "Extreme Donation",
						price: 999999999.99,
						category: "donation",
						quantity: "1",
					},
				],
			})

			try {
				await ChipService.createDonationPayment(
					donation,
					campaign,
					extremeParams,
				)
				expect.fail("Should have thrown an error for extreme amount")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(ErrorCode.PAYMENT_AMOUNT_INVALID)
				expect((error as AppError).message).toContain("cannot exceed")
			}
		})

		it("should handle invalid email formats", async () => {
			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const invalidEmailParams = createValidPaymentParams({
				email: "not-an-email",
			})

			try {
				await ChipService.createDonationPayment(
					donation,
					campaign,
					invalidEmailParams,
				)
				expect.fail("Should have thrown an error for invalid email")
			} catch (error) {
				expect(error).toBeInstanceOf(AppError)
				expect((error as AppError).code).toBe(
					ErrorCode.PAYMENT_VALIDATION_ERROR,
				)
				expect((error as AppError).message).toContain(
					"Valid email address is required",
				)
			}
		})

		it("should handle malformed phone numbers", async () => {
			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			// Phone validation is not strict in current implementation,
			// but we can test with various formats
			const phoneFormats = [
				"", // Empty phone (should be allowed)
				"invalid-phone", // Invalid format (should be allowed for now)
				"+60123456789", // Valid format
			]

			for (const phone of phoneFormats) {
				const params = createValidPaymentParams({ phone })

				const mockResponse = {
					id: "chip_payment_phone_test",
					checkout_url:
						"https://test.chip-in.asia/checkout/chip_payment_phone_test",
					status: "created",
				} as ChipPaymentResponse

				mockFetch.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve(mockResponse),
				})

				// Should not throw error for any phone format currently
				const result = await ChipService.createDonationPayment(
					donation,
					campaign,
					params,
				)
				expect(result.id).toBe("chip_payment_phone_test")
			}
		})
	})

	describe("Concurrent Access Scenarios", () => {
		it("should handle concurrent payment creation for same donation", async () => {
			const [donation] = await db
				.select()
				.from(donations)
				.where(eq(donations.id, testDonationId))
				.limit(1)

			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, testCampaignId))
				.limit(1)

			const params = createValidPaymentParams()

			// Mock successful responses for both requests
			const mockResponse1 = {
				id: "chip_payment_concurrent_1",
				checkout_url:
					"https://test.chip-in.asia/checkout/chip_payment_concurrent_1",
				status: "created",
			} as ChipPaymentResponse

			const mockResponse2 = {
				id: "chip_payment_concurrent_2",
				checkout_url:
					"https://test.chip-in.asia/checkout/chip_payment_concurrent_2",
				status: "created",
			} as ChipPaymentResponse

			mockFetch
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve(mockResponse1),
				})
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve(mockResponse2),
				})

			// Create two concurrent payment requests
			const promise1 = ChipService.createDonationPayment(
				donation,
				campaign,
				params,
			)
			const promise2 = ChipService.createDonationPayment(
				donation,
				campaign,
				params,
			)

			const [result1, result2] = await Promise.all([promise1, promise2])

			// Both should succeed (Chip will handle duplicate prevention)
			expect(result1.id).toBe("chip_payment_concurrent_1")
			expect(result2.id).toBe("chip_payment_concurrent_2")
		})
	})
})
