import { beforeEach, describe, expect, it, vi } from "vitest"
import type { ChipPaymentResponse } from "../payments.schema"

describe("Mock Chip API Responses for Testing", () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	// Comprehensive mock response factory
	class ChipMockResponseFactory {
		static createBaseResponse(
			overrides: Partial<ChipPaymentResponse> = {},
		): ChipPaymentResponse {
			const baseTimestamp = Date.now()

			return {
				id: `chip_mock_${Math.random().toString(36).substring(2, 15)}`,
				checkout_url: "https://test.chip-in.asia/checkout/mock_payment",
				status: "created",
				due: 0,
				type: "purchase",
				client: {
					email: "<EMAIL>",
					phone: "+60*********",
					full_name: "Mock User",
					cc: [],
					bcc: [],
					city: "Kuala Lumpur",
					state: "Selangor",
					country: "MY",
					zip_code: "50000",
					bank_code: "",
					brand_name: "",
					legal_name: "",
					tax_number: "",
					client_type: null,
					bank_account: "",
					personal_code: "",
					shipping_city: "",
					shipping_state: "",
					street_address: "123 Mock Street",
					delivery_methods: [],
					shipping_country: "",
					shipping_zip_code: "",
					registration_number: "",
					shipping_street_address: "",
				},
				issued: new Date(baseTimestamp).toISOString(),
				is_test: true,
				payment: null,
				product: "donation",
				user_id: null,
				brand_id: "mock_brand_id",
				order_id: null,
				platform: "web",
				purchase: {
					debt: 0,
					notes: "Mock donation payment",
					total: 5000, // 50.00 in cents
					currency: "MYR",
					language: "en",
					products: [
						{
							name: "Mock Donation",
							price: 5000, // 50.00 in cents
							category: "donation",
							quantity: "1",
						},
					],
					timezone: "Asia/Kuala_Lumpur",
					due_strict: false,
					email_message: "",
					total_override: null,
					shipping_options: [],
					subtotal_override: null,
					total_tax_override: null,
					has_upsell_products: false,
					payment_method_details: {},
					request_client_details: [],
					total_discount_override: null,
				},
				client_id: "mock_client_id",
				reference: "mock_donation_ref",
				viewed_on: 0,
				company_id: "mock_company_id",
				created_on: baseTimestamp,
				event_type: "purchase.created",
				updated_on: baseTimestamp,
				invoice_url: null,
				can_retrieve: true,
				send_receipt: false,
				skip_capture: false,
				creator_agent: "mock_agent",
				referral_code: null,
				can_chargeback: false,
				issuer_details: {
					website: "https://mock-issuer.com",
					brand_name: "Mock Brand",
					legal_city: "Kuala Lumpur",
					legal_name: "Mock Legal Entity",
					tax_number: "*********",
					bank_accounts: [
						{
							bank_code: "MOCK001",
							bank_account: "*********0",
						},
					],
					legal_country: "MY",
					legal_zip_code: "50000",
					registration_number: "REG123456",
					legal_street_address: "123 Legal Street",
				},
				marked_as_paid: false,
				status_history: [
					{
						status: "created",
						timestamp: baseTimestamp,
					},
				],
				cancel_redirect: "https://mock-frontend.com/cancel",
				created_from_ip: "127.0.0.1",
				direct_post_url: "https://test.chip-in.asia/direct/mock",
				force_recurring: false,
				recurring_token: null,
				failure_redirect: "https://mock-frontend.com/failure",
				success_callback: "https://mock-backend.com/webhook",
				success_redirect: "https://mock-frontend.com/success",
				transaction_data: {
					flow: "web",
					extra: {},
					country: "MY",
					attempts: [],
					payment_method: "",
					processing_tx_id: "",
				},
				upsell_campaigns: [],
				refundable_amount: 0,
				is_recurring_token: false,
				billing_template_id: null,
				currency_conversion: null,
				reference_generated: "mock_ref_generated",
				refund_availability: "none",
				referral_campaign_id: null,
				retain_level_details: null,
				referral_code_details: null,
				referral_code_generated: null,
				payment_method_whitelist: null,
				...overrides,
			}
		}

		// Payment creation response
		static createPaymentCreatedResponse(
			overrides: Partial<ChipPaymentResponse> = {},
		): ChipPaymentResponse {
			return ChipMockResponseFactory.createBaseResponse({
				status: "created",
				event_type: "purchase.created",
				checkout_url:
					"https://test.chip-in.asia/checkout/" +
					(overrides.id || "mock_payment"),
				...overrides,
			})
		}

		// Successful payment response
		static createPaymentSuccessResponse(
			overrides: Partial<ChipPaymentResponse> = {},
		): ChipPaymentResponse {
			const baseTimestamp = Date.now()

			return ChipMockResponseFactory.createBaseResponse({
				status: "paid",
				event_type: "purchase.paid",
				marked_as_paid: true,
				status_history: [
					{
						status: "created",
						timestamp: baseTimestamp - 300000, // 5 minutes ago
					},
					{
						status: "viewed",
						timestamp: baseTimestamp - 240000, // 4 minutes ago
					},
					{
						status: "paid",
						timestamp: baseTimestamp,
					},
				],
				transaction_data: {
					flow: "web",
					extra: {
						payment_method: "fpx",
						bank_code: "MOCK_BANK",
					},
					country: "MY",
					attempts: [
						{
							flow: "web",
							type: "payment",
							error: {
								code: "",
								message: "",
							},
							extra: {
								payment_method: "fpx",
								bank_code: "MOCK_BANK",
							},
							country: "MY",
							client_ip: "127.0.0.1",
							fee_amount: 0,
							successful: true,
							payment_method: "fpx",
							processing_time: 2500,
							processing_tx_id: `mock_tx_${Math.random().toString(36).substring(2, 15)}`,
						},
					],
					payment_method: "fpx",
					processing_tx_id: `mock_tx_${Math.random().toString(36).substring(2, 15)}`,
				},
				...overrides,
			})
		}

		// Failed payment response
		static createPaymentFailedResponse(
			overrides: Partial<ChipPaymentResponse> = {},
		): ChipPaymentResponse {
			const baseTimestamp = Date.now()

			return ChipMockResponseFactory.createBaseResponse({
				status: "failed",
				event_type: "purchase.failed",
				error_description: "Payment declined by bank",
				status_history: [
					{
						status: "created",
						timestamp: baseTimestamp - 300000,
					},
					{
						status: "viewed",
						timestamp: baseTimestamp - 240000,
					},
					{
						status: "failed",
						timestamp: baseTimestamp,
					},
				],
				transaction_data: {
					flow: "web",
					extra: {
						payment_method: "fpx",
						bank_code: "MOCK_BANK",
						error_code: "DECLINED",
						error_message: "Insufficient funds",
					},
					country: "MY",
					attempts: [
						{
							flow: "web",
							type: "payment",
							error: {
								code: "DECLINED",
								message: "Insufficient funds",
							},
							extra: {
								payment_method: "fpx",
								bank_code: "MOCK_BANK",
							},
							country: "MY",
							client_ip: "127.0.0.1",
							fee_amount: 0,
							successful: false,
							payment_method: "fpx",
							processing_time: 1500,
							processing_tx_id: `mock_tx_failed_${Math.random().toString(36).substring(2, 15)}`,
						},
					],
					payment_method: "fpx",
					processing_tx_id: `mock_tx_failed_${Math.random().toString(36).substring(2, 15)}`,
				},
				...overrides,
			})
		}

		// Canceled payment response
		static createPaymentCanceledResponse(
			overrides: Partial<ChipPaymentResponse> = {},
		): ChipPaymentResponse {
			const baseTimestamp = Date.now()

			return ChipMockResponseFactory.createBaseResponse({
				status: "canceled",
				event_type: "purchase.canceled",
				status_history: [
					{
						status: "created",
						timestamp: baseTimestamp - 300000,
					},
					{
						status: "viewed",
						timestamp: baseTimestamp - 120000,
					},
					{
						status: "canceled",
						timestamp: baseTimestamp,
					},
				],
				...overrides,
			})
		}

		// Recurring token setup response
		static createRecurringTokenResponse(
			overrides: Partial<ChipPaymentResponse> = {},
		): ChipPaymentResponse {
			return ChipMockResponseFactory.createBaseResponse({
				status: "paid",
				event_type: "purchase.paid",
				is_recurring_token: true,
				recurring_token: `recurring_token_${Math.random().toString(36).substring(2, 15)}`,
				marked_as_paid: true,
				purchase: {
					debt: 0,
					notes: "Recurring donation setup",
					total: 5000,
					currency: "MYR",
					language: "en",
					products: [
						{
							name: "Recurring Donation Setup",
							price: 5000,
							category: "recurring_donation",
							quantity: "1",
						},
					],
					timezone: "Asia/Kuala_Lumpur",
					due_strict: false,
					email_message: "",
					total_override: null,
					shipping_options: [],
					subtotal_override: null,
					total_tax_override: null,
					has_upsell_products: false,
					payment_method_details: {},
					request_client_details: [],
					total_discount_override: null,
				},
				...overrides,
			})
		}

		// Error responses from Chip API
		static createErrorResponse(errorType: string, overrides: any = {}) {
			const errorResponses = {
				unauthorized: {
					error: "Unauthorized",
					code: "UNAUTHORIZED",
					message: "Invalid API credentials",
					details: {
						api_key: "Invalid or missing API key",
					},
				},
				rate_limit: {
					error: "Rate limit exceeded",
					code: "RATE_LIMIT_EXCEEDED",
					message: "Too many requests. Please try again later.",
					retry_after: 60,
					details: {
						limit: 100,
						window: "1 minute",
						remaining: 0,
					},
				},
				invalid_brand: {
					error: "Invalid brand",
					code: "INVALID_BRAND_ID",
					message: "The specified brand ID is invalid or inactive",
					details: {
						brand_id: "The provided brand ID does not exist",
					},
				},
				invalid_amount: {
					error: "Invalid amount",
					code: "INVALID_AMOUNT",
					message: "Payment amount is invalid",
					details: {
						amount: "Amount must be greater than 0 and less than 1000000",
					},
				},
				server_error: {
					error: "Internal server error",
					code: "INTERNAL_ERROR",
					message: "An unexpected error occurred on our servers",
					details: {
						request_id: `req_${Math.random().toString(36).substring(2, 15)}`,
					},
				},
				maintenance: {
					error: "Service unavailable",
					code: "MAINTENANCE",
					message: "Service is temporarily unavailable due to maintenance",
					details: {
						estimated_duration: "30 minutes",
						retry_after: 1800,
					},
				},
			}

			return {
				...errorResponses[errorType as keyof typeof errorResponses],
				...overrides,
			}
		}
	}

	describe("Mock Response Factory Tests", () => {
		it("should create valid base payment response", () => {
			const response = ChipMockResponseFactory.createBaseResponse()

			expect(response).toHaveProperty("id")
			expect(response).toHaveProperty("checkout_url")
			expect(response).toHaveProperty("status")
			expect(response).toHaveProperty("client")
			expect(response).toHaveProperty("purchase")
			expect(response.is_test).toBe(true)
			expect(response.currency_conversion).toBeNull()
		})

		it("should create payment creation response", () => {
			const response = ChipMockResponseFactory.createPaymentCreatedResponse({
				id: "test_payment_123",
				reference: "donation_456",
			})

			expect(response.id).toBe("test_payment_123")
			expect(response.reference).toBe("donation_456")
			expect(response.status).toBe("created")
			expect(response.event_type).toBe("purchase.created")
			expect(response.checkout_url).toContain("test_payment_123")
		})

		it("should create successful payment response with transaction data", () => {
			const response = ChipMockResponseFactory.createPaymentSuccessResponse({
				id: "successful_payment_789",
			})

			expect(response.id).toBe("successful_payment_789")
			expect(response.status).toBe("paid")
			expect(response.event_type).toBe("purchase.paid")
			expect(response.marked_as_paid).toBe(true)
			expect(response.status_history).toHaveLength(3)
			expect(response.transaction_data.attempts).toHaveLength(1)
			expect(response.transaction_data.attempts[0].successful).toBe(true)
		})

		it("should create failed payment response with error details", () => {
			const response = ChipMockResponseFactory.createPaymentFailedResponse({
				id: "failed_payment_999",
				error_description: "Card expired",
			})

			expect(response.id).toBe("failed_payment_999")
			expect(response.status).toBe("failed")
			expect(response.event_type).toBe("purchase.failed")
			expect(response.error_description).toBe("Card expired")
			expect(response.transaction_data.attempts[0].successful).toBe(false)
			expect(response.transaction_data.attempts[0].error.code).toBe("DECLINED")
		})

		it("should create canceled payment response", () => {
			const response = ChipMockResponseFactory.createPaymentCanceledResponse({
				id: "canceled_payment_111",
			})

			expect(response.id).toBe("canceled_payment_111")
			expect(response.status).toBe("canceled")
			expect(response.event_type).toBe("purchase.canceled")
			expect(response.status_history).toHaveLength(3)
			expect(response.status_history[2].status).toBe("canceled")
		})

		it("should create recurring token response", () => {
			const response = ChipMockResponseFactory.createRecurringTokenResponse({
				id: "recurring_setup_222",
			})

			expect(response.id).toBe("recurring_setup_222")
			expect(response.status).toBe("paid")
			expect(response.is_recurring_token).toBe(true)
			expect(response.recurring_token).toBeTruthy()
			expect(response.purchase.products[0].category).toBe("recurring_donation")
		})
	})

	describe("Error Response Factory Tests", () => {
		it("should create unauthorized error response", () => {
			const error = ChipMockResponseFactory.createErrorResponse("unauthorized")

			expect(error.error).toBe("Unauthorized")
			expect(error.code).toBe("UNAUTHORIZED")
			expect(error.message).toContain("Invalid API credentials")
			expect(error.details).toHaveProperty("api_key")
		})

		it("should create rate limit error response", () => {
			const error = ChipMockResponseFactory.createErrorResponse("rate_limit")

			expect(error.error).toBe("Rate limit exceeded")
			expect(error.code).toBe("RATE_LIMIT_EXCEEDED")
			expect(error.retry_after).toBe(60)
			expect(error.details.limit).toBe(100)
		})

		it("should create invalid brand error response", () => {
			const error = ChipMockResponseFactory.createErrorResponse("invalid_brand")

			expect(error.error).toBe("Invalid brand")
			expect(error.code).toBe("INVALID_BRAND_ID")
			expect(error.details).toHaveProperty("brand_id")
		})

		it("should create server error response", () => {
			const error = ChipMockResponseFactory.createErrorResponse("server_error")

			expect(error.error).toBe("Internal server error")
			expect(error.code).toBe("INTERNAL_ERROR")
			expect(error.details).toHaveProperty("request_id")
		})

		it("should allow overriding error response fields", () => {
			const error = ChipMockResponseFactory.createErrorResponse(
				"unauthorized",
				{
					message: "Custom error message",
					details: {
						custom_field: "custom_value",
					},
				},
			)

			expect(error.message).toBe("Custom error message")
			expect(error.details.custom_field).toBe("custom_value")
		})
	})

	describe("Response Validation Tests", () => {
		it("should create responses with consistent structure", () => {
			const responses = [
				ChipMockResponseFactory.createPaymentCreatedResponse(),
				ChipMockResponseFactory.createPaymentSuccessResponse(),
				ChipMockResponseFactory.createPaymentFailedResponse(),
				ChipMockResponseFactory.createPaymentCanceledResponse(),
				ChipMockResponseFactory.createRecurringTokenResponse(),
			]

			responses.forEach((response) => {
				// Check required fields
				expect(response).toHaveProperty("id")
				expect(response).toHaveProperty("status")
				expect(response).toHaveProperty("client")
				expect(response).toHaveProperty("purchase")
				expect(response).toHaveProperty("created_on")
				expect(response).toHaveProperty("updated_on")

				// Check client structure
				expect(response.client).toHaveProperty("email")
				expect(response.client).toHaveProperty("phone")
				expect(response.client).toHaveProperty("full_name")

				// Check purchase structure
				expect(response.purchase).toHaveProperty("total")
				expect(response.purchase).toHaveProperty("currency")
				expect(response.purchase).toHaveProperty("products")
				expect(Array.isArray(response.purchase.products)).toBe(true)

				// Check timestamps are valid
				expect(typeof response.created_on).toBe("number")
				expect(typeof response.updated_on).toBe("number")
				expect(response.created_on).toBeGreaterThan(0)
				expect(response.updated_on).toBeGreaterThan(0)
			})
		})

		it("should create responses with valid status history", () => {
			const response = ChipMockResponseFactory.createPaymentSuccessResponse()

			expect(Array.isArray(response.status_history)).toBe(true)
			expect(response.status_history.length).toBeGreaterThan(0)

			response.status_history.forEach((entry) => {
				expect(entry).toHaveProperty("status")
				expect(entry).toHaveProperty("timestamp")
				expect(typeof entry.status).toBe("string")
				expect(typeof entry.timestamp).toBe("number")
			})

			// Check chronological order
			for (let i = 1; i < response.status_history.length; i++) {
				expect(response.status_history[i].timestamp).toBeGreaterThanOrEqual(
					response.status_history[i - 1].timestamp,
				)
			}
		})

		it("should create responses with valid transaction data for completed payments", () => {
			const successResponse =
				ChipMockResponseFactory.createPaymentSuccessResponse()
			const failedResponse =
				ChipMockResponseFactory.createPaymentFailedResponse()
			const responses = [successResponse, failedResponse]

			responses.forEach((response) => {
				expect(response.transaction_data).toHaveProperty("flow")
				expect(response.transaction_data).toHaveProperty("attempts")
				expect(Array.isArray(response.transaction_data.attempts)).toBe(true)

				if (response.transaction_data.attempts.length > 0) {
					const attempt = response.transaction_data.attempts[0]
					expect(attempt).toHaveProperty("successful")
					expect(attempt).toHaveProperty("payment_method")
					expect(attempt).toHaveProperty("processing_time")
					expect(typeof attempt.successful).toBe("boolean")
					expect(typeof attempt.processing_time).toBe("number")
				}
			})
		})
	})

	describe("Realistic Scenario Tests", () => {
		it("should create realistic donation flow responses", () => {
			const donationId = "donation_realistic_123"
			const campaignName = "Save the Whales Campaign"
			const donorEmail = "<EMAIL>"
			const amount = 100.0 // RM 100

			// Step 1: Payment creation
			const createdResponse =
				ChipMockResponseFactory.createPaymentCreatedResponse({
					reference: donationId,
					client: {
						...ChipMockResponseFactory.createBaseResponse().client,
						email: donorEmail,
						full_name: "John Doe",
					},
					purchase: {
						...ChipMockResponseFactory.createBaseResponse().purchase,
						total: amount * 100, // Convert to cents
						products: [
							{
								name: `Donation to ${campaignName}`,
								price: amount * 100,
								category: "donation",
								quantity: "1",
							},
						],
					},
				})

			expect(createdResponse.reference).toBe(donationId)
			expect(createdResponse.client.email).toBe(donorEmail)
			expect(createdResponse.purchase.total).toBe(10000) // 100.00 in cents
			expect(createdResponse.purchase.products[0].name).toContain(campaignName)

			// Step 2: Payment completion
			const completedResponse =
				ChipMockResponseFactory.createPaymentSuccessResponse({
					id: createdResponse.id,
					reference: donationId,
					client: createdResponse.client,
					purchase: createdResponse.purchase,
				})

			expect(completedResponse.status).toBe("paid")
			expect(completedResponse.marked_as_paid).toBe(true)
			expect(completedResponse.reference).toBe(donationId)
		})

		it("should create realistic error scenarios", () => {
			// Scenario 1: User enters wrong card details
			const cardDeclinedResponse =
				ChipMockResponseFactory.createPaymentFailedResponse({
					error_description: "Card declined - insufficient funds",
					transaction_data: {
						...ChipMockResponseFactory.createPaymentFailedResponse()
							.transaction_data,
						attempts: [
							{
								flow: "web",
								type: "payment",
								error: {
									code: "INSUFFICIENT_FUNDS",
									message: "Card declined due to insufficient funds",
								},
								extra: {
									payment_method: "card",
									card_type: "visa",
								},
								country: "MY",
								client_ip: "***********",
								fee_amount: 0,
								successful: false,
								payment_method: "card",
								processing_time: 3000,
								processing_tx_id: `tx_declined_${Math.random().toString(36).substring(2, 15)}`,
							},
						],
					},
				})

			expect(cardDeclinedResponse.status).toBe("failed")
			expect(cardDeclinedResponse.error_description).toContain(
				"insufficient funds",
			)
			expect(cardDeclinedResponse.transaction_data.attempts[0].error.code).toBe(
				"INSUFFICIENT_FUNDS",
			)

			// Scenario 2: Network timeout during payment
			const timeoutError = ChipMockResponseFactory.createErrorResponse(
				"server_error",
				{
					message: "Request timeout - please try again",
					code: "TIMEOUT",
					details: {
						timeout_duration: "30 seconds",
						retry_recommended: true,
					},
				},
			)

			expect(timeoutError.code).toBe("TIMEOUT")
			expect(timeoutError.details.retry_recommended).toBe(true)
		})

		it("should create responses for different payment methods", () => {
			const paymentMethods = [
				{ method: "fpx", bank: "MAYBANK", country: "MY" },
				{ method: "card", type: "visa", country: "MY" },
				{ method: "ewallet", provider: "grabpay", country: "MY" },
				{ method: "qr", provider: "duitnow", country: "MY" },
			]

			paymentMethods.forEach((pm) => {
				const response = ChipMockResponseFactory.createPaymentSuccessResponse({
					transaction_data: {
						flow: "web",
						extra: {
							payment_method: pm.method,
							...(pm.bank && { bank_code: pm.bank }),
							...(pm.type && { card_type: pm.type }),
							...(pm.provider && { provider: pm.provider }),
						},
						country: pm.country,
						attempts: [
							{
								flow: "web",
								type: "payment",
								error: { code: "", message: "" },
								extra: {
									payment_method: pm.method,
									...(pm.bank && { bank_code: pm.bank }),
									...(pm.type && { card_type: pm.type }),
									...(pm.provider && { provider: pm.provider }),
								},
								country: pm.country,
								client_ip: "127.0.0.1",
								fee_amount: 0,
								successful: true,
								payment_method: pm.method,
								processing_time: Math.floor(Math.random() * 5000) + 1000,
								processing_tx_id: `tx_${pm.method}_${Math.random().toString(36).substring(2, 15)}`,
							},
						],
						payment_method: pm.method,
						processing_tx_id: `tx_${pm.method}_${Math.random().toString(36).substring(2, 15)}`,
					},
				})

				expect(response.transaction_data.payment_method).toBe(pm.method)
				expect(response.transaction_data.attempts[0].payment_method).toBe(
					pm.method,
				)
				expect(response.transaction_data.attempts[0].successful).toBe(true)
			})
		})
	})

	// Export the factory for use in other tests
	describe("Factory Export", () => {
		it("should export factory for use in other test files", () => {
			// This test ensures the factory can be imported and used
			expect(ChipMockResponseFactory).toBeDefined()
			expect(typeof ChipMockResponseFactory.createBaseResponse).toBe("function")
			expect(typeof ChipMockResponseFactory.createPaymentCreatedResponse).toBe(
				"function",
			)
			expect(typeof ChipMockResponseFactory.createPaymentSuccessResponse).toBe(
				"function",
			)
			expect(typeof ChipMockResponseFactory.createPaymentFailedResponse).toBe(
				"function",
			)
			expect(typeof ChipMockResponseFactory.createPaymentCanceledResponse).toBe(
				"function",
			)
			expect(typeof ChipMockResponseFactory.createRecurringTokenResponse).toBe(
				"function",
			)
			expect(typeof ChipMockResponseFactory.createErrorResponse).toBe(
				"function",
			)
		})
	})
})

// Export the factory for use in other test files
export type { ChipMockResponseFactory }
