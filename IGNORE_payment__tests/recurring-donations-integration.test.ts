import { eq } from "drizzle-orm"
import { nanoid } from "nanoid"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { campaigns } from "../../campaigns/campaigns.schema"
import { db } from "../../db"
import { donations } from "../../donations/donations.schema"
import { recurringDonations } from "../../donations/recurring-donations.schema"
import { users } from "../../users/users.schema"
import type { ChipPaymentResponse } from "../payments.schema"
import { ChipService } from "../payments.service"

// Mock fetch globally
global.fetch = vi.fn()

describe("ChipService - Recurring Donations - Integration Tests", () => {
	beforeEach(async () => {
		// Clean up test data
		await db.delete(donations)
		await db.delete(recurringDonations)
		await db.delete(campaigns)
		await db.delete(users)

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://gate.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test-secret-key"
		process.env.CHIP_BRAND_ID = "test-brand-id"
		process.env.CHIP_WEBHOOK_SECRET = "test-webhook-secret"
		process.env.FRONTEND_URL = "https://test-frontend.com"
	})

	it("should handle recurring token setup successfully", async () => {
		// Create test user
		const [testUser] = await db
			.insert(users)
			.values({
				id: nanoid(),
				name: "Test User",
				email: "<EMAIL>",
				role: "user",
			})
			.returning()

		// Create test campaign
		const [testCampaign] = await db
			.insert(campaigns)
			.values({
				organizerId: testUser.id,
				name: "Test Campaign",
				description: "Test Description",
				slug: "test-campaign",
				isActive: true,
			})
			.returning()

		// Create test recurring donation
		const [testRecurringDonation] = await db
			.insert(recurringDonations)
			.values({
				campaignId: testCampaign.id,
				donorId: testUser.id,
				amount: "50.00",
				currency: "MYR",
				frequency: "monthly",
				status: "active",
				chipRecurringToken: "temp-token",
				donorName: "Test User",
				donorEmail: "<EMAIL>",
				donorPhone: "+60123456789",
				nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
				failedAttempts: 0,
				maxRetries: 3,
			})
			.returning()

		const mockWebhookData: ChipPaymentResponse = {
			id: "token-setup-payment-123",
			status: "paid",
			reference: testRecurringDonation.id,
			recurring_token: "new-token-456",
			is_recurring_token: true,
			// Add minimal required fields
			due: 0,
			type: "purchase",
			client: {} as any,
			issued: "2024-01-01",
			is_test: false,
			payment: null,
			product: "test",
			user_id: null,
			brand_id: "test-brand",
			order_id: null,
			platform: "web",
			purchase: {} as any,
			client_id: "test-client",
			viewed_on: 0,
			company_id: "test-company",
			created_on: Date.now(),
			event_type: "purchase.paid",
			updated_on: Date.now(),
			invoice_url: null,
			can_retrieve: true,
			checkout_url: "",
			send_receipt: false,
			skip_capture: false,
			creator_agent: "test",
			referral_code: null,
			can_chargeback: false,
			issuer_details: {} as any,
			marked_as_paid: true,
			status_history: [],
			cancel_redirect: "",
			created_from_ip: "",
			direct_post_url: "",
			force_recurring: false,
			failure_redirect: "",
			success_callback: "",
			success_redirect: "",
			transaction_data: {} as any,
			upsell_campaigns: [],
			refundable_amount: 0,
			billing_template_id: null,
			currency_conversion: null,
			reference_generated: "",
			refund_availability: "",
			referral_campaign_id: null,
			retain_level_details: null,
			referral_code_details: null,
			referral_code_generated: null,
			payment_method_whitelist: null,
		}

		await ChipService.handleRecurringTokenSetup(mockWebhookData)

		// Verify recurring donation was updated with token
		const [updatedRecurringDonation] = await db
			.select()
			.from(recurringDonations)
			.where(eq(recurringDonations.id, testRecurringDonation.id))

		expect(updatedRecurringDonation.chipRecurringToken).toBe("new-token-456")
		expect(updatedRecurringDonation.status).toBe("active")
		expect(updatedRecurringDonation.nextPaymentDate).toBeDefined()

		// Verify initial donation was created
		const donationRecords = await db
			.select()
			.from(donations)
			.where(eq(donations.chipPaymentId, "token-setup-payment-123"))

		expect(donationRecords).toHaveLength(1)
		expect(donationRecords[0].status).toBe("completed")
		expect(donationRecords[0].amount).toBe("50.00")
	})

	it("should create recurring donation payment with proper parameters", async () => {
		// Create test user
		const [testUser] = await db
			.insert(users)
			.values({
				id: nanoid(),
				name: "Test User 2",
				email: "<EMAIL>",
				role: "user",
			})
			.returning()

		// Create test campaign
		const [testCampaign] = await db
			.insert(campaigns)
			.values({
				organizerId: testUser.id,
				name: "Test Campaign 2",
				description: "Test Description 2",
				slug: "test-campaign-2",
				isActive: true,
			})
			.returning()

		// Create test recurring donation
		const [testRecurringDonation] = await db
			.insert(recurringDonations)
			.values({
				campaignId: testCampaign.id,
				donorId: testUser.id,
				amount: "100.00",
				currency: "MYR",
				frequency: "monthly",
				status: "active",
				chipRecurringToken: "test-token-123",
				donorName: "Test User 2",
				donorEmail: "<EMAIL>",
				donorPhone: "+60123456789",
				nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
				failedAttempts: 0,
				maxRetries: 3,
			})
			.returning()

		const mockChipResponse: ChipPaymentResponse = {
			id: "chip-payment-123",
			checkout_url: "https://chip.com/checkout/123",
			is_recurring_token: true,
			recurring_token: null,
			reference: testRecurringDonation.id,
			status: "created",
			// Add other required fields with mock values
			due: 0,
			type: "purchase",
			client: {} as any,
			issued: "2024-01-01",
			is_test: false,
			payment: null,
			product: "test",
			user_id: null,
			brand_id: "test-brand",
			order_id: null,
			platform: "web",
			purchase: {
				debt: 0,
				notes: "",
				total: 10000,
				currency: "MYR",
				language: "en",
				products: [],
				timezone: "UTC",
				due_strict: false,
				email_message: "",
				total_override: null,
				shipping_options: [],
				subtotal_override: null,
				total_tax_override: null,
				has_upsell_products: false,
				payment_method_details: {},
				request_client_details: [],
				total_discount_override: null,
			},
			client_id: "test-client",
			viewed_on: 0,
			company_id: "test-company",
			created_on: Date.now(),
			event_type: "purchase.created",
			updated_on: Date.now(),
			invoice_url: null,
			can_retrieve: true,
			send_receipt: false,
			skip_capture: false,
			creator_agent: "test",
			referral_code: null,
			can_chargeback: false,
			issuer_details: {} as any,
			marked_as_paid: false,
			status_history: [],
			cancel_redirect: "",
			created_from_ip: "",
			direct_post_url: "",
			force_recurring: false,
			failure_redirect: "",
			success_callback: "",
			success_redirect: "",
			transaction_data: {} as any,
			upsell_campaigns: [],
			refundable_amount: 0,
			billing_template_id: null,
			currency_conversion: null,
			reference_generated: "",
			refund_availability: "",
			referral_campaign_id: null,
			retain_level_details: null,
			referral_code_details: null,
			referral_code_generated: null,
			payment_method_whitelist: null,
		}

		vi.spyOn(global, "fetch").mockResolvedValueOnce({
			ok: true,
			json: async () => mockChipResponse,
		} as Response)

		const params = {
			amount: 100.0,
			currency: "MYR",
			email: "<EMAIL>",
			phone: "+60123456789",
			fullName: "Test User 2",
			products: [],
			successUrl: "https://test.com/success",
			failureUrl: "https://test.com/failure",
			successRedirectUrl: "https://test.com/success-redirect",
			failureRedirectUrl: "https://test.com/failure-redirect",
			cancelRedirectUrl: "https://test.com/cancel-redirect",
		}

		const result = await ChipService.createRecurringDonationPayment(
			testRecurringDonation,
			testCampaign,
			params,
		)

		expect(result).toEqual(mockChipResponse)
		expect(fetch).toHaveBeenCalledWith(
			"https://gate.chip-in.asia/api/v1/purchases/",
			expect.objectContaining({
				method: "POST",
				headers: expect.objectContaining({
					"Content-Type": "application/json",
					Authorization: expect.stringContaining("Bearer"),
				}),
				body: expect.stringContaining('"is_recurring_token":true'),
			}),
		)
	})
})
