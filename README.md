# Donation SaaS Platform - MVP & 1.0 Action Plan

## Executive Summary
A two-phase approach to building a donation SaaS platform: starting with a super minimal MVP to validate core functionality, then expanding to a feature-complete 1.0 release.

## Tech Stack

### Backend
- **Runtime**: Bun (for Elysia compatibility and performance)
- **Framework**: Elysia (Fast, TypeScript-first, excellent DX)
- **Database**: PostgreSQL 15+
- **ORM**: Drizzle ORM (Type-safe, performant)
- **Authentication**: Better-Auth (Modern, TypeScript-first)
- **Validation**: Zod
- **Queue**: BullMQ with Redis (for webhooks & async jobs)

### Frontend
- **Marketing Site**: Next.js 14+ (App Router)
- **SaaS Application**: Vite + TanStack Router
- **UI Components**: Shadcn/ui
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod
- **Styling**: Tailwind CSS
- **Language**: TypeScript (strict mode everywhere)

### Infrastructure
- **Hosting**: Railway.app (MVP) → AWS/GCP (1.0)
- **Database**: Supabase or Neon (managed PostgreSQL)
- **File Storage**: Cloudflare R2 or AWS S3
- **CDN**: Cloudflare
- **Monitoring**: Sentry + Better Stack

---

## Phase 1: Super Minimal MVP (4-6 weeks)

### Goal
Validate core donation flow with minimal features. Get something working that can process real donations.

### Core Features Only
1. **Single payment gateway (CHIP)**
2. **Basic organizer signup & login**
3. **Create one evergreen donation campaign per organizer**
4. **Public donation page**
5. **Process donations & auto-register donors**
6. **View list of donations (organizer dashboard)**
7. **Basic email receipts**

### Database Schema (MVP)

```typescript
// Drizzle Schema - MVP
import { pgTable, uuid, varchar, decimal, timestamp, pgEnum } from 'drizzle-orm/pg-core';

export const userRoleEnum = pgEnum('user_role', ['admin', 'organizer', 'user']);
export const donationStatusEnum = pgEnum('donation_status', ['pending', 'completed', 'failed']);

export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  role: userRoleEnum('role').notNull(),
  passwordHash: varchar('password_hash', { length: 255 }), // null for donors
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const campaigns = pgTable('campaigns', {
  id: uuid('id').defaultRandom().primaryKey(),
  organizerId: uuid('organizer_id').references(() => users.id).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  slug: varchar('slug', { length: 255 }).notNull().unique(), // for public URL
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const donations = pgTable('donations', {
  id: uuid('id').defaultRandom().primaryKey(),
  campaignId: uuid('campaign_id').references(() => campaigns.id).notNull(),
  donorId: uuid('donor_id').references(() => users.id).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('MYR').notNull(),
  chipPaymentId: varchar('chip_payment_id', { length: 255 }),
  status: donationStatusEnum('status').notNull(),
  donorName: varchar('donor_name', { length: 255 }).notNull(),
  donorEmail: varchar('donor_email', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});
```

### Project Structure (Current)

```
donorcare/
├── donorcare_be/        # Elysia API Backend
│   ├── src/
│   │   ├── campaigns/
│   │   │   └── campaigns.controller.ts
│   │   ├── donations/
│   │   │   └── donations.controller.ts
│   │   ├── users/
│   │   │   └── users.schema.ts
│   │   ├── lib/
│   │   │   └── auth.ts
│   │   └── index.ts
│   └── package.json
│
├── donorcare_app/       # Admin Panel (TanStack Router)
│   ├── src/
│   │   ├── routes/
│   │   │   ├── __root.tsx
│   │   │   ├── _authenticated/
│   │   │   │   ├── dashboard.tsx
│   │   │   │   └── settings.tsx
│   │   │   └── login.tsx
│   │   ├── components/
│   │   ├── lib/
│   │   │   ├── auth-client.ts
│   │   │   └── auth-store.ts
│   │   └── main.tsx
│   └── package.json
│
├── donorcare_fe/        # Public Frontend (TanStack Start SSR)
│   ├── src/
│   │   ├── routes/
│   │   │   ├── __root.tsx
│   │   │   ├── index.tsx
│   │   │   └── donate/
│   │   │       └── $slug.tsx
│   │   ├── components/
│   │   ├── lib/
│   │   │   └── api.ts
│   │   └── main.tsx
│   └── package.json
│
├── README.md
└── CLAUDE.md
```

### Implementation Tasks (MVP)

#### Week 1-2: Foundation
- [ ] Set up monorepo with Turborepo
- [ ] Configure Elysia backend with TypeScript
- [ ] Set up PostgreSQL with Drizzle ORM
- [ ] Implement Better-Auth with email/password
- [ ] Create basic database schema
- [ ] Set up Vite + TanStack Router

#### Week 3-4: Core Flow
- [ ] Build organizer registration/login
- [ ] Create campaign creation endpoint
- [ ] Implement CHIP payment integration
- [ ] Build public donation page
- [ ] Handle payment webhooks
- [ ] Auto-create donor accounts on successful payment

#### Week 5-6: Dashboard & Polish
- [ ] Build minimal organizer dashboard
- [ ] Display donations list with donor info
- [ ] Implement email receipts (Resend or SendGrid)
- [ ] Add basic error handling
- [ ] Deploy to Railway/Vercel
- [ ] Test with real payments

### API Endpoints (MVP)

```typescript
// Elysia Routes Structure
app.group('/api', (app) =>
  app
    // Auth
    .post('/auth/register', registerOrganizer)
    .post('/auth/login', login)
    .post('/auth/logout', logout)
    .get('/auth/me', getMe)

    // Campaigns
    .get('/campaigns/my', getCampaigns)      // organizer's campaigns
    .post('/campaigns', createCampaign)       // create campaign
    .get('/campaigns/:slug', getCampaign)     // public campaign info

    // Donations
    .post('/donations/initiate', initiateDonation)  // start CHIP payment
    .post('/donations/webhook', handleChipWebhook)  // CHIP callback
    .get('/donations', getDonations)                // organizer's donations
)
```

### Deployment (MVP)
- **Backend**: Railway.app (Elysia)
- **Database**: Supabase PostgreSQL
- **Frontend**: Vercel (Vite app)
- **Domain**: Single domain with subdomains (app.yourdomain.com)

---

## Phase 2: Feature Complete 1.0 (8-10 weeks)

### Goal
Build a production-ready platform with all planned features, multiple payment gateways, and scalable architecture.

### Additional Features
1. **Admin panel with approval system**
2. **Multiple payment gateways**
3. **Time-limited & amount-limited campaigns**
4. **Marketing website**
5. **Advanced donor management**
6. **Analytics & reporting**
7. **Email notifications & campaigns**
8. **API rate limiting & security**
9. **Subscription tiers for organizers**
10. **White-label options**

### Enhanced Database Schema (1.0)

```typescript
// Additional tables for 1.0

export const organizationStatusEnum = pgEnum('org_status', ['pending', 'approved', 'suspended']);
export const campaignTypeEnum = pgEnum('campaign_type', ['evergreen', 'time_limited', 'amount_limited']);
export const subscriptionTierEnum = pgEnum('subscription_tier', ['free', 'pro', 'enterprise']);

export const organizations = pgTable('organizations', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 50 }),
  address: text('address'),
  status: organizationStatusEnum('status').default('pending').notNull(),
  subscriptionTier: subscriptionTierEnum('tier').default('free').notNull(),
  verificationDocuments: jsonb('verification_documents'),
  approvedAt: timestamp('approved_at'),
  approvedBy: uuid('approved_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const campaignsEnhanced = pgTable('campaigns', {
  // ... existing fields plus:
  type: campaignTypeEnum('type').notNull(),
  targetAmount: decimal('target_amount', { precision: 10, scale: 2 }),
  currentAmount: decimal('current_amount', { precision: 10, scale: 2 }).default('0'),
  startDate: timestamp('start_date'),
  endDate: timestamp('end_date'),
  customFields: jsonb('custom_fields'), // for collecting additional donor info
  settings: jsonb('settings'), // min/max donation, currencies, etc
});

export const paymentGateways = pgTable('payment_gateways', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 50 }).notNull(), // chip, stripe, paypal
  organizationId: uuid('organization_id').references(() => organizations.id),
  config: jsonb('config'), // encrypted gateway credentials
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export const donorProfiles = pgTable('donor_profiles', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  fullName: varchar('full_name', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  address: text('address'),
  totalDonated: decimal('total_donated', { precision: 10, scale: 2 }).default('0'),
  donationCount: integer('donation_count').default(0),
  firstDonationAt: timestamp('first_donation_at'),
  lastDonationAt: timestamp('last_donation_at'),
  tags: jsonb('tags'), // for organizer segmentation
});

export const adminLogs = pgTable('admin_logs', {
  id: uuid('id').defaultRandom().primaryKey(),
  adminId: uuid('admin_id').references(() => users.id).notNull(),
  action: varchar('action', { length: 100 }).notNull(),
  entityType: varchar('entity_type', { length: 50 }),
  entityId: uuid('entity_id'),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});
```

### Payment Gateway Abstraction

```typescript
// Payment Gateway Interface
interface PaymentGateway {
  name: string;
  processPayment(params: PaymentParams): Promise<PaymentResult>;
  handleWebhook(payload: unknown): Promise<WebhookResult>;
  refund(transactionId: string, amount?: number): Promise<RefundResult>;
  getTransactionStatus(transactionId: string): Promise<TransactionStatus>;
}

// Payment Service with Strategy Pattern
class PaymentService {
  private gateways = new Map<string, PaymentGateway>();

  constructor() {
    this.gateways.set('chip', new ChipGateway());
    this.gateways.set('stripe', new StripeGateway());
    this.gateways.set('paypal', new PayPalGateway());
  }

  async processPayment(gateway: string, params: PaymentParams) {
    const processor = this.gateways.get(gateway);
    if (!processor) throw new Error(`Gateway ${gateway} not supported`);
    return processor.processPayment(params);
  }
}
```

### Implementation Timeline (1.0)

#### Weeks 1-2: Enhanced Architecture
- [ ] Refactor MVP code for scalability
- [ ] Implement proper error handling & logging
- [ ] Add comprehensive testing setup
- [ ] Set up CI/CD pipeline
- [ ] Implement rate limiting & security middleware

#### Weeks 3-4: Admin System
- [ ] Build admin authentication & authorization
- [ ] Create organization approval workflow
- [ ] Implement admin dashboard
- [ ] Add audit logging
- [ ] Build admin analytics

#### Weeks 5-6: Payment Expansion
- [ ] Abstract payment gateway implementation
- [ ] Integrate Stripe
- [ ] Integrate PayPal
- [ ] Add refund capabilities
- [ ] Implement recurring donations

#### Weeks 7-8: Campaign Features
- [ ] Add time-limited campaigns
- [ ] Add amount-limited campaigns
- [ ] Implement campaign analytics
- [ ] Add custom donor fields
- [ ] Build campaign widgets

#### Weeks 9-10: Polish & Launch
- [ ] Build marketing website (Next.js)
- [ ] Implement email campaigns
- [ ] Add export functionality (CSV, PDF)
- [ ] Performance optimization
- [ ] Security audit
- [ ] Documentation
- [ ] Load testing

### Marketing Website (Next.js)

```typescript
// Pages Structure
app/
├── (marketing)/
│   ├── page.tsx           // Landing page
│   ├── pricing/page.tsx   // Pricing tiers
│   ├── features/page.tsx  // Feature showcase
│   ├── about/page.tsx     // About us
│   └── blog/              // Content marketing
│
├── (auth)/
│   ├── login/page.tsx
│   └── signup/page.tsx
│
└── (legal)/
    ├── privacy/page.tsx
    └── terms/page.tsx
```

### API Structure (1.0)

```typescript
// Comprehensive API Routes
app.group('/api/v1', (app) =>
  app
    // Public endpoints
    .group('/public', (app) =>
      app
        .get('/campaigns/:slug', getPublicCampaign)
        .post('/donations/initiate', initiateDonation)
        .post('/webhooks/:gateway', handlePaymentWebhook)
    )

    // Organizer endpoints (protected)
    .group('/organizer', requireAuth('organizer'), (app) =>
      app
        .get('/dashboard/stats', getDashboardStats)
        .get('/campaigns', getMyCampaigns)
        .post('/campaigns', createCampaign)
        .patch('/campaigns/:id', updateCampaign)
        .delete('/campaigns/:id', deleteCampaign)
        .get('/donations', getMyDonations)
        .get('/donors', getMyDonors)
        .post('/donors/export', exportDonors)
        .get('/reports/generate', generateReport)
    )

    // Admin endpoints (protected)
    .group('/admin', requireAuth('admin'), (app) =>
      app
        .get('/organizations', getOrganizations)
        .patch('/organizations/:id/approve', approveOrganization)
        .patch('/organizations/:id/suspend', suspendOrganization)
        .get('/analytics', getPlatformAnalytics)
        .get('/audit-logs', getAuditLogs)
        .patch('/settings', updatePlatformSettings)
    )

    // Donor endpoints (protected)
    .group('/donor', requireAuth('user'), (app) =>
      app
        .get('/donations', getMyDonationHistory)
        .get('/receipts/:id', getReceipt)
        .patch('/profile', updateDonorProfile)
    )
)
```

## Security Implementation

### MVP Security (Basic)
- HTTPS everywhere
- Password hashing with Argon2
- JWT tokens with refresh rotation
- Basic rate limiting
- Input validation with Zod
- SQL injection prevention (Drizzle ORM)

### 1.0 Security (Comprehensive)
- OAuth 2.0 support
- 2FA for organizers and admins
- API key management for integrations
- Advanced rate limiting per tier
- GDPR compliance tools
- PCI DSS compliance (via payment gateways)
- Webhook signature verification
- Content Security Policy headers
- DDoS protection (Cloudflare)

## Monitoring & Analytics

### MVP Monitoring
- Sentry for error tracking
- Basic health checks
- Simple PostgreSQL query logging

### 1.0 Monitoring
- Application Performance Monitoring (APM)
- Custom metrics dashboard
- Real-time alerting
- Database query optimization
- User behavior analytics
- A/B testing framework

## Cost Breakdown

### MVP Costs (Monthly)
- **Railway/Render**: $20-50
- **Supabase (Database)**: $25
- **Vercel (Frontend)**: $0-20
- **Domain & Email**: $20
- **Total**: ~$85-115/month

### 1.0 Costs (Monthly at scale)
- **AWS/GCP Infrastructure**: $200-500
- **Database (Managed)**: $100-300
- **CDN & Storage**: $50-100
- **Email Service**: $50-200
- **Monitoring**: $100-200
- **Payment Gateway Fees**: 2.9% + $0.30 per transaction
- **Total**: ~$500-1,300/month (before transaction fees)

## Success Metrics

### MVP Success Criteria
- Successfully process 10+ real donations
- 5+ organizers signed up
- < 2 second page load time
- Zero critical security issues
- Basic donation flow works end-to-end

### 1.0 Success Criteria
- 100+ active organizers
- $100K+ monthly donation volume
- 99.9% uptime
- < 200ms API response time (p95)
- Support for 3+ payment gateways
- Fully automated organizer onboarding

## Development Team

### MVP Team (Minimal)
- 1 Full-stack developer (you)
- 1 Part-time UI/UX designer (optional)

### 1.0 Team
- 2 Full-stack developers
- 1 DevOps engineer (part-time)
- 1 UI/UX designer
- 1 QA tester (part-time)
- 1 Product manager (optional)

## Risk Management

### Technical Risks
- **Payment Gateway Issues**: Start with CHIP only in MVP, add fallbacks in 1.0
- **Scaling Problems**: MVP on managed services, optimize before 1.0
- **Security Breaches**: Follow OWASP guidelines, regular audits

### Business Risks
- **Regulatory Compliance**: Consult legal for donation laws
- **Competition**: Focus on Malaysian market first
- **Organizer Adoption**: Build referral program in 1.0

## Next Immediate Steps

### Week 1 Checklist
1. [ ] Set up GitHub repository with monorepo structure
2. [ ] Initialize Elysia backend project
3. [ ] Set up PostgreSQL database (local + Supabase)
4. [ ] Create Drizzle schema and migrations
5. [ ] Initialize Vite + TanStack Router frontend
6. [ ] Configure Better-Auth
7. [ ] Set up development environment
8. [ ] Review CHIP API documentation
9. [ ] Create basic UI components with Shadcn

### Week 2 Focus
1. [ ] Implement authentication flow
2. [ ] Build organizer registration
3. [ ] Create first API endpoints
4. [ ] Start CHIP integration
5. [ ] Build donation form UI

## Conclusion

This two-phase approach allows you to validate the core concept with a minimal MVP in 4-6 weeks, then expand to a feature-complete platform over the following 8-10 weeks. The tech stack you've chosen (Elysia, PostgreSQL, TanStack Router, Drizzle) provides excellent TypeScript support and performance while keeping the development experience smooth.
