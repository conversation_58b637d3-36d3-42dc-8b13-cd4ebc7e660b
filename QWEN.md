# DonorCARE Project Context

## Project Overview

DonorCARE is a donation SaaS platform MVP built with a modern tech stack. The project is structured as a Bun monorepo with four main packages:

1. **@donorcare/backend**: Elysia backend with Drizzle ORM, PostgreSQL, and Better-Auth (port 3000)
2. **@donorcare/admin**: Admin panel with TanStack Router for campaign and donation management (port 3001)
3. **@donorcare/frontend**: Public SSR frontend with TanStack Start for donation pages and SEO (port 3002)
4. **@donorcare/ui**: Shared UI components library using Shadcn/ui and Radix UI primitives

## Architecture

### Tech Stack

**Backend (@donorcare/backend)**
- **Runtime**: Bun
- **Framework**: Elysia (TypeScript-first web framework)
- **Database**: PostgreSQL with Drizzle ORM
- **Auth**: Better-Auth with cookie-based sessions
- **API Documentation**: Swagger/OpenAPI integration
- **Port**: 3000

**Admin Panel (@donorcare/admin)**
- **Framework**: Vite + React 19 + TypeScript
- **Routing**: TanStack Router with file-based routing
- **UI Components**: Shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS v4
- **State Management**: Zustand store for authentication
- **Forms**: React Hook Form + Zod validation
- **Auth**: Better-Auth client integration with enhanced session management
- **Testing**: Vitest + React Testing Library
- **Port**: 3001

**Public Frontend (@donorcare/frontend)**
- **Framework**: TanStack Start (SSR) with React 19 + TypeScript
- **Routing**: TanStack Router with SSR capabilities
- **UI Components**: Shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS v4
- **Data Fetching**: TanStack Query with tRPC
- **Forms**: React Hook Form + Zod validation
- **SEO**: Server-side rendering for better search indexing
- **Port**: 3002

**UI Library (@donorcare/ui)**
- **Components**: Shared UI components based on Shadcn/ui
- **Styling**: Tailwind CSS v4
- **Primitives**: Radix UI components

### Data Model

The application uses PostgreSQL with Drizzle ORM for database operations. Key entities include:

1. **Users**: Organizers and donors with role-based access control
2. **Campaigns**: Donation campaigns created by organizers
3. **Donations**: Individual donations made by donors to campaigns
4. **Sessions/Accounts**: Authentication-related entities managed by Better-Auth

## Development Commands

### From Root (Monorepo Commands)
```bash
# Development servers
bun run dev:backend   # Start backend dev server (port 3000)
bun run dev:admin     # Start admin dev server (port 3001)
bun run dev:frontend  # Start frontend dev server (port 3002)

# Build commands
bun run build:backend
bun run build:admin
bun run build:frontend

# Database operations
bun run db:migrate
bun run db:seed
bun run db:generate-migration

# Testing
bun run test:backend
bun run test:admin
bun run test:frontend

# Code formatting and linting
bun run format        # Format all packages
bun run lint          # Lint all packages
bun run check         # Check and fix issues
```

### Individual Package Commands

**Backend (@donorcare/backend)**
```bash
cd packages/backend
bun run dev          # Start dev server with file watching (port 3000)
bun run seed         # Seed database
bun run migrate      # Run database migrations
bun run generate-migration  # Generate new migration
bun run format       # Format code with Biome
```

**Admin Panel (@donorcare/admin)**
```bash
cd packages/admin
bun run dev          # Start dev server on port 3001
bun run build        # Build for production with TypeScript check
bun run test         # Run tests with Vitest
bun run format       # Format code with Biome
```

**Public Frontend (@donorcare/frontend)**
```bash
cd packages/frontend
bun run dev          # Start SSR dev server on port 3002
bun run build        # Build for production with SSR
bun run start        # Start production SSR server
bun run test         # Run tests with Vitest
bun run format       # Format code with Biome
```

**UI Library (@donorcare/ui)**
```bash
cd packages/ui
bun run dev          # UI package ready (no build needed)
bun run format       # Format code with Biome
```

## Key Architecture Patterns

### Authentication System
The auth system uses Better-Auth with enhanced session management:
- **Client**: Enhanced Better-Auth client with retry logic and session validation
- **Store**: Zustand store for auth state in the admin panel
- **Route Protection**: Comprehensive route protection with authentication checks

### Route Structure

**Admin Panel (packages/admin/src/routes/)**
- `__root.tsx` - Root layout with auth context
- `_authenticated.tsx` - Protected route wrapper
- `_authenticated/` - Protected pages (dashboard, profile, settings, campaigns, donations, donors)
- `index.tsx` - Public landing page
- `login.tsx` - Authentication page

**Public Frontend (packages/frontend/src/routes/)**
- `__root.tsx` - Root layout with SSR setup
- `index.tsx` - Public landing page
- `donate/$slug.tsx` - Dynamic donation page based on campaign slug

### API Structure (Backend)
- `src/index.ts` - Main Elysia server with Better-Auth integration
- `src/routes/health.ts` - Health check endpoints
- `src/auth/` - Authentication controller
- `src/users/` - User-related controllers and schemas
- `src/campaigns/` - Campaign-related controllers and schemas
- `src/donations/` - Donation-related controllers and schemas
- `src/donors/` - Donor-related controllers and schemas
- `src/webhooks/` - Payment webhook handlers
- `/api/auth/*` - Better-Auth endpoints (handled by auth.handler)

### Database Schema
- Uses Drizzle ORM with PostgreSQL
- Schema files organized by entity (users, campaigns, donations, donors)
- Migrations handled by Drizzle Kit
- Database URL configured via environment variables

## Development Workflow

1. Start backend: `bun run dev:backend` (port 3000)
2. Start admin panel: `bun run dev:admin` (port 3001)
3. Start public frontend: `bun run dev:frontend` (port 3002)
4. Use Playwright for frontend validation (per user's global instructions)
5. Run tests before committing changes
6. Format code with Biome before committing

### Application Roles
- **@donorcare/backend**: Handles all API endpoints, auth, database operations
- **@donorcare/admin**: Admin dashboard for campaign management, donor analytics
- **@donorcare/frontend**: Public donation pages with SEO optimization via SSR
- **@donorcare/ui**: Shared UI components used across applications

## Environment Setup

### Backend Environment
- Requires PostgreSQL database
- Database URL configured in environment variables
- Better-Auth configuration in `lib/auth.ts`

### Frontend Environment  
- Auth client configured to connect to backend on port 3000
- TanStack Router devtools enabled in development

## Testing Approach

### Frontend Testing
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: Auth flow and route protection testing
- **Test Setup**: Configures testing environment with jsdom

### Backend Testing
- **API Tests**: Endpoint validation and integration testing
- **Database Tests**: Schema validation and ORM operations

### Running Tests
```bash
# Run all tests
bun run test

# Run tests for specific package
bun run test:backend
bun run test:admin
bun run test:frontend
```

## Important Conventions

### File Organization
- UI components in `src/components/ui/` (Shadcn/ui)
- Custom components in `src/components/`
- Route components in `src/routes/`
- Auth utilities in `src/lib/auth/`
- Type definitions in `src/types/`

### Authentication Context
- All authenticated routes must be under `src/routes/_authenticated/` in admin panel
- Auth context is provided via TanStack Router context
- Session validation happens automatically via AuthClientService
- Use `useAuthStore()` for accessing auth state in components

### Database Operations
- Use Drizzle ORM for type-safe database operations
- Schema files should end with `.schema.ts`
- Include proper TypeBox validation schemas
- Database URL: `postgresql://localhost:5432/donorcare` (development)

### API Development
- Use Elysia's type-safe route definitions
- Include proper TypeBox validation schemas
- Handle CORS for frontend origins: localhost:3000, 3001, 3002
- All auth routes are handled by Better-Auth at `/api/auth/*`

## Code Quality

### Formatting and Linting
- Uses Biome for code formatting and linting
- Tab indentation with 80 character line width
- Double quotes for strings
- Semicolons omitted where not required
- Strict TypeScript configuration

### TypeScript Configuration
- Strict mode enabled
- ES2022 target
- Module resolution set to bundler
- JSX transform for React
- No unused locals or parameters

## Project Structure

```
donorcare/
├── packages/
│   ├── backend/        # Elysia API Backend (port 3000)
│   │   ├── src/
│   │   │   ├── auth/
│   │   │   ├── campaigns/
│   │   │   ├── donations/
│   │   │   ├── donors/
│   │   │   ├── lib/
│   │   │   ├── routes/
│   │   │   ├── users/
│   │   │   ├── webhooks/
│   │   │   ├── db.ts
│   │   │   └── index.ts
│   │   └── package.json
│   │
│   ├── admin/          # Admin Panel (TanStack Router) (port 3001)
│   │   ├── src/
│   │   │   ├── components/
│   │   │   ├── lib/
│   │   │   ├── routes/
│   │   │   │   ├── __root.tsx
│   │   │   │   ├── _authenticated/
│   │   │   │   │   ├── dashboard.tsx
│   │   │   │   │   ├── profile.tsx
│   │   │   │   │   ├── settings.tsx
│   │   │   │   │   └── [campaigns, donations, donors]/
│   │   │   │   ├── index.tsx
│   │   │   │   └── login.tsx
│   │   │   └── main.tsx
│   │   └── package.json
│   │
│   ├── frontend/       # Public Frontend (TanStack Start SSR) (port 3002)
│   │   ├── src/
│   │   │   ├── components/
│   │   │   ├── integrations/
│   │   │   ├── routes/
│   │   │   │   ├── __root.tsx
│   │   │   │   ├── index.tsx
│   │   │   │   └── donate/
│   │   │   │       └── $slug.tsx
│   │   │   └── main.tsx
│   │   └── package.json
│   │
│   └── ui/            # Shared UI Components
│       ├── src/
│       │   ├── components/
│       │   │   ├── ui/
│       │   │   └── animate-ui/
│       │   └── lib/
│       └── package.json
│
├── package.json        # Root package with workspace scripts
├── tsconfig.json       # Root TypeScript configuration
├── biome.json          # Biome formatting and linting config
├── README.md           # Project documentation
├── CLAUDE.md           # Instructions for Claude Code
└── QWEN.md             # Instructions for Qwen Code (this file)
```