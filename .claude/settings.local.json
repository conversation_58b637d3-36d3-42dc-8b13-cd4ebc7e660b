{"permissions": {"allow": ["Bash(bun run:*)", "Bash(bunx:*)", "<PERSON><PERSON>(createdb:*)", "Bash(psql:*)", "<PERSON><PERSON>(pkill:*)", "mcp__playwright__browser_type", "mcp__playwright__browser_snapshot", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(cp:*)", "Bash(node:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "Bash(bun install:*)", "Bash(bun:*)", "<PERSON><PERSON>(true)", "Bash(find:*)", "Bash(biome check:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_hover", "Bash(lsof:*)", "<PERSON><PERSON>(sed:*)", "Bash(git checkout:*)", "Bash(ls:*)"], "deny": [], "ask": []}}