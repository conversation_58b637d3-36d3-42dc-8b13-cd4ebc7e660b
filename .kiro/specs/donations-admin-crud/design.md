# Design Document

## Overview

The donations admin CRUD system provides organizers with comprehensive tools to manage donations made to their campaigns. The system follows the established patterns from the campaigns CRUD implementation, ensuring consistency in architecture, user experience, and code organization. The design emphasizes real-time data management, robust filtering capabilities, and secure access control.

## Architecture

### Frontend Architecture

The donations CRUD follows the same architectural patterns as the campaigns system:

- **Route Structure**: `/donations` main route with nested routes for detailed views
- **Component Hierarchy**: Reusable components following the established pattern
- **State Management**: Local React state with API integration
- **API Layer**: Dedicated donations API service with TypeScript types
- **Error Handling**: Consistent error handling with user-friendly messages

### Backend Integration

The system integrates with the existing donations controller in the backend:

- **Existing Endpoints**: Leverages current `/api/donations` endpoints
- **New Endpoints**: Extends backend with additional CRUD operations
- **Authentication**: Uses existing Better-Auth session management
- **Authorization**: Ensures organizers can only access their campaign donations

## Components and Interfaces

### Core Components

#### 1. DonationsTable Component
```typescript
interface DonationsTableProps {
  donations: DonationWithCampaign[]
  isLoading: boolean
  error?: DonationApiError | null
  onView: (donationId: string) => void
  onEdit: (donationId: string) => void
  onDelete: (donationId: string) => void
  onStatusChange: (donationId: string, status: DonationStatus) => Promise<void>
  onExport: () => void
  onRetry?: () => void
}
```

**Features:**
- TanStack Table-based data table with advanced features
- Column-based filtering (search, status, campaign, date range)
- Multi-column sorting by amount, date, donor name, campaign
- Row selection for bulk operations
- Pagination with configurable page sizes
- Column visibility controls
- Export functionality for selected/filtered data
- Real-time analytics summary above table

#### 2. DonationRowActions Component
```typescript
interface DonationRowActionsProps {
  donation: DonationWithCampaign
  onView: (donationId: string) => void
  onEdit: (donationId: string) => void
  onDelete: (donationId: string) => void
  onStatusChange: (donationId: string, status: DonationStatus) => Promise<void>
}
```

**Features:**
- Dropdown menu with donation actions (view, edit, delete, status change)
- Context-aware actions based on donation status
- Keyboard shortcuts for common actions
- Accessible dropdown implementation

#### 3. DonationForm Component
```typescript
interface DonationFormProps {
  mode: "edit"
  initialData?: Partial<DonationWithCampaign>
  onSubmit: (data: UpdateDonationData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}
```

**Features:**
- Edit donation metadata (donor name, email, notes)
- Form validation with Zod schemas
- Read-only fields for payment data
- Consistent styling with campaigns form

#### 4. DonationDetailsModal Component
```typescript
interface DonationDetailsModalProps {
  donation: DonationWithCampaign
  isOpen: boolean
  onClose: () => void
  onEdit: () => void
  onDelete: () => void
  onStatusChange: (status: DonationStatus) => Promise<void>
}
```

**Features:**
- Complete donation information display
- Transaction details and payment information
- Donor contact information
- Action buttons for management

#### 5. DonationTableColumns Configuration
```typescript
interface DonationTableColumn {
  id: string
  header: string
  accessorKey?: string
  cell?: (info: CellContext<DonationWithCampaign, unknown>) => React.ReactNode
  size?: number
  enableSorting?: boolean
  enableHiding?: boolean
  filterFn?: FilterFn<DonationWithCampaign>
}

const donationColumns: ColumnDef<DonationWithCampaign>[] = [
  // Selection column
  { id: "select", header: "Select", enableSorting: false, enableHiding: false },
  // Donor information
  { accessorKey: "donorName", header: "Donor Name", size: 180 },
  { accessorKey: "donorEmail", header: "Email", size: 220 },
  // Donation details
  { accessorKey: "amount", header: "Amount", size: 120 },
  { accessorKey: "campaign.name", header: "Campaign", size: 200 },
  { accessorKey: "status", header: "Status", size: 100, filterFn: statusFilterFn },
  { accessorKey: "createdAt", header: "Date", size: 140 },
  // Actions column
  { id: "actions", header: "Actions", size: 60, enableHiding: false }
]
```

#### 6. DonationAnalytics Component
```typescript
interface DonationAnalyticsProps {
  donations: DonationWithCampaign[]
  filteredDonations: DonationWithCampaign[]
  selectedDonations: DonationWithCampaign[]
  isLoading: boolean
}
```

**Features:**
- Real-time analytics that update based on table filters and selection
- Total donation amount and count for filtered/selected data
- Average donation amount calculation
- Status distribution with visual indicators
- Quick stats display above the table
- Export summary information

#### 7. Table Implementation Details

**TanStack Table Features:**
- Column-based filtering with faceted values
- Multi-column sorting with sort indicators
- Row selection with bulk operations
- Pagination with configurable page sizes
- Column visibility controls
- Responsive column sizing
- Loading states with skeleton rows
- Empty state handling

**Filter Implementation:**
- Multi-column search across donor name, email, and campaign name
- Status filter with checkbox selection and counts
- Campaign filter dropdown
- Date range picker for donation date filtering
- Real-time filter application with debounced search

**Performance Optimizations:**
- Virtual scrolling for large datasets (>1000 rows)
- Debounced search input (300ms delay)
- Memoized column definitions and filter functions
- Efficient re-rendering with React.memo for row components

### API Service Layer

#### DonationsApiService Class
```typescript
class DonationsApiService {
  // Core CRUD operations
  async getDonations(filters?: DonationFilters): Promise<ApiResult<DonationWithCampaign[]>>
  async getDonation(donationId: string): Promise<ApiResult<DonationWithCampaign>>
  async updateDonation(donationId: string, data: UpdateDonationData): Promise<ApiResult<DonationWithCampaign>>
  async deleteDonation(donationId: string): Promise<ApiResult<string>>
  async updateDonationStatus(donationId: string, status: DonationStatus): Promise<ApiResult<DonationWithCampaign>>
  
  // Additional operations
  async exportDonations(filters?: DonationFilters): Promise<ApiResult<Blob>>
  async getDonationAnalytics(filters?: DonationFilters): Promise<ApiResult<DonationAnalytics>>
}
```

## Data Models

### TypeScript Interfaces

```typescript
// Core donation interface matching backend schema
interface Donation {
  id: string
  campaignId: string
  donorId: string
  amount: string
  currency: string
  chipPaymentId: string | null
  status: DonationStatus
  donorName: string
  donorEmail: string
  donorMessage?: string
  internalNotes?: string
  createdAt: string
  updatedAt: string
}

// Extended interface with campaign information
interface DonationWithCampaign extends Donation {
  campaign: {
    id: string
    name: string
    slug: string
    organizerId: string
  }
}

// Form data interfaces
interface UpdateDonationData {
  donorName: string
  donorEmail: string
  internalNotes?: string
}

// API response interfaces
interface DonationsListResponse {
  success: boolean
  donations: DonationWithCampaign[]
  pagination?: {
    total: number
    page: number
    limit: number
  }
}

interface DonationResponse {
  success: boolean
  donation: DonationWithCampaign
}

// Analytics interface
interface DonationAnalytics {
  totalAmount: string
  totalCount: number
  averageAmount: string
  statusDistribution: {
    pending: number
    completed: number
    failed: number
  }
  topCampaigns: Array<{
    campaignId: string
    campaignName: string
    totalAmount: string
    donationCount: number
  }>
  monthlyTrends: Array<{
    month: string
    amount: string
    count: number
  }>
}

// Error handling
type DonationStatus = "pending" | "completed" | "failed"

interface DonationApiError {
  code: string
  message: string
  details?: string[]
}
```

### Backend Extensions

The backend donations controller will be extended with additional endpoints:

```typescript
// New endpoints to add
PUT /api/donations/:id - Update donation metadata
DELETE /api/donations/:id - Delete donation (with restrictions)
PATCH /api/donations/:id/status - Update donation status
GET /api/donations/export - Export donations as CSV
GET /api/donations/analytics - Get donation analytics
```

## Error Handling

### Error Categories

1. **Network Errors**: Connection issues, timeouts
2. **Authentication Errors**: Session expired, unauthorized access
3. **Authorization Errors**: Access to donations from other organizers
4. **Validation Errors**: Invalid form data, business rule violations
5. **Not Found Errors**: Donation doesn't exist or access denied
6. **Conflict Errors**: Cannot delete completed donations
7. **Server Errors**: Unexpected backend issues

### Error Recovery Strategies

- **Retry Mechanisms**: Automatic retry for network errors
- **Graceful Degradation**: Show cached data when possible
- **User Feedback**: Clear error messages with actionable steps
- **Fallback States**: Alternative UI when features are unavailable

## Testing Strategy

### Unit Testing

1. **Component Testing**
   - DonationsList filtering and sorting logic
   - DonationCard display and interactions
   - DonationForm validation and submission
   - API service methods and error handling

2. **Integration Testing**
   - API service integration with backend
   - Form submission workflows
   - Filter and search functionality
   - Status update operations

3. **E2E Testing**
   - Complete donation management workflows
   - Cross-browser compatibility
   - Mobile responsiveness
   - Error scenarios and recovery

### Test Coverage Goals

- **Components**: 90% coverage for business logic
- **API Service**: 95% coverage including error paths
- **Integration**: Key user workflows covered
- **E2E**: Critical paths and error scenarios

### Testing Tools

- **Vitest**: Unit and integration testing
- **Testing Library**: Component testing
- **MSW**: API mocking for tests
- **Playwright**: E2E testing

## Security Considerations

### Access Control

- **Authentication**: Verify user session for all operations
- **Authorization**: Ensure organizers can only access their donations
- **Data Validation**: Validate all input data on frontend and backend
- **CSRF Protection**: Use existing CSRF tokens for state-changing operations

### Data Protection

- **Sensitive Data**: Mask payment IDs and sensitive donor information
- **Audit Logging**: Log all donation modifications
- **Data Retention**: Follow data retention policies for deleted donations
- **Export Security**: Ensure exported data is properly secured

## Performance Considerations

### Frontend Optimization

- **Pagination**: Implement pagination for large donation lists
- **Virtual Scrolling**: For very large datasets
- **Debounced Search**: Prevent excessive API calls during search
- **Caching**: Cache donation data and analytics
- **Lazy Loading**: Load donation details on demand

### Backend Optimization

- **Database Indexing**: Optimize queries with proper indexes
- **Query Optimization**: Efficient joins and filtering
- **Caching**: Cache analytics and frequently accessed data
- **Rate Limiting**: Prevent abuse of export and analytics endpoints

## Accessibility

### WCAG Compliance

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Color Contrast**: Meet WCAG AA standards
- **Focus Management**: Clear focus indicators and logical tab order

### Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Touch Targets**: Appropriate size for touch interactions
- **Readable Text**: Proper font sizes and line spacing
- **Flexible Layouts**: Adapt to different screen sizes

## Migration and Deployment

### Database Changes

- **Schema Updates**: Add new fields for internal notes and metadata
- **Indexes**: Add indexes for filtering and sorting performance
- **Migrations**: Safe, reversible database migrations

### Feature Rollout

- **Feature Flags**: Gradual rollout with feature toggles
- **A/B Testing**: Test new UI components with user groups
- **Monitoring**: Track usage and performance metrics
- **Rollback Plan**: Quick rollback strategy if issues arise