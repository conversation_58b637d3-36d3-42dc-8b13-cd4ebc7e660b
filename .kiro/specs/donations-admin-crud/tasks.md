# Implementation Plan

- [x] 1. Set up monorepo type sharing infrastructure
  - Update backend package.json to export types from model.ts
  - Ensure admin package can import types using workspace protocol
  - Export donation-related types and interfaces from backend model.ts
  - Test type imports work correctly across packages
  - _Requirements: 1.1, 1.4, 1.5_

- [x] 2. Create donations API service following campaigns.api.ts pattern
  - Create donations.api.ts file in packages/admin/src/lib/ following campaigns.api.ts structure
  - Import donation schema types from @donorcare/backend/src/donations/donations.schema
  - Use treaty client with proper authentication (credentials: "include")
  - Implement API functions: getDonations, getDonation, updateDonation, deleteDonation, updateDonationStatus, getDonationAnalytics, exportDonations
  - Export client and helper types similar to campaigns API
  - _Requirements: 1.1, 1.4, 1.5_

- [x] 3. Create useDonations hook following useCampaigns.ts pattern
  - Create useDonations.ts in packages/admin/src/hooks/ following useCampaigns.ts structure
  - Implement query hooks: useDonations, useDonation, useDonationAnalytics
  - Implement mutation hooks: useUpdateDonation, useDeleteDonation, useUpdateDonationStatus, useExportDonations
  - Include optimistic updates, error handling, and toast notifications
  - Add proper query key management and cache invalidation
  - _Requirements: 1.1, 1.4, 1.5_

- [x] ~~3. Extend backend donations controller with CRUD endpoints~~
  - Add additional donation-related types to backend schema for API responses
  - Add PUT endpoint for updating donation metadata
  - Add DELETE endpoint for donation deletion with restrictions
  - Add PATCH endpoint for status updates
  - Add GET endpoint for donation analytics
  - Add GET endpoint for CSV export functionality
  - Export all donation types from backend model.ts for frontend consumption
  - Write integration tests for new endpoints
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 5.1, 5.2, 5.3, 6.1, 6.2, 7.1, 7.2, 8.1_

- [x] 4. Create donations component directory and index file
  - Create packages/admin/src/components/donations/ directory
  - Create index.ts file following campaigns/index.ts pattern
  - Export donation types from backend schema and hook types
  - Set up component exports structure for future components
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 5. Implement DonationsTable component following example-table.tsx pattern
  - Create DonationsTable.tsx following packages/ui/src/components/example-table.tsx structure
  - Use TanStack Table with column definitions for donor name, email, amount, campaign, status, date
  - Implement multi-column search filtering (donor name, email, campaign name)
  - Add status filtering with checkbox selection and counts
  - Include column visibility controls and sorting for all columns
  - Add row selection for bulk operations and export functionality
  - Write unit tests following table component testing patterns
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 6. Implement DonationRowActions component following example-table.tsx RowActions pattern
  - Create DonationRowActions.tsx following RowActions pattern from example-table.tsx
  - Add dropdown menu with view, edit, delete, and status change actions
  - Include keyboard shortcuts and proper accessibility
  - Add context-aware actions based on donation status
  - Write unit tests for action menu functionality
  - _Requirements: 1.1, 1.2, 1.3, 4.1, 5.1, 7.1_

- [x] 7. Create table column definitions and filter functions
  - Define donation table columns with proper types, sizes, and formatting
  - Implement custom filter functions for multi-column search and status filtering
  - Add currency formatting for amount column
  - Include status badge rendering with proper color coding
  - Add date formatting and sorting functionality
  - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 8. Create DonationForm component following CampaignForm pattern
  - Create DonationForm.tsx following CampaignForm.tsx structure
  - Implement edit mode for donation metadata (donor name, email, internal notes)
  - Add form validation using backend schema types
  - Include read-only fields for payment-related data
  - Write unit tests following CampaignForm.test.tsx pattern
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 9. Create DeleteDonationDialog following DeleteCampaignDialog pattern
  - Create DeleteDonationDialog.tsx following DeleteCampaignDialog.tsx structure
  - Add amount verification requirement for deletion confirmation
  - Implement deletion restrictions for completed donations
  - Include proper error handling and loading states
  - Write unit tests following DeleteCampaignDialog.test.tsx pattern
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 10. Create StatusToggle component for donations following campaigns StatusToggle
  - Create donation StatusToggle.tsx following campaigns StatusToggle.tsx pattern
  - Implement status change functionality (pending/completed/failed)
  - Add confirmation dialogs and failure reason input
  - Include proper accessibility and loading states
  - Write unit tests following StatusToggle.test.tsx pattern
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 11. Create donations routes following campaigns route structure
  - Create packages/admin/src/routes/\_authenticated/donations/ directory
  - Create index.tsx route following campaigns/index.tsx pattern
  - Integrate DonationsTable component with proper error boundaries
  - Add route-level loading states and error handling
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 12. Update AppSidebar navigation for donations
  - Update packages/admin/src/components/AppSidebar.tsx
  - Change donations navigation from "#" to functional "/donations" link
  - Ensure proper active state highlighting works
  - Test navigation integration and routing
  - _Requirements: 1.1, 1.4, 1.5_

- [x] 13. Add DonationAnalytics component for summary statistics
  - Create DonationAnalytics.tsx component for displaying summary stats above table
  - Show total amount, count, average donation, and status distribution
  - Update analytics in real-time based on table filters and selection
  - Add loading and error states for analytics
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [ ] 14. Add comprehensive component testing following campaigns test patterns
  - Write unit tests for all donation components following campaigns **tests** structure
  - Test API service functions and hook behaviors
  - Add integration tests for donation workflows
  - Ensure test coverage matches campaigns component coverage
  - _Requirements: All requirements - testing coverage_

- [ ] 15. Implement export functionality integration
  - Add export button to DonationsTable toolbar following example-table.tsx pattern
  - Export selected rows or all filtered data based on table state
  - Integrate with backend export endpoint using useDonations hook
  - Handle CSV file download and error states with proper user feedback
  - Add export progress indication and row count display
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 16. Final integration and polish
  - Ensure table follows consistent styling with other admin components
  - Add proper loading skeletons for table rows during data fetching
  - Verify responsive design works across all screen sizes with proper column handling
  - Test complete donation management workflow end-to-end including table interactions
  - Optimize table performance for large datasets with virtual scrolling if needed
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_
