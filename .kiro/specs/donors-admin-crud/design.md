# Design Document

## Overview

This design document outlines the implementation of donor management CRUD operations for the DonorCare admin panel. The feature enables organizers to view and manage donors who have contributed to their campaigns, following the established architectural patterns from campaigns and donations modules. The system ensures proper data isolation where each organizer can only access donors associated with their own campaigns.

## Architecture

### Backend Architecture

The backend follows the existing controller-service pattern established in the codebase:

- **Controller Layer**: `packages/backend/src/donors/donors.controller.ts`
- **Schema Layer**: `packages/backend/src/donors/donors.schema.ts`
- **Database Layer**: Uses existing `users` table with role filtering
- **Authentication**: Leverages existing auth middleware for organizer verification

### Frontend Architecture

The frontend follows the established React + TanStack Router + TanStack Query pattern:

- **Components**: `packages/admin/src/components/donors/`
- **Hooks**: `packages/admin/src/hooks/useDonors.ts`
- **API Client**: `packages/admin/src/lib/donors.api.ts`
- **Routes**: New donor management routes under `/donors`

### Data Flow

```mermaid
graph TD
    A[Admin UI] --> B[TanStack Query Hooks]
    B --> C[API Client]
    C --> D[Backend Controller]
    D --> E[Database Queries]
    E --> F[Users Table + Donations Join]
    F --> E
    E --> D
    D --> C
    C --> B
    B --> A
```

## Components and Interfaces

### Backend Components

#### Donors Controller (`donors.controller.ts`)

**Endpoints:**
- `GET /api/donors` - List organizer's donors with filtering and pagination
- `GET /api/donors/:id` - Get specific donor details
- `POST /api/donors` - Create new donor record
- `PUT /api/donors/:id` - Update donor information
- `DELETE /api/donors/:id` - Delete donor (with restrictions)
- `GET /api/donors/export` - Export donors as CSV

**Key Features:**
- Organizer-scoped data access (only donors who donated to organizer's campaigns)
- Advanced filtering by name, email, donation amount, date ranges
- Pagination support
- Aggregated donation statistics per donor
- CSV export functionality

#### Donors Schema (`donors.schema.ts`)

**DTOs:**
```typescript
// Request DTOs
donorFiltersDto: {
  search?: string
  minAmount?: number
  maxAmount?: number
  dateFrom?: string
  dateTo?: string
  campaignId?: string
  sortBy?: "name" | "email" | "totalAmount" | "lastDonation"
  sortOrder?: "asc" | "desc"
  page?: number
  limit?: number
}

createDonorDto: {
  name: string
  email: string
  phone?: string
  address?: string
  notes?: string
}

updateDonorDto: {
  name: string
  email: string
  phone?: string
  address?: string
  notes?: string
}

// Response DTOs
donorWithStatsDto: {
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
  totalDonated: string
  donationCount: number
  averageDonation: string
  lastDonationDate?: Date
  campaignsSupported: number
  firstDonationDate?: Date
}

donorsListResponseDto: {
  success: boolean
  donors: donorWithStatsDto[]
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}
```

### Frontend Components

#### DonorsList Component

**Features:**
- Responsive table with sorting and filtering
- Search by name/email
- Filter by donation amount ranges and date ranges
- Bulk selection and export
- Pagination controls
- Loading states and error handling

#### DonorCard Component

**Features:**
- Donor summary information
- Quick stats (total donated, donation count)
- Action buttons (view, edit, delete)
- Status indicators

#### DonorForm Component

**Features:**
- Create/edit donor information
- Form validation using React Hook Form + Zod
- Auto-complete for existing donors
- Notes and tags management

#### DonorDetails Component

**Features:**
- Complete donor profile view
- Donation history table (filtered to organizer's campaigns)
- Campaign participation summary
- Edit and delete actions

### API Integration

#### useDonors Hook

```typescript
export function useDonors(filters?: DonorFilters) {
  return useQuery({
    queryKey: ['donors', filters],
    queryFn: () => donorsApi.getDonors(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useDonor(donorId: string) {
  return useQuery({
    queryKey: ['donors', donorId],
    queryFn: () => donorsApi.getDonor(donorId),
    enabled: !!donorId,
  })
}

export function useCreateDonor() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: donorsApi.createDonor,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['donors'] })
    },
  })
}
```

## Data Models

### Donor Data Model

The donor management system leverages the existing `users` table with additional computed fields:

```typescript
interface DonorWithStats {
  // Base user fields
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  notes?: string
  createdAt: Date
  updatedAt: Date

  // Computed donation statistics
  totalDonated: string        // Sum of all donations to organizer's campaigns
  donationCount: number       // Count of donations to organizer's campaigns
  averageDonation: string     // Average donation amount
  lastDonationDate?: Date     // Most recent donation date
  firstDonationDate?: Date    // First donation date
  campaignsSupported: number  // Number of organizer's campaigns supported
}
```

### Database Queries

**Key Query Pattern for Organizer-Scoped Access:**
```sql
SELECT
  u.*,
  COUNT(d.id) as donation_count,
  SUM(d.amount) as total_donated,
  AVG(d.amount) as average_donation,
  MAX(d.created_at) as last_donation_date,
  MIN(d.created_at) as first_donation_date,
  COUNT(DISTINCT d.campaign_id) as campaigns_supported
FROM users u
INNER JOIN donations d ON u.id = d.donor_id
INNER JOIN campaigns c ON d.campaign_id = c.id
WHERE c.organizer_id = ? AND u.role = 'user'
GROUP BY u.id
```

## Error Handling

### Backend Error Handling

- **403 Forbidden**: When non-organizer tries to access donor endpoints
- **404 Not Found**: When donor doesn't exist or isn't associated with organizer's campaigns
- **409 Conflict**: When trying to delete donor with existing donations
- **400 Bad Request**: For validation errors and invalid filters

### Frontend Error Handling

- **Network Errors**: Retry mechanisms with exponential backoff
- **Validation Errors**: Real-time form validation with clear error messages
- **Access Denied**: Redirect to appropriate page with error message
- **Loading States**: Skeleton loaders and loading indicators

## Testing Strategy

### Backend Testing

**Unit Tests:**
- Controller endpoint logic
- Data access layer queries
- Schema validation
- Access control verification

**Integration Tests:**
- End-to-end API workflows
- Database query correctness
- Authentication and authorization
- Cross-organizer data isolation

### Frontend Testing

**Component Tests:**
- Donor list rendering and interactions
- Form validation and submission
- Error state handling
- Loading state behavior

**Integration Tests:**
- Complete donor management workflows
- API integration correctness
- Route navigation and guards
- State management consistency

### Test Data Scenarios

- Multiple organizers with separate donor bases
- Donors who have donated to multiple organizers
- Edge cases: donors with no donations, deleted campaigns
- Performance testing with large donor datasets

## Security Considerations

### Data Access Control

- **Organizer Isolation**: Strict enforcement that organizers can only access donors who have donated to their campaigns
- **Role-Based Access**: Only organizer role can access donor management endpoints
- **Query Filtering**: All database queries include organizer_id filtering through campaign relationships

### Data Privacy

- **PII Protection**: Sensitive donor information only accessible to campaign organizers
- **Audit Logging**: Track all donor data modifications
- **Data Retention**: Respect donor data deletion requests while preserving donation records

### Input Validation

- **Server-Side Validation**: All inputs validated using Zod schemas
- **SQL Injection Prevention**: Parameterized queries using Drizzle ORM
- **XSS Prevention**: Input sanitization and output encoding

## Performance Considerations

### Database Optimization

- **Indexes**: Composite indexes on (campaign_id, donor_id) and (organizer_id, created_at)
- **Query Optimization**: Efficient joins between users, donations, and campaigns tables
- **Pagination**: Cursor-based pagination for large donor lists

### Frontend Optimization

- **Data Caching**: TanStack Query caching with appropriate stale times
- **Virtual Scrolling**: For large donor lists (future enhancement)
- **Debounced Search**: Prevent excessive API calls during search input
- **Optimistic Updates**: Immediate UI feedback for mutations

### Scalability

- **Database Partitioning**: Consider partitioning by organizer_id for large datasets
- **API Rate Limiting**: Prevent abuse of export and search endpoints
- **CDN Caching**: Cache static donor profile images and assets
