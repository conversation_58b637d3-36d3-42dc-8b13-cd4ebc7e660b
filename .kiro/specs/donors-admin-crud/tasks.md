cu# Implementation Plan

- [x] 1. Create backend donors controller with organizer-scoped access
  - Create packages/backend/src/donors/donors.controller.ts following campaigns.controller.ts pattern
  - Implement GET /api/donors endpoint with filtering, pagination, and organizer-scoped queries
  - Implement GET /api/donors/:id endpoint with access control verification
  - Implement POST /api/donors endpoint for manual donor creation
  - Implement PUT /api/donors/:id endpoint for donor information updates
  - Implement DELETE /api/donors/:id endpoint with donation history restrictions
  - Implement GET /api/donors/export endpoint for CSV export functionality
  - Add proper authentication middleware and organizer role verification
  - _Requirements: 1.1, 1.6, 2.1, 2.6, 3.1, 3.7, 4.1, 4.7, 5.1, 5.6, 7.1, 7.6_

- [x] 2. Create donors schema definitions and DTOs
  - Create packages/backend/src/donors/donors.schema.ts following donations.schema.ts pattern
  - Define donorFiltersDto for search, filtering, and pagination parameters
  - Define createDonorDto and updateDonorDto for donor data validation
  - Define donorWithStatsDto for response data with computed donation statistics
  - Define donorsListResponseDto with pagination metadata
  - Define donorParamsDto for URL parameter validation
  - Export all schemas and types for frontend consumption
  - _Requirements: 1.1, 1.2, 2.1, 3.1, 4.1, 7.1_

- [x] 3. Implement database queries with organizer-scoped access
  - Create complex JOIN queries between users, donations, and campaigns tables
  - Implement donor statistics aggregation (total donated, donation count, average, etc.)
  - Add proper WHERE clauses to ensure organizer can only access their donors
  - Implement efficient filtering by name, email, donation amounts, and date ranges
  - Add sorting capabilities by name, email, total donated, and last donation date
  - Optimize queries with appropriate indexes and query performance
  - _Requirements: 1.1, 1.6, 2.1, 2.6, 7.1, 7.6_

- [x] 4. Create donors API client for frontend
  - Create packages/admin/src/lib/donors.api.ts following campaigns.api.ts pattern
  - Import donor schema types from @donorcare/backend/src/donors/donors.schema
  - Use treaty client with proper authentication (credentials: "include")
  - Implement API functions: getDonors, getDonor, createDonor, updateDonor, deleteDonor, exportDonors
  - Add proper error handling and TypeScript type safety
  - Export client and helper types for component consumption
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1, 7.1_

- [x] 5. Create useDonors hook following useCampaigns pattern
  - Create packages/admin/src/hooks/useDonors.ts following useCampaigns.ts structure
  - Implement query hooks: useDonors, useDonor with proper caching and stale time
  - Implement mutation hooks: useCreateDonor, useUpdateDonor, useDeleteDonor, useExportDonors
  - Include optimistic updates, error handling, and toast notifications
  - Add proper query key management and cache invalidation strategies
  - Add loading state management and error recovery mechanisms
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1, 7.1_

- [x] 6. Create donors component directory and structure
  - Create packages/admin/src/components/donors/ directory
  - Create index.ts file following donations/index.ts pattern for component exports
  - Set up component structure for DonorsTable, DonorForm, columns, and utility components
  - Export donor types from backend schema and hook types
  - Establish consistent naming conventions and file organization
  - _Requirements: 1.1, 2.1, 3.1, 4.1_

- [x] 7. Create donor table columns definition
  - Create columns.tsx following donations/columns.tsx pattern
  - Define table columns for donor name, email, total donated, donation count, last donation date
  - Add selection checkbox column for bulk operations
  - Include sortable columns with proper sorting functions
  - Add action column with view, edit, and delete dropdown menu
  - Implement proper TypeScript types and accessibility attributes
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

- [x] 8. Implement DonorsTable component with advanced filtering
  - Create DonorsTable.tsx following DonationsTable.tsx pattern exactly
  - Implement search functionality for donor name and email with debounced input
  - Add filtering by donation amount ranges, date ranges, and campaign participation
  - Include sorting options by name, email, total donated, and last donation date
  - Add bulk selection, delete, and export functionality
  - Include loading states, error handling, retry functionality, and empty states
  - Add proper pagination with configurable page sizes
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 9. Create DonorRowActions component for table actions
  - Create DonorRowActions.tsx following DonationRowActions.tsx pattern
  - Implement dropdown menu with view, edit, and delete actions
  - Add proper loading states for individual row actions
  - Include confirmation dialogs for destructive actions
  - Add proper accessibility attributes and keyboard navigation
  - Handle action callbacks with proper error handling
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [x] 10. Create DonorForm component for create and edit operations
  - Create DonorForm.tsx following DonationForm.tsx pattern
  - Support both create and edit modes with conditional field rendering
  - Implement form validation using React Hook Form and Zod schemas
  - Add fields for name, email, phone, address, and notes
  - Include form submission handling with loading states and error feedback
  - Add email validation and duplicate checking for new donors
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7_

- [x] 11. Create DonorAnalytics component for table summary
  - Create DonorAnalytics.tsx following DonationAnalytics.tsx pattern
  - Display total donors count, total donations amount, and average donation
  - Show filtered vs total statistics when filters are applied
  - Include selected donors statistics when rows are selected
  - Add proper loading states and error handling
  - Implement responsive design for different screen sizes
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 12. Create DeleteDonorDialog following DeleteDonationDialog pattern
  - Create DeleteDonorDialog.tsx following DeleteDonationDialog.tsx structure
  - Add donor name confirmation requirement for deletion safety
  - Implement deletion restrictions for donors with existing donations
  - Include proper warning messages about data preservation
  - Add loading states during deletion and error handling
  - Implement proper keyboard navigation and accessibility
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [x] 13. Implement donor notes and tags management
  - Add notes field to DonorForm component with rich text editing capabilities
  - Create tag management system for donor categorization
  - Implement tag filtering in DonorsTable component
  - Add timestamp tracking for notes with organizer attribution
  - Include search functionality within notes content
  - Add proper validation and character limits for notes
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 14. Create donors routes following donations route structure
  - Create packages/admin/src/routes/\_authenticated/donors/ directory
  - Create index.tsx route following donations/index.tsx pattern with DonorsTable
  - Create create.tsx route for new donor creation with DonorForm
  - Create $donorId/index.tsx route for donor details view
  - Create $donorId/edit.tsx route for donor editing with DonorForm
  - Integrate components with proper error boundaries and loading states
  - _Requirements: 1.1, 2.1, 3.1, 4.1_

- [x] 15. Update AppSidebar navigation for donors
  - Update packages/admin/src/components/AppSidebar.tsx
  - Add "Donors" navigation section with links to donor management pages
  - Change placeholder "#" URLs to functional "/donors" routes
  - Ensure proper active state highlighting for donor routes
  - Test navigation integration and routing functionality
  - _Requirements: 1.1, 2.1, 3.1, 4.1_

- [ ] 16. Implement CSV export functionality in DonorsTable
  - Add export button to DonorsTable component toolbar following DonationsTable pattern
  - Export selected donors or all filtered data based on current view
  - Include donor information, donation statistics, and campaign participation
  - Integrate with backend export endpoint using useDonors hook
  - Handle CSV file download with proper error states and user feedback
  - Add export progress indication and row count display
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_

- [ ] 17. Add comprehensive component testing
  - Write unit tests for all donor components following donations **tests** structure
  - Test DonorsTable, columns, DonorRowActions, DonorAnalytics, and DonorForm components
  - Test API service functions and hook behaviors with proper mocking
  - Add integration tests for complete donor management workflows
  - Test organizer-scoped access control and data isolation
  - Ensure test coverage matches existing component test coverage standards
  - Add accessibility testing for all interactive table components
  - _Requirements: All requirements - testing coverage_

- [x] 18. Final integration and performance optimization
  - Ensure consistent styling with DonationsTable and other admin components
  - Add proper loading skeletons for table rows during data fetching
  - Verify responsive design works across all screen sizes for table layout
  - Test complete donor management workflow end-to-end with table interactions
  - Optimize performance for large donor datasets with pagination and virtual scrolling
  - Add proper error boundaries and fallback UI components
  - Test table sorting, filtering, and bulk operations performance
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_
