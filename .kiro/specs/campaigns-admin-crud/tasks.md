# Implementation Plan

- [x] 1. Extend backend campaigns controller with missing CRUD endpoints
  - Add PUT /api/campaigns/:id endpoint for updating campaigns
  - Add DELETE /api/campaigns/:id endpoint for deleting campaigns
  - Add PATCH /api/campaigns/:id/status endpoint for toggling campaign status
  - Implement proper authorization checks for organizer ownership
  - Add comprehensive error handling and validation
  - _Requirements: 3.1, 3.2, 3.3, 4.1, 4.2, 4.3, 5.1, 5.2_

- [x] 2. Create campaign schema extensions for new endpoints
  - Define updateCampaignDto schema for campaign updates
  - Define campaignIdParamsDto schema for URL parameters
  - Define statusToggleDto schema for status updates
  - Add response schemas for new endpoints
  - _Requirements: 3.1, 4.1, 5.1_

- [x] 3. Create campaigns API client for frontend
  - Implement getCampaigns function to fetch organizer's campaigns
  - Implement createCampaign function for campaign creation
  - Implement updateCampaign function for campaign updates
  - Implement deleteCampaign function for campaign deletion
  - Implement toggleCampaignStatus function for status updates
  - Add proper error handling and TypeScript types
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1_

- [x] 4. Create reusable campaign form component
  - Build CampaignForm component with React Hook Form and Zod validation
  - Support both create and edit modes with conditional field rendering
  - Implement real-time slug generation from name in create mode
  - Add form validation with user-friendly error messages
  - Include loading states and form submission handling
  - _Requirements: 2.2, 2.3, 2.4, 3.1, 3.2, 3.3_

- [x] 5. Create campaigns list component
  - Build CampaignsList component to display campaigns in responsive layout
  - Implement CampaignCard component for individual campaign display
  - Add empty state component for when no campaigns exist
  - Include loading states and error handling
  - Add action buttons for edit, delete, and status toggle
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 6.1, 6.2_

- [x] 6. Create campaign status toggle component
  - Build StatusToggle component with immediate visual feedback
  - Implement optimistic updates with error rollback
  - Add loading states during API calls
  - Include proper accessibility attributes
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 6.3_

- [x] 7. Create delete confirmation dialog component
  - Build DeleteCampaignDialog component with clear warning messages
  - Add campaign name confirmation for safety
  - Include donation count warning if applicable
  - Implement loading states during deletion
  - Add proper keyboard navigation and accessibility
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 6.3_

- [x] 8. Implement campaigns list route page
  - Replace placeholder campaigns.tsx route with functional CampaignsList integration
  - Add API data fetching with proper loading and error states
  - Implement proper page title and meta tags
  - Include navigation breadcrumbs and empty state handling
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 6.1_

- [x] 9. Create campaign creation route page
  - Build create campaign route at /campaigns/create
  - Integrate CampaignForm component in create mode
  - Add form submission handling with success/error feedback
  - Implement navigation after successful creation
  - Add proper page title and breadcrumbs
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 6.1_

- [x] 10. Create campaign edit route page
  - Build edit campaign route at /campaigns/:campaignId/edit
  - Integrate CampaignForm component in edit mode with pre-populated data
  - Add campaign data fetching and loading states
  - Implement form submission with success/error feedback
  - Add navigation after successful update
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 6.1_

- [x] 11. Update admin sidebar navigation
  - Update AppSidebar component to include functional campaign links
  - Replace placeholder "#" URLs with actual route paths
  - Update "All Campaigns" link to point to /campaigns
  - Update "Create New" link to point to /campaigns/create
  - Ensure proper active state highlighting for campaign routes
  - _Requirements: 6.1, 6.4_
