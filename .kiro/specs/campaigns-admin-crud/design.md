# Design Document

## Overview

This design implements comprehensive campaigns CRUD functionality for the admin panel, building upon the existing backend campaigns controller and integrating it with the React-based admin interface. The solution follows the established patterns in the DonorCare admin panel, utilizing TanStack Router for navigation, <PERSON>ustand for state management, and the shared UI component library.

## Architecture

### Backend Architecture

The backend extends the existing campaigns controller (`packages/backend/src/campaigns/campaigns.controller.ts`) with additional CRUD endpoints:

- **GET /api/campaigns/my** - List organizer's campaigns (existing)
- **POST /api/campaigns** - Create new campaign (existing)
- **PUT /api/campaigns/:id** - Update existing campaign (new)
- **DELETE /api/campaigns/:id** - Delete campaign (new)
- **PATCH /api/campaigns/:id/status** - Toggle campaign active status (new)

### Frontend Architecture

The frontend follows the established admin panel patterns:

```
packages/admin/src/
├── routes/_authenticated/
│   └── campaigns/
│       ├── index.tsx          # Campaigns list page
│       ├── create.tsx         # Create campaign page
│       └── $campaignId/
│           └── edit.tsx       # Edit campaign page
├── components/
│   └── campaigns/
│       ├── CampaignsList.tsx      # Main campaigns list component
│       ├── CampaignForm.tsx       # Reusable form component
│       ├── CampaignCard.tsx       # Individual campaign card
│       ├── DeleteCampaignDialog.tsx # Confirmation dialog
│       └── StatusToggle.tsx       # Active/inactive toggle
└── lib/
    └── campaigns-api.ts       # API client functions
```

## Components and Interfaces

### Backend Components

#### Extended Campaigns Controller

```typescript
// Additional endpoints to add to existing controller
PUT /api/campaigns/:id
- Updates campaign name, description, and isActive status
- Validates organizer ownership
- Prevents slug modification for URL consistency

DELETE /api/campaigns/:id  
- Soft delete or hard delete based on business rules
- Checks for associated donations before deletion
- Validates organizer ownership

PATCH /api/campaigns/:id/status
- Toggles isActive status only
- Provides quick status updates
- Returns updated campaign data
```

#### Schema Extensions

```typescript
// Additional DTOs for new endpoints
export const updateCampaignDto = t.Object({
  name: t.String({ minLength: 1, maxLength: 255 }),
  description: t.Optional(t.String()),
  isActive: t.Boolean(),
})

export const campaignIdParamsDto = t.Object({
  id: t.String({ format: 'uuid' }),
})

export const statusToggleDto = t.Object({
  isActive: t.Boolean(),
})
```

### Frontend Components

#### CampaignsList Component

Primary list view component that displays all campaigns in a responsive grid/table format:

```typescript
interface CampaignsListProps {
  campaigns: Campaign[]
  isLoading: boolean
  onEdit: (campaignId: string) => void
  onDelete: (campaignId: string) => void
  onStatusToggle: (campaignId: string, isActive: boolean) => void
}
```

Features:
- Responsive design (cards on mobile, table on desktop)
- Search and filter functionality
- Sorting by name, date, status
- Empty state with create campaign CTA
- Loading states and error handling

#### CampaignForm Component

Reusable form component for both create and edit operations:

```typescript
interface CampaignFormProps {
  mode: 'create' | 'edit'
  initialData?: Partial<Campaign>
  onSubmit: (data: CampaignFormData) => Promise<void>
  onCancel: () => void
  isLoading: boolean
}

interface CampaignFormData {
  name: string
  description?: string
  slug: string // Only editable in create mode
  isActive: boolean // Only in edit mode
}
```

Features:
- React Hook Form with Zod validation
- Real-time slug generation from name (create mode)
- Disabled slug field in edit mode
- Form persistence on navigation
- Comprehensive validation feedback

#### CampaignCard Component

Individual campaign display component:

```typescript
interface CampaignCardProps {
  campaign: Campaign
  onEdit: (campaignId: string) => void
  onDelete: (campaignId: string) => void
  onStatusToggle: (campaignId: string, isActive: boolean) => void
}
```

Features:
- Campaign status indicator
- Quick action buttons
- Responsive design
- Status toggle with immediate feedback

#### DeleteCampaignDialog Component

Confirmation dialog for campaign deletion:

```typescript
interface DeleteCampaignDialogProps {
  campaign: Campaign | null
  isOpen: boolean
  onClose: () => void
  onConfirm: (campaignId: string) => Promise<void>
  isLoading: boolean
}
```

Features:
- Clear warning about permanent deletion
- Campaign name confirmation
- Donation count warning if applicable
- Loading state during deletion

#### StatusToggle Component

Quick status toggle component:

```typescript
interface StatusToggleProps {
  campaignId: string
  isActive: boolean
  onToggle: (campaignId: string, isActive: boolean) => Promise<void>
  disabled?: boolean
}
```

Features:
- Immediate visual feedback
- Optimistic updates with rollback on error
- Disabled state during API calls

## Data Models

### Campaign Interface (Frontend)

```typescript
interface Campaign {
  id: string
  organizerId: string
  name: string
  description: string | null
  slug: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
}

interface CampaignFormData {
  name: string
  description?: string
  slug: string
  isActive: boolean
}

interface CampaignFilters {
  search?: string
  status?: 'all' | 'active' | 'inactive'
  sortBy?: 'name' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}
```

### API Response Types

```typescript
interface CampaignsListResponse {
  success: boolean
  campaigns: Campaign[]
}

interface CampaignResponse {
  success: boolean
  campaign: Campaign
}

interface ApiError {
  error: string
  details?: string[]
}
```

## Error Handling

### Backend Error Handling

- **401 Unauthorized**: User not authenticated
- **403 Forbidden**: User not authorized (non-organizer or wrong owner)
- **404 Not Found**: Campaign doesn't exist
- **400 Bad Request**: Validation errors, duplicate slug
- **409 Conflict**: Cannot delete campaign with donations
- **500 Internal Server Error**: Database or server errors

### Frontend Error Handling

- **Network Errors**: Retry mechanism with exponential backoff
- **Validation Errors**: Real-time form validation with clear messages
- **Permission Errors**: Redirect to appropriate page with error message
- **Not Found Errors**: 404 page with navigation options
- **Conflict Errors**: Clear explanation and suggested actions

Error Display Strategy:
- Toast notifications for temporary feedback
- Inline form errors for validation issues
- Error boundaries for component-level failures
- Fallback UI for loading states

## Testing Strategy

### Backend Testing

- **Unit Tests**: Controller methods, validation logic, error handling
- **Integration Tests**: Full API endpoint testing with database
- **Authentication Tests**: Permission validation, session handling
- **Edge Case Tests**: Duplicate slugs, deletion constraints

### Frontend Testing

- **Component Tests**: Individual component behavior and props
- **Integration Tests**: Form submission, API interaction, routing
- **User Flow Tests**: Complete CRUD workflows
- **Accessibility Tests**: Keyboard navigation, screen reader support
- **Responsive Tests**: Mobile and desktop layouts

Testing Tools:
- Backend: Vitest with test database
- Frontend: Vitest + Testing Library + MSW for API mocking
- E2E: Playwright for critical user flows

## Implementation Approach

### Phase 1: Backend Extensions
1. Add missing CRUD endpoints to campaigns controller
2. Implement validation and error handling
3. Add comprehensive tests for new endpoints

### Phase 2: Frontend Components
1. Create reusable campaign components
2. Implement campaigns API client
3. Add comprehensive component tests

### Phase 3: Route Integration
1. Create campaign route pages
2. Integrate with existing navigation
3. Add route guards and error boundaries

### Phase 4: Polish and Optimization
1. Add loading states and optimistic updates
2. Implement search and filtering
3. Add accessibility improvements
4. Performance optimization

## Security Considerations

- **Authorization**: Verify organizer ownership for all operations
- **Input Validation**: Comprehensive validation on both client and server
- **SQL Injection Prevention**: Use parameterized queries via Drizzle ORM
- **XSS Prevention**: Sanitize user inputs, use React's built-in protections
- **CSRF Protection**: Leverage Better-Auth's built-in CSRF protection
- **Rate Limiting**: Implement rate limiting for campaign creation/updates

## Performance Considerations

- **Database Indexing**: Ensure proper indexes on organizerId and slug fields
- **Pagination**: Implement pagination for large campaign lists
- **Caching**: Cache campaign data with appropriate invalidation
- **Optimistic Updates**: Immediate UI feedback for better UX
- **Bundle Splitting**: Code splitting for campaign-related components
- **Image Optimization**: Optimize any campaign images or assets