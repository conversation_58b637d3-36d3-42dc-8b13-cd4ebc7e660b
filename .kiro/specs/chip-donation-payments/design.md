# Design Document

## Overview

This design implements Chip payment gateway integration for donation processing in the DonorCare platform. The system will replace the current mock payment flow with a real Chip payment integration, enabling secure donation processing with proper webhook handling and donation record management.

The integration leverages the existing donation schema and controller structure while adding proper Chip payment service integration and webhook signature verification.

## Architecture

### High-Level Flow

1. **Donation Initiation**: <PERSON><PERSON> submits donation form → System creates pending donation → Chip payment session created → Donor redirected to Chip checkout
2. **Payment Processing**: <PERSON><PERSON> completes payment on Chip → Chip sends webhook → System verifies webhook → Donation status updated
3. **Completion**: Donor redirected back to success/failure page → Confirmation email sent (future enhancement)

### Component Interaction

```mermaid
sequenceDiagram
    participant D as Donor
    participant F as Frontend
    participant API as Donations API
    participant PS as Payment Service
    participant CHIP as Chip Gateway
    participant WH as Webhook Handler

    D->>F: Submit donation form
    F->>API: POST /api/donations/initiate
    API->>PS: createPayment()
    PS->>CHIP: Create payment session
    CHIP-->>PS: Payment URL + ID
    PS-->>API: Payment response
    API-->>F: Payment URL
    F->>D: Redirect to Chip checkout
    D->>CHIP: Complete payment
    CHIP->>WH: Send webhook notification
    WH->>PS: Verify signature
    WH->>API: Update donation status
    CHIP->>D: Redirect to success/failure page
```

## Components and Interfaces

### 1. Enhanced Donations Controller

**Location**: `packages/backend/src/donations/donations.controller.ts`

**Modifications**:
- Replace mock payment URL generation with actual Chip payment creation
- Update webhook endpoint to use the centralized webhook controller
- Add proper error handling for payment failures
- Include campaign context in payment metadata

**Key Changes**:
```typescript
// In /initiate endpoint
const paymentParams: CreatePaymentParams = {
  amount: parseFloat(body.amount),
  currency: "MYR",
  email: body.donorEmail,
  phone: "", // Will need to add phone field to donation form
  fullName: body.donorName,
  products: [{
    name: `Donation to ${campaign[0].name}`,
    price: parseFloat(body.amount),
    category: "donation",
    quantity: "1"
  }],
  successUrl: `${process.env.FRONTEND_URL}/api/webhooks/chip`,
  failureUrl: `${process.env.FRONTEND_URL}/api/webhooks/chip`,
  successRedirectUrl: `${process.env.FRONTEND_URL}/donation/success?campaign=${campaign[0].slug}&donation=${newDonation.id}`,
  failureRedirectUrl: `${process.env.FRONTEND_URL}/donation/failed?campaign=${campaign[0].slug}&donation=${newDonation.id}`,
  cancelRedirectUrl: `${process.env.FRONTEND_URL}/campaigns/${campaign[0].slug}`,
  reference: newDonation.id,
  notes: body.message
}

const chipResponse = await ChipService.createPayment(paymentParams)
```

### 2. Enhanced Payment Service

**Location**: `packages/backend/src/payments/payments.service.ts`

**Enhancements**:
- Add donation-specific payment handling methods
- Implement proper error handling and logging
- Add validation for payment parameters
- Include donation metadata in payment creation

**New Methods**:
```typescript
static async createDonationPayment(
  donation: Donation,
  campaign: Campaign,
  params: CreatePaymentParams
): Promise<ChipPaymentResponse>

static async handleDonationCompleted(data: ChipPaymentResponse): Promise<void>

static async handleDonationFailed(data: ChipPaymentResponse): Promise<void>

static async handleDonationCanceled(data: ChipPaymentResponse): Promise<void>
```

### 3. Enhanced Webhook Controller

**Location**: `packages/backend/src/webhooks/webhooks.controller.ts`

**Enhancements**:
- Add donation-specific webhook handling
- Implement proper database updates for donation status
- Add error handling and retry logic
- Include logging for audit trails

**Key Changes**:
```typescript
switch (data.status) {
  case "paid":
    await ChipService.handleDonationCompleted(data)
    break
  case "error":
  case "failed":
    await ChipService.handleDonationFailed(data)
    break
  case "canceled":
    await ChipService.handleDonationCanceled(data)
    break
}
```

### 4. Database Schema Updates

**Location**: `packages/backend/src/donations/donations.schema.ts`

**Required Changes**:
- Add phone field to donation initiation DTO
- Update webhook DTO to match Chip response structure
- Add payment metadata fields if needed

```typescript
export const initiateDonationDto = t.Object({
  campaignSlug: t.String({ minLength: 1 }),
  amount: t.String({ pattern: "^\\d+(\\.\\d{1,2})?$" }),
  donorName: t.String({ minLength: 1, maxLength: 255 }),
  donorEmail: t.String({ format: "email" }),
  donorPhone: t.Optional(t.String({ minLength: 10, maxLength: 15 })),
  message: t.Optional(t.String({ maxLength: 1000 })),
})
```

## Data Models

### Payment Flow Data

```typescript
interface DonationPaymentContext {
  donationId: string
  campaignId: string
  campaignSlug: string
  amount: number
  currency: string
  donorInfo: {
    name: string
    email: string
    phone?: string
  }
  metadata: {
    message?: string
    reference: string
  }
}
```

### Webhook Processing Data

```typescript
interface WebhookProcessingResult {
  success: boolean
  donationId?: string
  previousStatus?: string
  newStatus?: string
  error?: string
  timestamp: Date
}
```

## Error Handling

### Payment Creation Errors

1. **Invalid Campaign**: Return 404 with clear message
2. **Inactive Campaign**: Return 400 with campaign status info
3. **Chip API Errors**: Log full error, return generic payment failure message
4. **Database Errors**: Log error, return 500 with retry suggestion

### Webhook Processing Errors

1. **Invalid Signature**: Return 401, log security event
2. **Malformed Payload**: Return 400, log payload for debugging
3. **Database Update Failures**: Log error, implement retry mechanism
4. **Unknown Payment ID**: Log warning, return 200 to prevent retries

### Error Response Format

```typescript
interface ErrorResponse {
  error: string
  code?: string
  details?: Record<string, unknown>
  timestamp: string
}
```

## Testing Strategy

### Unit Tests

1. **Payment Service Tests**:
   - Test payment creation with valid parameters
   - Test error handling for invalid parameters
   - Test webhook signature verification
   - Test donation status update logic

2. **Webhook Controller Tests**:
   - Test webhook signature validation
   - Test payment status processing
   - Test error handling for malformed webhooks
   - Test idempotency for duplicate webhooks

3. **Donations Controller Tests**:
   - Test donation initiation flow
   - Test integration with payment service
   - Test error handling for invalid campaigns
   - Test donor creation and lookup logic

### Integration Tests

1. **End-to-End Payment Flow**:
   - Create test campaign
   - Initiate donation with valid data
   - Verify payment URL generation
   - Simulate webhook processing
   - Verify donation status updates

2. **Error Scenarios**:
   - Test with inactive campaigns
   - Test with invalid payment amounts
   - Test webhook signature failures
   - Test database connection failures

### Test Data Setup

```typescript
const testCampaign = {
  id: "test-campaign-id",
  name: "Test Campaign",
  slug: "test-campaign",
  isActive: true,
  organizerId: "test-organizer-id"
}

const testDonation = {
  campaignSlug: "test-campaign",
  amount: "50.00",
  donorName: "John Doe",
  donorEmail: "<EMAIL>",
  donorPhone: "+60123456789",
  message: "Keep up the good work!"
}
```

## Security Considerations

### Webhook Security

1. **Signature Verification**: Always verify Chip webhook signatures using the configured public key
2. **HTTPS Only**: Ensure all webhook endpoints use HTTPS in production
3. **Rate Limiting**: Implement rate limiting on webhook endpoints
4. **Idempotency**: Handle duplicate webhook deliveries gracefully

### Payment Security

1. **Amount Validation**: Validate payment amounts are positive and within reasonable limits
2. **Campaign Validation**: Ensure campaigns are active and exist before creating payments
3. **Donor Data**: Sanitize and validate all donor input data
4. **Environment Variables**: Secure storage of Chip API credentials

### Data Protection

1. **PII Handling**: Minimize storage of sensitive donor information
2. **Audit Logging**: Log all payment operations for compliance
3. **Error Messages**: Avoid exposing sensitive information in error responses
4. **Database Security**: Use parameterized queries to prevent SQL injection

## Configuration Requirements

### Environment Variables

```bash
# Chip Payment Gateway
CHIP_BASE_URL=https://gate.chip-in.asia/api/v1
CHIP_SECRET_KEY=your_secret_key
CHIP_BRAND_ID=your_brand_id
CHIP_WEBHOOK_SECRET=your_webhook_public_key

# Application URLs
FRONTEND_URL=https://your-frontend-domain.com
BACKEND_URL=https://your-api-domain.com
```

### Deployment Considerations

1. **Webhook URL**: Ensure webhook URL is accessible from Chip servers
2. **SSL Certificate**: Valid SSL certificate required for webhook endpoints
3. **Environment Separation**: Use different Chip credentials for staging/production
4. **Monitoring**: Set up monitoring for payment failures and webhook processing

## Performance Considerations

### Database Optimization

1. **Indexes**: Ensure proper indexes on donation queries (campaign_id, status, created_at)
2. **Connection Pooling**: Use connection pooling for database operations
3. **Query Optimization**: Optimize donation listing queries with proper pagination

### API Performance

1. **Response Times**: Target <500ms for payment initiation
2. **Webhook Processing**: Process webhooks within 5 seconds
3. **Error Handling**: Fast-fail for invalid requests
4. **Caching**: Cache campaign data for payment creation

### Monitoring Metrics

1. **Payment Success Rate**: Track successful vs failed payments
2. **Webhook Processing Time**: Monitor webhook processing latency
3. **Error Rates**: Track API error rates by endpoint
4. **Database Performance**: Monitor query execution times