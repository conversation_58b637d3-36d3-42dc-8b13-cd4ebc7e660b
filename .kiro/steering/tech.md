# Technology Stack

## Build System & Package Management

- **Package Manager**: <PERSON><PERSON> (primary runtime and package manager)
- **Monorepo**: Workspace-based monorepo with packages in `packages/` directory
- **Build Tool**: Vite for frontend applications
- **TypeScript**: Strict mode enabled across all packages

## Backend Stack

- **Runtime**: Bun
- **Framework**: Elysia (TypeScript-first web framework)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Better-Auth
- **Validation**: Zod (via Drizzle TypeBox integration)

## Frontend Stack

- **Admin Panel**: React + TanStack Router + Vite
- **Public Frontend**: React + TanStack Start (SSR) + TanStack Router
- **UI Components**: Shared UI package with Shadcn/ui components
- **Styling**: Tailwind CSS v4
- **State Management**: Zustand (admin), TanStack Query (data fetching)
- **Forms**: React Hook Form + Zod validation

## Code Quality & Formatting

- **Linter/Formatter**: Biome (replaces <PERSON><PERSON><PERSON> + <PERSON><PERSON><PERSON>)
- **Testing**: Vitest + Testing Library
- **Type Checking**: TypeScript with strict configuration

## Common Commands

### Development
```bash
# Start all services
bun dev

# Start individual services
bun dev:backend    # Backend API (Elysia)
bun dev:admin      # Admin panel (port 3001)
bun dev:frontend   # Public frontend (port 3002)
```

### Database Operations
```bash
bun db:migrate              # Run database migrations
bun db:seed                 # Seed database with test data
bun db:generate-migration   # Generate new migration
```

### Code Quality
```bash
bun check          # Format, lint, and fix all packages
bun format         # Format code with Biome
bun lint           # Lint code with Biome
```

### Testing
```bash
bun test:backend   # Run backend tests
bun test:admin     # Run admin panel tests
bun test:frontend  # Run frontend tests
```

### Building
```bash
bun build:backend   # Build backend for production
bun build:admin     # Build admin panel
bun build:frontend  # Build public frontend
```

## Configuration Standards

- **TypeScript**: ES2022 target, strict mode, bundler module resolution
- **Biome**: Tab indentation, 80 character line width, double quotes
- **Import Organization**: Automatic import sorting enabled
- **File Extensions**: `.ts` for backend, `.tsx` for React components