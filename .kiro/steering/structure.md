# Project Structure

## Monorepo Organization

The project follows a workspace-based monorepo structure with four main packages:

```
donorcare/
├── packages/
│   ├── backend/     # Elysia API server
│   ├── admin/       # Admin dashboard (React + TanStack Router)
│   ├── frontend/    # Public frontend (TanStack Start SSR)
│   └── ui/          # Shared UI components library
├── .kiro/           # Kiro AI assistant configuration
├── .git/            # Git repository
└── package.json     # Root workspace configuration
```

## Backend Package (`packages/backend/`)

```
backend/
├── src/
│   ├── campaigns/           # Campaign-related controllers and logic
│   ├── donations/           # Donation processing and management
│   ├── users/              # User management and schemas
│   │   └── users.schema.ts # Database schema definitions
│   ├── routes/             # API route definitions
│   ├── db.ts              # Database connection setup
│   ├── model.ts           # Unified model exports
│   └── index.ts           # Main server entry point
├── lib/
│   ├── auth.ts            # Authentication configuration
│   ├── constants.ts       # Application constants
│   └── database/          # Database utilities
├── drizzle/
│   ├── migrations/        # Database migration files
│   ├── migrate.ts         # Migration runner
│   └── seed.ts           # Database seeding script
└── drizzle.config.ts      # Drizzle ORM configuration
```

## Admin Package (`packages/admin/`)

```
admin/
├── src/
│   ├── components/        # React components
│   │   ├── AppSidebar.tsx
│   │   ├── Header.tsx
│   │   ├── LoginForm.tsx
│   │   └── UserMenu.tsx
│   ├── lib/
│   │   ├── auth/          # Authentication system
│   │   │   ├── context.tsx
│   │   │   ├── route-guards.ts
│   │   │   └── session-manager.ts
│   │   ├── auth-client.ts
│   │   ├── auth-store.ts  # Zustand auth state
│   │   └── mockData.ts
│   ├── routes/            # TanStack Router route definitions
│   ├── types/
│   │   └── auth.ts        # TypeScript type definitions
│   ├── main.tsx          # Application entry point
│   └── styles.css        # Global styles
└── components.json        # Shadcn/ui configuration
```

## Frontend Package (`packages/frontend/`)

```
frontend/
├── src/
│   ├── components/        # React components
│   ├── integrations/
│   │   ├── tanstack-query/  # TanStack Query setup
│   │   └── trpc/           # tRPC client configuration
│   ├── lib/
│   │   └── api.ts         # API client utilities
│   ├── routes/            # TanStack Start route definitions
│   ├── env.ts            # Environment variable validation
│   ├── router.tsx        # Router configuration
│   └── styles.css        # Global styles
└── vite.config.ts        # Vite configuration
```

## UI Package (`packages/ui/`)

```
ui/
├── src/
│   ├── components/
│   │   ├── ui/            # Base Shadcn/ui components
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── input.tsx
│   │   │   └── ...
│   │   └── animate-ui/    # Enhanced animated components
│   ├── lib/
│   │   ├── utils.ts       # Utility functions (cn, etc.)
│   │   └── use-mobile.ts  # Mobile detection hook
│   ├── index.ts          # Package exports
│   └── styles.css        # Component styles
└── components.json        # Shadcn/ui configuration
```

## Key Architectural Patterns

### Database Schema Organization
- All database schemas defined in `packages/backend/src/users/users.schema.ts`
- Uses Drizzle ORM with PostgreSQL
- Follows Better-Auth schema conventions for user management
- Separate tables for users, sessions, accounts, campaigns, and donations

### Authentication Flow
- Better-Auth handles authentication across all applications
- Zustand store manages auth state in admin panel
- Route guards protect authenticated routes
- Session persistence across page refreshes

### Component Sharing
- UI components centralized in `@donorcare/ui` package
- Shadcn/ui as the base component library
- Consistent theming and styling across applications
- Workspace imports using `workspace:*` protocol

### API Structure
- RESTful API endpoints organized by domain (campaigns, donations, users)
- Elysia framework with TypeScript-first approach
- Zod validation for request/response schemas
- CORS enabled for cross-origin requests

### Development Workflow
- Each package has independent dev/build/test scripts
- Root-level scripts orchestrate multi-package operations
- Biome handles code formatting and linting consistently
- TypeScript project references for efficient compilation