{"name": "donorcare", "version": "0.1.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently -k -s first \"npm:dev:*\"", "dev:admin": "cd packages/admin && bun run dev", "dev:backend": "cd packages/backend && bun run dev", "dev:frontend": "cd packages/frontend && bun run dev", "build:backend": "cd packages/backend && bun run build", "build:admin": "cd packages/admin && bun run build", "build:frontend": "cd packages/frontend && bun run build", "test:backend": "cd packages/backend && bun run test", "test:admin": "cd packages/admin && bun run test", "test:frontend": "cd packages/frontend && bun run test", "format": "bun x biome format --write .", "lint": "bun x biome lint .", "check": "bun x biome check --write .", "format:backend": "cd packages/backend && bun run format", "format:admin": "cd packages/admin && bun run format", "format:frontend": "cd packages/frontend && bun run format", "db:migrate": "cd packages/backend && bun run migrate", "db:seed": "cd packages/backend && bun run seed", "db:generate-migration": "cd packages/backend && bun run generate-migration"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.3.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.2.1", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.3.5", "vitest": "^3.0.5", "web-vitals": "^4.2.4"}, "packageManager": "bun@1.0.0", "dependencies": {"@elysiajs/eden": "^1.3.3", "@sinclair/typemap": "^0.10.1"}}