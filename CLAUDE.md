# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DonorCARE is a donation SaaS platform MVP built with a modern tech stack. The project is structured as a Bun monorepo with three main applications:

- **@donorcare/backend**: Elysia backend with <PERSON><PERSON>zle ORM, PostgreSQL, and Better-Auth (port 3000)
- **@donorcare/admin**: Admin panel with TanStack Router for campaign and donation management (port 3001)
- **@donorcare/frontend**: Public SSR frontend with TanStack Start for donation pages and SEO (port 3002)

## Architecture

### Admin Panel (@donorcare/admin)
- **Framework**: Vite + React 19 + TypeScript
- **Routing**: TanStack Router with file-based routing
- **UI Components**: Shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS v4
- **State Management**: Zustand store for authentication
- **Forms**: React Hook Form + Zod validation
- **Auth**: Better-Auth client integration with enhanced session management
- **Testing**: Vitest + React Testing Library

### Public Frontend (@donorcare/frontend)
- **Framework**: TanStack Start (SSR) with React 19 + TypeScript
- **Routing**: TanStack Router with SSR capabilities
- **UI Components**: Shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS v4
- **Data Fetching**: TanStack Query with tRPC
- **Forms**: React Hook Form + Zod validation
- **SEO**: Server-side rendering for better search indexing

### Backend (@donorcare/backend)
- **Runtime**: Bun
- **Framework**: Elysia (TypeScript-first web framework)
- **Database**: PostgreSQL with Drizzle ORM
- **Auth**: Better-Auth with cookie-based sessions
- **API Documentation**: Swagger/OpenAPI integration

## Development Commands

### From Root (Monorepo Commands)
```bash
# Development servers
bun run dev:backend   # Start backend dev server (port 3000)
bun run dev:admin     # Start admin dev server (port 3001)
bun run dev:frontend  # Start frontend dev server (port 3002)

# Build commands
bun run build:backend
bun run build:admin
bun run build:frontend

# Database operations
bun run db:migrate
bun run db:seed
bun run db:generate-migration

# Testing
bun run test:backend
bun run test:admin
bun run test:frontend

# Code formatting
bun run format:backend
bun run format:admin
bun run format:frontend
```

### Individual Package Commands
```bash
# Backend (@donorcare/backend)
cd packages/backend
bun run dev          # Start dev server with file watching (port 3000)
bun run seed         # Seed database
bun run migrate      # Run database migrations
bun run generate-migration  # Generate new migration

# Admin Panel (@donorcare/admin)
cd packages/admin
npm run dev          # Start dev server on port 3001
npm run build        # Build for production with TypeScript check
npm run test         # Run tests with Vitest
npm run format       # Format code with Biome

# Public Frontend (@donorcare/frontend)
cd packages/frontend
npm run dev          # Start SSR dev server on port 3002
npm run build        # Build for production with SSR
npm run start        # Start production SSR server
npm run test         # Run tests with Vitest
npm run format       # Format code with Biome
```

## Key Architecture Patterns

### Authentication System
The auth system uses Better-Auth with enhanced session management:
- **Client**: `src/lib/auth-client.ts` - Enhanced Better-Auth client with retry logic and session validation
- **Store**: `src/lib/auth-store.ts` - Zustand store for auth state
- **Route Protection**: `src/lib/auth/route-guards.ts` - Comprehensive route protection with authentication checks

### Route Structure (Frontend)
- `src/routes/__root.tsx` - Root layout with auth context
- `src/routes/_authenticated.tsx` - Protected route wrapper
- `src/routes/_authenticated/` - Protected pages (dashboard, profile, settings)
- `src/routes/index.tsx` - Public landing page
- `src/routes/login.tsx` - Authentication page

### API Structure (Backend)
- `src/index.ts` - Main Elysia server with Better-Auth integration
- `src/routes/health.ts` - Health check endpoints
- `src/users/` - User-related controllers, schemas, and services
- `/api/auth/*` - Better-Auth endpoints (handled by auth.handler)

### Database Schema
- Uses Drizzle ORM with PostgreSQL
- Schema files: `src/users/users.schema.ts`
- Migrations: `drizzle/migrations/`
- Configuration: `drizzle.config.ts`

## Testing Approach

### Frontend Testing
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: Auth flow and route protection testing
- **Test Setup**: `src/test-setup.ts` configures testing environment
- **Auth Tests**: Comprehensive testing in `src/lib/__tests__/`

### Running Tests
```bash
# Run all tests
npm test

# Run specific test file
npm test auth-client.test.ts --run
```

## Important Conventions

### File Organization
- UI components in `src/components/ui/` (Shadcn/ui)
- Custom components in `src/components/`
- Route components in `src/routes/`
- Auth utilities in `src/lib/auth/`
- Type definitions in `src/types/`

### Authentication Context
- All authenticated routes must be under `src/routes/_authenticated/`
- Auth context is provided via TanStack Router context
- Session validation happens automatically via AuthClientService
- Use `useAuthStore()` for accessing auth state in components

### Database Operations
- Use Drizzle ORM for type-safe database operations
- Schema files should end with `.schema.ts`
- Include proper TypeBox validation schemas
- Database URL: `postgresql://localhost:5432/donorcare` (development)

### API Development
- Use Elysia's type-safe route definitions
- Include proper TypeBox validation schemas
- Handle CORS for frontend origins: localhost:3000, 3001, 3002
- All auth routes are handled by Better-Auth at `/api/auth/*`

## Development Workflow

1. Start backend: `bun run dev:backend` (port 3000)
2. Start admin panel: `bun run dev:admin` (port 3001)
3. Start public frontend: `bun run dev:frontend` (port 3002)
4. Use Playwright for frontend validation (per user's global instructions)
5. Run tests before committing changes
6. Format code with Biome before committing

### Application Roles
- **@donorcare/backend**: Handles all API endpoints, auth, database operations
- **@donorcare/admin**: Admin dashboard for campaign management, donor analytics
- **@donorcare/frontend**: Public donation pages with SEO optimization via SSR

## Environment Setup

### Backend Environment
- Requires PostgreSQL database
- Database URL configured in `drizzle.config.ts`
- Better-Auth configuration in `lib/auth.ts`

### Frontend Environment  
- No special environment setup required
- Auth client configured to connect to backend on port 3000
- TanStack Router devtools enabled in development