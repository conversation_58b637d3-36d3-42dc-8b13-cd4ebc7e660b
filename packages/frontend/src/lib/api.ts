// API base URL
const API_BASE = "http://localhost:3000/api"

// Types for API responses
export interface Campaign {
	id: string
	organizerId: string
	name: string
	description: string | null
	slug: string
	isActive: boolean
	createdAt: string
}

export interface PublicCampaign {
	id: string
	name: string
	description: string | null
	slug: string
	isActive: boolean
	createdAt: string
}

export interface Donation {
	id: string
	campaignId: string
	donorId: string
	amount: string
	currency: string
	status: "pending" | "completed" | "failed"
	donorName: string
	donorEmail: string
	createdAt: string
}

export interface DonationRequest {
	campaignSlug: string
	amount: string
	donorName: string
	donorEmail: string
	message?: string
}

export interface ApiResponse<T> {
	success: boolean
	data?: T
	error?: string
}

// API functions for public frontend (donorcare_fe)
export const api = {
	// Campaign APIs - only public endpoints needed for donation page
	async getPublicCampaign(
		slug: string,
	): Promise<ApiResponse<{ campaign: PublicCampaign }>> {
		try {
			const response = await fetch(`${API_BASE}/campaigns/${slug}`)
			const data = await response.json()

			if (!response.ok) {
				return {
					success: false,
					error: data.error || "Failed to fetch campaign",
				}
			}

			return { success: true, data }
		} catch (error) {
			return { success: false, error: "Network error" }
		}
	},

	// Donation APIs - focused on public donation flow
	async initiateDonation(
		donation: DonationRequest,
	): Promise<ApiResponse<{ donation: Donation; paymentUrl: string }>> {
		try {
			const response = await fetch(`${API_BASE}/donations/initiate`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(donation),
			})
			const data = await response.json()

			if (!response.ok) {
				return {
					success: false,
					error: data.error || "Failed to initiate donation",
				}
			}

			return { success: true, data }
		} catch (error) {
			return { success: false, error: "Network error" }
		}
	},
}
