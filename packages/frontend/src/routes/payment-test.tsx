import {
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Textarea,
} from "@donorcare/ui"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { CreditCard, Loader2, XCircle } from "lucide-react"
import { useEffect, useState } from "react"

export const Route = createFileRoute("/payment-test")({
	component: PaymentTestPage,
})

function PaymentTestPage() {
	const navigate = useNavigate()
	const [paymentStatus, setPaymentStatus] = useState<
		"idle" | "processing" | "success" | "failed"
	>("idle")
	const [timeLeft, setTimeLeft] = useState(30) // 30 seconds countdown
	const [error, setError] = useState<string | null>(null)

	// Form state
	const [formData, setFormData] = useState({
		campaignSlug: "test-campaign-2",
		amount: "50.00",
		donorName: "John Doe",
		donorEmail: "<EMAIL>",
		donorPhone: "",
		message: "Thank you for your great work!",
	})

	// Handle form input changes
	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => {
		const { name, value } = e.target
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}))
	}

	// Handle real payment processing
	const handlePayment = async () => {
		setPaymentStatus("processing")
		setTimeLeft(30)
		setError(null)

		try {
			const response = await fetch(
				"http://localhost:3000/api/donations/initiate",
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						campaignSlug: formData.campaignSlug,
						amount: formData.amount,
						donorName: formData.donorName,
						donorEmail: formData.donorEmail,
						donorPhone: formData.donorPhone || undefined,
						message: formData.message || undefined,
					}),
				},
			)

			const result = await response.json()

			if (!response.ok) {
				throw new Error(result.message || "Failed to initiate donation")
			}

			// Redirect to payment gateway
			if (result.paymentUrl) {
				window.location.href = result.paymentUrl
			} else {
				throw new Error("Payment URL not received from server")
			}
		} catch (err) {
			console.error("Payment error:", err)
			setPaymentStatus("failed")
			setError(err instanceof Error ? err.message : "An unknown error occurred")
		}
	}

	const resetPayment = () => {
		setPaymentStatus("idle")
		setTimeLeft(30)
		setError(null)
	}

	// Countdown timer for payment window
	useEffect(() => {
		let countdown: NodeJS.Timeout
		if (paymentStatus === "processing" && timeLeft > 0) {
			countdown = setTimeout(() => {
				setTimeLeft((prev) => prev - 1)
			}, 1000)
		} else if (timeLeft === 0 && paymentStatus === "processing") {
			setPaymentStatus("failed")
			setError("Payment timed out. Please try again.")
		}

		return () => {
			if (countdown) clearTimeout(countdown)
		}
	}, [paymentStatus, timeLeft])

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="container mx-auto max-w-2xl px-4">
				<Card className="mb-6">
					<CardHeader>
						<CardTitle className="text-2xl font-bold text-center">
							Chip Payment Gateway Test
						</CardTitle>
						<CardDescription className="text-center">
							Test the chip payment processing flow
						</CardDescription>
					</CardHeader>
					<CardContent>
						{paymentStatus === "idle" && (
							<div className="space-y-6">
								<div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
									<h3 className="font-medium text-blue-800 mb-2">
										Donation Details
									</h3>
									<div className="space-y-4">
										<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label htmlFor="campaignSlug">Campaign Slug</Label>
												<Input
													id="campaignSlug"
													name="campaignSlug"
													value={formData.campaignSlug}
													onChange={handleInputChange}
													placeholder="help-build-community-center"
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="amount">Amount (MYR)</Label>
												<Input
													id="amount"
													name="amount"
													type="number"
													step="0.01"
													min="1"
													value={formData.amount}
													onChange={handleInputChange}
													placeholder="50.00"
												/>
											</div>
										</div>

										<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label htmlFor="donorName">Donor Name</Label>
												<Input
													id="donorName"
													name="donorName"
													value={formData.donorName}
													onChange={handleInputChange}
													placeholder="John Doe"
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="donorEmail">Donor Email</Label>
												<Input
													id="donorEmail"
													name="donorEmail"
													type="email"
													value={formData.donorEmail}
													onChange={handleInputChange}
													placeholder="<EMAIL>"
												/>
											</div>
										</div>

										<div className="space-y-2">
											<Label htmlFor="donorPhone">Donor Phone (Optional)</Label>
											<Input
												id="donorPhone"
												name="donorPhone"
												value={formData.donorPhone}
												onChange={handleInputChange}
												placeholder="+60123456789"
											/>
										</div>

										<div className="space-y-2">
											<Label htmlFor="message">Message (Optional)</Label>
											<Textarea
												id="message"
												name="message"
												value={formData.message}
												onChange={handleInputChange}
												placeholder="Your message..."
												rows={3}
											/>
										</div>
									</div>
								</div>

								<div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
									<h3 className="font-medium text-yellow-800 mb-2 flex items-center gap-2">
										<CreditCard className="h-4 w-4" />
										Chip Payment Integration
									</h3>
									<p className="text-sm text-yellow-700 mb-4">
										This is a test page to process real chip payments. Click the
										button below to initiate a payment with the Chip payment
										gateway.
									</p>

									<Button
										onClick={handlePayment}
										className="w-full h-12 text-lg"
									>
										<CreditCard className="mr-2 h-5 w-5" />
										Process Chip Payment
									</Button>
								</div>

								<div className="text-center">
									<Button
										variant="outline"
										onClick={() => navigate({ to: "/" })}
									>
										Back to Home
									</Button>
								</div>
							</div>
						)}

						{paymentStatus === "processing" && (
							<div className="text-center py-8">
								<Loader2 className="h-12 w-12 animate-spin mx-auto text-blue-600 mb-4" />
								<h3 className="text-xl font-medium mb-2">Processing Payment</h3>
								<p className="text-gray-600 mb-4">
									Redirecting to the Chip payment gateway...
								</p>
								<div className="bg-gray-100 p-4 rounded-lg inline-block">
									<p className="text-sm font-medium">
										Time remaining: {timeLeft}s
									</p>
								</div>
							</div>
						)}

						{paymentStatus === "failed" && (
							<div className="text-center py-8">
								<XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
								<h3 className="text-2xl font-bold text-red-600 mb-2">
									Payment Failed
								</h3>
								{error && (
									<p className="text-gray-600 mb-4 bg-red-50 p-3 rounded-lg">
										Error: {error}
									</p>
								)}
								<div className="flex flex-col sm:flex-row gap-3 justify-center">
									<Button onClick={resetPayment}>Try Again</Button>
									<Button
										variant="outline"
										onClick={() => navigate({ to: "/" })}
									>
										Back to Home
									</Button>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				<div className="text-center text-sm text-gray-500 mt-6">
					<p>
						This is a test environment for the chip payment gateway integration.
					</p>
				</div>
			</div>
		</div>
	)
}
