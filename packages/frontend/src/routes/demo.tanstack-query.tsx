import { useMutation, useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { useCallback, useState } from "react"

export const Route = createFileRoute("/demo/tanstack-query")({
	component: TanStackQueryDemo,
})

type Todo = {
	id: number
	name: string
}

function TanStackQueryDemo() {
	const { data, refetch } = useQuery<Todo[]>({
		queryKey: ["todos"],
		queryFn: () => fetch("/api/demo-tq-todos").then((res) => res.json()),
		initialData: [],
	})

	const { mutate: addTodo } = useMutation({
		mutationFn: (todo: string) =>
			fetch("/api/demo-tq-todos", {
				method: "POST",
				body: JSON.stringify(todo),
			}).then((res) => res.json()),
		onSuccess: () => refetch(),
	})

	const [todo, setTodo] = useState("")

	const submitTodo = useCallback(async () => {
		await addTodo(todo)
		setTodo("")
	}, [addTodo, todo])

	return (
		<div
			className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-900 via-red-800 to-black p-4 text-white"
			style={{
				backgroundImage:
					"radial-gradient(50% 50% at 80% 20%, #3B021F 0%, #7B1028 60%, #1A000A 100%)",
			}}
		>
			<div className="w-full max-w-2xl p-8 rounded-xl backdrop-blur-md bg-black/50 shadow-xl border-8 border-black/10">
				<h1 className="text-2xl mb-4">TanStack Query Todos list</h1>
				<ul className="mb-4 space-y-2">
					{data?.map((t) => (
						<li
							key={t.id}
							className="bg-white/10 border border-white/20 rounded-lg p-3 backdrop-blur-sm shadow-md"
						>
							<span className="text-lg text-white">{t.name}</span>
						</li>
					))}
				</ul>
				<div className="flex flex-col gap-2">
					<input
						type="text"
						value={todo}
						onChange={(e) => setTodo(e.target.value)}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								submitTodo()
							}
						}}
						placeholder="Enter a new todo..."
						className="w-full px-4 py-3 rounded-lg border border-white/20 bg-white/10 backdrop-blur-sm text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
					/>
					<button
						disabled={todo.trim().length === 0}
						onClick={submitTodo}
						className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-500/50 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-lg transition-colors"
					>
						Add todo
					</button>
				</div>
			</div>
		</div>
	)
}
