import { useTRPC } from "@/integrations/trpc/react"
import { useMutation, useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { useCallback, useState } from "react"

export const Route = createFileRoute("/demo/trpc-todo")({
	component: TRPCTodos,
	loader: async ({ context }) => {
		await context.queryClient.prefetchQuery(
			context.trpc.todos.list.queryOptions(),
		)
	},
})

function TRPCTodos() {
	const trpc = useTRPC()
	const { data, refetch } = useQuery(trpc.todos.list.queryOptions())

	const [todo, setTodo] = useState("")
	const { mutate: addTodo } = useMutation({
		...trpc.todos.add.mutationOptions(),
		onSuccess: () => {
			refetch()
			setTodo("")
		},
	})

	const submitTodo = useCallback(() => {
		addTodo({ name: todo })
	}, [addTodo, todo])

	return (
		<div
			className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-100 to-blue-100 p-4 text-white"
			style={{
				backgroundImage:
					"radial-gradient(50% 50% at 95% 5%, #4a90c2 0%, #317eb9 50%, #1e4d72 100%)",
			}}
		>
			<div className="w-full max-w-2xl p-8 rounded-xl backdrop-blur-md bg-black/50 shadow-xl border-8 border-black/10">
				<h1 className="text-2xl mb-4">tRPC Todos list</h1>
				<ul className="mb-4 space-y-2">
					{data?.map((t) => (
						<li
							key={t.id}
							className="bg-white/10 border border-white/20 rounded-lg p-3 backdrop-blur-sm shadow-md"
						>
							<span className="text-lg text-white">{t.name}</span>
						</li>
					))}
				</ul>
				<div className="flex flex-col gap-2">
					<input
						type="text"
						value={todo}
						onChange={(e) => setTodo(e.target.value)}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								submitTodo()
							}
						}}
						placeholder="Enter a new todo..."
						className="w-full px-4 py-3 rounded-lg border border-white/20 bg-white/10 backdrop-blur-sm text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
					/>
					<button
						disabled={todo.trim().length === 0}
						onClick={submitTodo}
						className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-500/50 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-lg transition-colors"
					>
						Add todo
					</button>
				</div>
			</div>
		</div>
	)
}
