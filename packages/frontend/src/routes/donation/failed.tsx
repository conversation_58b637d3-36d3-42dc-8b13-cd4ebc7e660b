import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@donorcare/ui"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { AlertTriangle, XCircle } from "lucide-react"
import { useState } from "react"

export const Route = createFileRoute("/donation/failed")({
  component: DonationFailedPage,
})

function DonationFailedPage() {
  const navigate = useNavigate()
  const [errorDetails] = useState("Payment processing failed. Please check your payment details and try again.")
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto max-w-2xl px-4">
        <Card className="mb-6">
          <CardHeader className="text-center">
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-2xl font-bold text-red-600">
              Donation Failed
            </CardTitle>
            <CardDescription>
              Unfortunately, your donation could not be processed at this time.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="bg-red-50 p-6 rounded-lg border border-red-200 mb-6">
              <h3 className="font-medium text-red-800 mb-2 flex items-center justify-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Transaction Error
              </h3>
              <p className="text-red-700">
                {errorDetails}
              </p>
            </div>
            
            <div className="space-y-4">
              <p className="text-gray-600">
                Don't worry, no charges have been made to your account. Please try again or contact our support team for assistance.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                <Button 
                  onClick={() => navigate({ to: "/payment-test" })}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Try Again
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate({ to: "/" })}
                >
                  Back to Home
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="text-center text-sm text-gray-500">
          <p>
            Need help? Contact our support <NAME_EMAIL>
          </p>
        </div>
      </div>
    </div>
  )
}