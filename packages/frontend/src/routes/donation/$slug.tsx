import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { createFileRoute, useRouter } from "@tanstack/react-router"
import { <PERSON>ert<PERSON>riangle, DollarSign, Heart } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import {
	Alert,
	AlertDescription,
	Button,
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Textarea,
} from "@donorcare/ui"
import { type PublicCampaign, api } from "../../lib/api"

// Form validation schema
const donationSchema = z.object({
	amount: z
		.string()
		.min(1, "Amount is required")
		.regex(/^\d+(\.\d{1,2})?$/, "Please enter a valid amount")
		.refine(
			(val) => Number.parseFloat(val) >= 5,
			"Minimum donation is RM 5.00",
		),
	donorName: z.string().min(2, "Name must be at least 2 characters"),
	donorEmail: z.string().email("Please enter a valid email address"),
	message: z.string().optional(),
})

type DonationForm = z.infer<typeof donationSchema>

interface DonatePageState {
	campaign: PublicCampaign | null
	loading: boolean
	error: string | null
	submitting: boolean
}

export const Route = createFileRoute("/donation/$slug")({
	component: DonatePage,
	loader: async ({ params }) => {
		// Server-side data loading for SSR
		const result = await api.getPublicCampaign(params.slug)
		if (!result.success || !result.data) {
			throw new Error(result.error || "Campaign not found")
		}
		return result.data.campaign
	},
})

function DonatePage() {
	const { slug } = Route.useParams()
	const router = useRouter()
	const campaignData = Route.useLoaderData()

	const [state, setState] = useState<DonatePageState>({
		campaign: campaignData || null,
		loading: !campaignData,
		error: null,
		submitting: false,
	})

	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm<DonationForm>({
		resolver: zodResolver(donationSchema),
	})

	// Fetch campaign data if not loaded by SSR
	useEffect(() => {
		if (!state.campaign && !state.loading) {
			setState((prev) => ({ ...prev, loading: true }))
			api.getPublicCampaign(slug).then((result) => {
				if (result.success && result.data) {
					setState((prev) => ({
						...prev,
						campaign: result.data?.campaign,
						loading: false,
						error: null,
					}))
				} else {
					setState((prev) => ({
						...prev,
						loading: false,
						error: result.error || "Campaign not found",
					}))
				}
			})
		}
	}, [slug, state.campaign, state.loading])

	const onSubmit = async (data: DonationForm) => {
		if (!state.campaign) return

		setState((prev) => ({ ...prev, submitting: true }))

		try {
			const result = await api.initiateDonation({
				campaignSlug: state.campaign.slug,
				amount: data.amount,
				donorName: data.donorName,
				donorEmail: data.donorEmail,
				message: data.message,
			})

			if (result.success && result.data) {
				// Redirect to payment gateway
				window.location.href = result.data.paymentUrl
			} else {
				setState((prev) => ({
					...prev,
					error: result.error || "Failed to process donation",
					submitting: false,
				}))
			}
		} catch (error) {
			setState((prev) => ({
				...prev,
				error: "Network error. Please try again.",
				submitting: false,
			}))
		}
	}

	if (state.loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4" />
					<p className="text-gray-600">Loading campaign...</p>
				</div>
			</div>
		)
	}

	if (state.error || !state.campaign) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
				<Card className="w-full max-w-md">
					<CardHeader className="text-center">
						<AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-2" />
						<CardTitle className="text-red-600">Campaign Not Found</CardTitle>
						<CardDescription>
							{state.error ||
								"The campaign you're looking for doesn't exist or is no longer active."}
						</CardDescription>
					</CardHeader>
					<CardContent>
						<Button
							onClick={() => router.navigate({ to: "/" })}
							className="w-full"
						>
							Go Back Home
						</Button>
					</CardContent>
				</Card>
			</div>
		)
	}

	const quickAmounts = [10, 25, 50, 100, 200, 500]

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="container mx-auto max-w-2xl px-4">
				{/* Campaign Header */}
				<Card className="mb-6">
					<CardHeader>
						<div className="flex items-center gap-3 mb-2">
							<Heart className="h-6 w-6 text-red-500" />
							<CardTitle className="text-2xl font-bold text-gray-900">
								{state.campaign.name}
							</CardTitle>
						</div>
						{state.campaign.description && (
							<CardDescription className="text-base leading-relaxed">
								{state.campaign.description}
							</CardDescription>
						)}
					</CardHeader>
				</Card>

				{/* Donation Form */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<DollarSign className="h-5 w-5" />
							Make a Donation
						</CardTitle>
						<CardDescription>
							Your contribution helps support this cause. Thank you for your
							generosity!
						</CardDescription>
					</CardHeader>
					<CardContent>
						{state.error && (
							<Alert variant="destructive" className="mb-6">
								<AlertTriangle className="h-4 w-4" />
								<AlertDescription>{state.error}</AlertDescription>
							</Alert>
						)}

						<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
							{/* Quick Amount Selection */}
							<div>
								<Label className="text-base font-medium">
									Quick Select Amount (RM)
								</Label>
								<div className="grid grid-cols-3 gap-2 mt-2">
									{quickAmounts.map((amount) => (
										<Button
											key={amount}
											type="button"
											variant="outline"
											className="h-12"
											onClick={() => {
												const input = document.getElementById(
													"amount",
												) as HTMLInputElement
												if (input) input.value = amount.toString()
											}}
										>
											RM {amount}
										</Button>
									))}
								</div>
							</div>

							{/* Custom Amount */}
							<div>
								<Label htmlFor="amount" className="text-base font-medium">
									Donation Amount (RM) *
								</Label>
								<Input
									id="amount"
									type="text"
									placeholder="Enter amount (e.g., 50.00)"
									className="mt-1 h-12 text-lg"
									{...register("amount")}
								/>
								{errors.amount && (
									<p className="text-red-600 text-sm mt-1">
										{errors.amount.message}
									</p>
								)}
							</div>

							{/* Donor Information */}
							<div className="space-y-4">
								<h3 className="font-medium text-gray-900">Your Information</h3>

								<div>
									<Label htmlFor="donorName" className="text-base font-medium">
										Full Name *
									</Label>
									<Input
										id="donorName"
										type="text"
										placeholder="Enter your full name"
										className="mt-1 h-12"
										{...register("donorName")}
									/>
									{errors.donorName && (
										<p className="text-red-600 text-sm mt-1">
											{errors.donorName.message}
										</p>
									)}
								</div>

								<div>
									<Label htmlFor="donorEmail" className="text-base font-medium">
										Email Address *
									</Label>
									<Input
										id="donorEmail"
										type="email"
										placeholder="Enter your email address"
										className="mt-1 h-12"
										{...register("donorEmail")}
									/>
									{errors.donorEmail && (
										<p className="text-red-600 text-sm mt-1">
											{errors.donorEmail.message}
										</p>
									)}
								</div>

								<div>
									<Label htmlFor="message" className="text-base font-medium">
										Message (Optional)
									</Label>
									<Textarea
										id="message"
										placeholder="Leave a message of support..."
										className="mt-1 min-h-[100px]"
										{...register("message")}
									/>
								</div>
							</div>

							{/* Submit Button */}
							<Button
								type="submit"
								disabled={state.submitting}
								className="w-full h-14 text-lg font-semibold bg-blue-600 hover:bg-blue-700"
							>
								{state.submitting ? (
									<span className="flex items-center gap-2">
										<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
										Processing...
									</span>
								) : (
									<span className="flex items-center gap-2">
										<Heart className="h-5 w-5" />
										Donate Now
									</span>
								)}
							</Button>
						</form>

						{/* Security Notice */}
						<div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
							<p className="text-sm text-green-800">
								🔒 Your payment is processed securely through our payment
								gateway. We never store your payment information.
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	)
}
