import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@donorcare/ui"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { CheckCircle, Heart } from "lucide-react"

export const Route = createFileRoute("/donation/success")({
  component: DonationSuccessPage,
})

function DonationSuccessPage() {
  const navigate = useNavigate()
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto max-w-2xl px-4">
        <Card className="mb-6">
          <CardHeader className="text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-2xl font-bold text-green-600">
              Donation Successful!
            </CardTitle>
            <CardDescription>
              Thank you for your generous contribution. Your support means the world to us.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="bg-green-50 p-6 rounded-lg border border-green-200 mb-6">
              <h3 className="font-medium text-green-800 mb-2 flex items-center justify-center gap-2">
                <Heart className="h-5 w-5" />
                Transaction Details
              </h3>
              <p className="text-green-700">
                Your donation has been processed successfully. A confirmation email has been sent to your registered email address.
              </p>
            </div>
            
            <div className="space-y-4">
              <p className="text-gray-600">
                Your generosity will help us continue our mission and make a positive impact in the community.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                <Button 
                  onClick={() => navigate({ to: "/" })}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Back to Home
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate({ to: "/payment-test" })}
                >
                  Make Another Donation
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="text-center text-sm text-gray-500">
          <p>
            Need help? Contact our support <NAME_EMAIL>
          </p>
        </div>
      </div>
    </div>
  )
}