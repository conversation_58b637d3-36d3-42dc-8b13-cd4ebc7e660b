/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createServerRootRoute } from '@tanstack/react-start/server'

import { Route as rootRouteImport } from './routes/__root'
import { Route as PaymentTestRouteImport } from './routes/payment-test'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DonationSuccessRouteImport } from './routes/donation/success'
import { Route as DonationFailedRouteImport } from './routes/donation/failed'
import { Route as DonationSlugRouteImport } from './routes/donation/$slug'
import { Route as DemoTrpcTodoRouteImport } from './routes/demo.trpc-todo'
import { Route as DemoTanstackQueryRouteImport } from './routes/demo.tanstack-query'
import { Route as DemoStartServerFuncsRouteImport } from './routes/demo.start.server-funcs'
import { Route as DemoStartApiRequestRouteImport } from './routes/demo.start.api-request'
import { ServerRoute as ApiDemoTqTodosServerRouteImport } from './routes/api.demo-tq-todos'
import { ServerRoute as ApiDemoNamesServerRouteImport } from './routes/api.demo-names'
import { ServerRoute as ApiTrpcSplatServerRouteImport } from './routes/api.trpc.$'

const rootServerRouteImport = createServerRootRoute()

const PaymentTestRoute = PaymentTestRouteImport.update({
  id: '/payment-test',
  path: '/payment-test',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DonationSuccessRoute = DonationSuccessRouteImport.update({
  id: '/donation/success',
  path: '/donation/success',
  getParentRoute: () => rootRouteImport,
} as any)
const DonationFailedRoute = DonationFailedRouteImport.update({
  id: '/donation/failed',
  path: '/donation/failed',
  getParentRoute: () => rootRouteImport,
} as any)
const DonationSlugRoute = DonationSlugRouteImport.update({
  id: '/donation/$slug',
  path: '/donation/$slug',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoTrpcTodoRoute = DemoTrpcTodoRouteImport.update({
  id: '/demo/trpc-todo',
  path: '/demo/trpc-todo',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoTanstackQueryRoute = DemoTanstackQueryRouteImport.update({
  id: '/demo/tanstack-query',
  path: '/demo/tanstack-query',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoStartServerFuncsRoute = DemoStartServerFuncsRouteImport.update({
  id: '/demo/start/server-funcs',
  path: '/demo/start/server-funcs',
  getParentRoute: () => rootRouteImport,
} as any)
const DemoStartApiRequestRoute = DemoStartApiRequestRouteImport.update({
  id: '/demo/start/api-request',
  path: '/demo/start/api-request',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiDemoTqTodosServerRoute = ApiDemoTqTodosServerRouteImport.update({
  id: '/api/demo-tq-todos',
  path: '/api/demo-tq-todos',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiDemoNamesServerRoute = ApiDemoNamesServerRouteImport.update({
  id: '/api/demo-names',
  path: '/api/demo-names',
  getParentRoute: () => rootServerRouteImport,
} as any)
const ApiTrpcSplatServerRoute = ApiTrpcSplatServerRouteImport.update({
  id: '/api/trpc/$',
  path: '/api/trpc/$',
  getParentRoute: () => rootServerRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/payment-test': typeof PaymentTestRoute
  '/demo/tanstack-query': typeof DemoTanstackQueryRoute
  '/demo/trpc-todo': typeof DemoTrpcTodoRoute
  '/donation/$slug': typeof DonationSlugRoute
  '/donation/failed': typeof DonationFailedRoute
  '/donation/success': typeof DonationSuccessRoute
  '/demo/start/api-request': typeof DemoStartApiRequestRoute
  '/demo/start/server-funcs': typeof DemoStartServerFuncsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/payment-test': typeof PaymentTestRoute
  '/demo/tanstack-query': typeof DemoTanstackQueryRoute
  '/demo/trpc-todo': typeof DemoTrpcTodoRoute
  '/donation/$slug': typeof DonationSlugRoute
  '/donation/failed': typeof DonationFailedRoute
  '/donation/success': typeof DonationSuccessRoute
  '/demo/start/api-request': typeof DemoStartApiRequestRoute
  '/demo/start/server-funcs': typeof DemoStartServerFuncsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/payment-test': typeof PaymentTestRoute
  '/demo/tanstack-query': typeof DemoTanstackQueryRoute
  '/demo/trpc-todo': typeof DemoTrpcTodoRoute
  '/donation/$slug': typeof DonationSlugRoute
  '/donation/failed': typeof DonationFailedRoute
  '/donation/success': typeof DonationSuccessRoute
  '/demo/start/api-request': typeof DemoStartApiRequestRoute
  '/demo/start/server-funcs': typeof DemoStartServerFuncsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/payment-test'
    | '/demo/tanstack-query'
    | '/demo/trpc-todo'
    | '/donation/$slug'
    | '/donation/failed'
    | '/donation/success'
    | '/demo/start/api-request'
    | '/demo/start/server-funcs'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/payment-test'
    | '/demo/tanstack-query'
    | '/demo/trpc-todo'
    | '/donation/$slug'
    | '/donation/failed'
    | '/donation/success'
    | '/demo/start/api-request'
    | '/demo/start/server-funcs'
  id:
    | '__root__'
    | '/'
    | '/payment-test'
    | '/demo/tanstack-query'
    | '/demo/trpc-todo'
    | '/donation/$slug'
    | '/donation/failed'
    | '/donation/success'
    | '/demo/start/api-request'
    | '/demo/start/server-funcs'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  PaymentTestRoute: typeof PaymentTestRoute
  DemoTanstackQueryRoute: typeof DemoTanstackQueryRoute
  DemoTrpcTodoRoute: typeof DemoTrpcTodoRoute
  DonationSlugRoute: typeof DonationSlugRoute
  DonationFailedRoute: typeof DonationFailedRoute
  DonationSuccessRoute: typeof DonationSuccessRoute
  DemoStartApiRequestRoute: typeof DemoStartApiRequestRoute
  DemoStartServerFuncsRoute: typeof DemoStartServerFuncsRoute
}
export interface FileServerRoutesByFullPath {
  '/api/demo-names': typeof ApiDemoNamesServerRoute
  '/api/demo-tq-todos': typeof ApiDemoTqTodosServerRoute
  '/api/trpc/$': typeof ApiTrpcSplatServerRoute
}
export interface FileServerRoutesByTo {
  '/api/demo-names': typeof ApiDemoNamesServerRoute
  '/api/demo-tq-todos': typeof ApiDemoTqTodosServerRoute
  '/api/trpc/$': typeof ApiTrpcSplatServerRoute
}
export interface FileServerRoutesById {
  __root__: typeof rootServerRouteImport
  '/api/demo-names': typeof ApiDemoNamesServerRoute
  '/api/demo-tq-todos': typeof ApiDemoTqTodosServerRoute
  '/api/trpc/$': typeof ApiTrpcSplatServerRoute
}
export interface FileServerRouteTypes {
  fileServerRoutesByFullPath: FileServerRoutesByFullPath
  fullPaths: '/api/demo-names' | '/api/demo-tq-todos' | '/api/trpc/$'
  fileServerRoutesByTo: FileServerRoutesByTo
  to: '/api/demo-names' | '/api/demo-tq-todos' | '/api/trpc/$'
  id: '__root__' | '/api/demo-names' | '/api/demo-tq-todos' | '/api/trpc/$'
  fileServerRoutesById: FileServerRoutesById
}
export interface RootServerRouteChildren {
  ApiDemoNamesServerRoute: typeof ApiDemoNamesServerRoute
  ApiDemoTqTodosServerRoute: typeof ApiDemoTqTodosServerRoute
  ApiTrpcSplatServerRoute: typeof ApiTrpcSplatServerRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/payment-test': {
      id: '/payment-test'
      path: '/payment-test'
      fullPath: '/payment-test'
      preLoaderRoute: typeof PaymentTestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/donation/success': {
      id: '/donation/success'
      path: '/donation/success'
      fullPath: '/donation/success'
      preLoaderRoute: typeof DonationSuccessRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/donation/failed': {
      id: '/donation/failed'
      path: '/donation/failed'
      fullPath: '/donation/failed'
      preLoaderRoute: typeof DonationFailedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/donation/$slug': {
      id: '/donation/$slug'
      path: '/donation/$slug'
      fullPath: '/donation/$slug'
      preLoaderRoute: typeof DonationSlugRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/trpc-todo': {
      id: '/demo/trpc-todo'
      path: '/demo/trpc-todo'
      fullPath: '/demo/trpc-todo'
      preLoaderRoute: typeof DemoTrpcTodoRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/tanstack-query': {
      id: '/demo/tanstack-query'
      path: '/demo/tanstack-query'
      fullPath: '/demo/tanstack-query'
      preLoaderRoute: typeof DemoTanstackQueryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/start/server-funcs': {
      id: '/demo/start/server-funcs'
      path: '/demo/start/server-funcs'
      fullPath: '/demo/start/server-funcs'
      preLoaderRoute: typeof DemoStartServerFuncsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/demo/start/api-request': {
      id: '/demo/start/api-request'
      path: '/demo/start/api-request'
      fullPath: '/demo/start/api-request'
      preLoaderRoute: typeof DemoStartApiRequestRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}
declare module '@tanstack/react-start/server' {
  interface ServerFileRoutesByPath {
    '/api/demo-tq-todos': {
      id: '/api/demo-tq-todos'
      path: '/api/demo-tq-todos'
      fullPath: '/api/demo-tq-todos'
      preLoaderRoute: typeof ApiDemoTqTodosServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/demo-names': {
      id: '/api/demo-names'
      path: '/api/demo-names'
      fullPath: '/api/demo-names'
      preLoaderRoute: typeof ApiDemoNamesServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
    '/api/trpc/$': {
      id: '/api/trpc/$'
      path: '/api/trpc/$'
      fullPath: '/api/trpc/$'
      preLoaderRoute: typeof ApiTrpcSplatServerRouteImport
      parentRoute: typeof rootServerRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  PaymentTestRoute: PaymentTestRoute,
  DemoTanstackQueryRoute: DemoTanstackQueryRoute,
  DemoTrpcTodoRoute: DemoTrpcTodoRoute,
  DonationSlugRoute: DonationSlugRoute,
  DonationFailedRoute: DonationFailedRoute,
  DonationSuccessRoute: DonationSuccessRoute,
  DemoStartApiRequestRoute: DemoStartApiRequestRoute,
  DemoStartServerFuncsRoute: DemoStartServerFuncsRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
const rootServerRouteChildren: RootServerRouteChildren = {
  ApiDemoNamesServerRoute: ApiDemoNamesServerRoute,
  ApiDemoTqTodosServerRoute: ApiDemoTqTodosServerRoute,
  ApiTrpcSplatServerRoute: ApiTrpcSplatServerRoute,
}
export const serverRouteTree = rootServerRouteImport
  ._addFileChildren(rootServerRouteChildren)
  ._addFileTypes<FileServerRouteTypes>()
