import { QueryClient } from "@tanstack/react-query"
import { createTRPC<PERSON>lient, httpBatchStreamLink } from "@trpc/client"
import { createTRPCOptionsProxy } from "@trpc/tanstack-react-query"
import superjson from "superjson"

import type { TRPCRouter } from "@/integrations/trpc/router"

import { TRPCProvider } from "@/integrations/trpc/react"

function getUrl() {
	const base = (() => {
		if (typeof window !== "undefined") return ""
		return `http://localhost:${process.env.PORT ?? 3000}`
	})()
	return `${base}/api/trpc`
}

export const trpcClient = createTRPCClient<TRPCRouter>({
	links: [
		httpBatchStreamLink({
			transformer: superjson,
			url: getUrl(),
		}),
	],
})

export function getContext() {
	const queryClient = new QueryClient({
		defaultOptions: {
			dehydrate: { serializeData: superjson.serialize },
			hydrate: { deserializeData: superjson.deserialize },
		},
	})

	const serverHelpers = createTRPCOptionsProxy({
		client: trpcClient,
		queryClient: queryClient,
	})
	return {
		queryClient,
		trpc: serverHelpers,
	}
}

export function Provider({
	children,
	queryClient,
}: {
	children: React.ReactNode
	queryClient: QueryClient
}) {
	return (
		<TRPCProvider trpcClient={trpcClient} queryClient={queryClient}>
			{children}
		</TRPCProvider>
	)
}
