{"name": "@donorcare/frontend", "private": true, "type": "module", "scripts": {"dev": "bunx --bun vite dev --port 3002", "start": "bun run .output/server/index.mjs", "build": "bunx --bun vite build", "serve": "bunx --bun vite preview", "test": "vitest run", "format": "cd ../.. && biome format --write packages/frontend/src", "lint": "cd ../.. && biome lint packages/frontend/src", "check": "cd ../.. && biome check --write packages/frontend/src"}, "dependencies": {"@donorcare/ui": "workspace:*", "@t3-oss/env-core": "^0.12.0", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-devtools": "^0.2.2", "@tanstack/react-query": "^5.66.5", "@tanstack/react-query-devtools": "^5.84.2", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.131.5", "@tanstack/react-router-ssr-query": "^1.131.7", "@tanstack/react-start": "^1.131.7", "@tanstack/router-plugin": "^1.121.2", "@trpc/client": "^11.4.3", "@trpc/server": "^11.4.3", "@trpc/tanstack-react-query": "^11.4.3", "react": "^19.0.0", "react-dom": "^19.0.0", "superjson": "^2.2.2", "tailwindcss": "^4.0.6", "tw-animate-css": "^1.3.6", "vite-tsconfig-paths": "^5.1.4"}, "devDependencies": {}}