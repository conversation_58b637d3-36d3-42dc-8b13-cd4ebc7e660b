{
	"include": ["**/*.ts", "**/*.tsx"],
	"compilerOptions": {
		"target": "ES2022",
		"jsx": "react-jsx",
		"module": "ESNext",
		"lib": ["ES2022", "DOM", "DOM.Iterable"],
		"types": ["vite/client"],

		/* Module Resolution */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": false,
		"verbatimModuleSyntax": false,
		"noEmit": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,

		/* Linting */
		"skipLibCheck": true,
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedSideEffectImports": true,
		"baseUrl": ".",
		"paths": {
			"@/*": ["./src/*"],
			"@donorcare/ui/*": ["../ui/src/*"],
			"@donorcare/ui": ["../ui/src"],
			"@donorcare/backend/schemas": ["../backend/src/schemas.ts"],
			"@donorcare/backend/auth": ["../backend/lib/auth.ts"],
		}
	}
}
