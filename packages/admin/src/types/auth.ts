import type { User } from "@donorcare/backend/schemas"

// Authentication types for better-auth integration
export interface AuthError {
	code: string
	message: string
	details?: Record<string, any>
}


// TODO: use backend type
// Form types
export interface LoginFormData {
	email: string
	password: string
}

export interface SignUpFormData {
	name: string
	email: string
	password: string
	confirmPassword: string
}

// Auth state types
export interface AuthState {
	user: User | null
	isAuthenticated: boolean
	isLoading: boolean
	error: AuthError | null
	sessionChecked: boolean
}

// Better-auth client response types
export interface AuthResponse<T = any> {
	data?: T
	error?: {
		message: string
		code?: string
	}
}

// Route protection types
export interface RouterContext {
	auth: AuthState
}

export interface ProtectedRouteProps {
	children: React.ReactNode
	fallback?: React.ReactNode
	redirectTo?: string
}
