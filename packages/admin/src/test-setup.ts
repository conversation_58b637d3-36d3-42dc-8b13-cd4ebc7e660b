import * as matchers from "@testing-library/jest-dom/matchers"
import { cleanup } from "@testing-library/react"
import { afterEach, expect } from "vitest"

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers)

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
	constructor(callback: ResizeObserverCallback) {
		// Mock implementation
	}
	observe() {
		// Mock implementation
	}
	unobserve() {
		// Mock implementation
	}
	disconnect() {
		// Mock implementation
	}
}

// Cleanup after each test case
afterEach(() => {
	cleanup()
})
