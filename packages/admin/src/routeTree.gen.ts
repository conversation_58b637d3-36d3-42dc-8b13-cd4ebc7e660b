/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated/settings'
import { Route as AuthenticatedProfileRouteImport } from './routes/_authenticated/profile'
import { Route as AuthenticatedDashboardRouteImport } from './routes/_authenticated/dashboard'
import { Route as AuthenticatedOrganizersIndexRouteImport } from './routes/_authenticated/organizers/index'
import { Route as AuthenticatedDonorsIndexRouteImport } from './routes/_authenticated/donors/index'
import { Route as AuthenticatedDonationsIndexRouteImport } from './routes/_authenticated/donations/index'
import { Route as AuthenticatedCampaignsIndexRouteImport } from './routes/_authenticated/campaigns/index'
import { Route as AuthenticatedOrganizersCreateRouteImport } from './routes/_authenticated/organizers/create'
import { Route as AuthenticatedDonorsCreateRouteImport } from './routes/_authenticated/donors/create'
import { Route as AuthenticatedCampaignsCreateRouteImport } from './routes/_authenticated/campaigns/create'
import { Route as AuthenticatedOrganizersOrganizerIdIndexRouteImport } from './routes/_authenticated/organizers/$organizerId/index'
import { Route as AuthenticatedDonorsDonorIdIndexRouteImport } from './routes/_authenticated/donors/$donorId/index'
import { Route as AuthenticatedDonationsDonationIdIndexRouteImport } from './routes/_authenticated/donations/$donationId/index'
import { Route as AuthenticatedCampaignsCampaignIdIndexRouteImport } from './routes/_authenticated/campaigns/$campaignId/index'
import { Route as AuthenticatedDonorsDonorIdEditRouteImport } from './routes/_authenticated/donors/$donorId/edit'
import { Route as AuthenticatedDonationsDonationIdEditRouteImport } from './routes/_authenticated/donations/$donationId/edit'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedSettingsRoute = AuthenticatedSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedProfileRoute = AuthenticatedProfileRouteImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedDashboardRoute = AuthenticatedDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedOrganizersIndexRoute =
  AuthenticatedOrganizersIndexRouteImport.update({
    id: '/organizers/',
    path: '/organizers/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonorsIndexRoute =
  AuthenticatedDonorsIndexRouteImport.update({
    id: '/donors/',
    path: '/donors/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonationsIndexRoute =
  AuthenticatedDonationsIndexRouteImport.update({
    id: '/donations/',
    path: '/donations/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedCampaignsIndexRoute =
  AuthenticatedCampaignsIndexRouteImport.update({
    id: '/campaigns/',
    path: '/campaigns/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedOrganizersCreateRoute =
  AuthenticatedOrganizersCreateRouteImport.update({
    id: '/organizers/create',
    path: '/organizers/create',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonorsCreateRoute =
  AuthenticatedDonorsCreateRouteImport.update({
    id: '/donors/create',
    path: '/donors/create',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedCampaignsCreateRoute =
  AuthenticatedCampaignsCreateRouteImport.update({
    id: '/campaigns/create',
    path: '/campaigns/create',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedOrganizersOrganizerIdIndexRoute =
  AuthenticatedOrganizersOrganizerIdIndexRouteImport.update({
    id: '/organizers/$organizerId/',
    path: '/organizers/$organizerId/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonorsDonorIdIndexRoute =
  AuthenticatedDonorsDonorIdIndexRouteImport.update({
    id: '/donors/$donorId/',
    path: '/donors/$donorId/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonationsDonationIdIndexRoute =
  AuthenticatedDonationsDonationIdIndexRouteImport.update({
    id: '/donations/$donationId/',
    path: '/donations/$donationId/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedCampaignsCampaignIdIndexRoute =
  AuthenticatedCampaignsCampaignIdIndexRouteImport.update({
    id: '/campaigns/$campaignId/',
    path: '/campaigns/$campaignId/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonorsDonorIdEditRoute =
  AuthenticatedDonorsDonorIdEditRouteImport.update({
    id: '/donors/$donorId/edit',
    path: '/donors/$donorId/edit',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedDonationsDonationIdEditRoute =
  AuthenticatedDonationsDonationIdEditRouteImport.update({
    id: '/donations/$donationId/edit',
    path: '/donations/$donationId/edit',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/profile': typeof AuthenticatedProfileRoute
  '/settings': typeof AuthenticatedSettingsRoute
  '/campaigns/create': typeof AuthenticatedCampaignsCreateRoute
  '/donors/create': typeof AuthenticatedDonorsCreateRoute
  '/organizers/create': typeof AuthenticatedOrganizersCreateRoute
  '/campaigns': typeof AuthenticatedCampaignsIndexRoute
  '/donations': typeof AuthenticatedDonationsIndexRoute
  '/donors': typeof AuthenticatedDonorsIndexRoute
  '/organizers': typeof AuthenticatedOrganizersIndexRoute
  '/donations/$donationId/edit': typeof AuthenticatedDonationsDonationIdEditRoute
  '/donors/$donorId/edit': typeof AuthenticatedDonorsDonorIdEditRoute
  '/campaigns/$campaignId': typeof AuthenticatedCampaignsCampaignIdIndexRoute
  '/donations/$donationId': typeof AuthenticatedDonationsDonationIdIndexRoute
  '/donors/$donorId': typeof AuthenticatedDonorsDonorIdIndexRoute
  '/organizers/$organizerId': typeof AuthenticatedOrganizersOrganizerIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/profile': typeof AuthenticatedProfileRoute
  '/settings': typeof AuthenticatedSettingsRoute
  '/campaigns/create': typeof AuthenticatedCampaignsCreateRoute
  '/donors/create': typeof AuthenticatedDonorsCreateRoute
  '/organizers/create': typeof AuthenticatedOrganizersCreateRoute
  '/campaigns': typeof AuthenticatedCampaignsIndexRoute
  '/donations': typeof AuthenticatedDonationsIndexRoute
  '/donors': typeof AuthenticatedDonorsIndexRoute
  '/organizers': typeof AuthenticatedOrganizersIndexRoute
  '/donations/$donationId/edit': typeof AuthenticatedDonationsDonationIdEditRoute
  '/donors/$donorId/edit': typeof AuthenticatedDonorsDonorIdEditRoute
  '/campaigns/$campaignId': typeof AuthenticatedCampaignsCampaignIdIndexRoute
  '/donations/$donationId': typeof AuthenticatedDonationsDonationIdIndexRoute
  '/donors/$donorId': typeof AuthenticatedDonorsDonorIdIndexRoute
  '/organizers/$organizerId': typeof AuthenticatedOrganizersOrganizerIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/dashboard': typeof AuthenticatedDashboardRoute
  '/_authenticated/profile': typeof AuthenticatedProfileRoute
  '/_authenticated/settings': typeof AuthenticatedSettingsRoute
  '/_authenticated/campaigns/create': typeof AuthenticatedCampaignsCreateRoute
  '/_authenticated/donors/create': typeof AuthenticatedDonorsCreateRoute
  '/_authenticated/organizers/create': typeof AuthenticatedOrganizersCreateRoute
  '/_authenticated/campaigns/': typeof AuthenticatedCampaignsIndexRoute
  '/_authenticated/donations/': typeof AuthenticatedDonationsIndexRoute
  '/_authenticated/donors/': typeof AuthenticatedDonorsIndexRoute
  '/_authenticated/organizers/': typeof AuthenticatedOrganizersIndexRoute
  '/_authenticated/donations/$donationId/edit': typeof AuthenticatedDonationsDonationIdEditRoute
  '/_authenticated/donors/$donorId/edit': typeof AuthenticatedDonorsDonorIdEditRoute
  '/_authenticated/campaigns/$campaignId/': typeof AuthenticatedCampaignsCampaignIdIndexRoute
  '/_authenticated/donations/$donationId/': typeof AuthenticatedDonationsDonationIdIndexRoute
  '/_authenticated/donors/$donorId/': typeof AuthenticatedDonorsDonorIdIndexRoute
  '/_authenticated/organizers/$organizerId/': typeof AuthenticatedOrganizersOrganizerIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/dashboard'
    | '/profile'
    | '/settings'
    | '/campaigns/create'
    | '/donors/create'
    | '/organizers/create'
    | '/campaigns'
    | '/donations'
    | '/donors'
    | '/organizers'
    | '/donations/$donationId/edit'
    | '/donors/$donorId/edit'
    | '/campaigns/$campaignId'
    | '/donations/$donationId'
    | '/donors/$donorId'
    | '/organizers/$organizerId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/dashboard'
    | '/profile'
    | '/settings'
    | '/campaigns/create'
    | '/donors/create'
    | '/organizers/create'
    | '/campaigns'
    | '/donations'
    | '/donors'
    | '/organizers'
    | '/donations/$donationId/edit'
    | '/donors/$donorId/edit'
    | '/campaigns/$campaignId'
    | '/donations/$donationId'
    | '/donors/$donorId'
    | '/organizers/$organizerId'
  id:
    | '__root__'
    | '/'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/dashboard'
    | '/_authenticated/profile'
    | '/_authenticated/settings'
    | '/_authenticated/campaigns/create'
    | '/_authenticated/donors/create'
    | '/_authenticated/organizers/create'
    | '/_authenticated/campaigns/'
    | '/_authenticated/donations/'
    | '/_authenticated/donors/'
    | '/_authenticated/organizers/'
    | '/_authenticated/donations/$donationId/edit'
    | '/_authenticated/donors/$donorId/edit'
    | '/_authenticated/campaigns/$campaignId/'
    | '/_authenticated/donations/$donationId/'
    | '/_authenticated/donors/$donorId/'
    | '/_authenticated/organizers/$organizerId/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/profile': {
      id: '/_authenticated/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof AuthenticatedProfileRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/dashboard': {
      id: '/_authenticated/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/organizers/': {
      id: '/_authenticated/organizers/'
      path: '/organizers'
      fullPath: '/organizers'
      preLoaderRoute: typeof AuthenticatedOrganizersIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donors/': {
      id: '/_authenticated/donors/'
      path: '/donors'
      fullPath: '/donors'
      preLoaderRoute: typeof AuthenticatedDonorsIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donations/': {
      id: '/_authenticated/donations/'
      path: '/donations'
      fullPath: '/donations'
      preLoaderRoute: typeof AuthenticatedDonationsIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/campaigns/': {
      id: '/_authenticated/campaigns/'
      path: '/campaigns'
      fullPath: '/campaigns'
      preLoaderRoute: typeof AuthenticatedCampaignsIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/organizers/create': {
      id: '/_authenticated/organizers/create'
      path: '/organizers/create'
      fullPath: '/organizers/create'
      preLoaderRoute: typeof AuthenticatedOrganizersCreateRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donors/create': {
      id: '/_authenticated/donors/create'
      path: '/donors/create'
      fullPath: '/donors/create'
      preLoaderRoute: typeof AuthenticatedDonorsCreateRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/campaigns/create': {
      id: '/_authenticated/campaigns/create'
      path: '/campaigns/create'
      fullPath: '/campaigns/create'
      preLoaderRoute: typeof AuthenticatedCampaignsCreateRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/organizers/$organizerId/': {
      id: '/_authenticated/organizers/$organizerId/'
      path: '/organizers/$organizerId'
      fullPath: '/organizers/$organizerId'
      preLoaderRoute: typeof AuthenticatedOrganizersOrganizerIdIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donors/$donorId/': {
      id: '/_authenticated/donors/$donorId/'
      path: '/donors/$donorId'
      fullPath: '/donors/$donorId'
      preLoaderRoute: typeof AuthenticatedDonorsDonorIdIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donations/$donationId/': {
      id: '/_authenticated/donations/$donationId/'
      path: '/donations/$donationId'
      fullPath: '/donations/$donationId'
      preLoaderRoute: typeof AuthenticatedDonationsDonationIdIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/campaigns/$campaignId/': {
      id: '/_authenticated/campaigns/$campaignId/'
      path: '/campaigns/$campaignId'
      fullPath: '/campaigns/$campaignId'
      preLoaderRoute: typeof AuthenticatedCampaignsCampaignIdIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donors/$donorId/edit': {
      id: '/_authenticated/donors/$donorId/edit'
      path: '/donors/$donorId/edit'
      fullPath: '/donors/$donorId/edit'
      preLoaderRoute: typeof AuthenticatedDonorsDonorIdEditRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/donations/$donationId/edit': {
      id: '/_authenticated/donations/$donationId/edit'
      path: '/donations/$donationId/edit'
      fullPath: '/donations/$donationId/edit'
      preLoaderRoute: typeof AuthenticatedDonationsDonationIdEditRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedDashboardRoute: typeof AuthenticatedDashboardRoute
  AuthenticatedProfileRoute: typeof AuthenticatedProfileRoute
  AuthenticatedSettingsRoute: typeof AuthenticatedSettingsRoute
  AuthenticatedCampaignsCreateRoute: typeof AuthenticatedCampaignsCreateRoute
  AuthenticatedDonorsCreateRoute: typeof AuthenticatedDonorsCreateRoute
  AuthenticatedOrganizersCreateRoute: typeof AuthenticatedOrganizersCreateRoute
  AuthenticatedCampaignsIndexRoute: typeof AuthenticatedCampaignsIndexRoute
  AuthenticatedDonationsIndexRoute: typeof AuthenticatedDonationsIndexRoute
  AuthenticatedDonorsIndexRoute: typeof AuthenticatedDonorsIndexRoute
  AuthenticatedOrganizersIndexRoute: typeof AuthenticatedOrganizersIndexRoute
  AuthenticatedDonationsDonationIdEditRoute: typeof AuthenticatedDonationsDonationIdEditRoute
  AuthenticatedDonorsDonorIdEditRoute: typeof AuthenticatedDonorsDonorIdEditRoute
  AuthenticatedCampaignsCampaignIdIndexRoute: typeof AuthenticatedCampaignsCampaignIdIndexRoute
  AuthenticatedDonationsDonationIdIndexRoute: typeof AuthenticatedDonationsDonationIdIndexRoute
  AuthenticatedDonorsDonorIdIndexRoute: typeof AuthenticatedDonorsDonorIdIndexRoute
  AuthenticatedOrganizersOrganizerIdIndexRoute: typeof AuthenticatedOrganizersOrganizerIdIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedDashboardRoute: AuthenticatedDashboardRoute,
  AuthenticatedProfileRoute: AuthenticatedProfileRoute,
  AuthenticatedSettingsRoute: AuthenticatedSettingsRoute,
  AuthenticatedCampaignsCreateRoute: AuthenticatedCampaignsCreateRoute,
  AuthenticatedDonorsCreateRoute: AuthenticatedDonorsCreateRoute,
  AuthenticatedOrganizersCreateRoute: AuthenticatedOrganizersCreateRoute,
  AuthenticatedCampaignsIndexRoute: AuthenticatedCampaignsIndexRoute,
  AuthenticatedDonationsIndexRoute: AuthenticatedDonationsIndexRoute,
  AuthenticatedDonorsIndexRoute: AuthenticatedDonorsIndexRoute,
  AuthenticatedOrganizersIndexRoute: AuthenticatedOrganizersIndexRoute,
  AuthenticatedDonationsDonationIdEditRoute:
    AuthenticatedDonationsDonationIdEditRoute,
  AuthenticatedDonorsDonorIdEditRoute: AuthenticatedDonorsDonorIdEditRoute,
  AuthenticatedCampaignsCampaignIdIndexRoute:
    AuthenticatedCampaignsCampaignIdIndexRoute,
  AuthenticatedDonationsDonationIdIndexRoute:
    AuthenticatedDonationsDonationIdIndexRoute,
  AuthenticatedDonorsDonorIdIndexRoute: AuthenticatedDonorsDonorIdIndexRoute,
  AuthenticatedOrganizersOrganizerIdIndexRoute:
    AuthenticatedOrganizersOrganizerIdIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
