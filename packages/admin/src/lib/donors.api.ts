import type {
	assignTagsDto,
	createDonor<PERSON>to,
	createNoteDto,
	createTagDto,
	donorFiltersDto,
	updateDonorDto,
	updateNoteDto,
	updateTagDto,
} from "@donorcare/backend/schemas"
import type { Backend } from "@donorcare/backend/src"
import { treaty } from "@elysiajs/eden"
import type { Static } from "elysia"

// Create the treaty client with proper base URL and authentication
const client = treaty<Backend>("http://localhost:3000", {
	fetch: {
		credentials: "include", // Include cookies for authentication
	},
})

// Donor API functions with full type safety from backend
// GET /api/donors - Get organizer's donors with filtering and pagination
export const getDonors = (filters?: Static<typeof donorFiltersDto>) => {
	return client.api.donors.get({ query: filters || {} })
}

// GET /api/donors/:id - Get donor by ID (authenticated)
export const getDonor = (id: string) => client.api.donors({ id }).get()

// POST /api/donors - Create new donor
export const createDonor = (data: Static<typeof createDonorDto>) =>
	client.api.donors.post(data)

// PUT /api/donors/:id - Update existing donor
export const updateDonor = (id: string, data: Static<typeof updateDonorDto>) =>
	client.api.donors({ id }).put(data)

// DELETE /api/donors/:id - Delete donor
export const deleteDonor = (id: string) => client.api.donors({ id }).delete()

// GET /api/donors/export - Export donors as CSV
export const exportDonors = (filters?: Static<typeof donorFiltersDto>) => {
	return client.api.donors.export.get({ query: filters || {} })
}

// Tag Management API functions
// GET /api/donors/tags - Get organizer's tags
export const getTags = () => client.api.donors.tags.get()

// POST /api/donors/tags - Create new tag
export const createTag = (data: Static<typeof createTagDto>) =>
	client.api.donors.tags.post(data)

// PUT /api/donors/tags/:tagId - Update existing tag
export const updateTag = (tagId: string, data: Static<typeof updateTagDto>) =>
	client.api.donors.tags({ tagId }).put(data)

// DELETE /api/donors/tags/:tagId - Delete tag
export const deleteTag = (tagId: string) =>
	client.api.donors.tags({ tagId }).delete()

// POST /api/donors/:id/tags - Assign tags to donor
export const assignTags = (
	donorId: string,
	data: Static<typeof assignTagsDto>,
) => client.api.donors({ id: donorId }).tags.post(data)

// Note Management API functions
// GET /api/donors/:id/notes - Get donor's notes
export const getDonorNotes = (donorId: string) =>
	client.api.donors({ id: donorId }).notes.get()

// POST /api/donors/:id/notes - Create new note for donor
export const createDonorNote = (
	donorId: string,
	data: Static<typeof createNoteDto>,
) => client.api.donors({ id: donorId }).notes.post(data)

// PUT /api/donors/:id/notes/:noteId - Update existing note
export const updateDonorNote = (
	donorId: string,
	noteId: string,
	data: Static<typeof updateNoteDto>,
) => client.api.donors({ id: donorId }).notes({ noteId }).put(data)

// DELETE /api/donors/:id/notes/:noteId - Delete note
export const deleteDonorNote = (donorId: string, noteId: string) =>
	client.api.donors({ id: donorId }).notes({ noteId }).delete()

// Export the client for advanced usage
export { client }
export { client as donorsClient }

// Helper type exports for consumers
export type DonorsClient = typeof client
export type BackendType = Backend

// Re-export commonly used types from the schema for convenience
export type {
	AssignTags,
	AssignTagsResponse,
	CreateDonor,
	CreateDonorResponse,
	CreateNote,
	CreateNoteResponse,
	CreateTag,
	CreateTagResponse,
	DeleteDonorResponse,
	DeleteNoteResponse,
	DeleteTagResponse,
	DonorFilters,
	DonorResponse,
	DonorsListResponse,
	DonorWithStats,
	ExportDonorsResponse,
	Note,
	NotesListResponse,
	Tag,
	TagsListResponse,
	UpdateDonor,
	UpdateDonorResponse,
	UpdateNote,
	UpdateNoteResponse,
	UpdateTag,
	UpdateTagResponse,
} from "@donorcare/backend/schemas"
