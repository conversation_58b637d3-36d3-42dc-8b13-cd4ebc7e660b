import type { Backend } from "@donorcare/backend/src"
import { treaty } from "@elysiajs/eden"

// Create the treaty client with proper base URL and authentication
const client = treaty<Backend>("http://localhost:3000", {
	fetch: {
		credentials: "include", // Include cookies for authentication
	},
})

// Organizers API functions with full type safety from backend
// GET /api/organizers - Get all organizers (admin only)
export const getOrganizers = () => {
	return client.api.organizers.get()
}

// GET /api/organizers/:id - Get organizer by ID (admin only)
export const getOrganizer = (id: string) => client.api.organizers({ id }).get()

// POST /api/organizers - Create organizer (admin only)
export const createOrganizer = (data: { name: string; email: string }) => 
	client.api.organizers.post(data)

// DELETE /api/organizers/:id - Delete organizer (admin only)
export const deleteOrganizer = (id: string) =>
	client.api.organizers({ id }).delete()

// POST /api/organizers/:id/impersonate - Impersonate organizer (admin only)
export const impersonateOrganizer = (id: string) =>
	client.api.organizers({ id }).impersonate.post()

// GET /api/organizers/export - Export organizers as CSV (admin only)
export const exportOrganizers = (selectedIds?: string[]) => {
	return client.api.organizers.export.get({
		query: selectedIds ? { ids: selectedIds.join(",") } : {},
	})
}

// Export the client for advanced usage
export { client }
export { client as organizersClient }

// Helper type exports for consumers
export type OrganizersClient = typeof client
export type BackendType = Backend

// Re-export commonly used types from the schema for convenience
export type {
	CreateOrganizer,
	CreateOrganizerResponse,
	Organizer,
	OrganizerListResponse,
} from "@donorcare/backend/schemas"
