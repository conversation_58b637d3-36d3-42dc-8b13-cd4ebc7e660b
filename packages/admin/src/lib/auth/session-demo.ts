/**
 * Demo script to show session persistence functionality
 * This file demonstrates how the session persistence works
 */

import { useAuthStore } from "../auth-store"
import { sessionManager } from "./session-manager"

export class SessionPersistenceDemo {
	/**
	 * Demonstrate session initialization and restoration
	 */
	static async demonstrateSessionRestoration() {
		console.log("=== Session Persistence Demo ===")

		const store = useAuthStore.getState()

		console.log("1. Initial state:", {
			isAuthenticated: store.isAuthenticated,
			sessionChecked: store.sessionChecked,
			user: store.user ? "User present" : "No user",
		})

		console.log("2. Initializing authentication...")
		await store.initializeAuth()

		const stateAfterInit = useAuthStore.getState()
		console.log("3. State after initialization:", {
			isAuthenticated: stateAfterInit.isAuthenticated,
			sessionChecked: stateAfterInit.sessionChecked,
			user: stateAfterInit.user ? "User present" : "No user",
			sessionManagerInitialized: sessionManager.initialized,
		})

		console.log("4. Session persistence features:")
		console.log("   ✓ Session check flag persisted to localStorage")
		console.log("   ✓ User data NOT persisted for security")
		console.log("   ✓ Background session validation enabled")
		console.log("   ✓ Cross-tab synchronization ready")
		console.log("   ✓ Automatic session expiration handling")

		return stateAfterInit
	}

	/**
	 * Demonstrate session expiration handling
	 */
	static async demonstrateSessionExpiration() {
		console.log("=== Session Expiration Demo ===")

		const store = useAuthStore.getState()

		// Simulate a user being logged in
		store.setUser({
			id: "demo-user",
			name: "Demo User",
			email: "<EMAIL>",
			emailVerified: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		})

		console.log("1. User logged in:", {
			isAuthenticated: useAuthStore.getState().isAuthenticated,
			user: useAuthStore.getState().user?.name,
		})

		console.log("2. Simulating session expiration...")
		await store.handleSessionExpiration()

		const stateAfterExpiration = useAuthStore.getState()
		console.log("3. State after expiration:", {
			isAuthenticated: stateAfterExpiration.isAuthenticated,
			user: stateAfterExpiration.user,
			error: stateAfterExpiration.error?.code,
		})

		console.log("4. Session expiration features:")
		console.log("   ✓ User state cleared")
		console.log("   ✓ Authentication state reset")
		console.log("   ✓ Error message set for user feedback")
		console.log("   ✓ Background monitoring stopped")

		return stateAfterExpiration
	}

	/**
	 * Demonstrate cleanup functionality
	 */
	static demonstrateCleanup() {
		console.log("=== Session Cleanup Demo ===")

		const store = useAuthStore.getState()

		console.log("1. Before cleanup:", {
			sessionManagerInitialized: sessionManager.initialized,
		})

		store.cleanup()

		console.log("2. After cleanup:", {
			sessionManagerInitialized: sessionManager.initialized,
			authState: {
				isAuthenticated: useAuthStore.getState().isAuthenticated,
				user: useAuthStore.getState().user,
				sessionChecked: useAuthStore.getState().sessionChecked,
			},
		})

		console.log("3. Cleanup features:")
		console.log("   ✓ Session manager stopped")
		console.log("   ✓ Background validation stopped")
		console.log("   ✓ Auth state reset")
		console.log("   ✓ Ready for fresh initialization")
	}
}

// Export for use in browser console or testing
if (typeof window !== "undefined") {
	;(window as any).SessionPersistenceDemo = SessionPersistenceDemo
}
