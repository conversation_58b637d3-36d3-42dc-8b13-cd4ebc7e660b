# Authentication Library

This directory contains the enhanced authentication implementation for the DonorCARE frontend application.

## Overview

The authentication system integrates with the better-auth backend to provide secure user authentication, session management, and route protection with enhanced error handling and session validation.

## Components

- `index.ts` - Main authentication exports and utilities
- `../auth-client.ts` - Enhanced better-auth client with session validation and retry logic
- `../auth-store.ts` - Zustand store for authentication state management
- `../auth-config.ts` - Configuration constants and validation utilities
- `../../types/auth.ts` - TypeScript type definitions

## Enhanced Features

### 1. Better-Auth Client Integration
- **Enhanced Session Validation**: Automatic session validation with refresh logic
- **Retry Mechanism**: Network error retry with exponential backoff
- **Error Mapping**: Better-auth errors mapped to application-specific error codes
- **Background Validation**: Optional background session checking

### 2. Session Management
- **Automatic Refresh**: Sessions are automatically refreshed when needed
- **Concurrent Safety**: Prevents multiple simultaneous refresh attempts
- **Graceful Degradation**: Handles network failures and server errors
- **Security First**: Always clears local state on sign out, even if server fails

### 3. Error Handling
- **Categorized Errors**: Network, authentication, and validation errors
- **User-Friendly Messages**: Mapped error messages for better UX
- **Retry Logic**: Automatic retry for transient network errors
- **Logging**: Comprehensive error logging for debugging

## Usage

### Basic Setup
```typescript
import { initializeAuth, useAuthStore, authService } from '@/lib/auth'

// Initialize authentication system (call once at app start)
await initializeAuth()

// Use authentication in components
const { user, isAuthenticated, signIn, signOut } = useAuthStore()
```

### Advanced Usage
```typescript
// Manual session validation
const isValid = await authService.validateSession()

// Enhanced sign in with error handling
const result = await authService.signIn(email, password)
if (!result.success) {
  console.error('Sign in failed:', result.error?.message)
}

// Check session validity
const hasSession = await hasValidSession()

// Manual session refresh
const refreshed = await refreshUserSession()
```

### Background Session Validation
```typescript
// Start background validation (automatically called by initializeAuth)
authService.startBackgroundValidation(() => {
  // Handle session expiration
  console.log('Session expired, redirecting to login')
})

// Stop background validation (call on app unmount)
authService.stopBackgroundValidation()
```

## Configuration

### Error Codes
- `INVALID_CREDENTIALS`: Invalid email or password
- `SESSION_EXPIRED`: Session has expired
- `NETWORK_ERROR`: Network connectivity issues
- `SIGN_IN_ERROR`: General sign in failure
- `SIGN_OUT_ERROR`: Sign out failure
- `SESSION_VALIDATION_ERROR`: Session validation failure
- `REFRESH_ERROR`: Session refresh failure

### Validation Settings
```typescript
AUTH_CONFIG.VALIDATION = {
  BACKGROUND_CHECK_INTERVAL: 5 * 60 * 1000, // 5 minutes
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000 // 1 second
}
```

## Testing

The authentication system includes comprehensive tests:

- **Unit Tests**: `auth-client.test.ts` - Tests auth client service methods
- **Integration Tests**: `auth-integration.test.ts` - Tests store integration
- **Store Tests**: `auth-store.test.ts` - Tests Zustand store functionality

Run tests:
```bash
pnpm test auth-client.test.ts --run
pnpm test auth-integration.test.ts --run
pnpm test auth-store.test.ts --run
```

## Implementation Details

### AuthClientService Class
- **Singleton Pattern**: Ensures single instance across application
- **Session Validation**: `validateSession()` with automatic refresh
- **Enhanced Sign In**: `signIn()` with detailed error handling
- **Secure Sign Out**: `signOut()` always succeeds for security
- **Background Validation**: Optional periodic session checking
- **Retry Logic**: Automatic retry for network errors

### Zustand Store Integration
- **Enhanced Actions**: All actions use the AuthClientService
- **Error Handling**: Comprehensive error state management
- **Loading States**: Proper loading indicators
- **Persistence**: Session state persistence with security considerations

### Better-Auth Configuration
- **Cookie-Based Sessions**: HTTP-only cookies for security
- **CORS Support**: Proper cross-origin request handling
- **Error Mapping**: Better-auth errors mapped to application codes
- **Type Safety**: Full TypeScript integration

## Security Considerations

1. **HTTP-Only Cookies**: Sessions stored in secure HTTP-only cookies
2. **Automatic Cleanup**: Local state cleared on sign out regardless of server response
3. **Session Validation**: Regular validation prevents stale sessions
4. **Error Handling**: No sensitive information exposed in error messages
5. **Retry Logic**: Only retries network errors, not authentication failures

## Future Enhancements

- Multi-factor authentication support
- Remember me functionality
- Password reset flow
- Social authentication providers
- Advanced session management (multiple devices)
