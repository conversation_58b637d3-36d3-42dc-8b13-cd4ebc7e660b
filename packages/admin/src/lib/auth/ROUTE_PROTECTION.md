# Route Protection System

This document explains the comprehensive route protection system implemented for the DonorCARE frontend application.

## Overview

The route protection system provides multiple layers of authentication and authorization checks for TanStack Router routes. It includes:

- **Authentication Guards**: Protect routes requiring user authentication
- **Guest-Only Guards**: Redirect authenticated users away from auth pages
- **Custom Authorization**: Support for role-based and custom access control
- **Session Validation**: Strict session validation with backend verification
- **Redirect Management**: Intelligent redirect handling with destination preservation

## Quick Start

### Basic Protected Route

```typescript
import { createFileRoute } from '@tanstack/react-router'
import { authGuards } from '../lib/auth/route-guards'

export const Route = createFileRoute('/_authenticated/dashboard')({
  beforeLoad: authGuards.requireAuth,
  component: DashboardPage,
})
```

### Guest-Only Route (Login/Register)

```typescript
import { createFileRoute } from '@tanstack/react-router'
import { authGuards } from '../lib/auth/route-guards'

export const Route = createFileRoute('/login')({
  beforeLoad: authGuards.guestOnly,
  component: LoginPage,
})
```

### Strict Session Validation

```typescript
import { createFileRoute } from '@tanstack/react-router'
import { authGuards } from '../lib/auth/route-guards'

export const Route = createFileRoute('/_authenticated/profile')({
  beforeLoad: authGuards.requireValidSession, // Validates with backend
  component: ProfilePage,
})
```

## Pre-configured Guards

The system provides several pre-configured guards for common use cases:

### `authGuards.requireAuth`
- Redirects unauthenticated users to login
- Preserves intended destination for post-login redirect
- Standard authentication check

### `authGuards.requireValidSession`
- Validates session with backend on each access
- Refreshes session if needed
- Redirects if session is invalid

### `authGuards.guestOnly`
- Redirects authenticated users to dashboard
- Used for login, register, forgot password pages

### `authGuards.optionalAuth`
- Provides auth context without redirecting
- Useful for pages that work for both auth states

### `authGuards.quickAuth`
- Fast authentication check without session validation
- Good for performance-critical routes

## Custom Guards

### Basic Custom Guard

```typescript
import { createAuthGuard } from '../lib/auth/route-guards'

const customGuard = createAuthGuard({
  loginPath: '/custom-login',
  fallbackPath: '/custom-dashboard',
  preserveRedirect: false,
})

export const Route = createFileRoute('/protected')({
  beforeLoad: customGuard,
  component: ProtectedPage,
})
```

### Custom Authorization Logic

```typescript
import { createAuthGuard } from '../lib/auth/route-guards'

const adminOnlyGuard = createAuthGuard({
  customAuthCheck: async ({ auth }) => {
    // Check if user is authenticated and has admin role
    return auth.isAuthenticated && auth.user?.role === 'admin'
  },
})

export const Route = createFileRoute('/admin')({
  beforeLoad: adminOnlyGuard,
  component: AdminPage,
})
```

### Email Verification Required

```typescript
import { createAuthGuard } from '../lib/auth/route-guards'

const verifiedEmailGuard = createAuthGuard({
  customAuthCheck: async ({ auth }) => {
    return auth.isAuthenticated && auth.user?.emailVerified === true
  },
  loginPath: '/profile', // Redirect to profile to show verification status
})

export const Route = createFileRoute('/settings')({
  beforeLoad: verifiedEmailGuard,
  component: SettingsPage,
})
```

## Route Hooks

The system provides React hooks for component-level route protection and navigation:

### `useRouteAccess`

Check if the current route is accessible:

```typescript
import { useRouteAccess } from '../lib/auth/router-hooks'

function MyComponent() {
  const { canAccess, isLoading, redirectTo, reason } = useRouteAccess()

  if (isLoading) return <div>Checking access...</div>
  if (!canAccess) return <div>Access denied: {reason}</div>

  return <div>Welcome to the protected content!</div>
}
```

### `useAuthAwareNavigation`

Navigate with automatic auth handling:

```typescript
import { useAuthAwareNavigation } from '../lib/auth/router-hooks'

function NavigationComponent() {
  const { navigateTo, canNavigateTo, getNavigationUrl } = useAuthAwareNavigation()

  const handleNavigate = async () => {
    // Automatically handles auth requirements
    await navigateTo('/dashboard')
  }

  const checkAccess = async () => {
    const canAccess = await canNavigateTo('/admin')
    console.log('Can access admin:', canAccess)
  }

  return (
    <button onClick={handleNavigate}>
      Go to Dashboard
    </button>
  )
}
```

### `useAuthRedirect`

Handle authentication redirects:

```typescript
import { useAuthRedirect } from '../lib/auth/router-hooks'

function LoginForm() {
  const { redirectAfterLogin } = useAuthRedirect()

  const handleLoginSuccess = async () => {
    // Redirects to intended destination or dashboard
    await redirectAfterLogin()
  }

  return <form onSubmit={handleLoginSuccess}>...</form>
}
```

## Route Protection Middleware

For programmatic route protection outside of React components:

```typescript
import { routeProtection } from '../lib/auth/route-guards'

// Check if user can access a route
const accessResult = await routeProtection.canAccessRoute(
  '/dashboard',
  authState,
  { customAuthCheck: async ({ auth }) => auth.user?.role === 'admin' }
)

if (!accessResult.canAccess) {
  console.log('Access denied:', accessResult.reason)
  console.log('Redirect to:', accessResult.redirectTo)
}

// Get appropriate redirect URL
const redirectUrl = routeProtection.getRedirectUrl('/dashboard', authState)
```

## Route Patterns

The system automatically recognizes route patterns:

### Protected Routes
- `/_authenticated/*` - All routes under authenticated layout
- `/dashboard` - Dashboard routes
- `/bookings` - Booking management
- `/profile` - User profile
- `/settings` - User settings

### Guest-Only Routes
- `/login` - Login page
- `/register` - Registration page
- `/forgot-password` - Password reset
- `/reset-password` - Password reset confirmation

### Custom Pattern Matching

```typescript
import { isProtectedRoute, isGuestOnlyRoute } from '../lib/auth/route-guards'

const isProtected = isProtectedRoute('/my-route')
const isGuestOnly = isGuestOnlyRoute('/my-route')
```

## Configuration Options

### RouteGuardOptions

```typescript
interface RouteGuardOptions {
  redirectToLogin?: boolean        // Default: true
  loginPath?: string              // Default: '/login'
  preserveRedirect?: boolean      // Default: true
  fallbackPath?: string          // Default: '/dashboard'
  waitForSessionCheck?: boolean   // Default: true
  requiredRoles?: string[]       // For future role-based access
  customAuthCheck?: (context: RouterContext) => boolean | Promise<boolean>
}
```

## Error Handling

The route protection system handles various error scenarios:

### Session Expiration
```typescript
// Automatically redirects to login with error message
// Preserves intended destination for post-login redirect
```

### Network Errors
```typescript
// Graceful degradation with retry logic
// Fallback to cached session state when possible
```

### Authorization Failures
```typescript
// Clear error messages with appropriate redirects
// Logging for security monitoring
```

## Testing

### Unit Tests
```bash
pnpm test src/lib/auth/__tests__/route-guards.test.ts
```

### Integration Tests
```bash
pnpm test src/lib/auth/__tests__/route-protection-integration.test.tsx
```

### Demo Script
```typescript
import { runDemo } from '../lib/auth/__tests__/route-protection-demo'
await runDemo() // Demonstrates all protection scenarios
```

## Best Practices

### 1. Use Layout Routes for Bulk Protection
```typescript
// Protect multiple routes with a single guard
export const Route = createFileRoute('/_authenticated')({
  beforeLoad: authGuards.requireAuth,
  component: () => <Outlet />,
})
```

### 2. Combine Guards for Complex Requirements
```typescript
// Use custom guards for specific authorization needs
const settingsGuard = createAuthGuard({
  customAuthCheck: async ({ auth }) => {
    return auth.isAuthenticated &&
           auth.user?.emailVerified &&
           auth.user?.role !== 'readonly'
  }
})
```

### 3. Handle Loading States
```typescript
function ProtectedComponent() {
  const { canAccess, isLoading } = useRouteAccess()

  if (isLoading) return <LoadingSpinner />
  if (!canAccess) return <AccessDenied />

  return <ProtectedContent />
}
```

### 4. Provide Fallbacks
```typescript
const optionalAuthGuard = createAuthGuard({
  redirectToLogin: false, // Don't redirect, just provide auth context
})
```

### 5. Monitor Security Events
```typescript
// Guards automatically log security events
// Monitor for unusual access patterns
// Track authentication failures
```

## Migration Guide

### From Basic Route Protection

**Before:**
```typescript
beforeLoad: ({ context }) => {
  if (!context.auth.isAuthenticated) {
    throw redirect({ to: '/login' })
  }
}
```

**After:**
```typescript
beforeLoad: authGuards.requireAuth
```

### Adding Custom Authorization

**Before:**
```typescript
beforeLoad: ({ context }) => {
  if (!context.auth.isAuthenticated || !context.auth.user?.isAdmin) {
    throw redirect({ to: '/login' })
  }
}
```

**After:**
```typescript
beforeLoad: createAuthGuard({
  customAuthCheck: ({ auth }) => auth.isAuthenticated && auth.user?.isAdmin
})
```

## Troubleshooting

### Common Issues

1. **Infinite Redirect Loops**
   - Check that guest-only guards are not applied to protected routes
   - Ensure fallback paths are accessible

2. **Session Not Validated**
   - Use `requireValidSession` for strict validation
   - Check backend session endpoint configuration

3. **Custom Auth Check Failures**
   - Ensure custom auth functions handle all edge cases
   - Add proper error handling and logging

4. **Performance Issues**
   - Use `quickAuth` for performance-critical routes
   - Avoid unnecessary session validation calls

### Debug Mode

Enable debug logging:
```typescript
// Set in development environment
localStorage.setItem('auth-debug', 'true')
```

## Security Considerations

1. **Never trust client-side auth state alone**
2. **Always validate sessions on the backend**
3. **Use HTTPS in production**
4. **Implement proper CORS policies**
5. **Monitor for security events**
6. **Regular security audits**

## Performance Optimization

1. **Use appropriate guard types**
2. **Cache session validation results**
3. **Implement background session refresh**
4. **Minimize redirect chains**
5. **Optimize bundle splitting for auth code**
