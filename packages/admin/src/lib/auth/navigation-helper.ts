import type { NavigateOptions } from "@tanstack/react-router"

/**
 * Navigation helper for auth-related redirects
 * Provides a clean interface between auth store and router navigation
 */

type NavigateFunction = (options: NavigateOptions) => Promise<void> | void

interface NavigationHelper {
	navigate: NavigateFunction | null
	pendingRedirect: string | null
}

const navigationHelper: NavigationHelper = {
	navigate: null,
	pendingRedirect: null,
}

/**
 * Set the navigation function (called from components that have access to useNavigate)
 */
export function setNavigateFunction(navigateFn: NavigateFunction) {
	navigationHelper.navigate = navigateFn

	// If there's a pending redirect, execute it now
	if (navigationHelper.pendingRedirect) {
		const redirect = navigationHelper.pendingRedirect
		navigationHelper.pendingRedirect = null
		navigateFn({ to: redirect })
	}
}

/**
 * Navigate to a route (can be called from auth store)
 */
export function navigateToRoute(path: string) {
	if (navigationHelper.navigate) {
		navigationHelper.navigate({ to: path })
	} else {
		// Store the redirect for when navigation becomes available
		navigationHelper.pendingRedirect = path
		console.log(
			"Navigation function not available, storing pending redirect:",
			path,
		)
	}
}

/**
 * Clear any pending redirects
 */
export function clearPendingRedirect() {
	navigationHelper.pendingRedirect = null
}

/**
 * Get the current pending redirect
 */
export function getPendingRedirect(): string | null {
	return navigationHelper.pendingRedirect
}

/**
 * Handle post-login navigation
 */
export function handlePostLoginNavigation(redirectTo?: string) {
	const destination = redirectTo || "/dashboard"
	navigateToRoute(destination)
}

/**
 * Handle post-logout navigation
 */
export function handlePostLogoutNavigation() {
	// Clear any pending redirects on logout
	clearPendingRedirect()

	// Use window.location for logout to ensure clean navigation
	if (typeof window !== "undefined") {
		window.location.href = "/login"
	}
}
