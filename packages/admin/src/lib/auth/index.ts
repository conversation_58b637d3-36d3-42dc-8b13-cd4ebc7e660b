/**
 * Authentication utilities and exports
 *
 * This module provides a centralized interface for authentication functionality,
 * including the enhanced auth client service and store integration.
 */

// Export types
export type {
	AuthError,
	AuthResponse,
	AuthState,
	LoginFormData,
} from "../../types/auth"
// Export the enhanced auth client service
export { authClient, authService } from "../auth-client"
// Export configuration and validation utilities
export { AUTH_CONFIG, isValidEmail, isValidPassword } from "../auth-config"
// Export the auth store and hooks
export {
	useAuthError,
	useAuthLoading,
	useAuthStore,
	useIsAuthenticated,
	useUser,
} from "../auth-store"
// Export authentication context and provider
export { AuthProvider, useAuth, useAuthForRouter } from "./context"
// Export router-specific auth hooks
export {
	useIsRouterAuthenticated,
	useIsRouterAuthLoading,
	useRouterAuth,
	useRouterAuthError,
	useRouterUser,
} from "./router-hooks"

/**
 * Initialize authentication system
 * Call this once when your app starts
 */
export const initializeAuth = async () => {
	const { useAuthStore } = await import("../auth-store")
	const store = useAuthStore.getState()
	await store.initializeAuth()

	// Start background session validation
	const { authService } = await import("../auth-client")
	authService.startBackgroundValidation(() => {
		// Handle session expiration
		store.setUser(null)
		store.setError({
			code: "SESSION_EXPIRED",
			message: "Your session has expired. Please sign in again.",
		})
	})
}

/**
 * Cleanup authentication system
 * Call this when your app unmounts
 */
export const cleanupAuth = () => {
	const { authService } = require("../auth-client")
	authService.cleanup()
}

/**
 * Utility function to check if user has valid session
 */
export const hasValidSession = async (): Promise<boolean> => {
	const { authService } = await import("../auth-client")
	return await authService.validateSession()
}

/**
 * Utility function to refresh session manually
 */
export const refreshUserSession = async (): Promise<boolean> => {
	const { authService } = await import("../auth-client")
	return await authService.refreshSession()
}
