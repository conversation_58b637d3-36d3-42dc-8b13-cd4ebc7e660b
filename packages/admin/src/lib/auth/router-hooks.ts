import { useRouter } from "@tanstack/react-router"
import { useEffect, useState } from "react"
import { useAuthStore } from "../auth-store"
import type { RouteGuardOptions } from "./route-guards"
import {
	isGuestOnlyRoute,
	isProtectedRoute,
	routeProtection,
} from "./route-guards"

/**
 * Hook to check if the current route is accessible by the user
 * Useful for conditional rendering and navigation logic
 */
export function useRouteAccess(options: RouteGuardOptions = {}) {
	const router = useRouter()
	const authState = useAuthStore()
	const [accessInfo, setAccessInfo] = useState<{
		canAccess: boolean
		isLoading: boolean
		redirectTo?: string
		reason?: string
	}>({
		canAccess: true,
		isLoading: false,
	})

	useEffect(() => {
		const checkAccess = async () => {
			setAccessInfo((prev) => ({ ...prev, isLoading: true }))

			try {
				const currentPath = router.state.location.pathname
				const result = await routeProtection.canAccessRoute(
					currentPath,
					authState,
					options,
				)

				setAccessInfo({
					canAccess: result.canAccess,
					isLoading: false,
					redirectTo: result.redirectTo,
					reason: result.reason,
				})
			} catch (error) {
				console.error("Route access check failed:", error)
				setAccessInfo({
					canAccess: false,
					isLoading: false,
					reason: "Access check failed",
				})
			}
		}

		checkAccess()
	}, [
		router.state.location.pathname,
		authState.isAuthenticated,
		authState.sessionChecked,
		options,
	])

	return accessInfo
}

/**
 * Hook to get navigation helpers that respect authentication state
 */
export function useAuthAwareNavigation() {
	const router = useRouter()
	const authState = useAuthStore()

	/**
	 * Navigate to a path, handling authentication requirements automatically
	 */
	const navigateTo = async (path: string, options?: { replace?: boolean }) => {
		const redirectUrl = routeProtection.getRedirectUrl(path, authState)

		if (redirectUrl !== path) {
			// Need to redirect due to auth requirements
			await router.navigate({
				to: redirectUrl as any,
				replace: options?.replace,
			})
		} else {
			// Can navigate directly
			await router.navigate({
				to: path as any,
				replace: options?.replace,
			})
		}
	}

	/**
	 * Check if a path can be navigated to without redirects
	 */
	const canNavigateTo = async (path: string): Promise<boolean> => {
		const result = await routeProtection.canAccessRoute(path, authState)
		return result.canAccess
	}

	/**
	 * Get the appropriate redirect URL for a path
	 */
	const getNavigationUrl = (path: string): string => {
		return routeProtection.getRedirectUrl(path, authState)
	}

	return {
		navigateTo,
		canNavigateTo,
		getNavigationUrl,
	}
}

/**
 * Hook to check if current route requires authentication
 */
export function useIsProtectedRoute(): boolean {
	const router = useRouter()
	return isProtectedRoute(router.state.location.pathname)
}

/**
 * Hook to check if current route is guest-only
 */
export function useIsGuestOnlyRoute(): boolean {
	const router = useRouter()
	return isGuestOnlyRoute(router.state.location.pathname)
}

/**
 * Hook for handling authentication redirects in components
 * Useful for components that need to redirect based on auth state changes
 */
export function useAuthRedirect() {
	const router = useRouter()
	const authState = useAuthStore()

	/**
	 * Redirect to login with current path as redirect parameter
	 */
	const redirectToLogin = async (customPath?: string) => {
		const currentPath = customPath || router.state.location.pathname
		await router.navigate({
			to: "/login",
			search: {
				redirect: currentPath,
			},
		})
	}

	/**
	 * Redirect to dashboard or specified path after login
	 */
	const redirectAfterLogin = async (fallbackPath: string = "/dashboard") => {
		const searchParams = new URLSearchParams(router.state.location.search)
		const redirectTo = searchParams.get("redirect") || fallbackPath

		await router.navigate({
			to: redirectTo as any,
			replace: true, // Replace login page in history
		})
	}

	/**
	 * Redirect away from auth pages if user is already authenticated
	 */
	const redirectIfAuthenticated = async (
		fallbackPath: string = "/dashboard",
	) => {
		if (
			authState.isAuthenticated &&
			isGuestOnlyRoute(router.state.location.pathname)
		) {
			await router.navigate({
				to: fallbackPath as any,
				replace: true,
			})
		}
	}

	return {
		redirectToLogin,
		redirectAfterLogin,
		redirectIfAuthenticated,
	}
}

/**
 * Hook for route-based authentication effects
 * Automatically handles redirects based on route changes and auth state
 */
export function useRouteAuthEffects() {
	const router = useRouter()
	const authState = useAuthStore()
	const { redirectToLogin, redirectIfAuthenticated } = useAuthRedirect()

	useEffect(() => {
		const currentPath = router.state.location.pathname

		// Handle protected routes
		if (isProtectedRoute(currentPath)) {
			if (authState.sessionChecked && !authState.isAuthenticated) {
				redirectToLogin()
			}
		}

		// Handle guest-only routes
		if (isGuestOnlyRoute(currentPath)) {
			if (authState.isAuthenticated) {
				redirectIfAuthenticated()
			}
		}
	}, [
		router.state.location.pathname,
		authState.isAuthenticated,
		authState.sessionChecked,
		redirectToLogin,
		redirectIfAuthenticated,
	])
}

/**
 * Hook to get route protection status for the current route
 */
export function useCurrentRouteProtection() {
	const router = useRouter()
	const currentPath = router.state.location.pathname

	return {
		isProtected: isProtectedRoute(currentPath),
		isGuestOnly: isGuestOnlyRoute(currentPath),
		path: currentPath,
	}
}

/**
 * Higher-order hook that combines multiple auth-related route hooks
 * Provides a comprehensive auth state for route-aware components
 */
export function useRouteAuth(options: RouteGuardOptions = {}) {
	const authState = useAuthStore()
	const routeAccess = useRouteAccess(options)
	const navigation = useAuthAwareNavigation()
	const redirect = useAuthRedirect()
	const routeProtection = useCurrentRouteProtection()

	return {
		// Auth state
		...authState,

		// Route access info
		routeAccess,

		// Navigation helpers
		navigation,

		// Redirect helpers
		redirect,

		// Route protection status
		routeProtection,

		// Convenience flags
		canAccessCurrentRoute: routeAccess.canAccess,
		isLoadingAccess: routeAccess.isLoading,
		needsRedirect: !!routeAccess.redirectTo,
	}
}
