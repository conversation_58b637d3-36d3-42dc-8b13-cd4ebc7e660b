import { redirect } from "@tanstack/react-router"
import type { RouterContext } from "../../types/auth"
import { useAuthStore } from "../auth-store"

/**
 * Route protection utilities for TanStack Router
 * Provides beforeLoad hooks and authentication checks
 */

export interface RouteGuardOptions {
	/**
	 * Whether to redirect to login if not authenticated
	 * @default true
	 */
	redirectToLogin?: boolean

	/**
	 * Custom redirect path if not authenticated
	 * @default '/login'
	 */
	loginPath?: string

	/**
	 * Whether to preserve the intended destination
	 * @default true
	 */
	preserveRedirect?: boolean

	/**
	 * Custom fallback path if no redirect is preserved
	 * @default '/dashboard'
	 */
	fallbackPath?: string

	/**
	 * Whether to wait for session check to complete
	 * @default true
	 */
	waitForSessionCheck?: boolean

	/**
	 * Custom roles or permissions required (for future use)
	 */
	requiredRoles?: string[]

	/**
	 * Custom authorization check function
	 */
	customAuthCheck?: (context: RouterContext) => boolean | Promise<boolean>
}

/**
 * Default route guard options
 */
const DEFAULT_GUARD_OPTIONS: Required<
	Omit<RouteGuardOptions, "requiredRoles" | "customAuthCheck">
> = {
	redirectToLogin: true,
	loginPath: "/login",
	preserveRedirect: true,
	fallbackPath: "/dashboard",
	waitForSessionCheck: true,
}

/**
 * Creates a beforeLoad hook that protects routes requiring authentication
 *
 * @param options Route guard configuration options
 * @returns beforeLoad function for TanStack Router
 */
export function createAuthGuard(options: RouteGuardOptions = {}) {
	const config = { ...DEFAULT_GUARD_OPTIONS, ...options }

	return async ({ context, location }: { context: any; location: any }) => {
		// Use router context as primary source of auth state
		const { auth } = context as RouterContext

		// Uncomment for debugging auth guard execution
		// console.log("Auth guard executing:", {
		// 	path: location.pathname,
		// 	isAuthenticated: auth.isAuthenticated,
		// 	sessionChecked: auth.sessionChecked,
		// 	isLoading: auth.isLoading,
		// 	waitForSessionCheck: config.waitForSessionCheck,
		// 	user: auth.user?.email || "none",
		// })

		// If we should wait for session check and it hasn't completed yet
		if (config.waitForSessionCheck && !auth.sessionChecked) {
			// If auth is loading, we'll let the component handle the loading state
			if (auth.isLoading) {
				return
			}

			// If session hasn't been checked and we're not loading, trigger a check with timeout
			try {
				// Add timeout to prevent hanging
				const timeoutPromise = new Promise<boolean>((_, reject) => {
					setTimeout(() => reject(new Error("Session check timeout")), 5000) // 5 second timeout
				})

				const authStore = useAuthStore.getState()
				const sessionCheckPromise = authStore.checkSession()

				// Race between session check and timeout
				await Promise.race([sessionCheckPromise, timeoutPromise])

				// After checking (or timeout), get the current auth state from context
				if (!auth.isAuthenticated && config.redirectToLogin) {
					throw redirect({
						to: config.loginPath,
						search: config.preserveRedirect
							? {
									redirect: location.href,
								}
							: undefined,
					})
				}
			} catch (error) {
				console.warn("Session check failed or timed out:", error)
				// If session check fails and user is not authenticated, redirect to login
				if (!auth.isAuthenticated && config.redirectToLogin) {
					throw redirect({
						to: config.loginPath,
						search: config.preserveRedirect
							? {
									redirect: location.href,
								}
							: undefined,
					})
				}
			}

			return
		}

		// Custom authorization check
		if (options.customAuthCheck) {
			const isAuthorized = await options.customAuthCheck(
				context as RouterContext,
			)
			if (!isAuthorized) {
				// If user is authenticated but not authorized, redirect to fallback path
				// Otherwise redirect to login
				const redirectPath = auth.isAuthenticated
					? config.fallbackPath
					: config.loginPath

				throw redirect({
					to: redirectPath,
					search:
						!auth.isAuthenticated && config.preserveRedirect
							? {
									redirect: location.href,
								}
							: undefined,
				})
			}

			return
		}

		// Standard authentication check - use context state
		if (!auth.isAuthenticated) {
			if (config.redirectToLogin) {
				throw redirect({
					to: config.loginPath,
					search: config.preserveRedirect
						? {
								redirect: location.href,
							}
						: undefined,
				})
			}
		}

		// Future: Role-based access control
		if (options.requiredRoles && options.requiredRoles.length > 0) {
			// This would check user roles against required roles
			// For now, we'll just ensure the user is authenticated
			if (!auth.user) {
				throw redirect({
					to: config.loginPath,
					search: config.preserveRedirect
						? {
								redirect: location.href,
							}
						: undefined,
				})
			}
		}
	}
}

/**
 * Creates a beforeLoad hook that redirects authenticated users away from auth pages
 *
 * @param redirectTo Where to redirect authenticated users
 * @param fallbackPath Fallback if no redirect is specified
 */
export function createGuestOnlyGuard(
	redirectTo?: string,
	fallbackPath: string = "/dashboard",
) {
	return async ({ context, search }: { context: any; search: any }) => {
		// Use router context as primary source of auth state
		const { auth } = context as RouterContext

		// Uncomment for debugging guest-only guard execution
		// console.log("Guest-only guard executing:", {
		// 	path: context.location?.pathname || "unknown",
		// 	isAuthenticated: auth.isAuthenticated,
		// 	sessionChecked: auth.sessionChecked,
		// 	isLoading: auth.isLoading,
		// })

		// If session hasn't been checked yet, wait for it
		if (!auth.sessionChecked) {
			// If auth is loading, let the component handle the loading state
			if (auth.isLoading) {
				return
			}

			// If session hasn't been checked and we're not loading, trigger a check
			try {
				const authStore = useAuthStore.getState()
				await authStore.checkSession()

				// After session check, check if user is authenticated using context
				if (auth.isAuthenticated) {
					const destination = redirectTo || search?.redirect || fallbackPath
					throw redirect({
						to: destination,
					})
				}
			} catch (error) {
				// Check if this is a redirect (Response object) or actual error
				if (error instanceof Response) {
					// Re-throw redirect responses
					throw error
				}
				console.warn("Session check failed in guestOnly guard:", error)
				// Continue with current auth state if session check fails
			}
			return
		}

		// If user is authenticated, redirect them away from auth pages
		if (auth.isAuthenticated) {
			const destination = redirectTo || search?.redirect || fallbackPath
			throw redirect({
				to: destination,
			})
		}
	}
}

/**
 * Enhanced authentication guard with session validation
 * This guard ensures the session is still valid on the backend
 */
export function createStrictAuthGuard(options: RouteGuardOptions = {}) {
	const config = { ...DEFAULT_GUARD_OPTIONS, ...options }

	return async ({ context, location }: { context: any; location: any }) => {
		const { auth } = context as RouterContext

		// Always wait for session check in strict mode
		if (!auth.sessionChecked) {
			const authStore = useAuthStore.getState()
			if (authStore.checkSession) {
				const isValid = await authStore.checkSession()
				if (!isValid && config.redirectToLogin) {
					throw redirect({
						to: config.loginPath,
						search: config.preserveRedirect
							? {
									redirect: location.href,
								}
							: undefined,
					})
				}
			}
			return
		}

		// If user appears authenticated, validate the session with backend
		if (auth.isAuthenticated) {
			const authStore = useAuthStore.getState()
			if (authStore.refreshSession) {
				try {
					await authStore.refreshSession()
					// Check context again after refresh
					if (!auth.isAuthenticated && config.redirectToLogin) {
						throw redirect({
							to: config.loginPath,
							search: config.preserveRedirect
								? {
										redirect: location.href,
									}
								: undefined,
						})
					}
				} catch (error) {
					// If refresh failed, redirect to login
					if (config.redirectToLogin) {
						throw redirect({
							to: config.loginPath,
							search: config.preserveRedirect
								? {
										redirect: location.href,
									}
								: undefined,
						})
					}
				}
			}
		} else if (config.redirectToLogin) {
			// User is not authenticated
			throw redirect({
				to: config.loginPath,
				search: config.preserveRedirect
					? {
							redirect: location.href,
						}
					: undefined,
			})
		}
	}
}

/**
 * Utility function to check if a route requires authentication
 * Can be used in components or other route logic
 */
export function isProtectedRoute(pathname: string): boolean {
	// Routes that require authentication
	const protectedPatterns = [
		/^\/_authenticated/,
		/^\/dashboard/,
		/^\/bookings/,
		/^\/profile/,
		/^\/settings/,
	]

	return protectedPatterns.some((pattern) => pattern.test(pathname))
}

/**
 * Utility function to check if a route is for guests only (auth pages)
 */
export function isGuestOnlyRoute(pathname: string): boolean {
	const guestOnlyPatterns = [
		/^\/login$/,
		/^\/register$/,
		/^\/forgot-password$/,
		/^\/reset-password$/,
	]

	return guestOnlyPatterns.some((pattern) => pattern.test(pathname))
}

/**
 * Pre-configured auth guards for common use cases
 */
export const authGuards = {
	/**
	 * Standard authentication guard - redirects to login if not authenticated
	 */
	requireAuth: createAuthGuard(),

	/**
	 * Strict authentication guard - validates session with backend
	 */
	requireValidSession: createStrictAuthGuard(),

	/**
	 * Guest only guard - redirects authenticated users to dashboard
	 */
	guestOnly: createGuestOnlyGuard(),

	/**
	 * Optional auth guard - doesn't redirect but provides auth context
	 */
	optionalAuth: createAuthGuard({ redirectToLogin: false }),

	/**
	 * Quick auth check without waiting for session validation
	 */
	quickAuth: createAuthGuard({ waitForSessionCheck: false }),

	/**
	 * Development-friendly auth guard with shorter timeout
	 */
	devAuth: createAuthGuard({ waitForSessionCheck: true }),

	/**
	 * Admin only guard - requires admin role
	 */
	requireAdmin: createAuthGuard({
		customAuthCheck: async ({ auth }) => {
			// Check if user is authenticated and has admin role
			return auth.isAuthenticated && auth.user?.role === "admin"
		},
		fallbackPath: "/dashboard", // Redirect to dashboard instead of login if not admin
	}),
}

/**
 * Route protection middleware for programmatic route protection
 * Can be used outside of TanStack Router context
 */
export class RouteProtectionMiddleware {
	private static instance: RouteProtectionMiddleware

	static getInstance(): RouteProtectionMiddleware {
		if (!RouteProtectionMiddleware.instance) {
			RouteProtectionMiddleware.instance = new RouteProtectionMiddleware()
		}
		return RouteProtectionMiddleware.instance
	}

	/**
	 * Check if user can access a given route
	 */
	async canAccessRoute(
		pathname: string,
		authState: RouterContext["auth"],
		options: RouteGuardOptions = {},
	): Promise<{ canAccess: boolean; redirectTo?: string; reason?: string }> {
		const config = { ...DEFAULT_GUARD_OPTIONS, ...options }

		// Check if route requires authentication
		if (isProtectedRoute(pathname)) {
			if (!authState.sessionChecked && config.waitForSessionCheck) {
				return {
					canAccess: false,
					reason: "Session not yet validated",
				}
			}

			if (!authState.isAuthenticated) {
				return {
					canAccess: false,
					redirectTo: `${config.loginPath}?redirect=${encodeURIComponent(pathname)}`,
					reason: "Authentication required",
				}
			}

			// Custom auth check
			if (options.customAuthCheck) {
				const isAuthorized = await options.customAuthCheck({ auth: authState })
				if (!isAuthorized) {
					return {
						canAccess: false,
						redirectTo: config.loginPath,
						reason: "Authorization failed",
					}
				}
			}
		}

		// Check if route is guest-only
		if (isGuestOnlyRoute(pathname) && authState.isAuthenticated) {
			return {
				canAccess: false,
				redirectTo: config.fallbackPath,
				reason: "Already authenticated",
			}
		}

		return { canAccess: true }
	}

	/**
	 * Get the appropriate redirect URL for a user based on their auth state
	 */
	getRedirectUrl(
		intendedPath: string,
		authState: RouterContext["auth"],
		fallbackPath: string = "/dashboard",
	): string {
		if (authState.isAuthenticated) {
			// User is authenticated, check if intended path is accessible
			if (isGuestOnlyRoute(intendedPath)) {
				return fallbackPath
			}
			return intendedPath
		} else {
			// User is not authenticated, redirect to login with intended path
			if (isProtectedRoute(intendedPath)) {
				return `/login?redirect=${encodeURIComponent(intendedPath)}`
			}
			return intendedPath
		}
	}
}

// Export singleton instance
export const routeProtection = RouteProtectionMiddleware.getInstance()
