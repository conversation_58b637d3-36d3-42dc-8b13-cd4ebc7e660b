import { redirect } from "@tanstack/react-router"
import { beforeEach, describe, expect, it, vi } from "vitest"
import type { RouterContext } from "../../../types/auth"
import {
	authGuards,
	createAuthGuard,
	createGuestOnlyGuard,
	createStrictAuthGuard,
	isGuestOnlyRoute,
	isProtectedRoute,
	RouteProtectionMiddleware,
} from "../route-guards"

// Mock TanStack Router redirect
vi.mock("@tanstack/react-router", () => ({
	redirect: vi.fn(),
}))

// Mock auth store
const mockAuthStore = {
	checkSession: vi.fn(),
	refreshSession: vi.fn(),
	isAuthenticated: false,
	getState: vi.fn(),
}

vi.mock("../../auth-store", () => ({
	useAuthStore: {
		getState: () => mockAuthStore,
	},
}))

describe("Route Guards", () => {
	const mockLocation = {
		href: "/dashboard",
		pathname: "/dashboard",
	}

	const createMockContext = (
		authState: Partial<RouterContext["auth"]>,
	): { context: RouterContext; location: typeof mockLocation } => ({
		context: {
			auth: {
				user: null,
				isAuthenticated: false,
				isLoading: false,
				error: null,
				sessionChecked: true,
				...authState,
			},
		},
		location: mockLocation,
	})

	beforeEach(() => {
		vi.clearAllMocks()
		mockAuthStore.checkSession.mockResolvedValue(true)
		mockAuthStore.refreshSession.mockResolvedValue(undefined)
		mockAuthStore.isAuthenticated = false
	})

	describe("createAuthGuard", () => {
		it("should allow access for authenticated users", async () => {
			const guard = createAuthGuard()
			const { context, location } = createMockContext({
				isAuthenticated: true,
				sessionChecked: true,
			})

			await expect(guard({ context, location })).resolves.toBeUndefined()
			expect(redirect).not.toHaveBeenCalled()
		})

		it("should redirect unauthenticated users to login", async () => {
			const guard = createAuthGuard()
			const { context, location } = createMockContext({
				isAuthenticated: false,
				sessionChecked: true,
			})

			await expect(guard({ context, location })).rejects.toThrow()
			expect(redirect).toHaveBeenCalledWith({
				to: "/login",
				search: {
					redirect: "/dashboard",
				},
			})
		})

		it("should wait for session check when configured", async () => {
			const guard = createAuthGuard({ waitForSessionCheck: true })
			const { context, location } = createMockContext({
				isAuthenticated: false,
				sessionChecked: false,
				isLoading: false,
			})

			// Mock the auth store to return the mock functions
			mockAuthStore.getState.mockReturnValue(mockAuthStore)

			// The guard should still redirect after checking session since user is not authenticated
			await expect(guard({ context, location })).rejects.toThrow()
			expect(mockAuthStore.checkSession).toHaveBeenCalled()
			expect(redirect).toHaveBeenCalledWith({
				to: "/login",
				search: {
					redirect: "/dashboard",
				},
			})
		})

		it("should not redirect when redirectToLogin is false", async () => {
			const guard = createAuthGuard({ redirectToLogin: false })
			const { context, location } = createMockContext({
				isAuthenticated: false,
				sessionChecked: true,
			})

			await expect(guard({ context, location })).resolves.toBeUndefined()
			expect(redirect).not.toHaveBeenCalled()
		})

		it("should use custom login path", async () => {
			const guard = createAuthGuard({ loginPath: "/custom-login" })
			const { context, location } = createMockContext({
				isAuthenticated: false,
				sessionChecked: true,
			})

			await expect(guard({ context, location })).rejects.toThrow()
			expect(redirect).toHaveBeenCalledWith({
				to: "/custom-login",
				search: {
					redirect: "/dashboard",
				},
			})
		})

		it("should handle custom auth check", async () => {
			const customAuthCheck = vi.fn().mockResolvedValue(false)
			const guard = createAuthGuard({ customAuthCheck })
			const { context, location } = createMockContext({
				isAuthenticated: true,
				sessionChecked: true,
			})

			await expect(guard({ context, location })).rejects.toThrow()
			expect(customAuthCheck).toHaveBeenCalledWith(context)
			expect(redirect).toHaveBeenCalled()
		})
	})

	describe("createGuestOnlyGuard", () => {
		it("should allow access for unauthenticated users", async () => {
			const guard = createGuestOnlyGuard()
			const { context } = createMockContext({
				isAuthenticated: false,
			})

			await expect(guard({ context, search: {} })).resolves.toBeUndefined()
			expect(redirect).not.toHaveBeenCalled()
		})

		it("should redirect authenticated users to dashboard", async () => {
			const guard = createGuestOnlyGuard()
			const { context } = createMockContext({
				isAuthenticated: true,
			})

			await expect(guard({ context, search: {} })).rejects.toThrow()
			expect(redirect).toHaveBeenCalledWith({
				to: "/dashboard",
			})
		})

		it("should redirect to custom path", async () => {
			const guard = createGuestOnlyGuard("/custom-dashboard")
			const { context } = createMockContext({
				isAuthenticated: true,
			})

			await expect(guard({ context, search: {} })).rejects.toThrow()
			expect(redirect).toHaveBeenCalledWith({
				to: "/custom-dashboard",
			})
		})

		it("should redirect to search redirect parameter", async () => {
			const guard = createGuestOnlyGuard()
			const { context } = createMockContext({
				isAuthenticated: true,
			})

			await expect(
				guard({ context, search: { redirect: "/bookings" } }),
			).rejects.toThrow()
			expect(redirect).toHaveBeenCalledWith({
				to: "/bookings",
			})
		})
	})

	describe("createStrictAuthGuard", () => {
		it("should validate session for authenticated users", async () => {
			const guard = createStrictAuthGuard()
			const { context, location } = createMockContext({
				isAuthenticated: true,
				sessionChecked: true,
			})

			mockAuthStore.isAuthenticated = true

			await guard({ context, location })
			expect(mockAuthStore.refreshSession).toHaveBeenCalled()
		})

		it("should redirect if session refresh fails", async () => {
			const guard = createStrictAuthGuard()
			const { context, location } = createMockContext({
				isAuthenticated: true,
				sessionChecked: true,
			})

			mockAuthStore.refreshSession.mockRejectedValue(
				new Error("Session expired"),
			)

			await expect(guard({ context, location })).rejects.toThrow()
			expect(redirect).toHaveBeenCalledWith({
				to: "/login",
				search: {
					redirect: "/dashboard",
				},
			})
		})
	})

	describe("Route Pattern Matching", () => {
		describe("isProtectedRoute", () => {
			it("should identify protected routes", () => {
				expect(isProtectedRoute("/_authenticated/dashboard")).toBe(true)
				expect(isProtectedRoute("/dashboard")).toBe(true)
				expect(isProtectedRoute("/bookings")).toBe(true)
				expect(isProtectedRoute("/profile")).toBe(true)
				expect(isProtectedRoute("/settings")).toBe(true)
			})

			it("should identify non-protected routes", () => {
				expect(isProtectedRoute("/login")).toBe(false)
				expect(isProtectedRoute("/register")).toBe(false)
				expect(isProtectedRoute("/forgot-password")).toBe(false)
				expect(isProtectedRoute("/public")).toBe(false)
			})
		})

		describe("isGuestOnlyRoute", () => {
			it("should identify guest-only routes", () => {
				expect(isGuestOnlyRoute("/login")).toBe(true)
				expect(isGuestOnlyRoute("/register")).toBe(true)
				expect(isGuestOnlyRoute("/forgot-password")).toBe(true)
				expect(isGuestOnlyRoute("/reset-password")).toBe(true)
			})

			it("should identify non-guest-only routes", () => {
				expect(isGuestOnlyRoute("/dashboard")).toBe(false)
				expect(isGuestOnlyRoute("/bookings")).toBe(false)
				expect(isGuestOnlyRoute("/profile")).toBe(false)
			})
		})
	})

	describe("RouteProtectionMiddleware", () => {
		let middleware: RouteProtectionMiddleware

		beforeEach(() => {
			middleware = RouteProtectionMiddleware.getInstance()
		})

		describe("canAccessRoute", () => {
			it("should allow access to protected routes for authenticated users", async () => {
				const authState = {
					user: {
						id: "1",
						name: "Test User",
						email: "<EMAIL>",
					} as any,
					isAuthenticated: true,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const result = await middleware.canAccessRoute("/dashboard", authState)
				expect(result.canAccess).toBe(true)
			})

			it("should deny access to protected routes for unauthenticated users", async () => {
				const authState = {
					user: null,
					isAuthenticated: false,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const result = await middleware.canAccessRoute("/dashboard", authState)
				expect(result.canAccess).toBe(false)
				expect(result.redirectTo).toBe("/login?redirect=%2Fdashboard")
				expect(result.reason).toBe("Authentication required")
			})

			it("should deny access to guest-only routes for authenticated users", async () => {
				const authState = {
					user: {
						id: "1",
						name: "Test User",
						email: "<EMAIL>",
					} as any,
					isAuthenticated: true,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const result = await middleware.canAccessRoute("/login", authState)
				expect(result.canAccess).toBe(false)
				expect(result.redirectTo).toBe("/dashboard")
				expect(result.reason).toBe("Already authenticated")
			})

			it("should handle custom auth checks", async () => {
				const authState = {
					user: {
						id: "1",
						name: "Test User",
						email: "<EMAIL>",
						emailVerified: false,
					} as any,
					isAuthenticated: true,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const customAuthCheck = vi.fn().mockResolvedValue(false)
				const result = await middleware.canAccessRoute(
					"/dashboard",
					authState,
					{
						customAuthCheck,
					},
				)

				expect(result.canAccess).toBe(false)
				expect(result.reason).toBe("Authorization failed")
				expect(customAuthCheck).toHaveBeenCalledWith({ auth: authState })
			})
		})

		describe("getRedirectUrl", () => {
			it("should return intended path for authenticated users accessing allowed routes", () => {
				const authState = {
					user: { id: "1" } as any,
					isAuthenticated: true,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const result = middleware.getRedirectUrl("/dashboard", authState)
				expect(result).toBe("/dashboard")
			})

			it("should redirect authenticated users away from guest-only routes", () => {
				const authState = {
					user: { id: "1" } as any,
					isAuthenticated: true,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const result = middleware.getRedirectUrl("/login", authState)
				expect(result).toBe("/dashboard")
			})

			it("should redirect unauthenticated users to login for protected routes", () => {
				const authState = {
					user: null,
					isAuthenticated: false,
					isLoading: false,
					error: null,
					sessionChecked: true,
				}

				const result = middleware.getRedirectUrl("/dashboard", authState)
				expect(result).toBe("/login?redirect=%2Fdashboard")
			})
		})
	})

	describe("Pre-configured Guards", () => {
		it("should export pre-configured auth guards", () => {
			expect(authGuards.requireAuth).toBeDefined()
			expect(authGuards.requireValidSession).toBeDefined()
			expect(authGuards.guestOnly).toBeDefined()
			expect(authGuards.optionalAuth).toBeDefined()
			expect(authGuards.quickAuth).toBeDefined()
		})
	})
})
