/**
 * Route Protection Demonstration Script
 *
 * This script demonstrates the various route protection scenarios
 * that have been implemented in the authentication system.
 */

import type { RouterContext } from "../../../types/auth"
import {
	isGuestOnlyRoute,
	isProtectedRoute,
	routeProtection,
} from "../route-guards"

// Mock user data for demonstration
const mockUser = {
	id: "1",
	name: "<PERSON>",
	email: "<EMAIL>",
	emailVerified: true,
	createdAt: new Date("2024-01-01"),
	updatedAt: new Date("2024-01-15"),
}

const mockUnverifiedUser = {
	...mockUser,
	emailVerified: false,
}

// Different authentication states for testing
const authStates = {
	unauthenticated: {
		user: null,
		isAuthenticated: false,
		isLoading: false,
		error: null,
		sessionChecked: true,
	},
	authenticated: {
		user: mockUser,
		isAuthenticated: true,
		isLoading: false,
		error: null,
		sessionChecked: true,
	},
	unverifiedUser: {
		user: mockUnverifiedUser,
		isAuthenticated: true,
		isLoading: false,
		error: null,
		sessionChecked: true,
	},
	loading: {
		user: null,
		isAuthenticated: false,
		isLoading: true,
		error: null,
		sessionChecked: false,
	},
	sessionExpired: {
		user: null,
		isAuthenticated: false,
		isLoading: false,
		error: {
			code: "SESSION_EXPIRED",
			message: "Your session has expired",
		},
		sessionChecked: true,
	},
}

// Test routes for different scenarios
const testRoutes = [
	// Protected routes
	"/dashboard",
	"/_authenticated/dashboard",
	"/bookings",
	"/profile",
	"/settings",

	// Guest-only routes
	"/login",
	"/register",
	"/forgot-password",

	// Public routes
	"/about",
	"/contact",
	"/help",
]

/**
 * Demonstrates route protection for different authentication states
 */
export async function demonstrateRouteProtection() {
	console.log("🔐 Route Protection Demonstration\n")

	// Test route pattern matching
	console.log("📍 Route Pattern Matching:")
	testRoutes.forEach((route) => {
		const isProtected = isProtectedRoute(route)
		const isGuestOnly = isGuestOnlyRoute(route)
		const type = isProtected
			? "Protected"
			: isGuestOnly
				? "Guest-Only"
				: "Public"
		console.log(`  ${route} → ${type}`)
	})

	console.log("\n🔍 Access Control Tests:\n")

	// Test each authentication state against protected routes
	for (const [stateName, authState] of Object.entries(authStates)) {
		console.log(`👤 ${stateName.toUpperCase()} USER:`)

		// Test access to dashboard (protected route)
		const dashboardAccess = await routeProtection.canAccessRoute(
			"/dashboard",
			authState,
		)
		console.log(
			`  Dashboard: ${dashboardAccess.canAccess ? "✅ Allowed" : "❌ Denied"}`,
		)
		if (!dashboardAccess.canAccess) {
			console.log(`    → Redirect to: ${dashboardAccess.redirectTo}`)
			console.log(`    → Reason: ${dashboardAccess.reason}`)
		}

		// Test access to login (guest-only route)
		const loginAccess = await routeProtection.canAccessRoute(
			"/login",
			authState,
		)
		console.log(
			`  Login: ${loginAccess.canAccess ? "✅ Allowed" : "❌ Denied"}`,
		)
		if (!loginAccess.canAccess) {
			console.log(`    → Redirect to: ${loginAccess.redirectTo}`)
			console.log(`    → Reason: ${loginAccess.reason}`)
		}

		// Test access to settings (requires verified email)
		const settingsAccess = await routeProtection.canAccessRoute(
			"/settings",
			authState,
			{
				customAuthCheck: async ({ auth }) => {
					return auth.isAuthenticated && auth.user?.emailVerified === true
				},
			},
		)
		console.log(
			`  Settings: ${settingsAccess.canAccess ? "✅ Allowed" : "❌ Denied"}`,
		)
		if (!settingsAccess.canAccess) {
			console.log(`    → Redirect to: ${settingsAccess.redirectTo}`)
			console.log(`    → Reason: ${settingsAccess.reason}`)
		}

		console.log("")
	}

	// Demonstrate redirect URL generation
	console.log("🔄 Redirect URL Generation:")
	const redirectScenarios = [
		{ path: "/dashboard", state: authStates.unauthenticated },
		{ path: "/login", state: authStates.authenticated },
		{ path: "/profile", state: authStates.authenticated },
		{ path: "/settings", state: authStates.unverifiedUser },
	]

	redirectScenarios.forEach(({ path, state }) => {
		const redirectUrl = routeProtection.getRedirectUrl(path, state)
		const changed = redirectUrl !== path
		console.log(
			`  ${path} → ${redirectUrl} ${changed ? "(redirected)" : "(no change)"}`,
		)
	})

	console.log("\n✅ Route Protection Demonstration Complete!")
}

/**
 * Demonstrates different guard configurations
 */
export function demonstrateGuardConfigurations() {
	console.log("\n⚙️  Guard Configuration Examples:\n")

	const configurations = [
		{
			name: "Standard Auth Guard",
			description: "Redirects to login if not authenticated",
			config: {},
		},
		{
			name: "Strict Auth Guard",
			description: "Validates session with backend",
			config: { waitForSessionCheck: true },
		},
		{
			name: "Optional Auth Guard",
			description: "Provides auth context without redirecting",
			config: { redirectToLogin: false },
		},
		{
			name: "Custom Login Path",
			description: "Redirects to custom login page",
			config: { loginPath: "/custom-login" },
		},
		{
			name: "No Redirect Preservation",
			description: "Does not preserve intended destination",
			config: { preserveRedirect: false },
		},
		{
			name: "Custom Authorization",
			description: "Uses custom authorization logic",
			config: {
				customAuthCheck: async ({ auth }: { auth: any }) => {
					return auth.isAuthenticated && auth.user?.emailVerified === true
				},
			},
		},
	]

	configurations.forEach(({ name, description, config }) => {
		console.log(`🔧 ${name}:`)
		console.log(`   ${description}`)
		console.log(
			`   Config: ${JSON.stringify(config, null, 2).replace(/\n/g, "\n   ")}`,
		)
		console.log("")
	})
}

/**
 * Run the complete demonstration
 */
export async function runDemo() {
	await demonstrateRouteProtection()
	demonstrateGuardConfigurations()
}

// Export for testing purposes
export { authStates, testRoutes }
