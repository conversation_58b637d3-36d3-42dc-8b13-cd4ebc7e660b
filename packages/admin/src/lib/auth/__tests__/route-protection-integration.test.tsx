import {
	createM<PERSON>oryHistory,
	createRootRoute,
	create<PERSON>oute,
	createRouter,
	Outlet,
	RouterProvider,
} from "@tanstack/react-router"
import { render, screen, waitFor } from "@testing-library/react"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import type { RouterContext } from "../../../types/auth"
import { authGuards, createAuthGuard } from "../route-guards"

// Mock auth store
const mockAuthStore = {
	user: null,
	isAuthenticated: false,
	isLoading: false,
	error: null,
	sessionChecked: true,
	checkSession: vi.fn(),
	refreshSession: vi.fn(),
	getState: vi.fn(),
}

vi.mock("../../auth-store", () => ({
	useAuthStore: vi.fn(() => mockAuthStore),
}))

// Test components
function TestDashboard() {
	return <div data-testid="dashboard">Dashboard Content</div>
}

function TestLogin() {
	return <div data-testid="login">Login Form</div>
}

function TestProfile() {
	return <div data-testid="profile">Profile Content</div>
}

function TestSettings() {
	return <div data-testid="settings">Settings Content</div>
}

describe("Route Protection Integration", () => {
	let router: any
	let history: any

	const createTestRouter = (initialPath = "/") => {
		history = createMemoryHistory({
			initialEntries: [initialPath],
		})

		// Create root route
		const rootRoute = createRootRoute({
			context: (): RouterContext => ({
				auth: mockAuthStore,
			}),
			component: () => <Outlet />,
		})

		// Create routes with different protection levels
		const loginRoute = createRoute({
			getParentRoute: () => rootRoute,
			path: "/login",
			beforeLoad: authGuards.guestOnly,
			component: TestLogin,
		})

		const authenticatedRoute = createRoute({
			getParentRoute: () => rootRoute,
			path: "/_authenticated",
			beforeLoad: authGuards.requireAuth,
			component: () => <Outlet />,
		})

		const dashboardRoute = createRoute({
			getParentRoute: () => authenticatedRoute,
			path: "/dashboard",
			component: TestDashboard,
		})

		const profileRoute = createRoute({
			getParentRoute: () => authenticatedRoute,
			path: "/profile",
			beforeLoad: authGuards.requireValidSession,
			component: TestProfile,
		})

		const settingsRoute = createRoute({
			getParentRoute: () => authenticatedRoute,
			path: "/settings",
			beforeLoad: createAuthGuard({
				customAuthCheck: async ({ auth }) => {
					return auth.isAuthenticated && auth.user?.emailVerified === true
				},
			}),
			component: TestSettings,
		})

		const routeTree = rootRoute.addChildren([
			loginRoute,
			authenticatedRoute.addChildren([
				dashboardRoute,
				profileRoute,
				settingsRoute,
			]),
		])

		return createRouter({
			routeTree,
			history,
			context: {
				auth: mockAuthStore,
			},
		})
	}

	beforeEach(() => {
		vi.clearAllMocks()
		mockAuthStore.checkSession.mockResolvedValue(true)
		mockAuthStore.refreshSession.mockResolvedValue(undefined)

		// Reset auth state
		mockAuthStore.user = null
		mockAuthStore.isAuthenticated = false
		mockAuthStore.isLoading = false
		mockAuthStore.error = null
		mockAuthStore.sessionChecked = true
	})

	afterEach(() => {
		router?.unmount?.()
	})

	describe("Unauthenticated User Flow", () => {
		it("should redirect unauthenticated users from protected routes to login", async () => {
			router = createTestRouter("/dashboard")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(history.location.pathname).toBe("/login")
			})

			expect(screen.getByTestId("login")).toBeInTheDocument()
		})

		it("should preserve redirect parameter when redirecting to login", async () => {
			router = createTestRouter("/profile")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(history.location.pathname).toBe("/login")
				expect(history.location.search).toContain("redirect=")
			})
		})

		it("should allow access to login page for unauthenticated users", async () => {
			router = createTestRouter("/login")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(screen.getByTestId("login")).toBeInTheDocument()
			})

			expect(history.location.pathname).toBe("/login")
		})
	})

	describe("Authenticated User Flow", () => {
		beforeEach(() => {
			mockAuthStore.user = {
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			}
			mockAuthStore.isAuthenticated = true
		})

		it("should allow authenticated users to access protected routes", async () => {
			router = createTestRouter("/dashboard")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(screen.getByTestId("dashboard")).toBeInTheDocument()
			})

			expect(history.location.pathname).toBe("/dashboard")
		})

		it("should redirect authenticated users away from login page", async () => {
			router = createTestRouter("/login")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(history.location.pathname).toBe("/dashboard")
			})

			expect(screen.queryByTestId("login")).not.toBeInTheDocument()
		})

		it("should validate session for strict auth routes", async () => {
			router = createTestRouter("/profile")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(mockAuthStore.refreshSession).toHaveBeenCalled()
				expect(screen.getByTestId("profile")).toBeInTheDocument()
			})
		})

		it("should handle custom authorization checks", async () => {
			router = createTestRouter("/settings")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(screen.getByTestId("settings")).toBeInTheDocument()
			})

			expect(history.location.pathname).toBe("/settings")
		})
	})

	describe("Custom Authorization Scenarios", () => {
		beforeEach(() => {
			mockAuthStore.isAuthenticated = true
		})

		it("should deny access when custom auth check fails", async () => {
			// User with unverified email
			mockAuthStore.user = {
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: false, // This should fail the settings auth check
				createdAt: new Date(),
				updatedAt: new Date(),
			}

			router = createTestRouter("/settings")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				// Should redirect away from settings due to unverified email
				expect(history.location.pathname).not.toBe("/settings")
			})

			expect(screen.queryByTestId("settings")).not.toBeInTheDocument()
		})
	})

	describe("Session Validation Scenarios", () => {
		beforeEach(() => {
			mockAuthStore.isAuthenticated = true
			mockAuthStore.user = {
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			}
		})

		it("should redirect when session refresh fails", async () => {
			mockAuthStore.refreshSession.mockRejectedValue(
				new Error("Session expired"),
			)

			router = createTestRouter("/profile")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(mockAuthStore.refreshSession).toHaveBeenCalled()
				// Should redirect to login due to session refresh failure
				expect(history.location.pathname).toBe("/login")
			})
		})

		it("should handle session check during route loading", async () => {
			mockAuthStore.sessionChecked = false
			mockAuthStore.isLoading = false

			router = createTestRouter("/dashboard")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(mockAuthStore.checkSession).toHaveBeenCalled()
			})
		})
	})

	describe("Loading States", () => {
		it("should handle loading state during session check", async () => {
			mockAuthStore.isLoading = true
			mockAuthStore.sessionChecked = false

			router = createTestRouter("/dashboard")

			render(<RouterProvider router={router} />)

			// Should not immediately redirect while loading
			expect(history.location.pathname).toBe("/dashboard")
		})
	})

	describe("Error Scenarios", () => {
		it("should handle auth errors gracefully", async () => {
			mockAuthStore.error = {
				code: "SESSION_EXPIRED",
				message: "Session has expired",
			}
			mockAuthStore.isAuthenticated = false

			router = createTestRouter("/dashboard")

			render(<RouterProvider router={router} />)

			await waitFor(() => {
				expect(history.location.pathname).toBe("/login")
			})
		})
	})
})
