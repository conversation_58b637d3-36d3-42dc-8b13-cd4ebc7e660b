import { createContext, type <PERSON>actN<PERSON>, useContext, useEffect } from "react"
import type { AuthState } from "../../types/auth"
import { useAuthStore } from "../auth-store"

// Create the authentication context
const AuthContext = createContext<AuthState | null>(null)

// AuthProvider component that wraps the app and provides auth state
interface AuthProviderProps {
	children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
	const authState = useAuthStore()

	// Initialize authentication on mount and handle cleanup
	useEffect(() => {
		// Initialize authentication with session restoration
		authState.initializeAuth()

		// Handle page visibility changes for session validation
		const handleVisibilityChange = () => {
			if (document.visibilityState === "visible" && authState.isAuthenticated) {
				// Re-validate session when page becomes visible
				authState.checkSession()
			}
		}

		// Handle beforeunload for cleanup
		const handleBeforeUnload = () => {
			// Don't cleanup on page refresh, only on actual navigation away
			// The session should persist across page refreshes
		}

		// Add event listeners
		document.addEventListener("visibilitychange", handleVisibilityChange)
		window.addEventListener("beforeunload", handleBeforeUnload)

		// Cleanup function
		return () => {
			document.removeEventListener("visibilitychange", handleVisibilityChange)
			window.removeEventListener("beforeunload", handleBeforeUnload)
			// Note: We don't call authState.cleanup() here because we want
			// the session to persist across page refreshes
		}
	}, [authState])

	// Note: Cross-tab synchronization is now handled by the session manager

	return (
		<AuthContext.Provider value={authState}>{children}</AuthContext.Provider>
	)
}

// Hook to use authentication context
export function useAuth(): AuthState {
	const context = useContext(AuthContext)
	if (!context) {
		throw new Error("useAuth must be used within an AuthProvider")
	}
	return context
}

// Hook to get auth state for router context
export function useAuthForRouter(): AuthState {
	return useAuthStore()
}
