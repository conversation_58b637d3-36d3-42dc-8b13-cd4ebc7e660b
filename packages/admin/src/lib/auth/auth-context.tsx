import type React from "react"
import { createContext, useContext, useEffect, useState } from "react"
import type { AuthError, RouterContext } from "../../types/auth"
import type { User } from "@donorcare/backend/schemas"

/**
 * Enhanced Auth Context Provider following TanStack Router best practices
 * Handles session management at the app level, not in route guards
 */

interface AuthState {
	isAuthenticated: boolean
	isLoading: boolean
	user: User | null
	sessionChecked: boolean
	error: AuthError | null
}

interface AuthActions {
	login: (email: string, password: string) => Promise<void>
	logout: () => Promise<void>
	register: (data: RegisterData) => Promise<void>
	refreshSession: () => Promise<void>
	updateProfile: (updates: Partial<User>) => Promise<void>
	clearError: () => void
}

interface RegisterData {
	email: string
	password: string
	name: string
}

type AuthContextValue = AuthState & AuthActions

const AuthContext = createContext<AuthContextValue | undefined>(undefined)

interface AuthProviderProps {
	children: React.ReactNode
	/**
	 * API base URL for auth endpoints
	 */
	apiUrl?: string
	/**
	 * Storage key for auth token
	 */
	tokenKey?: string
	/**
	 * Custom token validator
	 */
	validateToken?: (token: string) => Promise<{ valid: boolean; user?: User }>
}

const DEFAULT_CONFIG = {
	apiUrl: "/api/auth",
	tokenKey: "auth-token",
}

export function AuthProvider({
	children,
	apiUrl = DEFAULT_CONFIG.apiUrl,
	tokenKey = DEFAULT_CONFIG.tokenKey,
	validateToken,
}: AuthProviderProps) {
	const [state, setState] = useState<AuthState>({
		isAuthenticated: false,
		isLoading: true, // Start with loading true
		user: null,
		sessionChecked: false,
		error: null,
	})

	/**
	 * Update state helper
	 */
	const updateState = (updates: Partial<AuthState>) => {
		setState((prev) => ({ ...prev, ...updates }))
	}

	/**
	 * API call helper with auth headers
	 */
	const apiCall = async (endpoint: string, options: RequestInit = {}) => {
		const token = localStorage.getItem(tokenKey)

		const response = await fetch(`${apiUrl}${endpoint}`, {
			...options,
			headers: {
				"Content-Type": "application/json",
				...(token && { Authorization: `Bearer ${token}` }),
				...options.headers,
			},
		})

		if (!response.ok) {
			const errorData = await response
				.json()
				.catch(() => ({ message: "Request failed" }))
			throw new Error(errorData.message || `Request failed: ${response.status}`)
		}

		return response.json()
	}

	/**
	 * Restore authentication state on app load
	 * This runs once when the app starts, before any routing
	 */
	useEffect(() => {
		let mounted = true

		const restoreSession = async () => {
			try {
				const token = localStorage.getItem(tokenKey)

				if (!token) {
					// No token found, user is not authenticated
					if (mounted) {
						updateState({
							isAuthenticated: false,
							isLoading: false,
							sessionChecked: true,
							user: null,
						})
					}
					return
				}

				// Validate token with custom validator or API call
				let userData: User

				if (validateToken) {
					const result = await validateToken(token)
					if (!result.valid || !result.user) {
						throw new Error("Invalid token")
					}
					userData = result.user
				} else {
					// Default validation via API
					userData = await apiCall("/me")
				}

				// Token is valid, restore user session
				if (mounted) {
					updateState({
						isAuthenticated: true,
						isLoading: false,
						sessionChecked: true,
						user: userData,
						error: null,
					})
				}
			} catch (error) {
				console.warn("Session restoration failed:", error)

				// Clear invalid token
				localStorage.removeItem(tokenKey)

				if (mounted) {
					updateState({
						isAuthenticated: false,
						isLoading: false,
						sessionChecked: true,
						user: null,
						error:
							error instanceof Error
								? error.message
								: "Session restoration failed",
					})
				}
			}
		}

		restoreSession()

		// Cleanup function
		return () => {
			mounted = false
		}
	}, [apiUrl, tokenKey, validateToken])

	/**
	 * Login user with email and password
	 */
	const login = async (email: string, password: string): Promise<void> => {
		try {
			updateState({ isLoading: true, error: null })

			const response = await apiCall("/login", {
				method: "POST",
				body: JSON.stringify({ email, password }),
			})

			const { user, token } = response

			// Store token for persistence
			localStorage.setItem(tokenKey, token)

			updateState({
				isAuthenticated: true,
				isLoading: false,
				user,
				sessionChecked: true,
				error: null,
			})
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Login failed"
			updateState({
				isLoading: false,
				error: errorMessage,
			})
			throw new Error(errorMessage)
		}
	}

	/**
	 * Register new user
	 */
	const register = async (data: RegisterData): Promise<void> => {
		try {
			updateState({ isLoading: true, error: null })

			const response = await apiCall("/register", {
				method: "POST",
				body: JSON.stringify(data),
			})

			const { user, token } = response

			// Store token for persistence
			localStorage.setItem(tokenKey, token)

			updateState({
				isAuthenticated: true,
				isLoading: false,
				user,
				sessionChecked: true,
				error: null,
			})
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Registration failed"
			updateState({
				isLoading: false,
				error: errorMessage,
			})
			throw new Error(errorMessage)
		}
	}

	/**
	 * Logout user and clear session
	 */
	const logout = async (): Promise<void> => {
		try {
			// Optional: Call logout endpoint to invalidate server session
			try {
				await apiCall("/logout", { method: "POST" })
			} catch (error) {
				// Don't fail logout if server call fails
				console.warn("Server logout failed:", error)
			}
		} finally {
			// Always clear local session
			localStorage.removeItem(tokenKey)

			updateState({
				isAuthenticated: false,
				isLoading: false,
				user: null,
				sessionChecked: true,
				error: null,
			})
		}
	}

	/**
	 * Refresh user session and token
	 */
	const refreshSession = async (): Promise<void> => {
		try {
			const response = await apiCall("/refresh", { method: "POST" })
			const { user, token } = response

			// Update stored token if provided
			if (token) {
				localStorage.setItem(tokenKey, token)
			}

			updateState({
				user,
				error: null,
			})
		} catch (error) {
			console.warn("Session refresh failed:", error)

			// If refresh fails, consider session invalid
			localStorage.removeItem(tokenKey)

			updateState({
				isAuthenticated: false,
				user: null,
				error: "Session expired",
			})

			throw error
		}
	}

	/**
	 * Update user profile
	 */
	const updateProfile = async (updates: Partial<User>): Promise<void> => {
		try {
			updateState({ isLoading: true, error: null })

			const response = await apiCall("/profile", {
				method: "PATCH",
				body: JSON.stringify(updates),
			})

			updateState({
				user: response.user,
				isLoading: false,
				error: null,
			})
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Profile update failed"
			updateState({
				isLoading: false,
				error: errorMessage,
			})
			throw new Error(errorMessage)
		}
	}

	/**
	 * Clear error state
	 */
	const clearError = () => {
		updateState({ error: null })
	}

	// Context value
	const contextValue: AuthContextValue = {
		// State
		...state,

		// Actions
		login,
		register,
		logout,
		refreshSession,
		updateProfile,
		clearError,
	}

	return (
		<AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
	)
}

/**
 * Hook to access auth context
 * Must be used within AuthProvider
 */
export function useAuth(): AuthContextValue {
	const context = useContext(AuthContext)
	if (context === undefined) {
		throw new Error("useAuth must be used within an AuthProvider")
	}
	return context
}

/**
 * Hook to get auth state for router context
 * Returns only the state part needed by the router
 */
export function useAuthForRouter(): RouterContext["auth"] {
	const auth = useAuth()

	return {
		isAuthenticated: auth.isAuthenticated,
		isLoading: auth.isLoading,
		user: auth.user,
		sessionChecked: auth.sessionChecked,
		error: auth.error,
		// Helper methods for route guards
		// hasRole: (role: string) => auth.user?.role === role,
		// hasAnyRole: (roles: string[]) => {
		// 	const userRole = auth.user?.role || ""
		// 	return roles.some((role) => userRole === role)
		// },
		// hasPermission: (permission: string) =>
		// 	auth.user?.permissions?.includes(permission) ?? false,
		// hasAnyPermission: (permissions: string[]) => {
		// 	const userPermissions = auth.user?.permissions || []
		// 	return permissions.some((permission) =>
		// 		userPermissions.includes(permission),
		// 	)
		// },
		// login: auth.login,
		// logout: auth.logout,
	}
}

/**
 * Higher-order component for components that need auth
 */
export function withAuth<P extends object>(
	Component: React.ComponentType<P>,
): React.ComponentType<P> {
	const displayName = Component.displayName || Component.name || "Component"

	const WrappedComponent = (props: P) => {
		const auth = useAuth()

		if (!auth.sessionChecked) {
			return (
				<div className="flex items-center justify-center min-h-screen">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
				</div>
			)
		}

		return <Component {...props} />
	}

	WrappedComponent.displayName = `withAuth(${displayName})`

	return WrappedComponent
}

/**
 * Loading component shown while auth is initializing
 */
export function AuthLoadingScreen() {
	return (
		<div className="fixed inset-0 flex items-center justify-center bg-white">
			<div className="text-center">
				<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
				<p className="text-gray-600">Checking authentication...</p>
			</div>
		</div>
	)
}

/**
 * Type exports for consuming components
 */
export type { User, AuthState, AuthActions, RegisterData }
