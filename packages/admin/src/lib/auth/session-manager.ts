/**
 * Session Manager - Handles session persistence, restoration, and cleanup
 */

import { authService } from "../auth-client"
import { AUTH_CONFIG } from "../auth-config"

export class SessionManager {
	private static instance: SessionManager
	private sessionCheckInterval: NodeJS.Timeout | null = null
	private isInitialized = false

	static getInstance(): SessionManager {
		if (!SessionManager.instance) {
			SessionManager.instance = new SessionManager()
		}
		return SessionManager.instance
	}

	/**
	 * Initialize session management
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) {
			return
		}

		this.isInitialized = true

		// Set up periodic session validation
		this.startPeriodicSessionCheck()

		// Handle page focus events
		this.setupFocusHandlers()

		// Handle storage events for cross-tab sync
		this.setupStorageHandlers()
	}

	/**
	 * Start periodic session validation
	 */
	private startPeriodicSessionCheck(): void {
		if (this.sessionCheckInterval) {
			return
		}

		this.sessionCheckInterval = setInterval(async () => {
			try {
				const isValid = await authService.validateSession()
				if (!isValid) {
					this.handleSessionExpiration()
				}
			} catch (error) {
				console.error("Periodic session check failed:", error)
			}
		}, AUTH_CONFIG.VALIDATION.BACKGROUND_CHECK_INTERVAL)
	}

	/**
	 * Stop periodic session validation
	 */
	private stopPeriodicSessionCheck(): void {
		if (this.sessionCheckInterval) {
			clearInterval(this.sessionCheckInterval)
			this.sessionCheckInterval = null
		}
	}

	/**
	 * Set up handlers for page focus events
	 */
	private setupFocusHandlers(): void {
		const handleFocus = async () => {
			// When page regains focus, validate session
			try {
				const isValid = await authService.validateSession()
				if (!isValid) {
					this.handleSessionExpiration()
				}
			} catch (error) {
				console.error("Focus session check failed:", error)
			}
		}

		window.addEventListener("focus", handleFocus)
		document.addEventListener("visibilitychange", () => {
			if (document.visibilityState === "visible") {
				handleFocus()
			}
		})
	}

	/**
	 * Set up handlers for storage events (cross-tab sync)
	 */
	private setupStorageHandlers(): void {
		const handleStorageChange = (event: StorageEvent) => {
			if (event.key === "auth-logout" && event.newValue === "true") {
				// Another tab logged out, sync this tab
				this.handleCrossTabLogout()
			}
		}

		window.addEventListener("storage", handleStorageChange)
	}

	/**
	 * Handle session expiration
	 */
	private handleSessionExpiration(): void {
		console.log("Session expired, redirecting to login...")

		// Stop session monitoring
		this.stopPeriodicSessionCheck()
		authService.stopBackgroundValidation()

		// Clear any auth-related storage
		this.clearAuthStorage()

		// Redirect to login with current path for post-login redirect
		if (typeof window !== "undefined") {
			const currentPath = window.location.pathname + window.location.search
			const loginUrl = `${AUTH_CONFIG.ROUTES.LOGIN}?redirect=${encodeURIComponent(currentPath)}`
			window.location.href = loginUrl
		}
	}

	/**
	 * Handle logout from another tab
	 */
	private handleCrossTabLogout(): void {
		console.log("Logout detected from another tab")

		// Stop session monitoring
		this.stopPeriodicSessionCheck()
		authService.stopBackgroundValidation()

		// Clear auth storage
		this.clearAuthStorage()

		// Redirect to login
		if (typeof window !== "undefined") {
			window.location.href = AUTH_CONFIG.ROUTES.LOGIN
		}
	}

	/**
	 * Clear authentication-related storage
	 */
	private clearAuthStorage(): void {
		try {
			// Clear auth storage
			localStorage.removeItem("auth-storage")

			// Clear any other auth-related items
			const keysToRemove = []
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i)
				if (key && (key.startsWith("auth-") || key.startsWith("session-"))) {
					keysToRemove.push(key)
				}
			}

			keysToRemove.forEach((key) => localStorage.removeItem(key))
		} catch (error) {
			console.error("Failed to clear auth storage:", error)
		}
	}

	/**
	 * Signal logout to other tabs
	 */
	signalLogout(): void {
		try {
			localStorage.setItem("auth-logout", "true")
			// Remove the signal after a short delay
			setTimeout(() => {
				localStorage.removeItem("auth-logout")
			}, 1000)
		} catch (error) {
			console.error("Failed to signal logout to other tabs:", error)
		}
	}

	/**
	 * Restore session from cookies/storage
	 */
	async restoreSession(): Promise<boolean> {
		try {
			// Check if we have a valid session cookie
			const sessionData = await authService.getSession()

			if (sessionData?.user) {
				// Validate the session is still active
				const isValid = await authService.validateSession()

				if (isValid) {
					// Start session monitoring
					this.startPeriodicSessionCheck()
					authService.startBackgroundValidation(() => {
						this.handleSessionExpiration()
					})

					return true
				}
			}

			return false
		} catch (error) {
			console.error("Session restoration failed:", error)
			return false
		}
	}

	/**
	 * Cleanup session manager
	 */
	cleanup(): void {
		this.stopPeriodicSessionCheck()
		authService.stopBackgroundValidation()
		this.isInitialized = false
	}

	/**
	 * Check if session manager is initialized
	 */
	get initialized(): boolean {
		return this.isInitialized
	}
}

// Export singleton instance
export const sessionManager = SessionManager.getInstance()
