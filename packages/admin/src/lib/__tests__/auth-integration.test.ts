import { beforeEach, describe, expect, it, vi } from "vitest"
import { useAuthStore } from "../auth-store"

// Mock better-auth client
vi.mock("better-auth/react", () => ({
	createAuthClient: vi.fn(() => ({
		signIn: {
			email: vi.fn().mockResolvedValue({
				data: {
					user: { id: "1", email: "<EMAIL>", name: "Test User" },
				},
			}),
		},
		signOut: vi.fn().mockResolvedValue({}),
		getSession: vi.fn().mockResolvedValue({
			data: { user: { id: "1", email: "<EMAIL>", name: "Test User" } },
		}),
	})),
}))

describe("Auth Integration", () => {
	beforeEach(() => {
		// Reset store state
		useAuthStore.setState({
			user: null,
			isAuthenticated: false,
			isLoading: false,
			error: null,
			sessionChecked: false,
		})
	})

	it("should integrate auth client with store for sign in", async () => {
		const store = useAuthStore.getState()

		await store.signIn("<EMAIL>", "password")

		const state = useAuthStore.getState()
		expect(state.isAuthenticated).toBe(true)
		expect(state.user).toEqual(
			expect.objectContaining({
				id: "1",
				email: "<EMAIL>",
				name: "Test User",
			}),
		)
		expect(state.error).toBeNull()
	})

	it("should integrate auth client with store for session check", async () => {
		const store = useAuthStore.getState()

		const isValid = await store.checkSession()

		expect(isValid).toBe(true)
		const state = useAuthStore.getState()
		expect(state.sessionChecked).toBe(true)
		expect(state.isAuthenticated).toBe(true)
	})

	it("should integrate auth client with store for sign out", async () => {
		// Mock window.location.href to test redirect
		const originalLocation = window.location
		delete (window as any).location
		window.location = { ...originalLocation, href: "" } as any

		// First sign in
		const store = useAuthStore.getState()
		await store.signIn("<EMAIL>", "password")

		// Then sign out
		await store.signOut()

		const state = useAuthStore.getState()
		expect(state.isAuthenticated).toBe(false)
		expect(state.user).toBeNull()

		// Verify redirect to login page (Requirement 3.2)
		expect(window.location.href).toBe("/login")

		// Restore original location
		window.location = originalLocation
	})

	it("should handle session refresh", async () => {
		const store = useAuthStore.getState()

		await store.refreshSession()

		const state = useAuthStore.getState()
		expect(state.isAuthenticated).toBe(true)
		expect(state.user).toEqual(
			expect.objectContaining({
				id: "1",
				email: "<EMAIL>",
			}),
		)
	})

	it("should redirect to login even when logout fails", async () => {
		// Mock window.location.href to test redirect
		const originalLocation = window.location
		delete (window as any).location
		window.location = { ...originalLocation, href: "" } as any

		// Mock authService.signOut to throw an error
		const { authService } = await import("../auth-client")
		const originalSignOut = authService.signOut
		authService.signOut = vi.fn().mockRejectedValue(new Error("Network error"))

		// First sign in
		const store = useAuthStore.getState()
		await store.signIn("<EMAIL>", "password")

		// Then sign out (should fail but still redirect)
		await store.signOut()

		const state = useAuthStore.getState()
		expect(state.isAuthenticated).toBe(false)
		expect(state.user).toBeNull()

		// Verify redirect to login page even when logout fails (Requirement 3.3)
		expect(window.location.href).toBe("/login")

		// Restore original methods and location
		authService.signOut = originalSignOut
		window.location = originalLocation
	})
})
