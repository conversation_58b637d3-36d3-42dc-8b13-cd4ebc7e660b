/**
 * Tests for session persistence and restoration functionality
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { sessionManager } from "../auth/session-manager"
import { authService } from "../auth-client"
import { useAuthStore } from "../auth-store"

// Mock the auth service
vi.mock("../auth-client", () => ({
	authService: {
		getSession: vi.fn(),
		validateSession: vi.fn(),
		startBackgroundValidation: vi.fn(),
		stopBackgroundValidation: vi.fn(),
		cleanup: vi.fn(),
	},
}))

// Mock localStorage
const localStorageMock = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn(),
}

Object.defineProperty(window, "localStorage", {
	value: localStorageMock,
})

describe("Session Persistence and Restoration", () => {
	beforeEach(() => {
		vi.clearAllMocks()
		// Reset the auth store state
		useAuthStore.getState().cleanup()
	})

	afterEach(() => {
		sessionManager.cleanup()
	})

	describe("Session Restoration", () => {
		it("should restore session from cookies on initialization", async () => {
			// Mock successful session restoration
			const mockUser = {
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			}

			vi.mocked(authService.getSession).mockResolvedValue({
				user: mockUser,
				session: { id: "session-1", userId: "1", expiresAt: new Date() },
			})
			vi.mocked(authService.validateSession).mockResolvedValue(true)

			const store = useAuthStore.getState()

			// Initialize authentication
			await store.initializeAuth()

			// Verify session was restored
			const state = useAuthStore.getState()
			expect(state.isAuthenticated).toBe(true)
			expect(state.user).toEqual(mockUser)
			expect(state.sessionChecked).toBe(true)
			expect(state.isLoading).toBe(false)
		})

		it("should handle failed session restoration gracefully", async () => {
			// Mock failed session restoration
			vi.mocked(authService.getSession).mockResolvedValue(null)
			vi.mocked(authService.validateSession).mockResolvedValue(false)

			const store = useAuthStore.getState()

			// Initialize authentication
			await store.initializeAuth()

			// Verify session restoration failed gracefully
			const state = useAuthStore.getState()
			expect(state.isAuthenticated).toBe(false)
			expect(state.user).toBe(null)
			expect(state.sessionChecked).toBe(true)
			expect(state.isLoading).toBe(false)
		})

		it("should not reinitialize if session already checked", async () => {
			const store = useAuthStore.getState()

			// Set sessionChecked to true
			store.setUser(null)
			useAuthStore.setState({ sessionChecked: true })

			// Initialize authentication again
			await store.initializeAuth()

			// Verify no additional calls were made
			expect(authService.getSession).not.toHaveBeenCalled()
			expect(authService.validateSession).not.toHaveBeenCalled()
		})
	})

	describe("Session Expiration Handling", () => {
		it("should handle session expiration correctly", async () => {
			const store = useAuthStore.getState()

			// Mock window.location
			const mockLocation = {
				pathname: "/_authenticated/dashboard",
				search: "",
				href: "",
			}
			Object.defineProperty(window, "location", {
				value: mockLocation,
				writable: true,
			})

			// Call session expiration handler
			await store.handleSessionExpiration()

			// Verify state was cleared
			const state = useAuthStore.getState()
			expect(state.isAuthenticated).toBe(false)
			expect(state.user).toBe(null)
			expect(state.error?.code).toBe("SESSION_EXPIRED")
		})
	})

	describe("Session Cleanup", () => {
		it("should cleanup session properly on sign out", async () => {
			const store = useAuthStore.getState()

			// Mock successful sign out
			vi.mocked(authService.signOut).mockResolvedValue({ success: true })

			// Set initial authenticated state
			store.setUser({
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			})

			// Sign out
			await store.signOut()

			// Verify cleanup
			const state = useAuthStore.getState()
			expect(state.isAuthenticated).toBe(false)
			expect(state.user).toBe(null)
			expect(authService.stopBackgroundValidation).toHaveBeenCalled()
		})

		it("should cleanup even if server sign out fails", async () => {
			const store = useAuthStore.getState()

			// Mock failed sign out
			vi.mocked(authService.signOut).mockRejectedValue(
				new Error("Network error"),
			)

			// Set initial authenticated state
			store.setUser({
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			})

			// Sign out (should not throw)
			await store.signOut()

			// Verify cleanup happened anyway for security
			const state = useAuthStore.getState()
			expect(state.isAuthenticated).toBe(false)
			expect(state.user).toBe(null)
		})
	})

	describe("Cross-tab Synchronization", () => {
		it("should handle storage events for cross-tab sync", () => {
			// This would be tested in integration tests with actual localStorage events
			// For now, we verify the session manager has the necessary handlers
			expect(sessionManager).toBeDefined()
			expect(typeof sessionManager.cleanup).toBe("function")
		})
	})

	describe("Background Session Validation", () => {
		it("should start background validation after successful sign in", async () => {
			const store = useAuthStore.getState()

			// Mock successful sign in
			const mockUser = {
				id: "1",
				name: "Test User",
				email: "<EMAIL>",
				emailVerified: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			}

			vi.mocked(authService.signIn).mockResolvedValue({ success: true })
			vi.mocked(authService.getSession).mockResolvedValue({
				user: mockUser,
				session: { id: "session-1", userId: "1", expiresAt: new Date() },
			})

			// Sign in
			await store.signIn("<EMAIL>", "password")

			// Verify session manager was initialized
			expect(sessionManager.initialized).toBe(true)
		})
	})
})
