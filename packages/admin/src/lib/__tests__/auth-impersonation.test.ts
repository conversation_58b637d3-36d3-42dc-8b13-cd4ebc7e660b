import { describe, expect, it, vi } from "vitest"
import { useAuthStore } from "../lib/auth-store"

// Mock the zustand store
vi.mock("zustand", () => ({
	create: () => (fn: any) => {
		const store = fn(() => {}, () => ({}))
		return {
			...store,
			getState: () => store,
		}
	},
}))

describe("Auth Store Impersonation", () => {
	it("should have impersonation methods", () => {
		const store = useAuthStore.getState()
		
		expect(store).toHaveProperty("startImpersonation")
		expect(store).toHaveProperty("stopImpersonation")
		expect(store).toHaveProperty("setImpersonating")
		expect(store).toHaveProperty("isImpersonating")
	})

	it("should initialize with correct impersonation state", () => {
		const store = useAuthStore.getState()
		
		expect(store.isImpersonating).toBe(false)
	})

	it("should update impersonation state correctly", () => {
		const store = useAuthStore.getState()
		
		store.setImpersonating(true)
		expect(store.isImpersonating).toBe(true)
		
		store.setImpersonating(false)
		expect(store.isImpersonating).toBe(false)
	})
})