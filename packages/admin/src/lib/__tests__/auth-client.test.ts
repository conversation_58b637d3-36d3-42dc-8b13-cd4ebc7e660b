import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { AUTH_CONFIG } from "../auth-config"

// Mock the better-auth client
const mockAuthClient = {
	signIn: {
		email: vi.fn(),
	},
	signOut: vi.fn(),
	getSession: vi.fn(),
}

vi.mock("better-auth/react", () => ({
	createAuthClient: vi.fn(() => mockAuthClient),
}))

// Import after mocking
const { AuthClientService } = await import("../auth-client")

describe("AuthClientService", () => {
	let authService: AuthClientService

	beforeEach(() => {
		// Reset the singleton instance
		;(AuthClientService as any).instance = undefined
		authService = AuthClientService.getInstance()
	})

	afterEach(() => {
		authService.cleanup()
		vi.clearAllMocks()
	})

	describe("validateSession", () => {
		it("should return true when session is valid", async () => {
			mockAuthClient.getSession.mockResolvedValue({
				data: { user: { id: "1", email: "<EMAIL>" } },
			})

			const result = await authService.validateSession()
			expect(result).toBe(true)
		})

		it("should return false when session is invalid", async () => {
			mockAuthClient.getSession.mockResolvedValue({ data: null })

			const result = await authService.validateSession()
			expect(result).toBe(false)
		})

		it("should handle network errors gracefully", async () => {
			mockAuthClient.getSession.mockRejectedValue(new TypeError("fetch failed"))

			const result = await authService.validateSession()
			expect(result).toBe(false)
		})
	})

	describe("signIn", () => {
		it("should return success when credentials are valid", async () => {
			mockAuthClient.signIn.email.mockResolvedValue({
				data: { user: { id: "1", email: "<EMAIL>" } },
			})
			mockAuthClient.getSession.mockResolvedValue({
				data: { user: { id: "1", email: "<EMAIL>" } },
			})

			const result = await authService.signIn("<EMAIL>", "password")
			expect(result.success).toBe(true)
			expect(result.error).toBeUndefined()
		})

		it("should return error when credentials are invalid", async () => {
			mockAuthClient.signIn.email.mockResolvedValue({
				error: { message: "Invalid credentials" },
			})

			const result = await authService.signIn("<EMAIL>", "wrong")
			expect(result.success).toBe(false)
			expect(result.error?.code).toBe(
				AUTH_CONFIG.ERROR_CODES.INVALID_CREDENTIALS,
			)
		})

		it("should handle network errors", async () => {
			mockAuthClient.signIn.email.mockRejectedValue(
				new TypeError("fetch failed"),
			)

			const result = await authService.signIn("<EMAIL>", "password")
			expect(result.success).toBe(false)
			expect(result.error?.code).toBe(AUTH_CONFIG.ERROR_CODES.NETWORK_ERROR)
		})
	})

	describe("signOut", () => {
		it("should return success when sign out succeeds", async () => {
			mockAuthClient.signOut.mockResolvedValue({})

			const result = await authService.signOut()
			expect(result.success).toBe(true)
		})

		it("should return success even when sign out fails (for security)", async () => {
			mockAuthClient.signOut.mockRejectedValue(new Error("Server error"))

			const result = await authService.signOut()
			expect(result.success).toBe(true)
		})
	})

	describe("getSession", () => {
		it("should return session data when available", async () => {
			const sessionData = { user: { id: "1", email: "<EMAIL>" } }
			mockAuthClient.getSession.mockResolvedValue({ data: sessionData })

			const result = await authService.getSession()
			expect(result).toEqual(sessionData)
		})

		it("should return null when no session", async () => {
			mockAuthClient.getSession.mockResolvedValue({ data: null })

			const result = await authService.getSession()
			expect(result).toBeNull()
		})

		it("should return null on error", async () => {
			mockAuthClient.getSession.mockRejectedValue(new Error("Network error"))

			const result = await authService.getSession()
			expect(result).toBeNull()
		})
	})

	describe("background validation", () => {
		it("should start and stop background validation", () => {
			const onSessionExpired = vi.fn()

			authService.startBackgroundValidation(onSessionExpired)
			expect((authService as any).backgroundCheckInterval).toBeTruthy()

			authService.stopBackgroundValidation()
			expect((authService as any).backgroundCheckInterval).toBeNull()
		})
	})
})
