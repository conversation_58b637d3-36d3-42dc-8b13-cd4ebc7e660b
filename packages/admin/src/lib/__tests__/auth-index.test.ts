import { describe, expect, it } from "vitest"
import {
	AUTH_CONFIG,
	authClient,
	isValidEmail,
	isValidPassword,
	useAuthError,
	useAuthLoading,
	useAuthStore,
	useIsAuthenticated,
	useUser,
} from "../auth/index"

describe("Auth Index Exports", () => {
	it("should export authClient", () => {
		expect(authClient).toBeDefined()
		expect(authClient.signIn).toBeDefined()
	})

	it("should export store hooks", () => {
		expect(useAuthStore).toBeDefined()
		expect(useUser).toBeDefined()
		expect(useIsAuthenticated).toBeDefined()
		expect(useAuthLoading).toBeDefined()
		expect(useAuthError).toBeDefined()
	})

	it("should export configuration", () => {
		expect(AUTH_CONFIG).toBeDefined()
		expect(AUTH_CONFIG.BACKEND_URL).toBe("http://localhost:3000")
		expect(AUTH_CONFIG.SESSION.CREDENTIALS).toBe("include")
	})

	it("should export validation utilities", () => {
		expect(typeof isValidEmail).toBe("function")
		expect(typeof isValidPassword).toBe("function")

		// Test email validation
		expect(isValidEmail("<EMAIL>")).toBe(true)
		expect(isValidEmail("invalid-email")).toBe(false)

		// Test password validation
		expect(isValidPassword("12345678")).toBe(true)
		expect(isValidPassword("1234567")).toBe(false)
	})
})
