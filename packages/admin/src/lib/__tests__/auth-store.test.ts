import { beforeEach, describe, expect, it } from "vitest"
import { useAuthStore } from "../auth-store"

describe("Auth Store", () => {
	beforeEach(() => {
		// Reset store state before each test
		useAuthStore.setState({
			user: null,
			isAuthenticated: false,
			isLoading: false,
			error: null,
			sessionChecked: false,
		})
	})

	it("should have initial state", () => {
		const state = useAuthStore.getState()

		expect(state.user).toBeNull()
		expect(state.isAuthenticated).toBe(false)
		expect(state.isLoading).toBe(false)
		expect(state.error).toBeNull()
		expect(state.sessionChecked).toBe(false)
	})

	it("should have required actions", () => {
		const state = useAuthStore.getState()

		expect(typeof state.signIn).toBe("function")
		expect(typeof state.signOut).toBe("function")
		expect(typeof state.clearError).toBe("function")
		expect(typeof state.setLoading).toBe("function")
		expect(typeof state.setError).toBe("function")
		expect(typeof state.setUser).toBe("function")
		expect(typeof state.refreshSession).toBe("function")
		expect(typeof state.checkSession).toBe("function")
		expect(typeof state.initializeAuth).toBe("function")
	})

	it("should update user state", () => {
		const mockUser = {
			id: "1",
			name: "Test User",
			email: "<EMAIL>",
			emailVerified: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		}

		useAuthStore.getState().setUser(mockUser)

		const state = useAuthStore.getState()
		expect(state.user).toEqual(mockUser)
		expect(state.isAuthenticated).toBe(true)
	})

	it("should clear user state", () => {
		const mockUser = {
			id: "1",
			name: "Test User",
			email: "<EMAIL>",
			emailVerified: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		}

		useAuthStore.getState().setUser(mockUser)
		useAuthStore.getState().setUser(null)

		const state = useAuthStore.getState()
		expect(state.user).toBeNull()
		expect(state.isAuthenticated).toBe(false)
	})

	it("should set loading state", () => {
		useAuthStore.getState().setLoading(true)
		expect(useAuthStore.getState().isLoading).toBe(true)

		useAuthStore.getState().setLoading(false)
		expect(useAuthStore.getState().isLoading).toBe(false)
	})

	it("should set and clear error", () => {
		const mockError = {
			code: "TEST_ERROR",
			message: "Test error message",
		}

		useAuthStore.getState().setError(mockError)
		expect(useAuthStore.getState().error).toEqual(mockError)

		useAuthStore.getState().clearError()
		expect(useAuthStore.getState().error).toBeNull()
	})
})
