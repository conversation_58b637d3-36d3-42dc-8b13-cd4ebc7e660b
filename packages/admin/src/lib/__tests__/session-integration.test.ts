/**
 * Integration test for session persistence and restoration
 */

import { afterEach, beforeEach, describe, expect, it } from "vitest"
import { sessionManager } from "../auth/session-manager"
import { useAuthStore } from "../auth-store"

describe("Session Persistence Integration", () => {
	beforeEach(() => {
		// Clear localStorage
		localStorage.clear()
		// Reset auth store
		useAuthStore.getState().cleanup()
	})

	afterEach(() => {
		sessionManager.cleanup()
	})

	it("should persist sessionChecked flag across store recreations", () => {
		// Get initial store state
		const store = useAuthStore.getState()

		// Simulate session check completion
		useAuthStore.setState({ sessionChecked: true })

		// Verify the flag is set
		expect(useAuthStore.getState().sessionChecked).toBe(true)

		// The persist middleware should have saved this to localStorage
		// In a real scenario, this would persist across page refreshes
		const persistedData = localStorage.getItem("auth-storage")
		expect(persistedData).toBeTruthy()

		if (persistedData) {
			const parsed = JSON.parse(persistedData)
			expect(parsed.state.sessionChecked).toBe(true)
		}
	})

	it("should not persist sensitive user data", () => {
		const mockUser = {
			id: "1",
			name: "Test User",
			email: "<EMAIL>",
			emailVerified: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		}

		// Set user data
		useAuthStore.getState().setUser(mockUser)

		// Check that user data is not persisted
		const persistedData = localStorage.getItem("auth-storage")
		if (persistedData) {
			const parsed = JSON.parse(persistedData)
			expect(parsed.state.user).toBe(null)
			expect(parsed.state.isAuthenticated).toBe(false)
		}
	})

	it("should handle session manager initialization", async () => {
		expect(sessionManager.initialized).toBe(false)

		await sessionManager.initialize()

		expect(sessionManager.initialized).toBe(true)
	})

	it("should handle session manager cleanup", () => {
		sessionManager.cleanup()

		expect(sessionManager.initialized).toBe(false)
	})

	it("should provide all required auth store methods", () => {
		const store = useAuthStore.getState()

		// Verify all required methods exist
		expect(typeof store.signIn).toBe("function")
		expect(typeof store.signOut).toBe("function")
		expect(typeof store.checkSession).toBe("function")
		expect(typeof store.initializeAuth).toBe("function")
		expect(typeof store.restoreSession).toBe("function")
		expect(typeof store.handleSessionExpiration).toBe("function")
		expect(typeof store.cleanup).toBe("function")
		expect(typeof store.refreshSession).toBe("function")
		expect(typeof store.clearError).toBe("function")
		expect(typeof store.setLoading).toBe("function")
		expect(typeof store.setError).toBe("function")
		expect(typeof store.setUser).toBe("function")
	})

	it("should maintain proper initial state", () => {
		const state = useAuthStore.getState()

		expect(state.user).toBe(null)
		expect(state.isAuthenticated).toBe(false)
		expect(state.isLoading).toBe(false)
		expect(state.error).toBe(null)
		expect(state.sessionChecked).toBe(false)
	})
})
