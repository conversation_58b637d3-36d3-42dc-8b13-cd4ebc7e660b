import type { Backend } from "@donorcare/backend/src"
import type {
	createCampaignDto,
	updateCampaignDto,
} from "@donorcare/backend/src/campaigns/campaigns.schema"
import { treaty } from "@elysiajs/eden"
import type { Static } from "elysia"

// Create the treaty client with proper base URL and authentication
const client = treaty<Backend>("http://localhost:3000", {
	fetch: {
		credentials: "include", // Include cookies for authentication
	},
})

// Campaign API functions with full type safety from backend
// GET /api/campaigns/my - Get organizer's campaigns
export const getMyCampaigns = () => client.api.campaigns.my.get()

// GET /api/campaigns/:id - Get campaign by ID (authenticated)
export const getCampaign = (id: string) => client.api.campaigns({ id }).get()

// GET /api/campaigns/public/:slug - Get public campaign by slug
export const getPublicCampaign = (slug: string) =>
	client.api.campaigns.public({ slug }).get()

// POST /api/campaigns - Create new campaign
export const createCampaign = (data: Static<typeof createCampaignDto>) =>
	client.api.campaigns.post(data)

// PUT /api/campaigns/:id - Update existing campaign
export const updateCampaign = (
	id: string,
	data: Static<typeof updateCampaignDto>,
) => client.api.campaigns({ id }).put(data)

// DELETE /api/campaigns/:id - Delete campaign
export const deleteCampaign = (id: string) =>
	client.api.campaigns({ id }).delete()

// PATCH /api/campaigns/:id/status - Toggle campaign status
export const toggleCampaignStatus = (id: string, isActive: boolean) =>
	client.api.campaigns({ id }).status.patch({ isActive })

// Export the client for advanced usage
export { client }
export { client as campaignsClient }

// Helper type exports for consumers
export type CampaignsClient = typeof client
export type BackendType = Backend
