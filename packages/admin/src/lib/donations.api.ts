import type {
	donationFiltersDto,
	updateDonationDto,
	updateStatusDto,
} from "@donorcare/backend/schemas"
import type { Backend } from "@donorcare/backend/src"
import { treaty } from "@elysiajs/eden"
import type { Static } from "elysia"

// Create the treaty client with proper base URL and authentication
const client = treaty<Backend>("http://localhost:3000", {
	fetch: {
		credentials: "include", // Include cookies for authentication
	},
})

// Donation API functions with full type safety from backend
// GET /api/donations - Get organizer's donations with filtering and pagination
export const getDonations = (filters?: Static<typeof donationFiltersDto>) =>
	client.api.donations.get(filters ? { query: filters } : { query: {} })

// GET /api/donations/:id - Get donation by ID (authenticated)
export const getDonation = (id: string) => client.api.donations({ id }).get()

// PUT /api/donations/:id - Update donation metadata
export const updateDonation = (
	id: string,
	data: Static<typeof updateDonationDto>,
) => client.api.donations({ id }).put(data)

// DELETE /api/donations/:id - Delete donation
export const deleteDonation = (id: string) =>
	client.api.donations({ id }).delete()

// PATCH /api/donations/:id/status - Update donation status
export const updateDonationStatus = (
	id: string,
	data: Static<typeof updateStatusDto>,
) => client.api.donations({ id }).status.patch(data)

// GET /api/donations/analytics - Get donation analytics
export const getDonationAnalytics = (
	filters?: Static<typeof donationFiltersDto>,
) =>
	client.api.donations.analytics.get(
		filters ? { query: filters } : { query: {} },
	)

// GET /api/donations/export - Export donations as CSV
export const exportDonations = (filters?: Static<typeof donationFiltersDto>) =>
	client.api.donations.export.get(filters ? { query: filters } : { query: {} })

// Export the client for advanced usage
export { client }
export { client as donationsClient }

// Helper type exports for consumers
export type DonationsClient = typeof client
export type BackendType = Backend
