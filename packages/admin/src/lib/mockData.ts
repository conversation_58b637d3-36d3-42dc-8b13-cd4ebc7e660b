// Mock data for DonorCARE FE - migrated from canvas_global_app
export type Party = {
	role: string
	name: string
	email?: string
	phone?: string
}

export type Invoice = {
	invoiceNo: string
	amount?: string
	date?: string
	approved?: string
}

export type Booking = {
	title: string
	bookingNo: string
	carrierBookingNo: string
	route: { from: string; to: string }
	status: string
	date: string
	poNumber: string
	remarks: string
	bookingType: string
	shipmentTerm: string
	shipmentType: string
	commodity: string
	portOfLoading: string
	placeOfReceipt: string
	eta: string
	portOfDischarge: string
	finalDestination: string
	etd: string
	vessel?: string
	fwdgReleasedCustomForms: string
	vgmUpdated: boolean
	carrierInvoices: Invoice[]
	carrierRate: string
	usdBuyRate: string
	customerRate: string
	usdSellRate: string
	profitPerUnitUSD: string
	profitTotalUSD: string
	profitPerUnitMYR: string
	profitTotalMYR: string
	containers: {
		containerNumber: string
		containerSize: string
		containerType: string
		containerVolume: string
		pickupMode: string
		pickupDepot: string
		pickupStatus: string
		pickupDate?: string
	}[]
	billOfLadingUpdated: string
	releasedToCustomer: string
	bookingConfirmed: string
	bookingConfirmedDescription: string
	bookingCreated: string
	paymentReceived?: string
	emptyStatus?: string
	ladenReturnStatus?: string
	parties: Party[]
	documents?: {
		file?: File
		type?: string
		fileName: string
		remarks?: string
		date: string
	}[]
	invoices?: {
		file?: File
		type?: string
		fileName: string
		remarks?: string
		date: string
	}[]
	fwdgInvoices: Invoice[]
	truckInvoices: Invoice[]
	cglSalesInvoices: Invoice[]
	priority?: number // 1-10, 10 is highest
	relatedBookings?: string[]
	truckNumber?: string
}

export const customers = [
	{
		role: "Customer",
		name: "Asia Log Logistics Sdn. Bhd.",
		email: "<EMAIL>",
		phone: "+60-3-1122-3344",
	},
	{
		role: "Customer",
		name: "Ajinoriki Food Industries (M) Sdn. Bhd.",
		email: "<EMAIL>",
		phone: "+60-4-5566-7788",
	},
	{
		role: "Customer",
		name: "Global Freight Solutions Pte. Ltd.",
		email: "<EMAIL>",
		phone: "+65-6789-0123",
	},
	{
		role: "Customer",
		name: "Oceanic Exports International",
		email: "<EMAIL>",
		phone: "+86-21-8765-4321",
	},
	{
		role: "Customer",
		name: "MegaTrade Distribution Sdn. Bhd.",
		email: "<EMAIL>",
		phone: "+60-7-9988-7766",
	},
]

export const salesPersons = [
	{
		role: "Salesperson",
		name: "Gary Lim",
		email: "<EMAIL>",
		phone: "+60-12-345-6789",
	},
	{
		role: "Salesperson",
		name: "John Tan",
		email: "<EMAIL>",
		phone: "+60-17-987-6543",
	},
	{
		role: "Salesperson",
		name: "Mark Wong",
		email: "<EMAIL>",
		phone: "+60-19-234-5678",
	},
]

export const carriers = [
	{
		role: "Carrier",
		name: "A.P. Moller - Maersk",
		email: "<EMAIL>",
		phone: "+45-3363-3363",
	},
	{
		role: "Carrier",
		name: "COSCO SHIPPING Lines Co., Ltd.",
		email: "<EMAIL>",
		phone: "+86-21-6596-6666",
	},
	{
		role: "Carrier",
		name: "Hapag-Lloyd AG",
		email: "<EMAIL>",
		phone: "+49-40-3001-0",
	},
]

export const mockBookings: Booking[] = [
	{
		title: "Asia Log Booking",
		bookingNo: "BK-2025-001",
		carrierBookingNo: "CB-2025-001",
		route: { from: "PEN", to: "NEW YORK" },
		status: "new",
		date: "2024-04-15",
		poNumber: "PO-2025-001",
		remarks: "Urgent shipment - high priority",
		bookingType: "TOTAL",
		shipmentTerm: "COLLECT",
		shipmentType: "FCL",
		commodity: "Rubberwood (HS: 441899)",
		portOfLoading: "Penang (PEN)",
		placeOfReceipt: "Penang Yard",
		eta: "2024-05-20",
		portOfDischarge: "New York (NYC)",
		finalDestination: "New York Warehouse",
		etd: "2024-04-15",
		vessel: "EVER BOARD 1541-063N",
		fwdgReleasedCustomForms: "Pending",
		vgmUpdated: false,
		carrierInvoices: [
			{ invoiceNo: "INV-001", date: "2024-03-15", approved: "2024-03-15" },
		],
		carrierRate: "1000",
		usdBuyRate: "4.5",
		customerRate: "1200",
		usdSellRate: "4.7",
		profitPerUnitUSD: "200",
		profitTotalUSD: "2000",
		profitPerUnitMYR: "900",
		profitTotalMYR: "9000",
		containers: [
			{
				containerNumber: "TCLU2052254",
				containerSize: "20ft GP 1.00 TEU",
				containerType: "GP",
				containerVolume: "1.00 TEU",
				pickupMode: "Road",
				pickupDepot: "PEN Terminal",
				pickupStatus: "Yet",
				pickupDate: "2024-04-15",
			},
		],
		billOfLadingUpdated: "Pending",
		releasedToCustomer: "Pending",
		bookingConfirmed: "2024-03-15",
		bookingConfirmedDescription: "Carrier has confirmed space allocation",
		bookingCreated: "2024-03-15",
		paymentReceived: "2024-03-15",
		parties: [customers[0], salesPersons[0], carriers[0]],
		documents: [],
		fwdgInvoices: [],
		truckInvoices: [],
		cglSalesInvoices: [],
		priority: 9, // High priority
	},
	{
		title: "Ajinoriki Booking",
		bookingNo: "BK-2025-002",
		carrierBookingNo: "",
		route: { from: "PEN", to: "QINGDAO" },
		status: "in transit",
		date: "2024-04-14",
		poNumber: "PO-2025-002",
		remarks: "Handle with care - fragile electronics",
		bookingType: "FOB",
		shipmentTerm: "PREPAID",
		shipmentType: "LCL",
		commodity: "Electronics",
		portOfLoading: "Penang (PEN)",
		placeOfReceipt: "Penang Yard",
		eta: "2024-05-22",
		portOfDischarge: "Qingdao (QDG)",
		finalDestination: "Qingdao Warehouse",
		etd: "2024-04-16",
		vessel: "COSCO STAR 2024-001",
		fwdgReleasedCustomForms: "YetToRelease",
		vgmUpdated: false,
		carrierInvoices: [
			{ invoiceNo: "INV-002", date: "2024-03-15", approved: "2024-03-15" },
		],
		carrierRate: "800",
		usdBuyRate: "4.4",
		customerRate: "950",
		usdSellRate: "4.6",
		profitPerUnitUSD: "150",
		profitTotalUSD: "1500",
		profitPerUnitMYR: "660",
		profitTotalMYR: "6600",
		containers: [
			{
				containerNumber: "TCLU2052255",
				containerSize: "20ft GP 1.00 TEU",
				containerType: "GP",
				containerVolume: "1.00 TEU",
				pickupMode: "Road",
				pickupDepot: "PEN Terminal",
				pickupStatus: "Yet",
				pickupDate: "2024-04-15",
			},
			{
				containerNumber: "CMAU8765432",
				containerSize: "40ft GP 2.00 TEU",
				containerType: "GP",
				containerVolume: "2.00 TEU",
				pickupMode: "Sea",
				pickupDepot: "QDG Port",
				pickupStatus: "Completed",
				pickupDate: "2024-04-18",
			},
		],
		billOfLadingUpdated: "2024-03-15",
		releasedToCustomer: "2024-03-15",
		bookingConfirmed: "Pending",
		bookingConfirmedDescription: "Awaiting carrier space allocation response",
		bookingCreated: "2024-03-15",
		parties: [customers[1], salesPersons[1], carriers[1]],
		documents: [],
		fwdgInvoices: [],
		truckInvoices: [],
		cglSalesInvoices: [],
		priority: 8, // High priority
	},
	{
		title: "Global Freight Booking",
		bookingNo: "BK-2025-003",
		carrierBookingNo: "CB-2025-003",
		route: { from: "SINGAPORE", to: "LOS ANGELES" },
		status: "departed",
		date: "2024-03-10",
		poNumber: "PO-2025-003",
		remarks: "Standard shipment",
		bookingType: "FREIGHT",
		shipmentTerm: "MBL-P / HBL-C",
		shipmentType: "FCL",
		commodity: "Textiles",
		portOfLoading: "Singapore (SGP)",
		placeOfReceipt: "SGP Port",
		eta: "2024-04-10",
		portOfDischarge: "Los Angeles (LAX)",
		finalDestination: "LA Warehouse",
		etd: "2024-03-12",
		vessel: "PACIFIC QUEEN 2024-003",
		fwdgReleasedCustomForms: "Complete",
		vgmUpdated: true,
		carrierInvoices: [
			{ invoiceNo: "INV-003", date: "2024-03-15", approved: "2024-03-15" },
		],
		carrierRate: "1500",
		usdBuyRate: "4.3",
		customerRate: "1800",
		usdSellRate: "4.8",
		profitPerUnitUSD: "300",
		profitTotalUSD: "3000",
		profitPerUnitMYR: "1290",
		profitTotalMYR: "12900",
		containers: [
			{
				containerNumber: "SEGU3344556",
				containerSize: "40ft HC 2.00 TEU",
				containerType: "HC",
				containerVolume: "2.00 TEU",
				pickupMode: "Road",
				pickupDepot: "SGP Port",
				pickupStatus: "Delivered",
				pickupDate: "2024-04-10",
			},
		],
		billOfLadingUpdated: "2024-03-15",
		releasedToCustomer: "2024-03-15",
		bookingConfirmed: "2024-03-15",
		bookingConfirmedDescription: "Carrier has confirmed space allocation",
		bookingCreated: "2024-03-15",
		parties: [customers[2], salesPersons[2], carriers[2]],
		documents: [],
		fwdgInvoices: [],
		truckInvoices: [],
		cglSalesInvoices: [],
		priority: 3, // Low priority
	},
	{
		title: "Oceanic Exports Booking",
		bookingNo: "BK-2025-004",
		carrierBookingNo: "CB-2025-004",
		route: { from: "PORT KLANG", to: "HAMBURG" },
		status: "draft",
		date: "2024-05-01",
		poNumber: "PO-2025-004",
		remarks: "Pending final approval from management",
		bookingType: "SOC",
		shipmentTerm: "TBA",
		shipmentType: "IMPORT",
		commodity: "Machinery",
		portOfLoading: "Port Klang (PKG)",
		placeOfReceipt: "PKG Yard",
		eta: "2024-06-05",
		portOfDischarge: "Hamburg (HAM)",
		finalDestination: "Hamburg Facility",
		etd: "2024-05-03",
		vessel: "ATLANTIC STAR 2024-004",
		fwdgReleasedCustomForms: "Shifting",
		vgmUpdated: false,
		carrierInvoices: [
			{ invoiceNo: "INV-004", date: "2024-03-15", approved: "2024-03-15" },
		],
		carrierRate: "2000",
		usdBuyRate: "4.6",
		customerRate: "2300",
		usdSellRate: "4.9",
		profitPerUnitUSD: "300",
		profitTotalUSD: "3000",
		profitPerUnitMYR: "1380",
		profitTotalMYR: "13800",
		containers: [
			{
				containerNumber: "TEMU9988776",
				containerSize: "40ft GP 2.00 TEU",
				containerType: "GP",
				containerVolume: "2.00 TEU",
				pickupMode: "Sea",
				pickupDepot: "PKG Yard",
				pickupStatus: "Pending",
				pickupDate: "2024-05-03",
			},
		],
		billOfLadingUpdated: "2024-03-15",
		releasedToCustomer: "2024-03-15",
		bookingConfirmed: "2024-03-15",
		bookingConfirmedDescription: "Carrier has confirmed space allocation",
		bookingCreated: "2024-03-15",
		parties: [customers[3], salesPersons[0], carriers[0]],
		documents: [],
		fwdgInvoices: [],
		truckInvoices: [],
		cglSalesInvoices: [],
		priority: 7, // High priority
	},
	{
		title: "MegaTrade Booking",
		bookingNo: "BK-2025-005",
		carrierBookingNo: "CB-2025-005",
		route: { from: "TOKYO", to: "SYDNEY" },
		status: "cancelled",
		date: "2024-04-20",
		poNumber: "PO-2025-005",
		remarks: "Client requested cancellation due to market conditions",
		bookingType: "FWDG",
		shipmentTerm: "NIL",
		shipmentType: "AIR",
		commodity: "Pharmaceuticals",
		portOfLoading: "Tokyo (TYO)",
		placeOfReceipt: "Tokyo Airport",
		eta: "2024-04-25",
		portOfDischarge: "Sydney (SYD)",
		finalDestination: "Sydney Medical Center",
		etd: "2024-04-21",
		fwdgReleasedCustomForms: "YetToRelease",
		vgmUpdated: false,
		carrierInvoices: [
			{ invoiceNo: "INV-005", date: "2024-03-15", approved: "2024-03-15" },
		],
		carrierRate: "500",
		usdBuyRate: "4.2",
		customerRate: "600",
		usdSellRate: "4.5",
		profitPerUnitUSD: "100",
		profitTotalUSD: "1000",
		profitPerUnitMYR: "420",
		profitTotalMYR: "4200",
		containers: [
			{
				containerNumber: "ECMU4455667",
				containerSize: "45ft HC 2.50 TEU",
				containerType: "HC",
				containerVolume: "2.50 TEU",
				pickupMode: "Air",
				pickupDepot: "Tokyo Airport",
				pickupStatus: "Not Applicable",
				pickupDate: "",
			},
		],
		billOfLadingUpdated: "2024-03-15",
		releasedToCustomer: "2024-03-15",
		bookingConfirmed: "2024-03-15",
		bookingConfirmedDescription: "Carrier has confirmed space allocation",
		bookingCreated: "2024-03-15",
		parties: [customers[4], salesPersons[2], carriers[1]],
		documents: [],
		fwdgInvoices: [],
		truckInvoices: [],
		cglSalesInvoices: [],
		priority: 5, // Medium priority
	},
	// Additional bookings for better testing
	{
		title: "Tech Solutions Booking",
		bookingNo: "BK-2025-006",
		carrierBookingNo: "CB-2025-006",
		route: { from: "PEN", to: "ROTTERDAM" },
		status: "new",
		date: "2024-04-22",
		poNumber: "PO-2025-006",
		remarks: "Temperature-controlled shipment required",
		bookingType: "REEFER",
		shipmentTerm: "PREPAID",
		shipmentType: "FCL",
		commodity: "Frozen Foods",
		portOfLoading: "Penang (PEN)",
		placeOfReceipt: "PEN Cold Storage",
		eta: "2024-05-25",
		portOfDischarge: "Rotterdam (RTM)",
		finalDestination: "Rotterdam Cold Chain Facility",
		etd: "2024-04-24",
		vessel: "ARCTIC BREEZE 2024-006",
		fwdgReleasedCustomForms: "Pending",
		vgmUpdated: false,
		carrierInvoices: [
			{ invoiceNo: "INV-006", date: "2024-03-20", approved: "Pending" },
		],
		carrierRate: "1800",
		usdBuyRate: "4.5",
		customerRate: "2200",
		usdSellRate: "4.8",
		profitPerUnitUSD: "400",
		profitTotalUSD: "4000",
		profitPerUnitMYR: "1800",
		profitTotalMYR: "18000",
		containers: [
			{
				containerNumber: "RFCU7788990",
				containerSize: "40ft RF 2.00 TEU",
				containerType: "RF",
				containerVolume: "2.00 TEU",
				pickupMode: "Road",
				pickupDepot: "PEN Cold Storage",
				pickupStatus: "Scheduled",
				pickupDate: "2024-04-24",
			},
		],
		billOfLadingUpdated: "Pending",
		releasedToCustomer: "Pending",
		bookingConfirmed: "Pending",
		bookingConfirmedDescription: "Awaiting reefer equipment allocation",
		bookingCreated: "2024-03-20",
		parties: [customers[0], salesPersons[1], carriers[2]],
		documents: [],
		fwdgInvoices: [],
		truckInvoices: [],
		cglSalesInvoices: [],
		priority: 6, // Medium priority
	},
]

// Activity feed for dashboard
export const recentActivity = [
	{
		id: "1",
		icon: "ship",
		title: "Vessel EVER BOARD departed PEN",
		description: "Container TCLU2052254 now in transit",
		timestamp: "2 hours ago",
		type: "departure",
	},
	{
		id: "2",
		icon: "check-circle",
		title: "Carrier confirmed space for BK-2025-004",
		description: "Hamburg route booking now confirmed",
		timestamp: "4 hours ago",
		type: "success",
	},
	{
		id: "3",
		icon: "file-text",
		title: "BL ready for collection - BK-2025-002",
		description: "Documents available at PEN office",
		timestamp: "6 hours ago",
		type: "warning",
	},
	{
		id: "4",
		icon: "alert-triangle",
		title: "VGM deadline approaching - BK-2025-006",
		description: "Submit VGM within 24 hours",
		timestamp: "8 hours ago",
		type: "alert",
	},
]

// Helper functions
export const loadBookingsFromStorage = (): Booking[] => {
	if (typeof window === "undefined") return mockBookings

	try {
		const stored = localStorage.getItem("canvas_global_bookings")
		return stored ? JSON.parse(stored) : mockBookings
	} catch {
		return mockBookings
	}
}

export const saveBookingsToStorage = (bookings: Booking[]) => {
	if (typeof window === "undefined") return

	try {
		localStorage.setItem("canvas_global_bookings", JSON.stringify(bookings))
	} catch (error) {
		console.error("Failed to save bookings:", error)
	}
}
