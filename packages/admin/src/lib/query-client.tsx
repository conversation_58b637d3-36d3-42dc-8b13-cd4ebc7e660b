import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import type { PropsWithChildren } from "react"

// Create a client with optimized defaults
export const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			// Cache data for 5 minutes by default
			staleTime: 1000 * 60 * 5,
			// Keep unused data in cache for 10 minutes
			gcTime: 1000 * 60 * 10,
			// Retry failed requests 3 times with exponential backoff
			retry: (failureCount, error) => {
				// Don't retry on 4xx errors (client errors)
				if (error && "status" in error && typeof error.status === "number") {
					if (error.status >= 400 && error.status < 500) {
						return false
					}
				}
				return failureCount < 3
			},
			retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
			// Refetch on window focus in production, but not in development
			refetchOnWindowFocus: process.env.NODE_ENV === "production",
			// Don't refetch on reconnect by default (let user control this)
			refetchOnReconnect: true,
		},
		mutations: {
			// Retry mutations once on failure
			retry: 1,
			retryDelay: 1000,
		},
	},
})

// Query provider component
export function QueryProvider({ children }: PropsWithChildren) {
	return (
		<QueryClientProvider client={queryClient}>
			{children}
			{process.env.NODE_ENV === "development" && (
				<ReactQueryDevtools initialIsOpen={false} position="bottom" />
			)}
		</QueryClientProvider>
	)
}

// Query keys factory for consistent key management
export const queryKeys = {
	// Campaign keys
	campaigns: {
		all: ["campaigns"] as const,
		lists: () => [...queryKeys.campaigns.all, "list"] as const,
		list: (filters?: Record<string, unknown>) =>
			[...queryKeys.campaigns.lists(), { filters }] as const,
		details: () => [...queryKeys.campaigns.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.campaigns.details(), id] as const,
		public: (slug: string) =>
			[...queryKeys.campaigns.all, "public", slug] as const,
	},
	// Donation keys
	donations: {
		all: ["donations"] as const,
		lists: () => [...queryKeys.donations.all, "list"] as const,
		list: (filters?: Record<string, unknown>) =>
			[...queryKeys.donations.lists(), { filters }] as const,
		details: () => [...queryKeys.donations.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.donations.details(), id] as const,
		analytics: (filters?: Record<string, unknown>) =>
			[...queryKeys.donations.all, "analytics", { filters }] as const,
	},
	// Donor keys
	donors: {
		all: ["donors"] as const,
		lists: () => [...queryKeys.donors.all, "list"] as const,
		list: (filters?: Record<string, unknown>) =>
			[...queryKeys.donors.lists(), { filters }] as const,
		details: () => [...queryKeys.donors.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.donors.details(), id] as const,
		tags: () => [...queryKeys.donors.all, "tags"] as const,
		notes: (donorId: string) =>
			[...queryKeys.donors.all, "notes", donorId] as const,
	},
	// Organizer keys (admin only)
	organizers: {
		all: ["organizers"] as const,
		lists: () => [...queryKeys.organizers.all, "list"] as const,
		list: (filters?: Record<string, unknown>) =>
			[...queryKeys.organizers.lists(), { filters }] as const,
		details: () => [...queryKeys.organizers.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.organizers.details(), id] as const,
	},
} as const

// Mutation keys factory
export const mutationKeys = {
	campaigns: {
		create: ["campaigns", "create"] as const,
		update: (id: string) => ["campaigns", "update", id] as const,
		delete: (id: string) => ["campaigns", "delete", id] as const,
		toggleStatus: (id: string) => ["campaigns", "toggleStatus", id] as const,
	},
	donations: {
		update: (id: string) => ["donations", "update", id] as const,
		delete: (id: string) => ["donations", "delete", id] as const,
		updateStatus: (id: string) => ["donations", "updateStatus", id] as const,
		export: ["donations", "export"] as const,
	},
	donors: {
		create: ["donors", "create"] as const,
		update: (id: string) => ["donors", "update", id] as const,
		delete: (id: string) => ["donors", "delete", id] as const,
		export: ["donors", "export"] as const,
		createTag: ["donors", "tags", "create"] as const,
		updateTag: (id: string) => ["donors", "tags", "update", id] as const,
		deleteTag: (id: string) => ["donors", "tags", "delete", id] as const,
		assignTags: (donorId: string) =>
			["donors", "tags", "assign", donorId] as const,
		createNote: (donorId: string) =>
			["donors", "notes", "create", donorId] as const,
		updateNote: (donorId: string, noteId: string) =>
			["donors", "notes", "update", donorId, noteId] as const,
		deleteNote: (donorId: string, noteId: string) =>
			["donors", "notes", "delete", donorId, noteId] as const,
	},
	organizers: {
		create: ["organizers", "create"] as const,
		delete: (id: string) => ["organizers", "delete", id] as const,
		impersonate: (id: string) => ["organizers", "impersonate", id] as const,
		export: ["organizers", "export"] as const,
	},
} as const

// Helper function to invalidate all campaign-related queries
export const invalidateCampaigns = () => {
	return queryClient.invalidateQueries({
		queryKey: queryKeys.campaigns.all,
	})
}

// Helper function to invalidate specific campaign
export const invalidateCampaign = (id: string) => {
	return Promise.all([
		queryClient.invalidateQueries({
			queryKey: queryKeys.campaigns.detail(id),
		}),
		queryClient.invalidateQueries({
			queryKey: queryKeys.campaigns.lists(),
		}),
	])
}

// Helper function to invalidate all donation-related queries
export const invalidateDonations = () => {
	return queryClient.invalidateQueries({
		queryKey: queryKeys.donations.all,
	})
}

// Helper function to invalidate specific donation
export const invalidateDonation = (id: string) => {
	return Promise.all([
		queryClient.invalidateQueries({
			queryKey: queryKeys.donations.detail(id),
		}),
		queryClient.invalidateQueries({
			queryKey: queryKeys.donations.lists(),
		}),
		queryClient.invalidateQueries({
			queryKey: queryKeys.donations.all,
		}),
	])
}

// Helper function to invalidate all donor-related queries
export const invalidateDonors = () => {
	return queryClient.invalidateQueries({
		queryKey: queryKeys.donors.all,
	})
}

// Helper function to invalidate specific donor
export const invalidateDonor = (id: string) => {
	return Promise.all([
		queryClient.invalidateQueries({
			queryKey: queryKeys.donors.detail(id),
		}),
		queryClient.invalidateQueries({
			queryKey: queryKeys.donors.lists(),
		}),
	])
}

// Helper function to invalidate all organizer-related queries
export const invalidateOrganizers = () => {
	return queryClient.invalidateQueries({
		queryKey: queryKeys.organizers.all,
	})
}

// Helper function to invalidate specific organizer
export const invalidateOrganizer = (id: string) => {
	return Promise.all([
		queryClient.invalidateQueries({
			queryKey: queryKeys.organizers.detail(id),
		}),
		queryClient.invalidateQueries({
			queryKey: queryKeys.organizers.lists(),
		}),
	])
}
