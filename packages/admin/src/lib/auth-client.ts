import { adminClient } from "better-auth/client/plugins"
import { createAuth<PERSON>lient } from "better-auth/react"
import type { AuthError } from "../types/auth"
import { AUTH_CONFIG } from "./auth-config"
import { ac, adminRole, userRole, organizerRole } from "@donorcare/backend/auth"

// Get the backend URL from environment or default to localhost
const getBackendURL = () => {
	// In production, this would come from environment variables
	// For development, we use the backend port (3000 for backend, 3001 for frontend on different processes)
	if (typeof window !== "undefined") {
		// Client-side: use environment variable or default
		return (window as any).__VITE_BACKEND_URL__ || AUTH_CONFIG.BACKEND_URL
	}
	// Server-side or build time: use default
	return AUTH_CONFIG.BACKEND_URL
}

// Create the better-auth client with enhanced configuration
export const authClient = createAuthClient({
	baseURL: getBackendURL(),
	fetchOptions: {
		credentials: "include", // Required for cookie-based sessions
	},
	plugins: [
		adminClient({
			ac,
			roles: {
				admin: admin<PERSON><PERSON>,
				user: user<PERSON><PERSON>,
				organizer: organizer<PERSON><PERSON>,
			},
		}),
	],
})

// Enhanced session validation and refresh utilities
export class AuthClientService {
	private static instance: AuthClientService
	private sessionRefreshPromise: Promise<boolean> | null = null
	private backgroundCheckInterval: NodeJS.Timeout | null = null

	static getInstance(): AuthClientService {
		if (!AuthClientService.instance) {
			AuthClientService.instance = new AuthClientService()
		}
		return AuthClientService.instance
	}

	/**
	 * Start background session validation
	 */
	startBackgroundValidation(onSessionExpired?: () => void): void {
		if (this.backgroundCheckInterval) {
			return // Already running
		}

		this.backgroundCheckInterval = setInterval(async () => {
			const isValid = await this.validateSession()
			if (!isValid && onSessionExpired) {
				onSessionExpired()
			}
		}, AUTH_CONFIG.VALIDATION.BACKGROUND_CHECK_INTERVAL)
	}

	/**
	 * Stop background session validation
	 */
	stopBackgroundValidation(): void {
		if (this.backgroundCheckInterval) {
			clearInterval(this.backgroundCheckInterval)
			this.backgroundCheckInterval = null
		}
	}

	/**
	 * Validates the current session and refreshes if needed
	 * Returns true if session is valid, false otherwise
	 */
	async validateSession(): Promise<boolean> {
		return await this.withRetry(async () => {
			try {
				// Add timeout to prevent hanging
				const timeoutPromise = new Promise<never>((_, reject) => {
					setTimeout(
						() => reject(new Error("Session validation timeout")),
						3000,
					) // 3 second timeout
				})

				const sessionPromise = authClient.getSession()

				// Race between session check and timeout
				const session = await Promise.race([sessionPromise, timeoutPromise])

				if (session.data?.user) {
					return true
				}

				// If no session, try to refresh
				return await this.refreshSession()
			} catch (error) {
				console.error("Session validation failed:", error)
				return false
			}
		})
	}

	/**
	 * Refreshes the current session
	 * Prevents multiple concurrent refresh attempts
	 */
	async refreshSession(): Promise<boolean> {
		// If a refresh is already in progress, wait for it
		if (this.sessionRefreshPromise) {
			return await this.sessionRefreshPromise
		}

		this.sessionRefreshPromise = this._performRefresh()

		try {
			const result = await this.sessionRefreshPromise
			return result
		} finally {
			this.sessionRefreshPromise = null
		}
	}

	private async _performRefresh(): Promise<boolean> {
		try {
			// Better-auth handles session refresh automatically via cookies
			// We just need to check if the session is still valid
			const session = await authClient.getSession()
			return !!session.data?.user
		} catch (error) {
			console.error("Session refresh failed:", error)
			return false
		}
	}

	/**
	 * Enhanced sign in with better error handling
	 */
	async signIn(
		email: string,
		password: string,
	): Promise<{ success: boolean; error?: AuthError }> {
		try {
			const result = await authClient.signIn.email({
				email,
				password,
			})

			if (result.error) {
				console.error("Sign in error:", result.error, result.error.message)

				return {
					success: false,
					error: this.mapAuthError(result.error),
				}
			}

			if (result.data?.user) {
				return { success: true }
			}

			return {
				success: false,
				error: {
					code: AUTH_CONFIG.ERROR_CODES.SIGN_IN_ERROR,
					message: "Authentication failed - no user data received",
				},
			}
		} catch (error) {
			console.error("Sign in catch:", error)

			return {
				success: false,
				error: this.mapNetworkError(error),
			}
		}
	}

	/**
	 * Enhanced sign out with error handling
	 */
	async signOut(): Promise<{ success: boolean; error?: AuthError }> {
		try {
			await authClient.signOut()
			return { success: true }
		} catch (error) {
			// Log the error but still return success for security
			console.error("Sign out error:", error)
			return { success: true } // Always succeed for security
		}
	}

	/**
	 * Get current session with error handling
	 */
	async getSession(): Promise<{ user: any; session: any } | null> {
		try {
			const session = await authClient.getSession()
			return session.data || null
		} catch (error) {
			console.error("Get session error:", error)
			return null
		}
	}

	/**
	 * Maps better-auth errors to our AuthError format
	 */
	private mapAuthError(error: any): AuthError {
		const message = error.message || "Authentication failed"

		// Map common better-auth error messages to our error codes
		if (
			message.toLowerCase().includes("invalid") ||
			message.toLowerCase().includes("credentials")
		) {
			return {
				code: AUTH_CONFIG.ERROR_CODES.INVALID_CREDENTIALS,
				message: "Invalid email or password",
			}
		}

		if (
			message.toLowerCase().includes("expired") ||
			message.toLowerCase().includes("session")
		) {
			return {
				code: AUTH_CONFIG.ERROR_CODES.SESSION_EXPIRED,
				message: "Your session has expired. Please sign in again.",
			}
		}

		return {
			code: AUTH_CONFIG.ERROR_CODES.SIGN_IN_ERROR,
			message,
		}
	}

	/**
	 * Maps network errors to our AuthError format
	 */
	private mapNetworkError(error: any): AuthError {
		if (error instanceof TypeError && error.message.includes("fetch")) {
			return {
				code: AUTH_CONFIG.ERROR_CODES.NETWORK_ERROR,
				message: "Network error. Please check your connection and try again.",
			}
		}

		return {
			code: AUTH_CONFIG.ERROR_CODES.NETWORK_ERROR,
			message:
				error instanceof Error ? error.message : "Network error occurred",
		}
	}

	/**
	 * Retry utility for network operations
	 */
	private async withRetry<T>(
		operation: () => Promise<T>,
		maxAttempts: number = AUTH_CONFIG.VALIDATION.MAX_RETRY_ATTEMPTS,
	): Promise<T> {
		let lastError: any

		for (let attempt = 1; attempt <= maxAttempts; attempt++) {
			try {
				return await operation()
			} catch (error) {
				lastError = error

				// Don't retry on authentication errors, only network errors
				if (attempt === maxAttempts || !this.isRetryableError(error)) {
					break
				}

				// Wait before retrying
				await new Promise((resolve) =>
					setTimeout(resolve, AUTH_CONFIG.VALIDATION.RETRY_DELAY * attempt),
				)
			}
		}

		throw lastError
	}

	/**
	 * Determines if an error is retryable
	 */
	private isRetryableError(error: any): boolean {
		// Retry network errors but not authentication errors
		return (
			error instanceof TypeError ||
			(error instanceof Error && error.message.includes("fetch")) ||
			(error instanceof Error && error.message.includes("network"))
		)
	}

	/**
	 * Cleanup method to stop background processes
	 */
	cleanup(): void {
		this.stopBackgroundValidation()
		this.sessionRefreshPromise = null
	}
}

// Export singleton instance
export const authService = AuthClientService.getInstance()

// Export types for better TypeScript integration
export type AuthClient = typeof authClient

// Re-export types from centralized location
export type { AuthError } from "../types/auth"

// Export the admin methods for impersonation
export const {
	admin: { impersonateUser, stopImpersonating },
} = authClient
