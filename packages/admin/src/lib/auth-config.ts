// Authentication configuration constants and utilities

export const AUTH_CONFIG = {
	// Backend API URL - will be configurable via environment variables
	BACKEND_URL: "http://localhost:3000",

	// Session configuration
	SESSION: {
		// Cookie-based sessions with HTTP-only cookies for security
		CREDENTIALS: "include" as const,

		// Session timeout (handled by backend)
		TIMEOUT_MS: 24 * 60 * 60 * 1000, // 24 hours
	},

	// Impersonation configuration
	IMPERSONATION: {
		// Duration of impersonation sessions in seconds
		SESSION_DURATION: 60 * 60 * 24, // 24 hours

		// Message to show when a user is banned
		BANNED_USER_MESSAGE:
			"You have been banned from this application. Please contact support if you believe this is an error.",

		// Roles that can impersonate users
		ADMIN_ROLES: ["admin"],
	},

	// Error codes that the frontend should handle
	ERROR_CODES: {
		INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
		SESSION_EXPIRED: "SESSION_EXPIRED",
		NETWORK_ERROR: "NETWORK_ERROR",
		SIGN_IN_ERROR: "SIGN_IN_ERROR",
		SIGN_OUT_ERROR: "SIGN_OUT_ERROR",
		SESSION_VALIDATION_ERROR: "SESSION_VALIDATION_ERROR",
		REFRESH_ERROR: "REFRESH_ERROR",
		IMPERSONATION_ERROR: "IMPERSONATION_ERROR",
	},

	// Session validation settings
	VALIDATION: {
		// How often to validate session in background (milliseconds)
		BACKGROUND_CHECK_INTERVAL: 5 * 60 * 1000, // 5 minutes

		// Retry attempts for failed session validation
		MAX_RETRY_ATTEMPTS: 3,

		// Delay between retry attempts (milliseconds)
		RETRY_DELAY: 1000, // 1 second
	},

	// Routes
	ROUTES: {
		LOGIN: "/login",
		DASHBOARD: "/",
		PROTECTED_PREFIX: "/_authenticated",
	},
} as const

// Type-safe error code type
export type AuthErrorCode = keyof typeof AUTH_CONFIG.ERROR_CODES

// Validation helpers
export const isValidEmail = (email: string): boolean => {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
	return emailRegex.test(email)
}

export const isValidPassword = (password: string): boolean => {
	// Minimum 8 characters - backend will enforce stronger requirements
	return password.length >= 8
}

// Environment detection
export const isDevelopment = () => {
	return (
		process.env.NODE_ENV === "development" ||
		(typeof window !== "undefined" && window.location.hostname === "localhost")
	)
}

export const isProduction = () => {
	return process.env.NODE_ENV === "production"
}
