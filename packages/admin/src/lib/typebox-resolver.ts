import { Value } from "@sinclair/typebox/value"

// TypeBox error type mapping
const getErrorTypeKey = (error: any): string | null => {
	// Check if the error has minLength constraint and the string is too short
	if (error.schema?.minLength !== undefined && error.type === 52) {
		return "minLength"
	}
	// Check if the error has maxLength constraint and the string is too long
	if (error.schema?.maxLength !== undefined && error.type === 53) {
		return "maxLength"
	}
	// Check if the error has pattern constraint and doesn't match
	if (error.schema?.pattern !== undefined && error.type === 54) {
		return "pattern"
	}
	return null
}

// Custom TypeBox resolver with better error messages
export function createTypeboxResolver(schema: any) {
	return (values: any) => {
		const result = Value.Check(schema, values)

		if (result) {
			return { values, errors: {} }
		}

		const errors: Record<string, any> = {}
		const errorIterator = Value.Errors(schema, values)

		for (const error of errorIterator) {
			const path = error.path.replace(/^\//, "") // Remove leading slash

			// Check for custom error messages in schema
			let customMessage = null
			const errorMessages = error.schema?.errorMessages

			if (errorMessages && typeof errorMessages === "object") {
				// Map numeric error type to string key
				const errorTypeKey = getErrorTypeKey(error)
				if (errorTypeKey) {
					customMessage = errorMessages[errorTypeKey]
				}
			}

			errors[path] = {
				type: error.type,
				message: customMessage || error.schema?.error || error.message,
			}
		}

		return { values: {}, errors }
	}
}
