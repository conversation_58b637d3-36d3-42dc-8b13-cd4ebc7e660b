import { create } from "zustand"
import { devtools, persist } from "zustand/middleware"
import type { AuthError, User } from "../types/auth"
import { sessionManager } from "./auth/session-manager"
import { authService, impersonateUser, stopImpersonating } from "./auth-client"

// Authentication state interface
interface AuthState {
	// State
	user: User | null
	isAuthenticated: boolean
	isLoading: boolean
	error: AuthError | null
	sessionChecked: boolean
	isImpersonating: boolean

	// Actions
	signIn: (email: string, password: string) => Promise<void>
	signOut: () => Promise<void>
	clearError: () => void
	setLoading: (loading: boolean) => void
	setError: (error: AuthError | null) => void
	setUser: (user: User | null) => void

	// Session management
	refreshSession: () => Promise<void>
	checkSession: () => Promise<boolean>
	initializeAuth: () => Promise<void>
	restoreSession: () => Promise<boolean>
	handleSessionExpiration: () => Promise<void>
	cleanup: () => void

	// Impersonation
	startImpersonation: (targetUserId: string) => Promise<void>
	stopImpersonation: () => Promise<void>
	setImpersonating: (isImpersonating: boolean) => void
}

export const useAuthStore = create<AuthState>()(
	devtools(
		persist(
			(set, get) => ({
				// Initial state
				user: null,
				isAuthenticated: false,
				isLoading: false,
				error: null,
				sessionChecked: false,
				isImpersonating: false,

				// Sign in action
				signIn: async (email: string, password: string) => {
					try {
						set({ isLoading: true, error: null })

						const result = await authService.signIn(email, password)

						if (!result.success) {
							console.error("Sign in failed:", result.error)
							set({
								error: result.error || {
									code: "SIGN_IN_ERROR",
									message: "Authentication failed",
								},
								isLoading: false,
								isAuthenticated: false,
								user: null,
							})
							throw new Error(result.error?.message || "Authentication failed")
						}

						// Get the updated session after successful sign in
						const sessionData = await authService.getSession()
						if (sessionData?.user) {
							console.log("Auth store: Setting authenticated state", {
								user: sessionData.user,
								timestamp: new Date().toISOString(),
							})

							set({
								user: sessionData.user as User,
								isAuthenticated: true,
								isLoading: false,
								error: null,
								sessionChecked: true,
							})

							console.log("Auth store: State updated, isAuthenticated:", true)

							// Initialize session manager if not already done
							if (!sessionManager.initialized) {
								await sessionManager.initialize()
							}
						} else {
							throw new Error("Failed to retrieve user session after sign in")
						}
					} catch (error) {
						const authError: AuthError = {
							code: "SIGN_IN_ERROR",
							message:
								error instanceof Error ? error.message : "Sign in failed",
						}
						set({
							error: authError,
							isLoading: false,
							isAuthenticated: false,
							user: null,
						})
						throw error
					}
				},

				// Sign out action
				signOut: async () => {
					try {
						set({ isLoading: true, error: null })

						// Signal logout to other tabs
						sessionManager.signalLogout()

						// Stop background session monitoring
						authService.stopBackgroundValidation()

						const result = await authService.signOut()

						// Always clear local state regardless of server response for security
						set({
							user: null,
							isAuthenticated: false,
							isLoading: false,
							error: result.error || null,
							sessionChecked: true,
						})

						// Redirect to login page after successful logout (Requirement 3.2)
						if (typeof window !== "undefined") {
							window.location.href = "/login"
						}
					} catch (_error) {
						// Even if sign out fails on server, clear local state for security
						sessionManager.signalLogout()
						authService.stopBackgroundValidation()
						set({
							user: null,
							isAuthenticated: false,
							isLoading: false,
							error: null,
							sessionChecked: true,
						})

						// Redirect to login page even if logout fails (Requirement 3.3)
						if (typeof window !== "undefined") {
							window.location.href = "/login"
						}
					}
				},

				// Clear error
				clearError: () => {
					set({ error: null })
				},

				// Set loading state
				setLoading: (loading: boolean) => {
					set({ isLoading: loading })
				},

				// Set error
				setError: (error: AuthError | null) => {
					set({ error })
				},

				// Set user
				setUser: (user: User | null) => {
					set({
						user,
						isAuthenticated: !!user,
					})
				},

				// Set impersonating state
				setImpersonating: (isImpersonating: boolean) => {
					set({ isImpersonating })
				},

				// Refresh session
				refreshSession: async () => {
					try {
						const isValid = await authService.refreshSession()

						if (isValid) {
							const sessionData = await authService.getSession()
							if (sessionData?.user) {
								// Check if this is an impersonation session
								const isImpersonationSession =
									!!sessionData.session?.impersonatedBy

								set({
									user: sessionData.user as User,
									isAuthenticated: true,
									error: null,
									isImpersonating: isImpersonationSession,
								})
							} else {
								set({
									user: null,
									isAuthenticated: false,
									isImpersonating: false,
								})
							}
						} else {
							set({
								user: null,
								isAuthenticated: false,
								isImpersonating: false,
							})
						}
					} catch (_error) {
						set({
							user: null,
							isAuthenticated: false,
							isImpersonating: false,
						})
					}
				},

				// Check session
				checkSession: async () => {
					try {
						set({ isLoading: true })

						// Add timeout to prevent hanging
						const timeoutPromise = new Promise<boolean>((_, reject) => {
							setTimeout(() => reject(new Error("Session check timeout")), 4000) // 4 second timeout
						})

						const sessionCheckPromise = authService.validateSession()

						// Race between session check and timeout
						const isValid = await Promise.race([
							sessionCheckPromise,
							timeoutPromise,
						])

						if (isValid) {
							const sessionData = await authService.getSession()
							if (sessionData?.user) {
								// Check if this is an impersonation session
								const isImpersonationSession =
									!!sessionData.session?.impersonatedBy

								set({
									user: sessionData.user as User,
									isAuthenticated: true,
									sessionChecked: true,
									isLoading: false,
									isImpersonating: isImpersonationSession,
								})
								return true
							}
						}

						set({
							user: null,
							isAuthenticated: false,
							sessionChecked: true,
							isLoading: false,
							isImpersonating: false,
						})
						return false
					} catch (error) {
						console.warn("Session check failed:", error)
						set({
							user: null,
							isAuthenticated: false,
							sessionChecked: true,
							isLoading: false,
							isImpersonating: false,
						})
						return false
					}
				},

				// Initialize authentication with enhanced session restoration
				initializeAuth: async () => {
					const { sessionChecked } = get()

					if (!sessionChecked) {
						set({ isLoading: true })

						try {
							// Initialize session manager
							await sessionManager.initialize()

							// First, try to restore session from cookies
							const sessionRestored = await get().restoreSession()

							if (!sessionRestored) {
								// If no session found, perform a fresh session check
								await get().checkSession()
							}
						} catch (error) {
							console.error("Auth initialization failed:", error)
							set({
								user: null,
								isAuthenticated: false,
								sessionChecked: true,
								isLoading: false,
								error: {
									code: "INITIALIZATION_ERROR",
									message: "Failed to initialize authentication",
								},
							})
						}
					}
				},

				// Restore session from cookies/storage
				restoreSession: async () => {
					try {
						set({ isLoading: true })

						// Use session manager for restoration
						const sessionRestored = await sessionManager.restoreSession()

						if (sessionRestored) {
							// Get the session data
							const sessionData = await authService.getSession()

							if (sessionData?.user) {
								// Check if this is an impersonation session
								const isImpersonationSession =
									!!sessionData.session?.impersonatedBy

								set({
									user: sessionData.user as User,
									isAuthenticated: true,
									sessionChecked: true,
									isLoading: false,
									error: null,
									isImpersonating: isImpersonationSession,
								})
								return true
							}
						}

						// No valid session found
						set({
							user: null,
							isAuthenticated: false,
							sessionChecked: true,
							isLoading: false,
							isImpersonating: false,
						})
						return false
					} catch (error) {
						console.error("Session restoration failed:", error)
						set({
							user: null,
							isAuthenticated: false,
							sessionChecked: true,
							isLoading: false,
							isImpersonating: false,
						})
						return false
					}
				},

				// Handle session expiration
				handleSessionExpiration: async () => {
					console.log("Session expired, cleaning up...")

					// Stop background validation
					authService.stopBackgroundValidation()

					// Clear auth state
					set({
						user: null,
						isAuthenticated: false,
						error: {
							code: "SESSION_EXPIRED",
							message: "Your session has expired. Please sign in again.",
						},
					})

					// Redirect to login if we're on a protected route
					if (typeof window !== "undefined") {
						const currentPath = window.location.pathname
						if (
							currentPath.startsWith("/_authenticated") ||
							currentPath === "/"
						) {
							window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`
						}
					}
				},

				// Start impersonation
				startImpersonation: async (targetUserId: string) => {
					try {
						set({ isLoading: true, error: null })

						// Call the impersonation endpoint
						const response = await impersonateUser({
							userId: targetUserId,
						})

						if (response.error) {
							throw new Error(
								response.error.message || "Failed to start impersonation",
							)
						}

						if (response.data) {
							// Update state to reflect impersonation
							set({
								user: response.data.user as User,
								isAuthenticated: true,
								isLoading: false,
								isImpersonating: true,
								error: null,
							})

							// Reload to ensure all components update with new session
							window.location.reload()
						}
					} catch (error) {
						const authError: AuthError = {
							code: "IMPERSONATION_ERROR",
							message:
								error instanceof Error
									? error.message
									: "Failed to start impersonation",
						}
						set({
							error: authError,
							isLoading: false,
						})
						throw error
					}
				},

				// Stop impersonation
				stopImpersonation: async () => {
					try {
						set({ isLoading: true, error: null })

						// Call the stop impersonation endpoint
						const response = await stopImpersonating()

						if (response.error) {
							throw new Error(
								response.error.message || "Failed to stop impersonation",
							)
						}

						if (response.data) {
							// Update state to reflect end of impersonation
							set({
								user: response.data.user as User,
								isAuthenticated: true,
								isLoading: false,
								isImpersonating: false,
								error: null,
							})

							// Reload to ensure all components update with original session
							window.location.reload()
						}
					} catch (error) {
						const authError: AuthError = {
							code: "IMPERSONATION_ERROR",
							message:
								error instanceof Error
									? error.message
									: "Failed to stop impersonation",
						}
						set({
							error: authError,
							isLoading: false,
						})
						throw error
					}
				},

				// Enhanced cleanup method
				cleanup: () => {
					sessionManager.cleanup()
					authService.stopBackgroundValidation()
					authService.cleanup()
					set({
						user: null,
						isAuthenticated: false,
						isLoading: false,
						error: null,
						sessionChecked: false,
						isImpersonating: false,
					})
				},
			}),
			{
				name: "auth-storage",
				// Don't persist session state for security - always check on app start
				partialize: (state) => ({
					user: null, // Never persist user data for security
					isAuthenticated: false, // Never persist auth state for security
					isLoading: false,
					error: null,
					sessionChecked: false, // Always reset to force session check on startup
					isImpersonating: false, // Never persist impersonation state
					signIn: state.signIn,
					signOut: state.signOut,
					clearError: state.clearError,
					setLoading: state.setLoading,
					setError: state.setError,
					setUser: state.setUser,
					setImpersonating: state.setImpersonating,
					refreshSession: state.refreshSession,
					checkSession: state.checkSession,
					initializeAuth: state.initializeAuth,
					restoreSession: state.restoreSession,
					handleSessionExpiration: state.handleSessionExpiration,
					startImpersonation: state.startImpersonation,
					stopImpersonation: state.stopImpersonation,
					cleanup: state.cleanup,
				}),
			},
		),
		{
			name: "auth-store",
		},
	),
)

// Selector hooks for better performance
export const useUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () =>
	useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.isLoading)
export const useAuthError = () => useAuthStore((state) => state.error)
export const useIsImpersonating = () =>
	useAuthStore((state) => state.isImpersonating)
