import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import { Button } from "@donorcare/ui/components/ui/button"
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuPortal,
	DropdownMenuSeparator,
	DropdownMenuShortcut,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger,
} from "@donorcare/ui/components/ui/dropdown-menu"
import type { Row } from "@tanstack/react-table"
import type { Static } from "elysia"
import {
	CheckCircleIcon,
	ClockIcon,
	EditIcon,
	EllipsisIcon,
	EyeIcon,
	TrashIcon,
	XCircleIcon,
} from "lucide-react"
import { useState } from "react"

type DonationWithCampaign = Static<typeof donationWithCampaignDto>
type DonationStatus = "pending" | "completed" | "failed"

export interface DonationRowActionsProps {
	donation: DonationWithCampaign
	onView: (donationId: string) => void
	onEdit: (donationId: string) => void
	onDelete: (donationId: string) => void
	onStatusChange: (donationId: string, status: DonationStatus) => Promise<void>
}

export function DonationRowActions({
	donation,
	onView,
	onEdit,
	onDelete,
	onStatusChange,
}: DonationRowActionsProps) {
	const [isStatusChanging, setIsStatusChanging] = useState(false)

	const handleStatusChange = async (status: DonationStatus) => {
		if (donation.status === status || isStatusChanging) return

		try {
			setIsStatusChanging(true)
			await onStatusChange(donation.id, status)
		} finally {
			setIsStatusChanging(false)
		}
	}

	// Get available status actions based on current status
	const getStatusActions = () => {
		const actions = []

		if (donation.status !== "completed") {
			actions.push({
				label: "Mark as Completed",
				status: "completed" as const,
				icon: CheckCircleIcon,
				shortcut: "⌘C",
			})
		}

		if (donation.status !== "pending") {
			actions.push({
				label: "Mark as Pending",
				status: "pending" as const,
				icon: ClockIcon,
				shortcut: "⌘P",
			})
		}

		if (donation.status !== "failed") {
			actions.push({
				label: "Mark as Failed",
				status: "failed" as const,
				icon: XCircleIcon,
				shortcut: "⌘F",
			})
		}

		return actions
	}

	const statusActions = getStatusActions()

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<div className="flex justify-end">
					<Button
						size="icon"
						variant="ghost"
						className="shadow-none"
						aria-label={`Actions for donation from ${donation.donorName}`}
						disabled={isStatusChanging}
					>
						<EllipsisIcon size={16} aria-hidden="true" />
					</Button>
				</div>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-48">
				<DropdownMenuGroup>
					<DropdownMenuItem
						onClick={() => onView(donation.id)}
						className="cursor-pointer"
					>
						<EyeIcon className="mr-2 h-4 w-4" />
						<span>View Details</span>
						<DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => onEdit(donation.id)}
						className="cursor-pointer"
					>
						<EditIcon className="mr-2 h-4 w-4" />
						<span>Edit</span>
						<DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
					</DropdownMenuItem>
				</DropdownMenuGroup>

				{statusActions.length > 0 && (
					<>
						<DropdownMenuSeparator />
						<DropdownMenuGroup>
							{statusActions.length === 1 ? (
								// Single status action - show directly
								<DropdownMenuItem
									onClick={() => handleStatusChange(statusActions[0].status)}
									disabled={isStatusChanging}
									className="cursor-pointer"
								>
									{(() => {
										const IconComponent = statusActions[0].icon
										return <IconComponent className="mr-2 h-4 w-4" />
									})()}
									<span>{statusActions[0].label}</span>
									<DropdownMenuShortcut>
										{statusActions[0].shortcut}
									</DropdownMenuShortcut>
								</DropdownMenuItem>
							) : (
								// Multiple status actions - show in submenu
								<DropdownMenuSub>
									<DropdownMenuSubTrigger className="cursor-pointer">
										<ClockIcon className="mr-2 h-4 w-4" />
										<span>Change Status</span>
									</DropdownMenuSubTrigger>
									<DropdownMenuPortal>
										<DropdownMenuSubContent>
											{statusActions.map((action) => (
												<DropdownMenuItem
													key={action.status}
													onClick={() => handleStatusChange(action.status)}
													disabled={isStatusChanging}
													className="cursor-pointer"
												>
													{(() => {
														const IconComponent = action.icon
														return <IconComponent className="mr-2 h-4 w-4" />
													})()}
													<span>{action.label}</span>
													<DropdownMenuShortcut>
														{action.shortcut}
													</DropdownMenuShortcut>
												</DropdownMenuItem>
											))}
										</DropdownMenuSubContent>
									</DropdownMenuPortal>
								</DropdownMenuSub>
							)}
						</DropdownMenuGroup>
					</>
				)}

				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive cursor-pointer"
					onClick={() => onDelete(donation.id)}
					disabled={isStatusChanging}
				>
					<TrashIcon className="mr-2 h-4 w-4" />
					<span>Delete</span>
					<DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	)
}

// For use with TanStack Table Row
export interface DonationRowActionsTableProps {
	row: Row<DonationWithCampaign>
	table: any
}

export function DonationRowActionsTable({
	row,
	table,
}: DonationRowActionsTableProps) {
	const { onView, onEdit, onDelete, onStatusChange } = table.options.meta

	return (
		<DonationRowActions
			donation={row.original}
			onView={onView}
			onEdit={onEdit}
			onDelete={onDelete}
			onStatusChange={onStatusChange}
		/>
	)
}
