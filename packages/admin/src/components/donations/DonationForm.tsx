import type { Donation } from "@donorcare/backend/schemas"
import { updateDonationDto } from "@donorcare/backend/schemas"
import {
	Badge,
	Button,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Textarea,
} from "@donorcare/ui"
import type { Static } from "elysia"
import { Calendar, CreditCard, DollarSign, Loader2, User } from "lucide-react"
import { useForm } from "react-hook-form"
import { createTypeboxResolver } from "@/lib/typebox-resolver"

// Form data types
export type EditDonationFormData = Static<typeof updateDonationDto>

// Extended donation type with campaign info
export interface DonationWithCampaign
	extends Omit<Donation, "createdAt" | "updatedAt"> {
	createdAt: string
	updatedAt: string
	campaign: {
		id: string
		name: string
		slug: string
		organizerId: string
	}
}

// Component props
export interface DonationFormProps {
	mode: "edit"
	initialData?: Partial<DonationWithCampaign>
	onSubmit: (data: EditDonationFormData) => Promise<void>
	onCancel: () => void
	isLoading?: boolean
}

/**
 * Format currency amount for display
 */
function formatCurrency(amount: string, currency: string = "MYR"): string {
	const numAmount = parseFloat(amount)
	return new Intl.NumberFormat("en-MY", {
		style: "currency",
		currency: currency,
	}).format(numAmount)
}

/**
 * Format date for display
 */
function formatDate(dateString: string): string {
	return new Date(dateString).toLocaleDateString("en-MY", {
		year: "numeric",
		month: "long",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	})
}

/**
 * Get status badge variant based on donation status
 */
function getStatusVariant(
	status: string,
): "default" | "secondary" | "destructive" {
	switch (status) {
		case "completed":
			return "default"
		case "pending":
			return "secondary"
		case "failed":
			return "destructive"
		default:
			return "secondary"
	}
}

/**
 * Donation form component for editing donation metadata
 */
export function DonationForm({
	initialData,
	onSubmit,
	onCancel,
	isLoading = false,
}: DonationFormProps) {
	// Form setup with validation schema
	const form = useForm<EditDonationFormData>({
		resolver: createTypeboxResolver(updateDonationDto),
		defaultValues: {
			donorName: initialData?.donorName || "",
			donorEmail: initialData?.donorEmail || "",
			internalNotes: initialData?.internalNotes || "",
		},
	})

	// Handle form submission
	const handleSubmit = async (data: EditDonationFormData) => {
		await onSubmit(data)
	}

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-2xl font-bold tracking-tight">Edit Donation</h2>
				<p className="text-muted-foreground">
					Update donation information and add internal notes.
				</p>
			</div>

			{/* Read-only Payment Information Section */}
			{initialData && (
				<div className="rounded-lg border p-4 bg-muted/50">
					<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
						<CreditCard className="h-5 w-5" />
						Payment Information
					</h3>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<DollarSign className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Amount:</span>
								<span className="text-sm">
									{formatCurrency(
										initialData.amount || "0",
										initialData.currency,
									)}
								</span>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-sm font-medium">Status:</span>
								<Badge
									variant={getStatusVariant(initialData.status || "pending")}
								>
									{initialData.status}
								</Badge>
							</div>
						</div>
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<Calendar className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Created:</span>
								<span className="text-sm">
									{initialData.createdAt
										? formatDate(initialData.createdAt)
										: "N/A"}
								</span>
							</div>
							{initialData.chipPaymentId && (
								<div className="flex items-center gap-2">
									<span className="text-sm font-medium">Payment ID:</span>
									<span className="text-sm font-mono text-muted-foreground">
										{initialData.chipPaymentId}
									</span>
								</div>
							)}
						</div>
					</div>
					{initialData.campaign && (
						<div className="mt-4 pt-4 border-t">
							<div className="flex items-center gap-2">
								<span className="text-sm font-medium">Campaign:</span>
								<span className="text-sm">{initialData.campaign.name}</span>
							</div>
						</div>
					)}
					{initialData.donorMessage && (
						<div className="mt-4 pt-4 border-t">
							<div className="space-y-2">
								<span className="text-sm font-medium">Donor Message:</span>
								<p className="text-sm text-muted-foreground bg-background p-3 rounded border">
									{initialData.donorMessage}
								</p>
							</div>
						</div>
					)}
				</div>
			)}

			<Form {...form}>
				<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
					{/* Donor Name Field */}
					<FormField
						control={form.control}
						name="donorName"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Donor Name *</FormLabel>
								<FormControl>
									<div className="relative">
										<User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
										<Input
											placeholder="Enter donor name"
											className="pl-10"
											disabled={isLoading}
											{...field}
										/>
									</div>
								</FormControl>
								<FormDescription>
									The name of the person who made this donation.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Donor Email Field */}
					<FormField
						control={form.control}
						name="donorEmail"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Donor Email *</FormLabel>
								<FormControl>
									<Input
										type="email"
										placeholder="Enter donor email"
										disabled={isLoading}
										{...field}
									/>
								</FormControl>
								<FormDescription>
									The email address of the donor for communication and receipts.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Internal Notes Field */}
					<FormField
						control={form.control}
						name="internalNotes"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Internal Notes</FormLabel>
								<FormControl>
									<Textarea
										placeholder="Add internal notes about this donation..."
										className="min-h-[100px]"
										disabled={isLoading}
										{...field}
									/>
								</FormControl>
								<FormDescription>
									Private notes for your organization. These are not visible to
									the donor.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Form Actions */}
					<div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
						<Button
							type="button"
							variant="outline"
							onClick={onCancel}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button type="submit" disabled={isLoading}>
							{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
							Save Changes
						</Button>
					</div>
				</form>
			</Form>
		</div>
	)
}
