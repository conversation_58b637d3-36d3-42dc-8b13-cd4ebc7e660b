import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@donorcare/ui/components/ui/alert-dialog"
import { Button } from "@donorcare/ui/components/ui/button"
import { Checkbox } from "@donorcare/ui/components/ui/checkbox"
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuTrigger,
} from "@donorcare/ui/components/ui/dropdown-menu"
import { Input } from "@donorcare/ui/components/ui/input"
import { Label } from "@donorcare/ui/components/ui/label"
import {
	Pagination,
	PaginationContent,
	PaginationItem,
} from "@donorcare/ui/components/ui/pagination"
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@donorcare/ui/components/ui/popover"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@donorcare/ui/components/ui/select"
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@donorcare/ui/components/ui/table"
import { cn } from "@donorcare/ui/lib/utils"
import {
	type ColumnFiltersState,
	flexRender,
	getCoreRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	type PaginationState,
	type SortingState,
	useReactTable,
	type VisibilityState,
} from "@tanstack/react-table"
import type { Static } from "elysia"
import {
	ChevronDownIcon,
	ChevronFirstIcon,
	ChevronLastIcon,
	ChevronLeftIcon,
	ChevronRightIcon,
	ChevronUpIcon,
	CircleAlertIcon,
	CircleXIcon,
	Columns3Icon,
	DownloadIcon,
	FilterIcon,
	ListFilterIcon,
	TrashIcon,
} from "lucide-react"
import { useId, useMemo, useRef, useState } from "react"
import { donationColumns } from "./utils/columns"
import { DonationAnalytics } from "./DonationAnalytics"

type DonationWithCampaign = Static<typeof donationWithCampaignDto>

export interface DonationsTableProps {
	donations: DonationWithCampaign[]
	isLoading?: boolean
	error?: Error | null
	onView: (donationId: string) => void
	onEdit: (donationId: string) => void
	onDelete: (donationId: string) => void
	onStatusChange: (
		donationId: string,
		status: "pending" | "completed" | "failed",
	) => Promise<void>
	onExport: (selectedIds?: string[]) => void
	onRetry?: () => void
}

export function DonationsTable({
	donations,
	isLoading = false,
	error,
	onView,
	onEdit,
	onDelete,
	onStatusChange,
	onExport,
	onRetry,
}: DonationsTableProps) {
	const id = useId()
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
	const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
	const [pagination, setPagination] = useState<PaginationState>({
		pageIndex: 0,
		pageSize: 10,
	})
	const inputRef = useRef<HTMLInputElement>(null)

	const [sorting, setSorting] = useState<SortingState>([
		{
			id: "createdAt",
			desc: true,
		},
	])

	const handleDeleteRows = () => {
		const selectedRows = table.getSelectedRowModel().rows
		selectedRows.forEach((row) => {
			onDelete(row.original.id)
		})
		table.resetRowSelection()
	}

	const handleExportSelected = () => {
		const selectedRows = table.getSelectedRowModel().rows
		const selectedIds = selectedRows.map((row) => row.original.id)
		onExport(selectedIds.length > 0 ? selectedIds : undefined)
	}

	const table = useReactTable({
		data: donations,
		columns: donationColumns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		enableSortingRemoval: false,
		getPaginationRowModel: getPaginationRowModel(),
		onPaginationChange: setPagination,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		getFilteredRowModel: getFilteredRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
		state: {
			sorting,
			pagination,
			columnFilters,
			columnVisibility,
		},
		meta: {
			onView,
			onEdit,
			onDelete,
			onStatusChange,
		},
	})

	// Get unique status values
	const uniqueStatusValues = useMemo(() => {
		const statusColumn = table.getColumn("status")

		if (!statusColumn) return []

		const values = Array.from(statusColumn.getFacetedUniqueValues().keys())

		return values.sort()
	}, [table.getColumn("status")?.getFacetedUniqueValues()])

	// Get counts for each status
	const statusCounts = useMemo(() => {
		const statusColumn = table.getColumn("status")
		if (!statusColumn) return new Map()
		return statusColumn.getFacetedUniqueValues()
	}, [table.getColumn("status")?.getFacetedUniqueValues()])

	const selectedStatuses = useMemo(() => {
		const filterValue = table.getColumn("status")?.getFilterValue() as string[]
		return filterValue ?? []
	}, [table.getColumn("status")?.getFilterValue()])

	const handleStatusChange = (checked: boolean, value: string) => {
		const filterValue = table.getColumn("status")?.getFilterValue() as string[]
		const newFilterValue = filterValue ? [...filterValue] : []

		if (checked) {
			newFilterValue.push(value)
		} else {
			const index = newFilterValue.indexOf(value)
			if (index > -1) {
				newFilterValue.splice(index, 1)
			}
		}

		table
			.getColumn("status")
			?.setFilterValue(newFilterValue.length ? newFilterValue : undefined)
	}

	// Show error state
	if (error && !isLoading) {
		return (
			<div className="flex flex-col items-center justify-center py-12 space-y-4">
				<CircleAlertIcon className="h-12 w-12 text-muted-foreground" />
				<div className="text-center">
					<h3 className="text-lg font-semibold">Failed to load donations</h3>
					<p className="text-sm text-muted-foreground mt-1">{error.message}</p>
				</div>
				{onRetry && (
					<Button onClick={onRetry} variant="outline">
						Try Again
					</Button>
				)}
			</div>
		)
	}

	return (
		<div className="space-y-4">
			{/* Analytics */}
			<DonationAnalytics
				donations={donations}
				filteredDonations={table
					.getFilteredRowModel()
					.rows.map((row) => row.original)}
				selectedDonations={table
					.getSelectedRowModel()
					.rows.map((row) => row.original)}
				isLoading={isLoading}
				error={error}
			/>

			{/* Filters */}
			<div className="flex flex-wrap items-center justify-between gap-3">
				<div className="flex items-center gap-3">
					{/* Filter by donor name, email, or campaign */}
					<div className="relative">
						<Input
							id={`${id}-input`}
							ref={inputRef}
							className={cn(
								"peer min-w-60 ps-9",
								Boolean(table.getColumn("donorName")?.getFilterValue()) &&
									"pe-9",
							)}
							value={
								(table.getColumn("donorName")?.getFilterValue() ?? "") as string
							}
							onChange={(e) =>
								table.getColumn("donorName")?.setFilterValue(e.target.value)
							}
							placeholder="Filter by donor name, email, or campaign..."
							type="text"
							aria-label="Filter by donor name, email, or campaign"
							disabled={isLoading}
						/>
						<div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
							<ListFilterIcon size={16} aria-hidden="true" />
						</div>
						{Boolean(table.getColumn("donorName")?.getFilterValue()) && (
							<button
								className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
								aria-label="Clear filter"
								onClick={() => {
									table.getColumn("donorName")?.setFilterValue("")
									if (inputRef.current) {
										inputRef.current.focus()
									}
								}}
								disabled={isLoading}
							>
								<CircleXIcon size={16} aria-hidden="true" />
							</button>
						)}
					</div>
					{/* Filter by status */}
					<Popover>
						<PopoverTrigger asChild>
							<Button variant="outline" disabled={isLoading}>
								<FilterIcon
									className="-ms-1 opacity-60"
									size={16}
									aria-hidden="true"
								/>
								Status
								{selectedStatuses.length > 0 && (
									<span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
										{selectedStatuses.length}
									</span>
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto min-w-36 p-3" align="start">
							<div className="space-y-3">
								<div className="text-muted-foreground text-xs font-medium">
									Filters
								</div>
								<div className="space-y-3">
									{uniqueStatusValues.map((value, i) => (
										<div key={value} className="flex items-center gap-2">
											<Checkbox
												id={`${id}-${i}`}
												checked={selectedStatuses.includes(value)}
												onCheckedChange={(checked: boolean) =>
													handleStatusChange(checked, value)
												}
											/>
											<Label
												htmlFor={`${id}-${i}`}
												className="flex grow justify-between gap-2 font-normal"
											>
												{value.charAt(0).toUpperCase() + value.slice(1)}{" "}
												<span className="text-muted-foreground ms-2 text-xs">
													{statusCounts.get(value)}
												</span>
											</Label>
										</div>
									))}
								</div>
							</div>
						</PopoverContent>
					</Popover>
					{/* Toggle columns visibility */}
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="outline" disabled={isLoading}>
								<Columns3Icon
									className="-ms-1 opacity-60"
									size={16}
									aria-hidden="true"
								/>
								View
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
							{table
								.getAllColumns()
								.filter((column) => column.getCanHide())
								.map((column) => {
									return (
										<DropdownMenuCheckboxItem
											key={column.id}
											className="capitalize"
											checked={column.getIsVisible()}
											onCheckedChange={(value) =>
												column.toggleVisibility(!!value)
											}
											onSelect={(event) => event.preventDefault()}
										>
											{column.id === "donorName"
												? "Donor Name"
												: column.id === "donorEmail"
													? "Email"
													: column.id === "campaign.name"
														? "Campaign"
														: column.id === "createdAt"
															? "Date"
															: column.id}
										</DropdownMenuCheckboxItem>
									)
								})}
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
				<div className="flex items-center gap-3">
					{/* Delete button */}
					{table.getSelectedRowModel().rows.length > 0 && (
						<AlertDialog>
							<AlertDialogTrigger asChild>
								<Button
									className="ml-auto"
									variant="outline"
									disabled={isLoading}
								>
									<TrashIcon
										className="-ms-1 opacity-60"
										size={16}
										aria-hidden="true"
									/>
									Delete
									<span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
										{table.getSelectedRowModel().rows.length}
									</span>
								</Button>
							</AlertDialogTrigger>
							<AlertDialogContent>
								<div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
									<div
										className="flex size-9 shrink-0 items-center justify-center rounded-full border"
										aria-hidden="true"
									>
										<CircleAlertIcon className="opacity-80" size={16} />
									</div>
									<AlertDialogHeader>
										<AlertDialogTitle>
											Are you absolutely sure?
										</AlertDialogTitle>
										<AlertDialogDescription>
											This action cannot be undone. This will permanently delete{" "}
											{table.getSelectedRowModel().rows.length} selected{" "}
											{table.getSelectedRowModel().rows.length === 1
												? "donation"
												: "donations"}
											.
										</AlertDialogDescription>
									</AlertDialogHeader>
								</div>
								<AlertDialogFooter>
									<AlertDialogCancel>Cancel</AlertDialogCancel>
									<AlertDialogAction onClick={handleDeleteRows}>
										Delete
									</AlertDialogAction>
								</AlertDialogFooter>
							</AlertDialogContent>
						</AlertDialog>
					)}
					{/* Export button */}
					<Button
						className="ml-auto"
						variant="outline"
						onClick={handleExportSelected}
						disabled={isLoading}
					>
						<DownloadIcon
							className="-ms-1 opacity-60"
							size={16}
							aria-hidden="true"
						/>
						Export
						{table.getSelectedRowModel().rows.length > 0 && (
							<span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
								{table.getSelectedRowModel().rows.length}
							</span>
						)}
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="bg-background overflow-hidden rounded-md border">
				<Table className="table-fixed">
					<TableHeader>
						{table.getHeaderGroups().map((headerGroup) => (
							<TableRow key={headerGroup.id} className="hover:bg-transparent">
								{headerGroup.headers.map((header) => {
									return (
										<TableHead
											key={header.id}
											style={{ width: `${header.getSize()}px` }}
											className="h-11"
										>
											{header.isPlaceholder ? null : header.column.getCanSort() ? (
												<div
													className={cn(
														header.column.getCanSort() &&
															"flex h-full cursor-pointer items-center justify-between gap-2 select-none",
													)}
													onClick={header.column.getToggleSortingHandler()}
													onKeyDown={(e) => {
														// Enhanced keyboard handling for sorting
														if (
															header.column.getCanSort() &&
															(e.key === "Enter" || e.key === " ")
														) {
															e.preventDefault()
															header.column.getToggleSortingHandler()?.(e)
														}
													}}
													tabIndex={header.column.getCanSort() ? 0 : undefined}
												>
													{flexRender(
														header.column.columnDef.header,
														header.getContext(),
													)}
													{{
														asc: (
															<ChevronUpIcon
																className="shrink-0 opacity-60"
																size={16}
																aria-hidden="true"
															/>
														),
														desc: (
															<ChevronDownIcon
																className="shrink-0 opacity-60"
																size={16}
																aria-hidden="true"
															/>
														),
													}[header.column.getIsSorted() as string] ?? null}
												</div>
											) : (
												flexRender(
													header.column.columnDef.header,
													header.getContext(),
												)
											)}
										</TableHead>
									)
								})}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{isLoading ? (
							// Loading skeleton rows
							Array.from({ length: pagination.pageSize }).map((_, index) => (
								<TableRow key={`skeleton-${index}`}>
									{donationColumns.map((column) => (
										<TableCell key={column.id} className="last:py-0">
											<div className="h-4 bg-muted animate-pulse rounded" />
										</TableCell>
									))}
								</TableRow>
							))
						) : table.getRowModel().rows?.length ? (
							table.getRowModel().rows.map((row) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
								>
									{row.getVisibleCells().map((cell) => (
										<TableCell key={cell.id} className="last:py-0">
											{flexRender(
												cell.column.columnDef.cell,
												cell.getContext(),
											)}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell
									colSpan={donationColumns.length}
									className="h-24 text-center"
								>
									No donations found.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>

			{/* Pagination */}
			<div className="flex items-center justify-between gap-8">
				{/* Results per page */}
				<div className="flex items-center gap-3">
					<Label htmlFor={id} className="max-sm:sr-only">
						Rows per page
					</Label>
					<Select
						value={table.getState().pagination.pageSize.toString()}
						onValueChange={(value) => {
							table.setPageSize(Number(value))
						}}
						disabled={isLoading}
					>
						<SelectTrigger id={id} className="w-fit whitespace-nowrap">
							<SelectValue placeholder="Select number of results" />
						</SelectTrigger>
						<SelectContent className="[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2">
							{[5, 10, 25, 50].map((pageSize) => (
								<SelectItem key={pageSize} value={pageSize.toString()}>
									{pageSize}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
				{/* Page number information */}
				<div className="text-muted-foreground flex grow justify-end text-sm whitespace-nowrap">
					<p
						className="text-muted-foreground text-sm whitespace-nowrap"
						aria-live="polite"
					>
						<span className="text-foreground">
							{table.getState().pagination.pageIndex *
								table.getState().pagination.pageSize +
								1}
							-
							{Math.min(
								Math.max(
									table.getState().pagination.pageIndex *
										table.getState().pagination.pageSize +
										table.getState().pagination.pageSize,
									0,
								),
								table.getRowCount(),
							)}
						</span>{" "}
						of{" "}
						<span className="text-foreground">
							{table.getRowCount().toString()}
						</span>
					</p>
				</div>

				{/* Pagination buttons */}
				<div>
					<Pagination>
						<PaginationContent>
							{/* First page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.firstPage()}
									disabled={!table.getCanPreviousPage() || isLoading}
									aria-label="Go to first page"
								>
									<ChevronFirstIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
							{/* Previous page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.previousPage()}
									disabled={!table.getCanPreviousPage() || isLoading}
									aria-label="Go to previous page"
								>
									<ChevronLeftIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
							{/* Next page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.nextPage()}
									disabled={!table.getCanNextPage() || isLoading}
									aria-label="Go to next page"
								>
									<ChevronRightIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
							{/* Last page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.lastPage()}
									disabled={!table.getCanNextPage() || isLoading}
									aria-label="Go to last page"
								>
									<ChevronLastIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
						</PaginationContent>
					</Pagination>
				</div>
			</div>
		</div>
	)
}
