import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	Label,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Textarea,
} from "@donorcare/ui"
import { Loader2 } from "lucide-react"
import { useState } from "react"
import { useUpdateDonationStatus } from "@/hooks/useDonations"

export interface StatusToggleProps {
	donationId: string
	currentStatus: "pending" | "completed" | "failed"
	disabled?: boolean
	size?: "sm" | "default"
	className?: string
}

/**
 * Donation status toggle component with confirmation dialogs and failure reason input
 * Provides status change functionality for pending/completed/failed states
 */
export function StatusToggle({
	donationId,
	currentStatus,
	disabled = false,
	size = "default",
	className,
}: StatusToggleProps) {
	const [isDialogOpen, setIsDialogOpen] = useState(false)
	const [selectedStatus, setSelectedStatus] = useState<
		"pending" | "completed" | "failed"
	>(currentStatus)
	const [failureReason, setFailureReason] = useState("")

	const updateStatusMutation = useUpdateDonationStatus()

	const handleStatusChange = (
		newStatus: "pending" | "completed" | "failed",
	) => {
		if (
			newStatus === currentStatus ||
			updateStatusMutation.isPending ||
			disabled
		) {
			return
		}

		setSelectedStatus(newStatus)
		setIsDialogOpen(true)
	}

	const handleConfirm = () => {
		updateStatusMutation.mutate(
			{
				donationId,
				status: selectedStatus,
				failureReason: selectedStatus === "failed" ? failureReason : undefined,
			},
			{
				onSuccess: () => {
					setIsDialogOpen(false)
					setFailureReason("")
				},
			},
		)
	}

	const handleCancel = () => {
		setIsDialogOpen(false)
		setSelectedStatus(currentStatus)
		setFailureReason("")
	}

	const getStatusColor = (status: string) => {
		switch (status) {
			case "completed":
				return "text-green-600 bg-green-50 border-green-200"
			case "failed":
				return "text-red-600 bg-red-50 border-red-200"
			default:
				return "text-yellow-600 bg-yellow-50 border-yellow-200"
		}
	}

	const getStatusLabel = (status: string) => {
		switch (status) {
			case "completed":
				return "Completed"
			case "failed":
				return "Failed"
			default:
				return "Pending"
		}
	}

	const selectId = `status-select-${donationId}`
	const selectLabelId = `status-label-${donationId}`

	// Use optimistic state during loading, otherwise use actual state
	const displayStatus = updateStatusMutation.isPending
		? (updateStatusMutation.variables?.status ?? currentStatus)
		: currentStatus

	return (
		<>
			<div className={`flex items-center gap-2 ${className || ""}`}>
				<div className="relative">
					<Select
						value={displayStatus}
						onValueChange={handleStatusChange}
						disabled={disabled || updateStatusMutation.isPending}
					>
						<SelectTrigger
							id={selectId}
							className={`${size === "sm" ? "h-8 text-xs" : "h-9 text-sm"} ${getStatusColor(displayStatus)} font-medium border`}
							aria-labelledby={selectLabelId}
							aria-describedby={`${selectId}-description`}
						>
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="pending" className="text-yellow-600">
								Pending
							</SelectItem>
							<SelectItem value="completed" className="text-green-600">
								Completed
							</SelectItem>
							<SelectItem value="failed" className="text-red-600">
								Failed
							</SelectItem>
						</SelectContent>
					</Select>
					{updateStatusMutation.isPending && (
						<div className="absolute inset-0 flex items-center justify-center">
							<Loader2
								className="h-3 w-3 animate-spin text-muted-foreground"
								data-testid="loading-spinner"
							/>
						</div>
					)}
				</div>
				{/* Screen reader description */}
				<span id={`${selectId}-description`} className="sr-only">
					{`Donation status is currently ${getStatusLabel(displayStatus).toLowerCase()}`}
					{updateStatusMutation.isPending && ". Status is being updated."}
				</span>
			</div>

			<AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Confirm Status Change</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to change the donation status from{" "}
							<span className="font-medium">
								{getStatusLabel(currentStatus)}
							</span>{" "}
							to{" "}
							<span className="font-medium">
								{getStatusLabel(selectedStatus)}
							</span>
							?
							{selectedStatus === "failed" && (
								<span className="block mt-2 text-sm">
									This action will mark the donation as failed and may affect
									reporting.
								</span>
							)}
							{selectedStatus === "completed" &&
								currentStatus === "pending" && (
									<span className="block mt-2 text-sm">
										This will mark the donation as successfully completed.
									</span>
								)}
						</AlertDialogDescription>
					</AlertDialogHeader>

					{selectedStatus === "failed" && (
						<div className="space-y-2">
							<Label htmlFor={`failure-reason-${donationId}`}>
								Failure Reason{" "}
								<span className="text-muted-foreground">(Optional)</span>
							</Label>
							<Textarea
								id={`failure-reason-${donationId}`}
								placeholder="Enter the reason for marking this donation as failed..."
								value={failureReason}
								onChange={(e) => setFailureReason(e.target.value)}
								maxLength={500}
								className="min-h-[80px]"
							/>
							<p className="text-xs text-muted-foreground">
								{failureReason.length}/500 characters
							</p>
						</div>
					)}

					<AlertDialogFooter>
						<AlertDialogCancel
							onClick={handleCancel}
							disabled={updateStatusMutation.isPending}
						>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={handleConfirm}
							disabled={updateStatusMutation.isPending}
							className={
								selectedStatus === "failed"
									? "bg-red-600 hover:bg-red-700"
									: selectedStatus === "completed"
										? "bg-green-600 hover:bg-green-700"
										: undefined
							}
						>
							{updateStatusMutation.isPending && (
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							)}
							Confirm Change
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	)
}
