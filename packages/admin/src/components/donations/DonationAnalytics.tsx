import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import { Badge } from "@donorcare/ui/components/ui/badge"
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@donorcare/ui/components/ui/card"
import { Skeleton } from "@donorcare/ui/components/ui/skeleton"
import { cn } from "@donorcare/ui/lib/utils"
import type { Static } from "elysia"
import {
	AlertCircleIcon,
	CheckCircleIcon,
	ClockIcon,
	DollarSignIcon,
	TrendingUpIcon,
	UsersIcon,
	XCircleIcon,
} from "lucide-react"
import { useMemo } from "react"
import { formatAmount, getStatusBadgeVariant } from "./utils/formatters"

type DonationWithCampaign = Static<typeof donationWithCampaignDto>

export interface DonationAnalyticsProps {
	donations: DonationWithCampaign[]
	filteredDonations: DonationWithCampaign[]
	selectedDonations: DonationWithCampaign[]
	isLoading: boolean
	error?: Error | null
	className?: string
}

interface AnalyticsData {
	totalAmount: string
	totalCount: number
	averageAmount: string
	statusDistribution: {
		pending: number
		completed: number
		failed: number
	}
}

export function DonationAnalytics({
	donations,
	filteredDonations,
	selectedDonations,
	isLoading,
	error,
	className,
}: DonationAnalyticsProps) {
	// Calculate analytics based on the current context (selected > filtered > all)
	const analyticsData = useMemo((): AnalyticsData => {
		const dataToAnalyze =
			selectedDonations.length > 0
				? selectedDonations
				: filteredDonations.length > 0
					? filteredDonations
					: donations

		if (dataToAnalyze.length === 0) {
			return {
				totalAmount: "0.00",
				totalCount: 0,
				averageAmount: "0.00",
				statusDistribution: {
					pending: 0,
					completed: 0,
					failed: 0,
				},
			}
		}

		// Calculate total amount
		const totalAmountNum = dataToAnalyze.reduce((sum, donation) => {
			return sum + parseFloat(donation.amount)
		}, 0)

		// Calculate status distribution
		const statusDistribution = dataToAnalyze.reduce(
			(acc, donation) => {
				acc[donation.status as keyof typeof acc]++
				return acc
			},
			{ pending: 0, completed: 0, failed: 0 },
		)

		// Calculate average
		const averageAmountNum = totalAmountNum / dataToAnalyze.length

		return {
			totalAmount: totalAmountNum.toFixed(2),
			totalCount: dataToAnalyze.length,
			averageAmount: averageAmountNum.toFixed(2),
			statusDistribution,
		}
	}, [donations, filteredDonations, selectedDonations])

	// Determine the context for display
	const context = useMemo(() => {
		if (selectedDonations.length > 0) {
			return {
				type: "selected" as const,
				count: selectedDonations.length,
				label: `${selectedDonations.length} selected`,
			}
		}
		if (filteredDonations.length !== donations.length) {
			return {
				type: "filtered" as const,
				count: filteredDonations.length,
				label: `${filteredDonations.length} filtered`,
			}
		}
		return {
			type: "all" as const,
			count: donations.length,
			label: "All donations",
		}
	}, [donations.length, filteredDonations.length, selectedDonations.length])

	// Show error state
	if (error && !isLoading) {
		return (
			<Card className={cn("border-destructive/50", className)}>
				<CardContent className="flex items-center justify-center py-6">
					<div className="flex items-center gap-2 text-destructive">
						<AlertCircleIcon className="h-4 w-4" />
						<span className="text-sm">Failed to load analytics</span>
					</div>
				</CardContent>
			</Card>
		)
	}

	return (
		<div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
			{/* Total Amount */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">Total Amount</CardTitle>
					<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-8 w-24" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div>
							<div className="text-2xl font-bold">
								{formatAmount(analyticsData.totalAmount)}
							</div>
							<p className="text-xs text-muted-foreground">{context.label}</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Total Count */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">Total Donations</CardTitle>
					<UsersIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-8 w-16" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div>
							<div className="text-2xl font-bold">
								{analyticsData.totalCount.toLocaleString()}
							</div>
							<p className="text-xs text-muted-foreground">
								{context.type === "selected"
									? "donations selected"
									: context.type === "filtered"
										? "donations match filters"
										: "total donations"}
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Average Amount */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">Average Amount</CardTitle>
					<TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-8 w-20" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div>
							<div className="text-2xl font-bold">
								{analyticsData.totalCount > 0
									? formatAmount(analyticsData.averageAmount)
									: formatAmount("0.00")}
							</div>
							<p className="text-xs text-muted-foreground">per donation</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Status Distribution */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">
						Status Distribution
					</CardTitle>
					<ClockIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-6 w-full" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div className="space-y-2">
							<div className="flex flex-wrap gap-1">
								<Badge
									variant={getStatusBadgeVariant("completed")}
									className="text-xs"
								>
									<CheckCircleIcon className="mr-1 h-3 w-3" />
									{analyticsData.statusDistribution.completed}
								</Badge>
								<Badge
									variant={getStatusBadgeVariant("pending")}
									className="text-xs"
								>
									<ClockIcon className="mr-1 h-3 w-3" />
									{analyticsData.statusDistribution.pending}
								</Badge>
								<Badge
									variant={getStatusBadgeVariant("failed")}
									className="text-xs"
								>
									<XCircleIcon className="mr-1 h-3 w-3" />
									{analyticsData.statusDistribution.failed}
								</Badge>
							</div>
							<p className="text-xs text-muted-foreground">by status</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	)
}
