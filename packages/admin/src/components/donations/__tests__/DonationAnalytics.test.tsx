import { render, screen } from "@testing-library/react"
import { describe, expect, it } from "vitest"
import { DonationAnalytics } from "../DonationAnalytics"

// Mock donation data
const mockDonations = [
	{
		id: "1",
		campaignId: "camp1",
		donorId: "donor1",
		amount: "100.00",
		currency: "MYR",
		chipPaymentId: "chip1",
		status: "completed" as const,
		donorName: "John Doe",
		donorEmail: "<EMAIL>",
		donorMessage: "Good luck!",
		internalNotes: null,
		createdAt: "2024-01-01T00:00:00Z",
		updatedAt: "2024-01-01T00:00:00Z",
		campaign: {
			id: "camp1",
			name: "Test Campaign",
			slug: "test-campaign",
			organizerId: "org1",
		},
	},
	{
		id: "2",
		campaignId: "camp1",
		donorId: "donor2",
		amount: "50.00",
		currency: "MYR",
		chipPaymentId: "chip2",
		status: "pending" as const,
		donorName: "<PERSON>",
		donorEmail: "<EMAIL>",
		donorMessage: null,
		internalNotes: null,
		createdAt: "2024-01-02T00:00:00Z",
		updatedAt: "2024-01-02T00:00:00Z",
		campaign: {
			id: "camp1",
			name: "Test Campaign",
			slug: "test-campaign",
			organizerId: "org1",
		},
	},
	{
		id: "3",
		campaignId: "camp2",
		donorId: "donor3",
		amount: "25.00",
		currency: "MYR",
		chipPaymentId: "chip3",
		status: "failed" as const,
		donorName: "Bob Johnson",
		donorEmail: "<EMAIL>",
		donorMessage: null,
		internalNotes: null,
		createdAt: "2024-01-03T00:00:00Z",
		updatedAt: "2024-01-03T00:00:00Z",
		campaign: {
			id: "camp2",
			name: "Another Campaign",
			slug: "another-campaign",
			organizerId: "org1",
		},
	},
]

describe("DonationAnalytics", () => {
	it("renders analytics cards with correct data", () => {
		render(
			<DonationAnalytics
				donations={mockDonations}
				filteredDonations={mockDonations}
				selectedDonations={[]}
				isLoading={false}
			/>,
		)

		// Check if all analytics cards are rendered
		expect(screen.getByText("Total Amount")).toBeInTheDocument()
		expect(screen.getByText("Total Donations")).toBeInTheDocument()
		expect(screen.getByText("Average Amount")).toBeInTheDocument()
		expect(screen.getByText("Status Distribution")).toBeInTheDocument()

		// Check calculated values
		expect(screen.getByText("RM 175.00")).toBeInTheDocument() // Total amount
		expect(screen.getByText("3")).toBeInTheDocument() // Total count
		expect(screen.getByText("RM 58.33")).toBeInTheDocument() // Average amount
		expect(screen.getByText("All donations")).toBeInTheDocument() // Context label
	})

	it("shows filtered context when filtered donations differ from all donations", () => {
		const filteredDonations = mockDonations.slice(0, 2) // Only first 2 donations

		render(
			<DonationAnalytics
				donations={mockDonations}
				filteredDonations={filteredDonations}
				selectedDonations={[]}
				isLoading={false}
			/>,
		)

		// Should show filtered context
		expect(screen.getByText("2 filtered")).toBeInTheDocument()
		expect(screen.getByText("RM 150.00")).toBeInTheDocument() // Total of first 2 donations
		expect(screen.getByText("RM 75.00")).toBeInTheDocument() // Average of first 2 donations
	})

	it("shows selected context when donations are selected", () => {
		const selectedDonations = [mockDonations[0]] // Only first donation

		render(
			<DonationAnalytics
				donations={mockDonations}
				filteredDonations={mockDonations}
				selectedDonations={selectedDonations}
				isLoading={false}
			/>,
		)

		// Should show selected context (takes priority over filtered)
		expect(screen.getByText("1 selected")).toBeInTheDocument()
		expect(screen.getByText("donations selected")).toBeInTheDocument()

		// Check that the total amount appears (even if duplicated)
		const amounts = screen.getAllByText("RM 100.00")
		expect(amounts.length).toBeGreaterThan(0)
	})

	it("shows correct status distribution", () => {
		render(
			<DonationAnalytics
				donations={mockDonations}
				filteredDonations={mockDonations}
				selectedDonations={[]}
				isLoading={false}
			/>,
		)

		// Check that status distribution section exists
		expect(screen.getByText("Status Distribution")).toBeInTheDocument()
		expect(screen.getByText("by status")).toBeInTheDocument()

		// Check that badges are rendered (we can't easily test the exact counts due to multiple "1"s)
		const badges = document.querySelectorAll('[data-slot="badge"]')
		expect(badges).toHaveLength(3) // Should have 3 status badges
	})

	it("shows loading skeletons when loading", () => {
		render(
			<DonationAnalytics
				donations={[]}
				filteredDonations={[]}
				selectedDonations={[]}
				isLoading={true}
			/>,
		)

		// Should show skeleton loaders
		const skeletons = document.querySelectorAll('[data-slot="skeleton"]')
		expect(skeletons.length).toBeGreaterThan(0)
	})

	it("shows error state when there is an error", () => {
		const error = new Error("Failed to load data")

		render(
			<DonationAnalytics
				donations={[]}
				filteredDonations={[]}
				selectedDonations={[]}
				isLoading={false}
				error={error}
			/>,
		)

		expect(screen.getByText("Failed to load analytics")).toBeInTheDocument()
	})

	it("handles empty data gracefully", () => {
		render(
			<DonationAnalytics
				donations={[]}
				filteredDonations={[]}
				selectedDonations={[]}
				isLoading={false}
			/>,
		)

		// Should show zero values - use getAllByText to handle multiple instances
		const zeroAmounts = screen.getAllByText("RM 0.00")
		expect(zeroAmounts.length).toBeGreaterThan(0) // Should have at least one RM 0.00
		expect(screen.getByText("All donations")).toBeInTheDocument() // Context
	})

	it("calculates analytics correctly for different amounts", () => {
		const customDonations = [
			{
				...mockDonations[0],
				amount: "1000.50",
				status: "completed" as const,
			},
			{
				...mockDonations[1],
				amount: "500.25",
				status: "completed" as const,
			},
		]

		render(
			<DonationAnalytics
				donations={customDonations}
				filteredDonations={customDonations}
				selectedDonations={[]}
				isLoading={false}
			/>,
		)

		// Check calculated values
		expect(screen.getByText("RM 1,500.75")).toBeInTheDocument() // Total
		expect(screen.getByText("RM 750.38")).toBeInTheDocument() // Average
		expect(screen.getByText("total donations")).toBeInTheDocument() // Context text
	})
})
