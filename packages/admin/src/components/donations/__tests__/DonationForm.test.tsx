import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"
import type { DonationWithCampaign } from "../DonationForm"
import { DonationForm } from "../DonationForm"

describe("DonationForm", () => {
	const mockOnSubmit = vi.fn()
	const mockOnCancel = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	describe("Edit Mode", () => {
		const mockDonation: DonationWithCampaign = {
			id: "donation-1",
			campaignId: "campaign-1",
			donorId: "donor-1",
			amount: "100.00",
			currency: "MYR",
			chipPaymentId: "chip_123456",
			status: "completed",
			donorName: "John Doe",
			donorEmail: "<EMAIL>",
			donorMessage: "Keep up the good work!",
			internalNotes: "VIP donor",
			createdAt: "2024-01-01T00:00:00Z",
			updatedAt: "2024-01-01T00:00:00Z",
			campaign: {
				id: "campaign-1",
				name: "Test Campaign",
				slug: "test-campaign",
				organizerId: "org-1",
			},
		}

		it("renders edit form with correct title", () => {
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("Edit Donation")).toBeInTheDocument()
			expect(screen.getByText("Save Changes")).toBeInTheDocument()
		})

		it("populates form with donation data", () => {
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByDisplayValue("John Doe")).toBeInTheDocument()
			expect(screen.getByDisplayValue("<EMAIL>")).toBeInTheDocument()
			expect(screen.getByDisplayValue("VIP donor")).toBeInTheDocument()
		})

		it("displays read-only payment information", () => {
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("Payment Information")).toBeInTheDocument()
			expect(screen.getByText("RM100.00")).toBeInTheDocument()
			expect(screen.getByText("completed")).toBeInTheDocument()
			expect(screen.getByText("chip_123456")).toBeInTheDocument()
			expect(screen.getByText("Test Campaign")).toBeInTheDocument()
		})

		it("displays donor message when present", () => {
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("Donor Message:")).toBeInTheDocument()
			expect(screen.getByText("Keep up the good work!")).toBeInTheDocument()
		})

		it("does not display donor message section when not present", () => {
			const donationWithoutMessage = { ...mockDonation, donorMessage: null }
			render(
				<DonationForm
					mode="edit"
					initialData={donationWithoutMessage}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.queryByText("Donor Message:")).not.toBeInTheDocument()
		})

		it("submits form with updated data", async () => {
			const user = userEvent.setup()
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const nameInput = screen.getByDisplayValue("John Doe")
			const emailInput = screen.getByDisplayValue("<EMAIL>")
			const notesInput = screen.getByDisplayValue("VIP donor")

			await user.clear(nameInput)
			await user.type(nameInput, "Jane Smith")
			await user.clear(emailInput)
			await user.type(emailInput, "<EMAIL>")
			await user.clear(notesInput)
			await user.type(notesInput, "Updated notes")

			const submitButton = screen.getByText("Save Changes")
			await user.click(submitButton)

			await waitFor(() => {
				expect(mockOnSubmit).toHaveBeenCalledWith({
					donorName: "Jane Smith",
					donorEmail: "<EMAIL>",
					internalNotes: "Updated notes",
				})
			})
		})

		it("validates required fields", async () => {
			const user = userEvent.setup()
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const nameInput = screen.getByDisplayValue("John Doe")
			const emailInput = screen.getByDisplayValue("<EMAIL>")

			await user.clear(nameInput)
			await user.clear(emailInput)

			const submitButton = screen.getByText("Save Changes")
			await user.click(submitButton)

			await waitFor(() => {
				expect(screen.getByText(/Expected string/)).toBeInTheDocument()
			})

			expect(mockOnSubmit).not.toHaveBeenCalled()
		})

		it("validates email format", async () => {
			const user = userEvent.setup()
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const emailInput = screen.getByDisplayValue("<EMAIL>")

			await user.clear(emailInput)
			await user.type(emailInput, "invalid-email")

			const submitButton = screen.getByText("Save Changes")
			await user.click(submitButton)

			await waitFor(() => {
				expect(screen.getByText(/Expected email/)).toBeInTheDocument()
			})

			expect(mockOnSubmit).not.toHaveBeenCalled()
		})
	})

	describe("Common Functionality", () => {
		const mockDonation: DonationWithCampaign = {
			id: "donation-1",
			campaignId: "campaign-1",
			donorId: "donor-1",
			amount: "50.00",
			currency: "MYR",
			chipPaymentId: null,
			status: "pending",
			donorName: "Test User",
			donorEmail: "<EMAIL>",
			donorMessage: null,
			internalNotes: "",
			createdAt: "2024-01-01T00:00:00Z",
			updatedAt: "2024-01-01T00:00:00Z",
			campaign: {
				id: "campaign-1",
				name: "Test Campaign",
				slug: "test-campaign",
				organizerId: "org-1",
			},
		}

		it("calls onCancel when cancel button is clicked", async () => {
			const user = userEvent.setup()
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const cancelButton = screen.getByText("Cancel")
			await user.click(cancelButton)

			expect(mockOnCancel).toHaveBeenCalled()
		})

		it("shows loading state when isLoading is true", () => {
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
					isLoading={true}
				/>,
			)

			const nameInput = screen.getByDisplayValue("Test User")
			expect(nameInput).toBeDisabled()

			const submitButton = screen.getByText("Save Changes")
			expect(submitButton).toBeDisabled()
		})

		it("handles different status badges correctly", () => {
			const completedDonation = {
				...mockDonation,
				status: "completed" as const,
			}
			const { rerender } = render(
				<DonationForm
					mode="edit"
					initialData={completedDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("completed")).toBeInTheDocument()

			const failedDonation = { ...mockDonation, status: "failed" as const }
			rerender(
				<DonationForm
					mode="edit"
					initialData={failedDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("failed")).toBeInTheDocument()
		})

		it("formats currency correctly", () => {
			const donationWithLargeAmount = { ...mockDonation, amount: "1234.56" }
			render(
				<DonationForm
					mode="edit"
					initialData={donationWithLargeAmount}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("RM1,234.56")).toBeInTheDocument()
		})

		it("handles missing payment ID gracefully", () => {
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.queryByText(/Payment ID:/)).not.toBeInTheDocument()
		})

		it("allows empty internal notes", async () => {
			const user = userEvent.setup()
			render(
				<DonationForm
					mode="edit"
					initialData={mockDonation}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const submitButton = screen.getByText("Save Changes")
			await user.click(submitButton)

			await waitFor(() => {
				expect(mockOnSubmit).toHaveBeenCalledWith({
					donorName: "Test User",
					donorEmail: "<EMAIL>",
					internalNotes: "",
				})
			})
		})
	})
})
