import { describe, expect, it } from "vitest"
import { donationColumns } from "../utils/columns"
import { multiColumnFilterFn, statusFilterFn } from "../utils/filters"
import { formatAmount, formatDate, getStatusBadgeVariant } from "../utils/formatters"

describe("Donation Columns", () => {
	it("should have correct number of columns", () => {
		expect(donationColumns).toHaveLength(8)
	})

	it("should have select column as first column", () => {
		expect(donationColumns[0].id).toBe("select")
		expect(donationColumns[0].enableSorting).toBe(false)
		expect(donationColumns[0].enableHiding).toBe(false)
	})

	it("should have actions column as last column", () => {
		const lastColumn = donationColumns[donationColumns.length - 1]
		expect(lastColumn.id).toBe("actions")
		expect(lastColumn.enableHiding).toBe(false)
		expect(lastColumn.enableSorting).toBe(false)
	})

	it("should have donor name column with multi-column filter", () => {
		const donorNameColumn = donationColumns.find(
			(col) => col.accessorKey === "donorName",
		)
		expect(donorNameColumn).toBeDefined()
		expect(donorNameColumn?.filterFn).toBe(multiColumnFilterFn)
		expect(donorNameColumn?.enableHiding).toBe(false)
	})

	it("should have status column with status filter", () => {
		const statusColumn = donationColumns.find(
			(col) => col.accessorKey === "status",
		)
		expect(statusColumn).toBeDefined()
		expect(statusColumn?.filterFn).toBe(statusFilterFn)
		expect(statusColumn?.enableSorting).toBe(true)
	})

	it("should have amount column with sorting enabled", () => {
		const amountColumn = donationColumns.find(
			(col) => col.accessorKey === "amount",
		)
		expect(amountColumn).toBeDefined()
		expect(amountColumn?.enableSorting).toBe(true)
	})

	it("should have date column with sorting enabled", () => {
		const dateColumn = donationColumns.find(
			(col) => col.accessorKey === "createdAt",
		)
		expect(dateColumn).toBeDefined()
		expect(dateColumn?.enableSorting).toBe(true)
	})
})

describe("Filter Functions", () => {
	const mockDonation = {
		id: "1",
		donorName: "John Doe",
		donorEmail: "<EMAIL>",
		campaign: {
			id: "camp1",
			name: "Test Campaign",
			slug: "test-campaign",
			organizerId: "org1",
		},
		status: "completed" as const,
		amount: "100.00",
		currency: "MYR",
		createdAt: new Date("2024-01-01"),
		updatedAt: new Date("2024-01-01"),
		donorId: "donor1",
		campaignId: "camp1",
		chipPaymentId: null,
		donorMessage: null,
		internalNotes: null,
	}

	describe("multiColumnFilterFn", () => {
		it("should filter by donor name", () => {
			const row = { original: mockDonation } as any
			expect(multiColumnFilterFn(row, "donorName", "john")).toBe(true)
			expect(multiColumnFilterFn(row, "donorName", "jane")).toBe(false)
		})

		it("should filter by donor email", () => {
			const row = { original: mockDonation } as any
			expect(multiColumnFilterFn(row, "donorName", "john@example")).toBe(true)
			expect(multiColumnFilterFn(row, "donorName", "jane@example")).toBe(false)
		})

		it("should filter by campaign name", () => {
			const row = { original: mockDonation } as any
			expect(multiColumnFilterFn(row, "donorName", "test campaign")).toBe(true)
			expect(multiColumnFilterFn(row, "donorName", "other campaign")).toBe(
				false,
			)
		})

		it("should be case insensitive", () => {
			const row = { original: mockDonation } as any
			expect(multiColumnFilterFn(row, "donorName", "JOHN")).toBe(true)
			expect(multiColumnFilterFn(row, "donorName", "TEST CAMPAIGN")).toBe(true)
		})
	})

	describe("statusFilterFn", () => {
		it("should return true when no filter value", () => {
			const row = { getValue: () => "completed" } as any
			expect(statusFilterFn(row, "status", [])).toBe(true)
			expect(statusFilterFn(row, "status", undefined as any)).toBe(true)
		})

		it("should filter by status", () => {
			const row = { getValue: () => "completed" } as any
			expect(statusFilterFn(row, "status", ["completed"])).toBe(true)
			expect(statusFilterFn(row, "status", ["pending"])).toBe(false)
			expect(statusFilterFn(row, "status", ["completed", "pending"])).toBe(true)
		})
	})
})

describe("Formatters", () => {
	describe("formatAmount", () => {
		it("should format MYR currency correctly", () => {
			const result = formatAmount("100.50", "MYR")
			console.log("Actual result:", JSON.stringify(result))
			expect(result).toContain("100.50")
			expect(result).toContain("RM")
		})

		it("should format USD currency correctly", () => {
			expect(formatAmount("100.50", "USD")).toBe("$100.50")
		})

		it("should handle invalid amounts", () => {
			expect(formatAmount("invalid", "MYR")).toBe("MYR 0.00")
		})

		it("should default to MYR", () => {
			const result = formatAmount("100.50")
			expect(result).toContain("100.50")
			expect(result).toContain("RM")
		})
	})

	describe("formatDate", () => {
		it("should format date string correctly", () => {
			const result = formatDate("2024-01-15T10:00:00Z")
			expect(result).toBe("Jan 15, 2024")
		})

		it("should format Date object correctly", () => {
			const date = new Date("2024-01-15T10:00:00Z")
			const result = formatDate(date)
			expect(result).toBe("Jan 15, 2024")
		})

		it("should handle invalid dates", () => {
			expect(formatDate("invalid")).toBe("Invalid Date")
		})
	})

	describe("getStatusBadgeVariant", () => {
		it("should return correct variants for each status", () => {
			expect(getStatusBadgeVariant("completed")).toBe("default")
			expect(getStatusBadgeVariant("pending")).toBe("secondary")
			expect(getStatusBadgeVariant("failed")).toBe("destructive")
			expect(getStatusBadgeVariant("unknown")).toBe("outline")
		})
	})
})
