import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { DeleteDonationDialog } from "../DeleteDonationDialog"
import type { DonationWithCampaign } from "../DonationForm"

// Mock donation data
const mockDonation: DonationWithCampaign = {
	id: "donation-1",
	campaignId: "campaign-1",
	donorId: "donor-1",
	amount: "100.00",
	currency: "MYR",
	chipPaymentId: "chip-123",
	status: "completed",
	donorName: "<PERSON> Doe",
	donorEmail: "<EMAIL>",
	donorMessage: "Good luck with the campaign!",
	internalNotes: null,
	createdAt: "2024-01-01T00:00:00Z",
	updatedAt: "2024-01-01T00:00:00Z",
	campaign: {
		id: "campaign-1",
		name: "Test Campaign",
		slug: "test-campaign",
		organizerId: "organizer-1",
	},
}

const mockPendingDonation: DonationWithCampaign = {
	...mockDonation,
	id: "donation-2",
	status: "pending",
	amount: "50.50",
}

describe("DeleteDonationDialog", () => {
	const mockOnClose = vi.fn()
	const mockOnConfirm = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("renders nothing when donation is null", () => {
		render(
			<DeleteDonationDialog
				donation={null}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.queryByText("Delete Donation")).not.toBeInTheDocument()
	})

	it("renders dialog when donation is provided and isOpen is true", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByRole("alertdialog")).toBeInTheDocument()
		expect(
			screen.getByText("This action cannot be undone."),
		).toBeInTheDocument()
		expect(
			screen.getByText(
				/You are about to permanently delete a RM100.00 donation from John Doe/,
			),
		).toBeInTheDocument()
	})

	it("shows donation amount in confirmation input placeholder", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByText("RM100.00")).toBeInTheDocument()
		expect(
			screen.getByPlaceholderText("Enter donation amount"),
		).toBeInTheDocument()
	})

	it("disables delete button when confirmation text doesn't match", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const deleteButton = screen.getByRole("button", { name: /Delete Donation/ })
		expect(deleteButton).toBeDisabled()
	})

	it("enables delete button when confirmation text matches donation amount", async () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donation amount")
		const deleteButton = screen.getByRole("button", { name: /Delete Donation/ })

		fireEvent.change(input, { target: { value: "RM100.00" } })

		await waitFor(() => {
			expect(deleteButton).not.toBeDisabled()
		})
	})

	it("shows validation error when confirmation text is incorrect", async () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donation amount")
		fireEvent.change(input, { target: { value: "RM50.00" } })

		await waitFor(() => {
			expect(
				screen.getByText("Donation amount does not match"),
			).toBeInTheDocument()
		})
	})

	it("calls onConfirm when delete button is clicked with correct confirmation", async () => {
		mockOnConfirm.mockResolvedValue(undefined)

		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donation amount")
		const deleteButton = screen.getByRole("button", { name: /Delete Donation/ })

		fireEvent.change(input, { target: { value: "RM100.00" } })

		await waitFor(() => {
			expect(deleteButton).not.toBeDisabled()
		})

		fireEvent.click(deleteButton)

		await waitFor(() => {
			expect(mockOnConfirm).toHaveBeenCalledWith(mockDonation.id)
		})
	})

	it("shows loading state when isLoading is true", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={true}
			/>,
		)

		expect(screen.getByText("Deleting...")).toBeInTheDocument()
		expect(screen.getByRole("button", { name: /Deleting/ })).toBeDisabled()
	})

	it("shows completed donation warning when donation status is completed", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(
			screen.getByText("⚠️ This is a completed donation"),
		).toBeInTheDocument()
		expect(
			screen.getByText(
				/Deleting completed donations may affect financial records/,
			),
		).toBeInTheDocument()
	})

	it("does not show completed donation warning for pending donations", () => {
		render(
			<DeleteDonationDialog
				donation={mockPendingDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(
			screen.queryByText("⚠️ This is a completed donation"),
		).not.toBeInTheDocument()
	})

	it("formats different currency amounts correctly", () => {
		render(
			<DeleteDonationDialog
				donation={mockPendingDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByText("RM50.50")).toBeInTheDocument()
		expect(
			screen.getByText(
				/You are about to permanently delete a RM50.50 donation from John Doe/,
			),
		).toBeInTheDocument()
	})

	it("calls onClose when cancel button is clicked", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const cancelButton = screen.getByRole("button", { name: "Cancel" })
		fireEvent.click(cancelButton)

		expect(mockOnClose).toHaveBeenCalled()
	})

	it("resets confirmation text when dialog is closed and reopened", async () => {
		const { rerender } = render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donation amount")
		fireEvent.change(input, { target: { value: "Some text" } })
		expect(input).toHaveValue("Some text")

		// Close dialog by changing isOpen to false
		rerender(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={false}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		// Reopen dialog
		rerender(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		// The input should be reset when dialog reopens
		const newInput = screen.getByPlaceholderText("Enter donation amount")
		expect(newInput).toHaveValue("")
	})

	it("has proper accessibility attributes", () => {
		render(
			<DeleteDonationDialog
				donation={mockDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donation amount")
		expect(input).toHaveAttribute("id")

		const label = screen.getByText(
			"Type the donation amount to confirm deletion:",
		)
		expect(label).toHaveAttribute("for", input.id)
	})

	it("handles donations with different currencies", () => {
		const usdDonation: DonationWithCampaign = {
			...mockDonation,
			currency: "USD",
			amount: "75.25",
		}

		render(
			<DeleteDonationDialog
				donation={usdDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		// Note: The currency formatting might show as USD or $ depending on locale
		// We'll check for the amount value which should be consistent
		expect(screen.getByText(/75\.25/)).toBeInTheDocument()
	})

	it("handles failed donation status without completed warning", () => {
		const failedDonation: DonationWithCampaign = {
			...mockDonation,
			status: "failed",
		}

		render(
			<DeleteDonationDialog
				donation={failedDonation}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(
			screen.queryByText("⚠️ This is a completed donation"),
		).not.toBeInTheDocument()
	})
})
