import { beforeEach } from "node:test"
import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import type { Static } from "elysia"
import { describe, expect, it, vi } from "vitest"
import { DonationsTable } from "../DonationsTable"
import "@testing-library/jest-dom"

// Mock donation data
const mockDonations: Static<typeof donationWithCampaignDto>[] = [
	{
		id: "1",
		campaignId: "campaign-1",
		donorId: "donor-1",
		amount: "100.00",
		currency: "MYR",
		chipPaymentId: "chip-123",
		status: "completed",
		donorName: "<PERSON>",
		donorEmail: "<EMAIL>",
		donorMessage: "Good luck with the campaign!",
		internalNotes: null,
		createdAt: new Date("2024-01-15T10:00:00Z"),
		updatedAt: new Date("2024-01-15T10:00:00Z"),
		campaign: {
			id: "campaign-1",
			name: "Help Build School",
			slug: "help-build-school",
			organizerId: "org-1",
		},
	},
	{
		id: "2",
		campaignId: "campaign-2",
		donorId: "donor-2",
		amount: "250.50",
		currency: "MYR",
		chipPaymentId: null,
		status: "pending",
		donorName: "Jane Smith",
		donorEmail: "<EMAIL>",
		donorMessage: null,
		internalNotes: "Follow up needed",
		createdAt: new Date("2024-01-20T10:00:00Z"),
		updatedAt: new Date("2024-01-20T10:00:00Z"),
		campaign: {
			id: "campaign-2",
			name: "Medical Fund",
			slug: "medical-fund",
			organizerId: "org-1",
		},
	},
	{
		id: "3",
		campaignId: "campaign-1",
		donorId: "donor-3",
		amount: "50.00",
		currency: "MYR",
		chipPaymentId: "chip-456",
		status: "failed",
		donorName: "Bob Wilson",
		donorEmail: "<EMAIL>",
		donorMessage: "Hope this helps",
		internalNotes: null,
		createdAt: new Date("2024-01-10T10:00:00Z"),
		updatedAt: new Date("2024-01-10T10:00:00Z"),
		campaign: {
			id: "campaign-1",
			name: "Help Build School",
			slug: "help-build-school",
			organizerId: "org-1",
		},
	},
]

// Helper function to render with QueryClient
function renderWithQueryClient(ui: React.ReactElement) {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	})

	return render(
		<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
	)
}

describe("DonationsTable", () => {
	const mockOnView = vi.fn()
	const mockOnEdit = vi.fn()
	const mockOnDelete = vi.fn()
	const mockOnStatusChange = vi.fn()
	const mockOnExport = vi.fn()
	const mockOnRetry = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	const defaultProps = {
		donations: mockDonations,
		isLoading: false,
		error: null,
		onView: mockOnView,
		onEdit: mockOnEdit,
		onDelete: mockOnDelete,
		onStatusChange: mockOnStatusChange,
		onExport: mockOnExport,
		onRetry: mockOnRetry,
	}

	it("renders donations table correctly", () => {
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Check table headers
		expect(screen.getByText("Donor Name")).toBeInTheDocument()
		expect(screen.getByText("Email")).toBeInTheDocument()
		expect(screen.getByText("Amount")).toBeInTheDocument()
		expect(screen.getByText("Campaign")).toBeInTheDocument()
		expect(screen.getByText("Status")).toBeInTheDocument()
		expect(screen.getByText("Date")).toBeInTheDocument()

		// Check donation data
		expect(screen.getByText("John Doe")).toBeInTheDocument()
		expect(screen.getByText("<EMAIL>")).toBeInTheDocument()
		expect(screen.getByText("RM100.00")).toBeInTheDocument()
		expect(screen.getByText("Help Build School")).toBeInTheDocument()
		expect(screen.getByText("Completed")).toBeInTheDocument()

		expect(screen.getByText("Jane Smith")).toBeInTheDocument()
		expect(screen.getByText("<EMAIL>")).toBeInTheDocument()
		expect(screen.getByText("RM250.50")).toBeInTheDocument()
		expect(screen.getByText("Medical Fund")).toBeInTheDocument()
		expect(screen.getByText("Pending")).toBeInTheDocument()

		expect(screen.getByText("Bob Wilson")).toBeInTheDocument()
		expect(screen.getByText("Failed")).toBeInTheDocument()
	})

	it("renders loading state correctly", () => {
		renderWithQueryClient(
			<DonationsTable {...defaultProps} isLoading={true} donations={[]} />,
		)

		// Should show skeleton rows
		const skeletonRows = screen.getAllByRole("row")
		// Header row + 10 skeleton rows (default page size)
		expect(skeletonRows).toHaveLength(11)

		// Controls should be disabled
		const searchInput = screen.getByPlaceholderText(
			"Filter by donor name, email, or campaign...",
		)
		expect(searchInput).toBeDisabled()

		const statusButton = screen.getByRole("button", { name: /status/i })
		expect(statusButton).toBeDisabled()
	})

	it("renders error state correctly", () => {
		const error = new Error("Failed to load donations")
		renderWithQueryClient(
			<DonationsTable
				{...defaultProps}
				error={error}
				isLoading={false}
				donations={[]}
			/>,
		)

		expect(screen.getByText("Failed to load donations")).toBeInTheDocument()
		expect(screen.getByText(error.message)).toBeInTheDocument()
		expect(
			screen.getByRole("button", { name: /try again/i }),
		).toBeInTheDocument()
	})

	it("calls onRetry when retry button is clicked", () => {
		const error = new Error("Failed to load donations")
		renderWithQueryClient(
			<DonationsTable
				{...defaultProps}
				error={error}
				isLoading={false}
				donations={[]}
			/>,
		)

		const retryButton = screen.getByRole("button", { name: /try again/i })
		fireEvent.click(retryButton)

		expect(mockOnRetry).toHaveBeenCalledTimes(1)
	})

	it("renders empty state when no donations exist", () => {
		renderWithQueryClient(<DonationsTable {...defaultProps} donations={[]} />)

		expect(screen.getByText("No donations found.")).toBeInTheDocument()
	})

	it("filters donations by search term", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		const searchInput = screen.getByPlaceholderText(
			"Filter by donor name, email, or campaign...",
		)

		await user.type(searchInput, "John")

		await waitFor(() => {
			expect(screen.getByText("John Doe")).toBeInTheDocument()
			expect(screen.queryByText("Jane Smith")).not.toBeInTheDocument()
			expect(screen.queryByText("Bob Wilson")).not.toBeInTheDocument()
		})
	})

	it("filters donations by email", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		const searchInput = screen.getByPlaceholderText(
			"Filter by donor name, email, or campaign...",
		)

		await user.type(searchInput, "<EMAIL>")

		await waitFor(() => {
			expect(screen.getByText("Jane Smith")).toBeInTheDocument()
			expect(screen.queryByText("John Doe")).not.toBeInTheDocument()
			expect(screen.queryByText("Bob Wilson")).not.toBeInTheDocument()
		})
	})

	it("filters donations by campaign name", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		const searchInput = screen.getByPlaceholderText(
			"Filter by donor name, email, or campaign...",
		)

		await user.type(searchInput, "Medical Fund")

		await waitFor(() => {
			expect(screen.getByText("Jane Smith")).toBeInTheDocument()
			expect(screen.queryByText("John Doe")).not.toBeInTheDocument()
			expect(screen.queryByText("Bob Wilson")).not.toBeInTheDocument()
		})
	})

	it("clears search filter when clear button is clicked", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		const searchInput = screen.getByPlaceholderText(
			"Filter by donor name, email, or campaign...",
		)

		await user.type(searchInput, "John")

		// Wait for filter to apply
		await waitFor(() => {
			expect(screen.queryByText("Jane Smith")).not.toBeInTheDocument()
		})

		// Click clear button
		const clearButton = screen.getByLabelText("Clear filter")
		await user.click(clearButton)

		await waitFor(() => {
			expect(screen.getByText("John Doe")).toBeInTheDocument()
			expect(screen.getByText("Jane Smith")).toBeInTheDocument()
			expect(screen.getByText("Bob Wilson")).toBeInTheDocument()
		})
	})

	it("filters donations by status", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Open status filter
		const statusButton = screen.getByRole("button", { name: /status/i })
		await user.click(statusButton)

		// Select "completed" status
		const completedCheckbox = screen.getByLabelText("Completed")
		await user.click(completedCheckbox)

		await waitFor(() => {
			expect(screen.getByText("John Doe")).toBeInTheDocument()
			expect(screen.queryByText("Jane Smith")).not.toBeInTheDocument()
			expect(screen.queryByText("Bob Wilson")).not.toBeInTheDocument()
		})
	})

	it("shows status counts in filter", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Open status filter
		const statusButton = screen.getByRole("button", { name: /status/i })
		await user.click(statusButton)

		// Check status counts
		expect(screen.getByText("Completed")).toBeInTheDocument()
		expect(screen.getByText("Pending")).toBeInTheDocument()
		expect(screen.getByText("Failed")).toBeInTheDocument()
		// Each status should have count of 1
		const countElements = screen.getAllByText("1")
		expect(countElements).toHaveLength(3)
	})

	it("toggles column visibility", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Open column visibility menu
		const viewButton = screen.getByRole("button", { name: /view/i })
		await user.click(viewButton)

		// Hide email column
		const emailCheckbox = screen.getByRole("menuitemcheckbox", {
			name: /email/i,
		})
		await user.click(emailCheckbox)

		await waitFor(() => {
			expect(screen.queryByText("Email")).not.toBeInTheDocument()
			expect(screen.queryByText("<EMAIL>")).not.toBeInTheDocument()
		})
	})

	it("sorts donations by column", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Click on donor name header to sort
		const donorNameHeader = screen.getByText("Donor Name")
		await user.click(donorNameHeader)

		await waitFor(() => {
			const rows = screen.getAllByRole("row")
			// Skip header row, check data rows
			const firstDataRow = rows[1]
			expect(firstDataRow).toHaveTextContent("Bob Wilson")
		})
	})

	it("selects and deselects rows", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Select first row
		const checkboxes = screen.getAllByRole("checkbox")
		const firstRowCheckbox = checkboxes[1] // Skip header checkbox
		await user.click(firstRowCheckbox)

		expect(firstRowCheckbox).toBeChecked()

		// Select all rows
		const headerCheckbox = checkboxes[0]
		await user.click(headerCheckbox)

		// All row checkboxes should be checked
		checkboxes.slice(1).forEach((checkbox) => {
			expect(checkbox).toBeChecked()
		})
	})

	it("shows delete button when rows are selected", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Initially no delete button
		expect(
			screen.queryByRole("button", { name: /delete/i }),
		).not.toBeInTheDocument()

		// Select first row
		const checkboxes = screen.getAllByRole("checkbox")
		const firstRowCheckbox = checkboxes[1]
		await user.click(firstRowCheckbox)

		// Delete button should appear
		expect(screen.getByRole("button", { name: /delete/i })).toBeInTheDocument()
	})

	it("calls onDelete when delete is confirmed", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Select first row
		const checkboxes = screen.getAllByRole("checkbox")
		const firstRowCheckbox = checkboxes[1]
		await user.click(firstRowCheckbox)

		// Click delete button
		const deleteButton = screen.getByRole("button", { name: /delete/i })
		await user.click(deleteButton)

		// Confirm deletion
		const confirmButton = screen.getByRole("button", { name: /delete/i })
		await user.click(confirmButton)

		expect(mockOnDelete).toHaveBeenCalledWith("1")
	})

	it("calls onExport when export button is clicked", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		const exportButton = screen.getByRole("button", { name: /export/i })
		await user.click(exportButton)

		expect(mockOnExport).toHaveBeenCalledWith(undefined)
	})

	it("calls onExport with selected IDs when rows are selected", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Select first row
		const checkboxes = screen.getAllByRole("checkbox")
		const firstRowCheckbox = checkboxes[1]
		await user.click(firstRowCheckbox)

		const exportButton = screen.getByRole("button", { name: /export/i })
		await user.click(exportButton)

		expect(mockOnExport).toHaveBeenCalledWith(["1"])
	})

	it("opens row actions menu and calls handlers", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Click on first row actions button
		const actionButtons = screen.getAllByLabelText("Edit donation")
		await user.click(actionButtons[0])

		// Click view details
		const viewButton = screen.getByText("View Details")
		await user.click(viewButton)

		expect(mockOnView).toHaveBeenCalledWith("1")
	})

	it("calls onEdit from row actions", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Click on first row actions button
		const actionButtons = screen.getAllByLabelText("Edit donation")
		await user.click(actionButtons[0])

		// Click edit
		const editButton = screen.getByText("Edit")
		await user.click(editButton)

		expect(mockOnEdit).toHaveBeenCalledWith("1")
	})

	it("calls onStatusChange from row actions", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Click on first row actions button (completed donation)
		const actionButtons = screen.getAllByLabelText("Edit donation")
		await user.click(actionButtons[0])

		// Click mark as pending (should be enabled since current status is completed)
		const pendingButton = screen.getByText("Mark as Pending")
		await user.click(pendingButton)

		expect(mockOnStatusChange).toHaveBeenCalledWith("1", "pending")
	})

	it("disables status change options for current status", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Click on first row actions button (completed donation)
		const actionButtons = screen.getAllByLabelText("Edit donation")
		await user.click(actionButtons[0])

		// Mark as completed should be disabled since it's already completed
		const completedButton = screen.getByText("Mark as Completed")
		expect(completedButton.closest("div")).toHaveAttribute(
			"data-disabled",
			"true",
		)
	})

	it("changes page size", async () => {
		const user = userEvent.setup()
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Open page size selector
		const pageSizeSelect = screen.getByRole("combobox")
		await user.click(pageSizeSelect)

		// Select 25 items per page
		const option25 = screen.getByText("25")
		await user.click(option25)

		// Verify the selection changed
		expect(screen.getByDisplayValue("25")).toBeInTheDocument()
	})

	it("navigates between pages", async () => {
		const user = userEvent.setup()
		// Create more donations to test pagination
		const manyDonations = Array.from({ length: 15 }, (_, i) => ({
			...mockDonations[0],
			id: `donation-${i}`,
			donorName: `Donor ${i}`,
			donorEmail: `donor${i}@example.com`,
		}))

		renderWithQueryClient(
			<DonationsTable {...defaultProps} donations={manyDonations} />,
		)

		// Should show page navigation buttons
		const nextButton = screen.getByLabelText("Go to next page")
		expect(nextButton).toBeInTheDocument()
		expect(nextButton).not.toBeDisabled()

		await user.click(nextButton)

		// Should be on page 2 now
		const pageInfo = screen.getByText("11-15 of 15")
		expect(pageInfo).toBeInTheDocument()
	})

	it("shows correct pagination info", () => {
		renderWithQueryClient(<DonationsTable {...defaultProps} />)

		// Should show "1-3 of 3" for 3 donations
		expect(screen.getByText("1-3 of 3")).toBeInTheDocument()
	})
})
