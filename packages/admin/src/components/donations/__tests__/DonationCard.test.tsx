import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import type { Static } from "elysia"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { DonationCard } from "../DonationCard"

// Mock the useUpdateDonationStatus hook
const mockUseUpdateDonationStatus = vi.fn()

vi.mock("@/hooks/useDonations", () => ({
	useUpdateDonationStatus: () => mockUseUpdateDonationStatus(),
}))

// Helper function to render with QueryClient
function renderWithQueryClient(ui: React.ReactElement) {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	})

	return render(
		<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
	)
}

// Mock donation data
const mockDonation: Static<typeof donationWithCampaignDto> = {
	id: "donation-1",
	campaignId: "campaign-1",
	donorId: "donor-1",
	amount: "100.00",
	currency: "MYR",
	chipPaymentId: "chip-123",
	status: "completed",
	donorName: "John Doe",
	donorEmail: "<EMAIL>",
	donorMessage: "Keep up the great work!",
	internalNotes: null,
	createdAt: new Date("2024-01-15T10:00:00Z"),
	updatedAt: new Date("2024-01-15T10:00:00Z"),
	campaign: {
		id: "campaign-1",
		name: "Test Campaign",
		slug: "test-campaign",
		organizerId: "org-1",
	},
}

const mockPendingDonation: Static<typeof donationWithCampaignDto> = {
	...mockDonation,
	id: "donation-2",
	status: "pending",
	amount: "50.00",
	donorName: "Jane Smith",
}

const mockFailedDonation: Static<typeof donationWithCampaignDto> = {
	...mockDonation,
	id: "donation-3",
	status: "failed",
	amount: "25.00",
	donorName: "Bob Wilson",
}

const mockDonationWithoutMessage: Static<typeof donationWithCampaignDto> = {
	...mockDonation,
	id: "donation-4",
	donorMessage: null,
}

describe("DonationCard", () => {
	const mockOnView = vi.fn()
	const mockOnEdit = vi.fn()
	const mockOnDelete = vi.fn()
	const mockOnStatusChange = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()

		// Default mock implementation
		mockUseUpdateDonationStatus.mockReturnValue({
			mutate: vi.fn(),
			isPending: false,
		})
	})

	it("renders donation information correctly", () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		expect(screen.getByText("RM100.00")).toBeInTheDocument()
		expect(screen.getByText("John Doe")).toBeInTheDocument()
		expect(screen.getByText("Test Campaign")).toBeInTheDocument()
		expect(screen.getByText("Completed")).toBeInTheDocument()
		expect(screen.getByText("Keep up the great work!")).toBeInTheDocument()
		expect(screen.getByText("Jan 15, 2024")).toBeInTheDocument()
	})

	it("renders pending donation with correct badge", () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockPendingDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		expect(screen.getByText("RM50.00")).toBeInTheDocument()
		expect(screen.getByText("Jane Smith")).toBeInTheDocument()
		expect(screen.getByText("Pending")).toBeInTheDocument()
	})

	it("renders failed donation with correct badge", () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockFailedDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		expect(screen.getByText("RM25.00")).toBeInTheDocument()
		expect(screen.getByText("Bob Wilson")).toBeInTheDocument()
		expect(screen.getByText("Failed")).toBeInTheDocument()
	})

	it("handles donation without message", () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonationWithoutMessage}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		expect(screen.getByText("RM100.00")).toBeInTheDocument()
		expect(
			screen.queryByText("Keep up the great work!"),
		).not.toBeInTheDocument()
	})

	it("calls onView when view menu item is clicked", async () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		// Open dropdown menu
		const menuButton = screen.getByRole("button", { name: /open menu/i })
		fireEvent.click(menuButton)

		// Wait for dropdown to open and find view option
		await waitFor(() => {
			const viewButton = screen.getByText("View Details")
			fireEvent.click(viewButton)
		})

		expect(mockOnView).toHaveBeenCalledWith("donation-1")
	})

	it("calls onEdit when edit menu item is clicked", async () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		// Open dropdown menu
		const menuButton = screen.getByRole("button", { name: /open menu/i })
		fireEvent.click(menuButton)

		// Wait for dropdown to open and find edit option
		await waitFor(() => {
			const editButton = screen.getByText("Edit Donation")
			fireEvent.click(editButton)
		})

		expect(mockOnEdit).toHaveBeenCalledWith("donation-1")
	})

	it("calls onDelete when delete menu item is clicked", async () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		// Open dropdown menu
		const menuButton = screen.getByRole("button", { name: /open menu/i })
		fireEvent.click(menuButton)

		// Wait for dropdown to open and find delete option
		await waitFor(() => {
			const deleteButton = screen.getByText("Delete Donation")
			fireEvent.click(deleteButton)
		})

		expect(mockOnDelete).toHaveBeenCalledWith("donation-1")
	})

	it("renders StatusToggle component", () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		// StatusToggle should be present
		expect(
			screen.getByRole("button", { name: /change donation status/i }),
		).toBeInTheDocument()
	})

	it("disables menu button when loading", () => {
		renderWithQueryClient(
			<DonationCard
				donation={mockDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
				isLoading={true}
			/>,
		)

		const menuButton = screen.getByRole("button", { name: /open menu/i })
		expect(menuButton).toBeDisabled()
	})

	it("truncates long donor names", () => {
		const longNameDonation: Static<typeof donationWithCampaignDto> = {
			...mockDonation,
			donorName:
				"This is a very long donor name that should be truncated when displayed in the card component",
		}

		renderWithQueryClient(
			<DonationCard
				donation={longNameDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		const donorNameElement = screen.getByText(longNameDonation.donorName)
		expect(donorNameElement).toHaveClass("truncate")
	})

	it("truncates long campaign names", () => {
		const longCampaignNameDonation: Static<typeof donationWithCampaignDto> = {
			...mockDonation,
			campaign: {
				...mockDonation.campaign,
				name: "This is a very long campaign name that should be truncated when displayed",
			},
		}

		renderWithQueryClient(
			<DonationCard
				donation={longCampaignNameDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		const campaignNameElement = screen.getByText(
			longCampaignNameDonation.campaign.name,
		)
		expect(campaignNameElement).toHaveClass("truncate")
	})

	it("formats amount correctly for different currencies", () => {
		const usdDonation: Static<typeof donationWithCampaignDto> = {
			...mockDonation,
			amount: "150.50",
			currency: "USD",
		}

		renderWithQueryClient(
			<DonationCard
				donation={usdDonation}
				onView={mockOnView}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusChange={mockOnStatusChange}
			/>,
		)

		// Note: The exact format may vary based on locale, but should include the amount
		expect(screen.getByText(/150\.50/)).toBeInTheDocument()
	})
})
