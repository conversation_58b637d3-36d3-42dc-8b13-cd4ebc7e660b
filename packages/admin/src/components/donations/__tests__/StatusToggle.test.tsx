import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { StatusToggle } from "../StatusToggle"

// Mock the useUpdateDonationStatus hook
const mockUseUpdateDonationStatus = vi.fn()

vi.mock("@/hooks/useDonations", () => ({
	useUpdateDonationStatus: () => mockUseUpdateDonationStatus(),
}))

// Helper function to render with QueryClient
function renderWithQueryClient(ui: React.ReactElement) {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	})

	return render(
		<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
	)
}

describe("StatusToggle", () => {
	const mockMutate = vi.fn()
	const defaultProps = {
		donationId: "test-donation-id",
		currentStatus: "pending" as const,
	}

	beforeEach(() => {
		vi.clearAllMocks()

		// Default mock implementation
		mockUseUpdateDonationStatus.mockReturnValue({
			mutate: mockMutate,
			isPending: false,
			variables: undefined,
		})
	})

	describe("Rendering", () => {
		it("renders with pending status", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			expect(select).toBeInTheDocument()
			expect(select).toHaveValue("pending")
			expect(select).toHaveClass(
				"text-yellow-600",
				"bg-yellow-50",
				"border-yellow-200",
			)
		})

		it("renders with completed status", () => {
			renderWithQueryClient(
				<StatusToggle {...defaultProps} currentStatus="completed" />,
			)

			const select = screen.getByRole("combobox")
			expect(select).toBeInTheDocument()
			expect(select).toHaveValue("completed")
			expect(select).toHaveClass(
				"text-green-600",
				"bg-green-50",
				"border-green-200",
			)
		})

		it("renders with failed status", () => {
			renderWithQueryClient(
				<StatusToggle {...defaultProps} currentStatus="failed" />,
			)

			const select = screen.getByRole("combobox")
			expect(select).toBeInTheDocument()
			expect(select).toHaveValue("failed")
			expect(select).toHaveClass("text-red-600", "bg-red-50", "border-red-200")
		})

		it("applies custom className", () => {
			const { container } = renderWithQueryClient(
				<StatusToggle {...defaultProps} className="custom-class" />,
			)

			expect(container.firstChild).toHaveClass("custom-class")
		})

		it("renders with small size", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} size="sm" />)

			const select = screen.getByRole("combobox")
			expect(select).toHaveClass("h-8", "text-xs")
		})
	})

	describe("Accessibility", () => {
		it("has proper ARIA attributes", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")

			expect(select).toHaveAttribute("id", "status-select-test-donation-id")
			expect(select).toHaveAttribute(
				"aria-labelledby",
				"status-label-test-donation-id",
			)
			expect(select).toHaveAttribute(
				"aria-describedby",
				"status-select-test-donation-id-description",
			)
		})

		it("has screen reader description", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const description = screen.getByText(
				"Donation status is currently pending",
			)
			expect(description).toHaveClass("sr-only")
		})

		it("updates screen reader description for different statuses", () => {
			renderWithQueryClient(
				<StatusToggle {...defaultProps} currentStatus="completed" />,
			)

			const description = screen.getByText(
				"Donation status is currently completed",
			)
			expect(description).toHaveClass("sr-only")
		})
	})

	describe("User Interactions", () => {
		it("opens confirmation dialog when status is changed", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const completedOption = screen.getByRole("option", { name: "Completed" })
			await user.click(completedOption)

			// Should open confirmation dialog
			expect(screen.getByText("Confirm Status Change")).toBeInTheDocument()
			expect(
				screen.getByText(
					/change the donation status from Pending to Completed/,
				),
			).toBeInTheDocument()
		})

		it("shows failure reason input when selecting failed status", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const failedOption = screen.getByRole("option", { name: "Failed" })
			await user.click(failedOption)

			// Should show failure reason textarea
			expect(screen.getByLabelText(/Failure Reason/)).toBeInTheDocument()
			expect(
				screen.getByPlaceholderText(
					"Enter the reason for marking this donation as failed...",
				),
			).toBeInTheDocument()
		})

		it("calls mutate when confirmation is clicked", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const completedOption = screen.getByRole("option", { name: "Completed" })
			await user.click(completedOption)

			const confirmButton = screen.getByRole("button", {
				name: "Confirm Change",
			})
			await user.click(confirmButton)

			expect(mockMutate).toHaveBeenCalledWith(
				{
					donationId: "test-donation-id",
					status: "completed",
					failureReason: undefined,
				},
				expect.any(Object),
			)
		})

		it("includes failure reason when confirming failed status", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const failedOption = screen.getByRole("option", { name: "Failed" })
			await user.click(failedOption)

			const reasonTextarea = screen.getByLabelText(/Failure Reason/)
			await user.type(reasonTextarea, "Payment gateway error")

			const confirmButton = screen.getByRole("button", {
				name: "Confirm Change",
			})
			await user.click(confirmButton)

			expect(mockMutate).toHaveBeenCalledWith(
				{
					donationId: "test-donation-id",
					status: "failed",
					failureReason: "Payment gateway error",
				},
				expect.any(Object),
			)
		})

		it("closes dialog when cancel is clicked", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const completedOption = screen.getByRole("option", { name: "Completed" })
			await user.click(completedOption)

			const cancelButton = screen.getByRole("button", { name: "Cancel" })
			await user.click(cancelButton)

			expect(
				screen.queryByText("Confirm Status Change"),
			).not.toBeInTheDocument()
			expect(mockMutate).not.toHaveBeenCalled()
		})

		it("does not open dialog when selecting same status", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const pendingOption = screen.getByRole("option", { name: "Pending" })
			await user.click(pendingOption)

			expect(
				screen.queryByText("Confirm Status Change"),
			).not.toBeInTheDocument()
		})

		it("does not open dialog when disabled", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} disabled />)

			const select = screen.getByRole("combobox")
			expect(select).toBeDisabled()

			// Should not be able to interact
			await user.click(select)
			expect(
				screen.queryByText("Confirm Status Change"),
			).not.toBeInTheDocument()
		})
	})

	describe("Optimistic Updates", () => {
		it("shows optimistic state immediately", async () => {
			mockUseUpdateDonationStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: {
					donationId: "test-donation-id",
					status: "completed",
				},
			})

			renderWithQueryClient(
				<StatusToggle {...defaultProps} currentStatus="pending" />,
			)

			const select = screen.getByRole("combobox")

			// During pending state with variables.status = "completed", should show optimistic state
			expect(select).toHaveValue("completed")
			expect(select).toHaveClass(
				"text-green-600",
				"bg-green-50",
				"border-green-200",
			)
		})

		it("handles error state correctly", () => {
			// The error handling is now managed by the hook/mutation
			// This test can focus on the component behavior
			renderWithQueryClient(
				<StatusToggle {...defaultProps} currentStatus="pending" />,
			)

			const select = screen.getByRole("combobox")
			expect(select).toHaveValue("pending")
		})
	})

	describe("Loading States", () => {
		it("shows loading spinner during mutation", () => {
			mockUseUpdateDonationStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			// Should show loading spinner
			const loader = screen.getByTestId("loading-spinner")
			expect(loader).toBeInTheDocument()
			expect(loader).toHaveClass("animate-spin")
		})

		it("disables select during mutation", () => {
			mockUseUpdateDonationStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")

			// Select should be disabled during loading
			expect(select).toBeDisabled()
		})

		it("updates screen reader description during mutation", () => {
			mockUseUpdateDonationStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			// Should include loading message in description
			expect(
				screen.getByText(/Status is being updated/, { exact: false }),
			).toBeInTheDocument()
		})

		it("disables dialog buttons during mutation", async () => {
			const user = userEvent.setup()

			// First render without pending state to open dialog
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const completedOption = screen.getByRole("option", { name: "Completed" })
			await user.click(completedOption)

			// Now mock pending state
			mockUseUpdateDonationStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			// Re-render to apply pending state
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			// Click select again to open dialog with pending state
			const newSelect = screen.getByRole("combobox")
			await user.click(newSelect)

			const newCompletedOption = screen.getByRole("option", {
				name: "Completed",
			})
			await user.click(newCompletedOption)

			const confirmButton = screen.getByRole("button", {
				name: "Confirm Change",
			})
			const cancelButton = screen.getByRole("button", { name: "Cancel" })

			expect(confirmButton).toBeDisabled()
			expect(cancelButton).toBeDisabled()
		})
	})

	describe("Edge Cases", () => {
		it("prevents multiple simultaneous requests", async () => {
			const user = userEvent.setup()
			mockUseUpdateDonationStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")

			// Should be disabled during pending state
			expect(select).toBeDisabled()

			// Try to click - should not open dialog
			await user.click(select)
			expect(
				screen.queryByText("Confirm Status Change"),
			).not.toBeInTheDocument()
		})

		it("handles state changes from parent", () => {
			const { rerender } = renderWithQueryClient(
				<StatusToggle {...defaultProps} currentStatus="pending" />,
			)

			let select = screen.getByRole("combobox")
			expect(select).toHaveValue("pending")

			// Parent updates the state
			rerender(<StatusToggle {...defaultProps} currentStatus="completed" />)

			select = screen.getByRole("combobox")
			expect(select).toHaveValue("completed")
		})

		it("limits failure reason character count", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const failedOption = screen.getByRole("option", { name: "Failed" })
			await user.click(failedOption)

			const reasonTextarea = screen.getByLabelText(/Failure Reason/)
			const longText = "a".repeat(600) // Exceeds 500 character limit

			await user.type(reasonTextarea, longText)

			// Should be limited to 500 characters
			expect(reasonTextarea).toHaveValue("a".repeat(500))
			expect(screen.getByText("500/500 characters")).toBeInTheDocument()
		})

		it("resets failure reason when dialog is cancelled", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const failedOption = screen.getByRole("option", { name: "Failed" })
			await user.click(failedOption)

			const reasonTextarea = screen.getByLabelText(/Failure Reason/)
			await user.type(reasonTextarea, "Some reason")

			const cancelButton = screen.getByRole("button", { name: "Cancel" })
			await user.click(cancelButton)

			// Open dialog again
			await user.click(select)
			await user.click(failedOption)

			const newReasonTextarea = screen.getByLabelText(/Failure Reason/)
			expect(newReasonTextarea).toHaveValue("")
		})
	})

	describe("Dialog Content", () => {
		it("shows appropriate message for pending to completed change", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const completedOption = screen.getByRole("option", { name: "Completed" })
			await user.click(completedOption)

			expect(
				screen.getByText(
					"This will mark the donation as successfully completed.",
				),
			).toBeInTheDocument()
		})

		it("shows appropriate message for failed status change", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const failedOption = screen.getByRole("option", { name: "Failed" })
			await user.click(failedOption)

			expect(
				screen.getByText(
					"This action will mark the donation as failed and may affect reporting.",
				),
			).toBeInTheDocument()
		})

		it("applies correct button styling for different statuses", async () => {
			const user = userEvent.setup()
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const select = screen.getByRole("combobox")
			await user.click(select)

			const failedOption = screen.getByRole("option", { name: "Failed" })
			await user.click(failedOption)

			const confirmButton = screen.getByRole("button", {
				name: "Confirm Change",
			})
			expect(confirmButton).toHaveClass("bg-red-600", "hover:bg-red-700")
		})
	})
})
