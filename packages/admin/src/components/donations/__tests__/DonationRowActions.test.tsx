/**
 * @vitest-environment jsdom
 */

import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import type { Static } from "elysia"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { DonationRowActions } from "../DonationRowActions"

type DonationWithCampaign = Static<typeof donationWithCampaignDto>

// Mock donation data
const mockDonation: DonationWithCampaign = {
	id: "test-donation-id",
	campaignId: "test-campaign-id",
	donorId: "test-donor-id",
	amount: "100.00",
	currency: "MYR",
	chipPaymentId: "chip-payment-123",
	status: "pending",
	donorName: "John Doe",
	donorEmail: "<EMAIL>",
	donorMessage: "Good luck with the campaign!",
	internalNotes: null,
	createdAt: new Date("2024-01-15T10:00:00Z"),
	updatedAt: new Date("2024-01-15T10:00:00Z"),
	campaign: {
		id: "test-campaign-id",
		name: "Test Campaign",
		slug: "test-campaign",
		organizerId: "test-organizer-id",
	},
}

describe("DonationRowActions", () => {
	const mockOnView = vi.fn()
	const mockOnEdit = vi.fn()
	const mockOnDelete = vi.fn()
	const mockOnStatusChange = vi.fn()

	const defaultProps = {
		donation: mockDonation,
		onView: mockOnView,
		onEdit: mockOnEdit,
		onDelete: mockOnDelete,
		onStatusChange: mockOnStatusChange,
	}

	beforeEach(() => {
		vi.clearAllMocks()
		mockOnStatusChange.mockResolvedValue(undefined)
	})

	describe("Basic Rendering", () => {
		it("renders the dropdown trigger button", () => {
			render(<DonationRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button", {
				name: "Actions for donation from John Doe",
			})
			expect(triggerButton).toBeInTheDocument()
			expect(triggerButton).toHaveAttribute(
				"aria-label",
				"Actions for donation from John Doe",
			)
		})

		it("renders all basic action items when dropdown is opened", async () => {
			const user = userEvent.setup()
			render(<DonationRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button", {
				name: "Actions for donation from John Doe",
			})
			await user.click(triggerButton)

			expect(screen.getByText("View Details")).toBeInTheDocument()
			expect(screen.getByText("Edit")).toBeInTheDocument()
			expect(screen.getByText("Delete")).toBeInTheDocument()
		})

		it("calls onView when View Details is clicked", async () => {
			const user = userEvent.setup()
			render(<DonationRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const viewItem = screen.getByText("View Details")
			await user.click(viewItem)

			expect(mockOnView).toHaveBeenCalledWith("test-donation-id")
		})

		it("calls onEdit when Edit is clicked", async () => {
			const user = userEvent.setup()
			render(<DonationRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const editItem = screen.getByText("Edit")
			await user.click(editItem)

			expect(mockOnEdit).toHaveBeenCalledWith("test-donation-id")
		})

		it("calls onDelete when Delete is clicked", async () => {
			const user = userEvent.setup()
			render(<DonationRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const deleteItem = screen.getByText("Delete")
			await user.click(deleteItem)

			expect(mockOnDelete).toHaveBeenCalledWith("test-donation-id")
		})
	})

	describe("Status Actions", () => {
		it("shows available status actions for pending donation", async () => {
			const user = userEvent.setup()
			render(<DonationRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			// Should show Change Status submenu for multiple options
			expect(screen.getByText("Change Status")).toBeInTheDocument()
		})

		it("shows different status actions for completed donation", async () => {
			const user = userEvent.setup()
			const completedDonation = {
				...mockDonation,
				status: "completed" as const,
			}
			render(
				<DonationRowActions {...defaultProps} donation={completedDonation} />,
			)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			// Should show Change Status submenu
			expect(screen.getByText("Change Status")).toBeInTheDocument()
		})
	})
})
