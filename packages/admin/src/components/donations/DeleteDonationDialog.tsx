import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	Input,
	Label,
} from "@donorcare/ui"
import { Al<PERSON><PERSON>riangle, Loader2 } from "lucide-react"
import { useId, useState } from "react"
import type { DonationWithCampaign } from "./DonationForm"

export interface DeleteDonationDialogProps {
	/** The donation to delete, or null if dialog is closed */
	donation: DonationWithCampaign | null
	/** Whether the dialog is open */
	isOpen: boolean
	/** Function to close the dialog */
	onClose: () => void
	/** Function to confirm deletion */
	onConfirm: (donationId: string) => Promise<void>
	/** Whether deletion is in progress */
	isLoading: boolean
}

/**
 * Format currency amount for display
 */
function formatCurrency(amount: string, currency: string = "MYR"): string {
	const numAmount = parseFloat(amount)
	return new Intl.NumberFormat("en-MY", {
		style: "currency",
		currency: currency,
	}).format(numAmount)
}

/**
 * Confirmation dialog for donation deletion with safety measures
 * Requires donation amount confirmation and shows warnings for completed donations
 */
export function DeleteDonationDialog({
	donation,
	isOpen,
	onClose,
	onConfirm,
	isLoading,
}: DeleteDonationDialogProps) {
	const [confirmationText, setConfirmationText] = useState("")

	// Reset confirmation text when dialog opens/closes
	const handleOpenChange = (open: boolean) => {
		if (!open) {
			setConfirmationText("")
			onClose()
		}
	}

	const inputId = useId()

	const handleConfirm = async () => {
		if (!donation) return

		try {
			await onConfirm(donation.id)
			setConfirmationText("")
		} catch (error) {
			// Error handling is managed by the parent component
			console.error("Delete donation error:", error)
		}
	}

	// Check if confirmation text matches donation amount
	const expectedAmount = donation
		? formatCurrency(donation.amount, donation.currency)
		: ""
	const isConfirmationValid = donation && confirmationText === expectedAmount
	const hasConfirmationError =
		confirmationText.length > 0 && !isConfirmationValid

	// Check if donation is completed (should show additional warning)
	const isCompletedDonation = donation?.status === "completed"

	if (!donation) return null

	return (
		<AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
			<AlertDialogContent className="sm:max-w-md">
				<AlertDialogHeader>
					<div className="flex items-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
							<AlertTriangle className="h-5 w-5 text-destructive" />
						</div>
						<div className="flex-1">
							<AlertDialogTitle className="text-left">
								Delete Donation
							</AlertDialogTitle>
							<AlertDialogDescription className="text-left mt-1">
								This action cannot be undone.
							</AlertDialogDescription>
						</div>
					</div>
				</AlertDialogHeader>

				<div className="space-y-4">
					{/* Warning message */}
					<div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
						<p className="text-sm text-destructive font-medium mb-2">
							You are about to permanently delete a {expectedAmount} donation
							from {donation.donorName}
						</p>
						<p className="text-sm text-muted-foreground">
							This will remove the donation and all associated data from the
							system.
						</p>
					</div>

					{/* Completed donation warning */}
					{isCompletedDonation && (
						<div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
							<p className="text-sm text-amber-800 font-medium mb-1">
								⚠️ This is a completed donation
							</p>
							<p className="text-sm text-amber-700">
								Deleting completed donations may affect financial records and
								reporting. Consider adding internal notes instead of deletion.
							</p>
						</div>
					)}

					{/* Confirmation input */}
					<div className="space-y-2">
						<Label htmlFor={inputId} className="text-sm font-medium">
							Type the donation amount to confirm deletion:
						</Label>
						<div className="space-y-1">
							<p className="text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded">
								{expectedAmount}
							</p>
							<Input
								id={inputId}
								type="text"
								placeholder="Enter donation amount"
								value={confirmationText}
								onChange={(e) => setConfirmationText(e.target.value)}
								disabled={isLoading}
								className={
									hasConfirmationError
										? "border-destructive focus-visible:ring-destructive"
										: ""
								}
								autoComplete="off"
								autoFocus
							/>
							{hasConfirmationError && (
								<p className="text-sm text-destructive">
									Donation amount does not match
								</p>
							)}
						</div>
					</div>
				</div>

				<AlertDialogFooter>
					<AlertDialogCancel
						disabled={isLoading}
						onClick={() => handleOpenChange(false)}
					>
						Cancel
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={handleConfirm}
						disabled={!isConfirmationValid || isLoading}
						className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-destructive"
					>
						{isLoading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Deleting...
							</>
						) : (
							"Delete Donation"
						)}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	)
}
