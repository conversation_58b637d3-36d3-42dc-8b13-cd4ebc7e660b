import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import {
	<PERSON>ge,
	<PERSON><PERSON>,
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@donorcare/ui"
import type { Static } from "elysia"
import { Edit, Eye, MoreHorizontal, Trash2 } from "lucide-react"
import { StatusToggle } from "./StatusToggle"

export interface DonationCardProps {
	donation: Static<typeof donationWithCampaignDto>
	onView: (donationId: string) => void
	onEdit: (donationId: string) => void
	onDelete: (donationId: string) => void
	onStatusChange: (
		donationId: string,
		status: "pending" | "completed" | "failed",
	) => Promise<void>
	isLoading?: boolean
}

/**
 * Individual donation card component for displaying donation information
 * with action buttons for view, edit, delete, and status change
 */
export function DonationCard({
	donation,
	onView,
	onEdit,
	onDelete,
	onStatusChange,
	isLoading = false,
}: DonationCardProps) {
	const handleView = () => {
		onView(donation.id)
	}

	const handleEdit = () => {
		onEdit(donation.id)
	}

	const handleDelete = () => {
		onDelete(donation.id)
	}

	const formatDate = (dateString: string | Date) => {
		const date =
			typeof dateString === "string" ? new Date(dateString) : dateString
		return date.toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		})
	}

	const formatAmount = (amount: string, currency: string = "MYR") => {
		const numAmount = parseFloat(amount)
		return new Intl.NumberFormat("en-MY", {
			style: "currency",
			currency: currency,
		}).format(numAmount)
	}

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case "completed":
				return "default" as const
			case "pending":
				return "secondary" as const
			case "failed":
				return "destructive" as const
			default:
				return "outline" as const
		}
	}

	const getStatusLabel = (status: string) => {
		switch (status) {
			case "completed":
				return "Completed"
			case "pending":
				return "Pending"
			case "failed":
				return "Failed"
			default:
				return status
		}
	}

	return (
		<Card className="h-full">
			<CardHeader>
				<div className="flex items-center justify-between">
					<div className="flex flex-col flex-1 min-w-0">
						<CardTitle className="text-lg truncate">
							{formatAmount(donation.amount, donation.currency)}
						</CardTitle>
						<span className="text-xs text-muted-foreground truncate">
							{donation.donorName}
						</span>
					</div>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="ghost"
								size="sm"
								className="h-8 w-8 p-0"
								disabled={isLoading}
							>
								<MoreHorizontal className="h-4 w-4" />
								<span className="sr-only">Open menu</span>
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-48">
							<DropdownMenuItem onClick={handleView}>
								<Eye className="mr-2 h-4 w-4" />
								View Details
							</DropdownMenuItem>
							<DropdownMenuItem onClick={handleEdit}>
								<Edit className="mr-2 h-4 w-4" />
								Edit Donation
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								onClick={handleDelete}
								className="text-destructive focus:text-destructive"
							>
								<Trash2 className="mr-2 h-4 w-4" />
								Delete Donation
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</CardHeader>
			<CardContent className="flex-grow">
				<div className="space-y-3">
					<div className="flex items-center justify-between">
						<span className="text-sm text-muted-foreground">Campaign:</span>
						<span className="text-sm font-medium truncate max-w-[150px]">
							{donation.campaign.name}
						</span>
					</div>
					<div className="flex items-center justify-between">
						<span className="text-sm text-muted-foreground">Status:</span>
						<Badge variant={getStatusBadgeVariant(donation.status)}>
							{getStatusLabel(donation.status)}
						</Badge>
					</div>
					{donation.donorMessage && (
						<CardDescription className="text-sm text-muted-foreground overflow-hidden">
							<div className="line-clamp-2">{donation.donorMessage}</div>
						</CardDescription>
					)}
				</div>
			</CardContent>
			<CardFooter className="flex items-center justify-between gap-2">
				<div className="text-xs text-muted-foreground">
					{formatDate(donation.createdAt)}
				</div>
				<StatusToggle
					donationId={donation.id}
					currentStatus={donation.status}
					size="sm"
					disabled={isLoading}
				/>
			</CardFooter>
		</Card>
	)
}
