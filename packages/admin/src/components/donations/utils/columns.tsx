import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import { Badge } from "@donorcare/ui/components/ui/badge"
import { Checkbox } from "@donorcare/ui/components/ui/checkbox"
import type { ColumnDef } from "@tanstack/react-table"
import type { Static } from "elysia"
import { DonationRowActionsTable } from "../DonationRowActions"
import { multiColumnFilterFn, statusFilterFn } from "./filters"
import { formatAmount, formatDate, getStatusBadgeVariant } from "./formatters"

type DonationWithCampaign = Static<typeof donationWithCampaignDto>

/**
 * Column definitions for the donations table
 * Follows TanStack Table patterns with proper sizing, formatting, and filtering
 */
export const donationColumns: ColumnDef<DonationWithCampaign>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && "indeterminate")
				}
				onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
				aria-label="Select all"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
			/>
		),
		size: 28,
		enableSorting: false,
		enableHiding: false,
	},
	{
		header: "Donor Name",
		accessorKey: "donorName",
		cell: ({ row }) => (
			<div className="font-medium">{row.getValue("donorName")}</div>
		),
		size: 180,
		filterFn: multiColumnFilterFn,
		enableHiding: false,
	},
	{
		header: "Email",
		accessorKey: "donorEmail",
		cell: ({ row }) => (
			<div className="text-sm text-muted-foreground">
				{row.getValue("donorEmail")}
			</div>
		),
		size: 220,
	},
	{
		header: "Amount",
		accessorKey: "amount",
		cell: ({ row }) => (
			<div className="font-medium">
				{formatAmount(row.getValue("amount"), row.original.currency)}
			</div>
		),
		size: 120,
		enableSorting: true,
	},
	{
		header: "Campaign",
		accessorKey: "campaign.name",
		cell: ({ row }) => (
			<div
				className="max-w-[200px] truncate"
				title={row.original.campaign.name}
			>
				{row.original.campaign.name}
			</div>
		),
		size: 200,
		enableSorting: true,
	},
	{
		header: "Status",
		accessorKey: "status",
		cell: ({ row }) => (
			<Badge variant={getStatusBadgeVariant(row.getValue("status"))}>
				{(row.getValue("status") as string).charAt(0).toUpperCase() +
					(row.getValue("status") as string).slice(1)}
			</Badge>
		),
		size: 100,
		filterFn: statusFilterFn,
		enableSorting: true,
	},
	{
		header: "Date",
		accessorKey: "createdAt",
		cell: ({ row }) => (
			<div className="text-sm">{formatDate(row.getValue("createdAt"))}</div>
		),
		size: 140,
		enableSorting: true,
	},
	{
		id: "actions",
		header: () => <span className="sr-only">Actions</span>,
		cell: ({ row, table }) => (
			<DonationRowActionsTable row={row} table={table} />
		),
		size: 60,
		enableHiding: false,
		enableSorting: false,
	},
]
