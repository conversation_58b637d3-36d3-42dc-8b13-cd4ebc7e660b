/**
 * Format currency amount with proper locale and currency symbol
 * @param amount - The amount as a string
 * @param currency - The currency code (default: MYR)
 * @returns Formatted currency string
 */
export const formatAmount = (
	amount: string,
	currency: string = "MYR",
): string => {
	const numAmount = parseFloat(amount)

	// Handle invalid amounts
	if (Number.isNaN(numAmount)) {
		return `${currency} 0.00`
	}

	// Use appropriate locale based on currency
	const locale = getLocaleForCurrency(currency)

	return new Intl.NumberFormat(locale, {
		style: "currency",
		currency: currency,
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(numAmount)
}

/**
 * Format date for display in table
 * @param dateString - Date string or Date object
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | Date): string => {
	const date =
		typeof dateString === "string" ? new Date(dateString) : dateString

	// Handle invalid dates
	if (isNaN(date.getTime())) {
		return "Invalid Date"
	}

	return date.toLocaleDateString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
	})
}

/**
 * Format date and time for detailed views
 * @param dateString - Date string or Date object
 * @returns Formatted date and time string
 */
export const formatDateTime = (dateString: string | Date): string => {
	const date =
		typeof dateString === "string" ? new Date(dateString) : dateString

	// Handle invalid dates
	if (Number.isNaN(date.getTime())) {
		return "Invalid Date"
	}

	return date.toLocaleString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
		hour12: true,
	})
}

/**
 * Get status badge variant based on donation status
 * @param status - The donation status
 * @returns Badge variant
 */
export const getStatusBadgeVariant = (status: string) => {
	switch (status) {
		case "completed":
			return "default" as const
		case "pending":
			return "secondary" as const
		case "failed":
			return "destructive" as const
		default:
			return "outline" as const
	}
}

/**
 * Get status color for visual indicators
 * @param status - The donation status
 * @returns Color class or hex code
 */
export const getStatusColor = (status: string): string => {
	switch (status) {
		case "completed":
			return "text-green-600"
		case "pending":
			return "text-yellow-600"
		case "failed":
			return "text-red-600"
		default:
			return "text-gray-600"
	}
}

/**
 * Format status text with proper capitalization
 * @param status - The donation status
 * @returns Formatted status string
 */
export const formatStatus = (status: string): string => {
	return status.charAt(0).toUpperCase() + status.slice(1)
}

/**
 * Get appropriate locale for currency formatting
 * @param currency - Currency code
 * @returns Locale string
 */
function getLocaleForCurrency(currency: string): string {
	switch (currency) {
		case "MYR":
			return "en-MY"
		case "USD":
			return "en-US"
		case "EUR":
			return "en-EU"
		case "GBP":
			return "en-GB"
		case "SGD":
			return "en-SG"
		default:
			return "en-US"
	}
}

/**
 * Format large numbers with appropriate suffixes (K, M, B)
 * @param amount - The amount as a string or number
 * @param currency - The currency code (optional)
 * @returns Formatted amount with suffix
 */
export const formatCompactAmount = (
	amount: string | number,
	currency?: string,
): string => {
	const numAmount = typeof amount === "string" ? parseFloat(amount) : amount

	if (Number.isNaN(numAmount)) {
		return currency ? `${currency} 0` : "0"
	}

	const formatter = new Intl.NumberFormat("en-US", {
		notation: "compact",
		compactDisplay: "short",
		minimumFractionDigits: 0,
		maximumFractionDigits: 1,
	})

	const formatted = formatter.format(numAmount)
	return currency ? `${currency} ${formatted}` : formatted
}

/**
 * Parse amount string to number for calculations
 * @param amount - Amount string
 * @returns Parsed number or 0 if invalid
 */
export const parseAmount = (amount: string): number => {
	const parsed = parseFloat(amount)
	return Number.isNaN(parsed) ? 0 : parsed
}
