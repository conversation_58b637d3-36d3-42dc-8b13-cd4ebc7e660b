import type { donationWithCampaignDto } from "@donorcare/backend/schemas"
import type { FilterFn } from "@tanstack/react-table"
import type { Static } from "elysia"

type DonationWithCampaign = Static<typeof donationWithCampaignDto>

/**
 * Custom filter function for multi-column searching
 * Searches across donor name, donor email, and campaign name
 */
export const multiColumnFilterFn: FilterFn<DonationWithCampaign> = (
	row,
	_columnId,
	filterValue,
) => {
	const searchableRowContent =
		`${row.original.donorName} ${row.original.donorEmail} ${row.original.campaign.name}`.toLowerCase()
	const searchTerm = (filterValue ?? "").toLowerCase()
	return searchableRowContent.includes(searchTerm)
}

/**
 * Custom filter function for status filtering
 * Supports multiple status selection
 */
export const statusFilterFn: FilterFn<DonationWithCampaign> = (
	row,
	columnId,
	filterValue: string[],
) => {
	if (!filterValue?.length) return true
	const status = row.getValue(columnId) as string
	return filterValue.includes(status)
}

/**
 * Custom filter function for campaign filtering
 * Filters by campaign ID
 */
export const campaignFilterFn: FilterFn<DonationWithCampaign> = (
	row,
	_columnId,
	filterValue: string,
) => {
	if (!filterValue) return true
	return row.original.campaign.id === filterValue
}

/**
 * Custom filter function for date range filtering
 * Filters donations within a specific date range
 */
export const dateRangeFilterFn: FilterFn<DonationWithCampaign> = (
	row,
	_columnId,
	filterValue: { from?: Date; to?: Date },
) => {
	if (!filterValue?.from && !filterValue?.to) return true

	const donationDate = new Date(row.original.createdAt)

	if (filterValue.from && donationDate < filterValue.from) return false
	if (filterValue.to && donationDate > filterValue.to) return false

	return true
}

/**
 * Custom filter function for amount range filtering
 * Filters donations within a specific amount range
 */
export const amountRangeFilterFn: FilterFn<DonationWithCampaign> = (
	row,
	_columnId,
	filterValue: { min?: number; max?: number },
) => {
	if (!filterValue?.min && !filterValue?.max) return true

	const amount = parseFloat(row.original.amount)

	if (filterValue.min && amount < filterValue.min) return false
	if (filterValue.max && amount > filterValue.max) return false

	return true
}
