import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { useAuthStore, useUser } from "@/lib/auth-store"
import type { User } from "@/types/auth"
import { UserMenu } from "../UserMenu"

// Mock the auth store
vi.mock("@/lib/auth-store", () => ({
	useAuthStore: vi.fn(),
	useUser: vi.fn(),
}))

const mockUser: User = {
	id: "1",
	name: "<PERSON>",
	email: "<EMAIL>",
	emailVerified: true,
	image: "https://example.com/avatar.jpg",
	createdAt: new Date("2024-01-01"),
	updatedAt: new Date("2024-01-01"),
}

const mockAuthStore = {
	signOut: vi.fn(),
	isLoading: false,
}

describe("UserMenu", () => {
	beforeEach(() => {
		vi.clearAllMocks()
		vi.mocked(useAuthStore).mockReturnValue(mockAuthStore)
	})

	it("should not render when user is not authenticated", () => {
		vi.mocked(useUser).mockReturnValue(null)

		const { container } = render(<UserMenu />)
		expect(container.firstChild).toBeNull()
	})

	it("should render user avatar and information when authenticated", () => {
		vi.mocked(useUser).mockReturnValue(mockUser)

		render(<UserMenu />)

		// Check if the avatar trigger button is rendered
		const avatarButton = screen.getByRole("button", {
			name: /user menu for john doe/i,
		})
		expect(avatarButton).toBeInTheDocument()
	})

	it("should display user initials as fallback when no image", () => {
		const userWithoutImage = { ...mockUser, image: undefined }
		vi.mocked(useUser).mockReturnValue(userWithoutImage)

		render(<UserMenu />)

		// The initials should be "JD" for "John Doe" - visible in the avatar fallback
		expect(screen.getByText("JD")).toBeInTheDocument()
	})

	it("should display user information in dropdown menu", async () => {
		const user = userEvent.setup()
		vi.mocked(useUser).mockReturnValue(mockUser)

		render(<UserMenu />)

		// Click to open the dropdown
		const avatarButton = screen.getByRole("button", {
			name: /user menu for john doe/i,
		})
		await user.click(avatarButton)

		// Wait for dropdown to appear and check user info
		await waitFor(() => {
			expect(screen.getByText("John Doe")).toBeInTheDocument()
			expect(screen.getByText("<EMAIL>")).toBeInTheDocument()
			expect(screen.getByText("✓ Email verified")).toBeInTheDocument()
		})
	})

	it("should show logout confirmation dialog when sign out is clicked", async () => {
		const user = userEvent.setup()
		vi.mocked(useUser).mockReturnValue(mockUser)

		render(<UserMenu />)

		// Open dropdown
		const avatarButton = screen.getByRole("button", {
			name: /user menu for john doe/i,
		})
		await user.click(avatarButton)

		// Click sign out
		const signOutButton = await screen.findByText("Sign out")
		await user.click(signOutButton)

		// Check if confirmation dialog appears
		await waitFor(() => {
			expect(screen.getByText("Sign out of your account?")).toBeInTheDocument()
			expect(
				screen.getByText(/You will be signed out of your account/),
			).toBeInTheDocument()
		})
	})

	it("should call signOut when logout is confirmed", async () => {
		const user = userEvent.setup()
		vi.mocked(useUser).mockReturnValue(mockUser)

		render(<UserMenu />)

		// Open dropdown
		const avatarButton = screen.getByRole("button", {
			name: /user menu for john doe/i,
		})
		await user.click(avatarButton)

		// Click sign out
		const signOutButton = await screen.findByText("Sign out")
		await user.click(signOutButton)

		// Confirm logout in dialog
		const confirmButton = await screen.findByRole("button", {
			name: /sign out/i,
		})
		await user.click(confirmButton)

		// Verify signOut was called
		await waitFor(() => {
			expect(mockAuthStore.signOut).toHaveBeenCalledOnce()
		})
	})

	it("should show loading state during logout", async () => {
		const user = userEvent.setup()
		vi.mocked(useUser).mockReturnValue(mockUser)
		vi.mocked(useAuthStore).mockReturnValue({
			...mockAuthStore,
			isLoading: true,
		})

		render(<UserMenu />)

		// Open dropdown
		const avatarButton = screen.getByRole("button", {
			name: /user menu for john doe/i,
		})
		await user.click(avatarButton)

		// Click sign out
		const signOutButton = await screen.findByText("Sign out")
		await user.click(signOutButton)

		// Check loading state in confirmation dialog
		await waitFor(() => {
			expect(screen.getByText("Signing out...")).toBeInTheDocument()
		})
	})

	it("should generate correct initials for different name formats", () => {
		const testCases = [
			{ name: "John Doe", expected: "JD" },
			{ name: "Alice", expected: "A" },
			{ name: "Mary Jane Watson", expected: "MJ" },
			{ name: "jean-claude van damme", expected: "JV" },
		]

		testCases.forEach(({ name, expected }) => {
			const testUser = { ...mockUser, name }
			vi.mocked(useUser).mockReturnValue(testUser)

			const { unmount } = render(<UserMenu />)

			// The initials should be visible in the avatar fallback
			expect(screen.getByText(expected)).toBeInTheDocument()

			unmount()
		})
	})
})
