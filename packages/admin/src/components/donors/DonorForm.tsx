import type { DonorWithStats, Tag } from "@donorcare/backend/schemas"
import { createDonorDto, updateDonorDto } from "@donorcare/backend/schemas"
import {
	Badge,
	Button,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Textarea,
} from "@donorcare/ui"
import type { Static } from "elysia"
import {
	Calendar,
	DollarSign,
	Loader2,
	Mail,
	MapPin,
	Phone,
	Plus,
	StickyNote,
	Tag as TagIcon,
	User,
	X,
} from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { useAssignTags, useCreateTag, useTags } from "@/hooks/useDonors"
import { createTypeboxResolver } from "@/lib/typebox-resolver"

// Form data types
export type CreateDonorFormData = Static<typeof createDonorDto>
export type EditDonorFormData = Static<typeof updateDonorDto>

// Component props
export interface DonorFormProps {
	mode: "create" | "edit"
	initialData?: Partial<DonorWithStats>
	onSubmit: (data: CreateDonorFormData | EditDonorFormData) => Promise<void>
	onCancel: () => void
	isLoading?: boolean
}

/**
 * Format currency amount for display
 */
function formatCurrency(amount: string, currency: string = "MYR"): string {
	const numAmount = parseFloat(amount)
	return new Intl.NumberFormat("en-MY", {
		style: "currency",
		currency: currency,
	}).format(numAmount)
}

/**
 * Format date for display
 */
function formatDate(dateString: string): string {
	return new Date(dateString).toLocaleDateString("en-MY", {
		year: "numeric",
		month: "long",
		day: "numeric",
	})
}

/**
 * Donor form component for creating and editing donors
 */
export function DonorForm({
	mode,
	initialData,
	onSubmit,
	onCancel,
	isLoading = false,
}: DonorFormProps) {
	// Form setup with validation schema based on mode
	const form = useForm<CreateDonorFormData | EditDonorFormData>({
		resolver: createTypeboxResolver(
			mode === "create" ? createDonorDto : updateDonorDto,
		),
		defaultValues: {
			name: initialData?.name || "",
			email: initialData?.email || "",
			phone: initialData?.phone || "",
			address: initialData?.address || "",
			notes: initialData?.notes || "",
		},
	})

	// Handle form submission
	const handleSubmit = async (
		data: CreateDonorFormData | EditDonorFormData,
	) => {
		await onSubmit(data)
	}

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-2xl font-bold tracking-tight">
					{mode === "create" ? "Create New Donor" : "Edit Donor"}
				</h2>
				<p className="text-muted-foreground">
					{mode === "create"
						? "Add a new donor to your organization's database."
						: "Update donor information and manage their details."}
				</p>
			</div>

			{/* Read-only Donor Statistics Section (Edit mode only) */}
			{mode === "edit" && initialData && (
				<div className="rounded-lg border p-4 bg-muted/50">
					<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
						<DollarSign className="h-5 w-5" />
						Donor Statistics
					</h3>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<DollarSign className="h-4 w-4 text-muted-foreground" />
								<span className="text-sm font-medium">Total Donated:</span>
								<span className="text-sm">
									{formatCurrency(initialData.totalDonated || "0")}
								</span>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-sm font-medium">Donation Count:</span>
								<span className="text-sm">
									{initialData.donationCount || 0}
								</span>
							</div>
						</div>
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<span className="text-sm font-medium">Average Donation:</span>
								<span className="text-sm">
									{formatCurrency(initialData.averageDonation || "0")}
								</span>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-sm font-medium">
									Campaigns Supported:
								</span>
								<span className="text-sm">
									{initialData.campaignsSupported || 0}
								</span>
							</div>
						</div>
						<div className="space-y-2">
							{initialData.firstDonationDate && (
								<div className="flex items-center gap-2">
									<Calendar className="h-4 w-4 text-muted-foreground" />
									<span className="text-sm font-medium">First Donation:</span>
									<span className="text-sm">
										{formatDate(initialData.firstDonationDate.toString())}
									</span>
								</div>
							)}
							{initialData.lastDonationDate && (
								<div className="flex items-center gap-2">
									<Calendar className="h-4 w-4 text-muted-foreground" />
									<span className="text-sm font-medium">Last Donation:</span>
									<span className="text-sm">
										{formatDate(initialData.lastDonationDate.toString())}
									</span>
								</div>
							)}
						</div>
					</div>
				</div>
			)}

			<Form {...form}>
				<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
					{/* Donor Name Field */}
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Full Name *</FormLabel>
								<FormControl>
									<div className="relative">
										<User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
										<Input
											placeholder="Enter donor's full name"
											className="pl-10"
											disabled={isLoading}
											{...field}
										/>
									</div>
								</FormControl>
								<FormDescription>
									The full name of the donor as it should appear in records.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Donor Email Field */}
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email Address *</FormLabel>
								<FormControl>
									<div className="relative">
										<Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
										<Input
											type="email"
											placeholder="Enter donor's email address"
											className="pl-10"
											disabled={isLoading}
											{...field}
										/>
									</div>
								</FormControl>
								<FormDescription>
									Primary email address for communication and receipts.
									{mode === "create" && " Must be unique."}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Phone Number Field */}
					<FormField
						control={form.control}
						name="phone"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Phone Number</FormLabel>
								<FormControl>
									<div className="relative">
										<Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
										<Input
											type="tel"
											placeholder="Enter phone number (optional)"
											className="pl-10"
											disabled={isLoading}
											{...field}
										/>
									</div>
								</FormControl>
								<FormDescription>
									Contact phone number for the donor (optional).
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Address Field */}
					<FormField
						control={form.control}
						name="address"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Address</FormLabel>
								<FormControl>
									<div className="relative">
										<MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
										<Textarea
											placeholder="Enter donor's address (optional)"
											className="pl-10 min-h-[80px]"
											disabled={isLoading}
											{...field}
										/>
									</div>
								</FormControl>
								<FormDescription>
									Physical address of the donor (optional).
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Notes Field */}
					<FormField
						control={form.control}
						name="notes"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Internal Notes</FormLabel>
								<FormControl>
									<div className="relative">
										<StickyNote className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
										<Textarea
											placeholder="Add internal notes about this donor (optional)"
											className="pl-10 min-h-[100px]"
											disabled={isLoading}
											{...field}
										/>
									</div>
								</FormControl>
								<FormDescription>
									Private notes for your organization. These are not visible to
									the donor. Character limit: 2000.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Tag Management Section (Edit mode only) */}
					{mode === "edit" && initialData && (
						<TagManagementSection
							donorId={initialData.id!}
							currentTags={initialData.tags || []}
							isLoading={isLoading}
						/>
					)}

					{/* Form Actions */}
					<div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
						<Button
							type="button"
							variant="outline"
							onClick={onCancel}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button type="submit" disabled={isLoading}>
							{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
							{mode === "create" ? "Create Donor" : "Save Changes"}
						</Button>
					</div>
				</form>
			</Form>
		</div>
	)
}

/**
 * Tag Management Section Component
 */
interface TagManagementSectionProps {
	donorId: string
	currentTags: Tag[]
	isLoading?: boolean
}

function TagManagementSection({
	donorId,
	currentTags,
	isLoading,
}: TagManagementSectionProps) {
	const [selectedTagIds, setSelectedTagIds] = useState<string[]>(
		currentTags.map((tag) => tag.id),
	)
	const [newTagName, setNewTagName] = useState("")
	const [newTagColor, setNewTagColor] = useState("#3b82f6")
	const [showCreateTag, setShowCreateTag] = useState(false)

	const { data: allTags = [], isLoading: tagsLoading } = useTags()
	const createTagMutation = useCreateTag()
	const assignTagsMutation = useAssignTags()

	const handleTagToggle = (tagId: string) => {
		setSelectedTagIds((prev) =>
			prev.includes(tagId)
				? prev.filter((id) => id !== tagId)
				: [...prev, tagId],
		)
	}

	const handleCreateTag = async () => {
		if (!newTagName.trim()) return

		try {
			await createTagMutation.mutateAsync({
				name: newTagName.trim(),
				color: newTagColor,
			})
			setNewTagName("")
			setNewTagColor("#3b82f6")
			setShowCreateTag(false)
		} catch (error) {
			// Error is handled by the mutation
		}
	}

	const handleSaveTags = async () => {
		try {
			await assignTagsMutation.mutateAsync({
				donorId,
				tagIds: selectedTagIds,
			})
		} catch (error) {
			// Error is handled by the mutation
		}
	}

	const hasChanges =
		JSON.stringify(selectedTagIds.sort()) !==
		JSON.stringify(currentTags.map((t) => t.id).sort())

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<div>
					<h3 className="text-lg font-semibold flex items-center gap-2">
						<TagIcon className="h-5 w-5" />
						Tags
					</h3>
					<p className="text-sm text-muted-foreground">
						Organize and categorize this donor with tags.
					</p>
				</div>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={() => setShowCreateTag(!showCreateTag)}
					disabled={isLoading || tagsLoading}
				>
					<Plus className="h-4 w-4 mr-2" />
					New Tag
				</Button>
			</div>

			{/* Create New Tag Section */}
			{showCreateTag && (
				<div className="border rounded-lg p-4 space-y-3">
					<h4 className="font-medium">Create New Tag</h4>
					<div className="flex gap-2">
						<Input
							placeholder="Tag name"
							value={newTagName}
							onChange={(e) => setNewTagName(e.target.value)}
							className="flex-1"
							maxLength={50}
						/>
						<div className="flex items-center gap-2">
							<input
								type="color"
								value={newTagColor}
								onChange={(e) => setNewTagColor(e.target.value)}
								className="w-10 h-10 rounded border cursor-pointer"
							/>
							<Button
								type="button"
								size="sm"
								onClick={handleCreateTag}
								disabled={!newTagName.trim() || createTagMutation.isPending}
							>
								{createTagMutation.isPending && (
									<Loader2 className="h-4 w-4 mr-2 animate-spin" />
								)}
								Create
							</Button>
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={() => {
									setShowCreateTag(false)
									setNewTagName("")
									setNewTagColor("#3b82f6")
								}}
							>
								Cancel
							</Button>
						</div>
					</div>
				</div>
			)}

			{/* Available Tags */}
			<div className="space-y-3">
				<h4 className="font-medium">Available Tags</h4>
				{tagsLoading ? (
					<div className="flex items-center gap-2">
						<Loader2 className="h-4 w-4 animate-spin" />
						<span className="text-sm text-muted-foreground">
							Loading tags...
						</span>
					</div>
				) : allTags.length === 0 ? (
					<p className="text-sm text-muted-foreground">
						No tags available. Create your first tag above.
					</p>
				) : (
					<div className="flex flex-wrap gap-2">
						{allTags.map((tag: any) => {
							const isSelected = selectedTagIds.includes(tag.id)
							return (
								<Badge
									key={tag.id}
									variant={isSelected ? "default" : "outline"}
									className="cursor-pointer hover:opacity-80 transition-opacity"
									style={{
										backgroundColor: isSelected ? tag.color : "transparent",
										borderColor: tag.color,
										color: isSelected ? "white" : tag.color,
									}}
									onClick={() => handleTagToggle(tag.id)}
								>
									{tag.name}
									{isSelected && <X className="h-3 w-3 ml-1" />}
								</Badge>
							)
						})}
					</div>
				)}
			</div>

			{/* Current Tags Display */}
			{currentTags.length > 0 && (
				<div className="space-y-2">
					<h4 className="font-medium">Current Tags</h4>
					<div className="flex flex-wrap gap-2">
						{currentTags.map((tag) => (
							<Badge
								key={tag.id}
								style={{
									backgroundColor: tag.color,
									color: "white",
								}}
							>
								{tag.name}
							</Badge>
						))}
					</div>
				</div>
			)}

			{/* Save Tags Button */}
			{hasChanges && (
				<div className="flex justify-end">
					<Button
						type="button"
						onClick={handleSaveTags}
						disabled={assignTagsMutation.isPending || isLoading}
						size="sm"
					>
						{assignTagsMutation.isPending && (
							<Loader2 className="h-4 w-4 mr-2 animate-spin" />
						)}
						Save Tag Changes
					</Button>
				</div>
			)}
		</div>
	)
}
