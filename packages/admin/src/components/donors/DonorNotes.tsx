import type { Note } from "@donorcare/backend/schemas"
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
	Button,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Textarea,
} from "@donorcare/ui"
import { zodResolver } from "@hookform/resolvers/zod"
import { formatDistanceToNow } from "date-fns"
import {
	Edit,
	Loader2,
	MessageSquare,
	Plus,
	StickyNote,
	Trash2,
	User,
} from "lucide-react"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import {
	useCreateDonorNote,
	useDeleteDonorNote,
	useDonorNotes,
	useUpdateDonorNote,
} from "@/hooks/useDonors"

// Form validation schema
const noteFormSchema = z.object({
	content: z
		.string()
		.min(1, "Note content is required")
		.max(5000, "Note content must be less than 5000 characters"),
})

type NoteFormData = z.infer<typeof noteFormSchema>

interface DonorNotesProps {
	donorId: string
	donorName: string
}

/**
 * Donor Notes Management Component
 */
export function DonorNotes({ donorId, donorName }: DonorNotesProps) {
	const [isCreating, setIsCreating] = useState(false)
	const [editingNoteId, setEditingNoteId] = useState<string | null>(null)

	const { data: notes = [], isLoading } = useDonorNotes(donorId)
	const createNoteMutation = useCreateDonorNote()
	const updateNoteMutation = useUpdateDonorNote()
	const deleteNoteMutation = useDeleteDonorNote()

	const createForm = useForm<NoteFormData>({
		resolver: zodResolver(noteFormSchema),
		defaultValues: {
			content: "",
		},
	})

	const editForm = useForm<NoteFormData>({
		resolver: zodResolver(noteFormSchema),
		defaultValues: {
			content: "",
		},
	})

	const handleCreateNote = async (data: NoteFormData) => {
		try {
			await createNoteMutation.mutateAsync({
				donorId,
				data: { content: data.content },
			})
			createForm.reset()
			setIsCreating(false)
		} catch (error) {
			// Error is handled by the mutation
		}
	}

	const handleEditNote = (note: Note) => {
		setEditingNoteId(note.id)
		editForm.setValue("content", note.content)
	}

	const handleUpdateNote = async (data: NoteFormData) => {
		if (!editingNoteId) return

		try {
			await updateNoteMutation.mutateAsync({
				donorId,
				noteId: editingNoteId,
				data: { content: data.content },
			})
			editForm.reset()
			setEditingNoteId(null)
		} catch (error) {
			// Error is handled by the mutation
		}
	}

	const handleDeleteNote = async (noteId: string) => {
		try {
			await deleteNoteMutation.mutateAsync({ donorId, noteId })
		} catch (error) {
			// Error is handled by the mutation
		}
	}

	const handleCancelEdit = () => {
		setEditingNoteId(null)
		editForm.reset()
	}

	const handleCancelCreate = () => {
		setIsCreating(false)
		createForm.reset()
	}

	if (isLoading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<MessageSquare className="h-5 w-5" />
						Notes
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="flex items-center gap-2">
						<Loader2 className="h-4 w-4 animate-spin" />
						<span className="text-sm text-muted-foreground">
							Loading notes...
						</span>
					</div>
				</CardContent>
			</Card>
		)
	}

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<CardTitle className="flex items-center gap-2">
						<MessageSquare className="h-5 w-5" />
						Notes for {donorName}
					</CardTitle>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setIsCreating(true)}
						disabled={isCreating || editingNoteId !== null}
					>
						<Plus className="h-4 w-4 mr-2" />
						Add Note
					</Button>
				</div>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Create Note Form */}
				{isCreating && (
					<div className="border rounded-lg p-4 space-y-4">
						<h4 className="font-medium flex items-center gap-2">
							<StickyNote className="h-4 w-4" />
							Add New Note
						</h4>
						<Form {...createForm}>
							<form
								onSubmit={createForm.handleSubmit(handleCreateNote)}
								className="space-y-4"
							>
								<FormField
									control={createForm.control}
									name="content"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Note Content</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Enter your note about this donor..."
													className="min-h-[100px]"
													{...field}
												/>
											</FormControl>
											<FormDescription>
												Add internal notes about this donor. Maximum 5000
												characters.
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
								<div className="flex gap-2">
									<Button
										type="submit"
										size="sm"
										disabled={createNoteMutation.isPending}
									>
										{createNoteMutation.isPending && (
											<Loader2 className="h-4 w-4 mr-2 animate-spin" />
										)}
										Save Note
									</Button>
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={handleCancelCreate}
									>
										Cancel
									</Button>
								</div>
							</form>
						</Form>
					</div>
				)}

				{/* Notes List */}
				{notes.length === 0 ? (
					<div className="text-center py-8">
						<MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
						<h3 className="text-lg font-medium mb-2">No notes yet</h3>
						<p className="text-muted-foreground mb-4">
							Start adding notes to keep track of important information about
							this donor.
						</p>
						{!isCreating && (
							<Button onClick={() => setIsCreating(true)}>
								<Plus className="h-4 w-4 mr-2" />
								Add First Note
							</Button>
						)}
					</div>
				) : (
					<div className="space-y-4">
						{notes.map((note: any) => (
							<div key={note.id} className="border rounded-lg p-4 space-y-3">
								{editingNoteId === note.id ? (
									// Edit Mode
									<Form {...editForm}>
										<form
											onSubmit={editForm.handleSubmit(handleUpdateNote)}
											className="space-y-4"
										>
											<FormField
												control={editForm.control}
												name="content"
												render={({ field }) => (
													<FormItem>
														<FormLabel>Edit Note</FormLabel>
														<FormControl>
															<Textarea className="min-h-[100px]" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<div className="flex gap-2">
												<Button
													type="submit"
													size="sm"
													disabled={updateNoteMutation.isPending}
												>
													{updateNoteMutation.isPending && (
														<Loader2 className="h-4 w-4 mr-2 animate-spin" />
													)}
													Save Changes
												</Button>
												<Button
													type="button"
													variant="outline"
													size="sm"
													onClick={handleCancelEdit}
												>
													Cancel
												</Button>
											</div>
										</form>
									</Form>
								) : (
									// View Mode
									<>
										<div className="flex items-start justify-between">
											<div className="flex items-center gap-2 text-sm text-muted-foreground">
												<User className="h-4 w-4" />
												<span>{note.organizerName}</span>
												<span>•</span>
												<span>
													{formatDistanceToNow(new Date(note.createdAt), {
														addSuffix: true,
													})}
												</span>
												{note.updatedAt !== note.createdAt && (
													<>
														<span>•</span>
														<span className="italic">
															edited{" "}
															{formatDistanceToNow(new Date(note.updatedAt), {
																addSuffix: true,
															})}
														</span>
													</>
												)}
											</div>
											<div className="flex gap-1">
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleEditNote(note)}
													disabled={editingNoteId !== null || isCreating}
												>
													<Edit className="h-4 w-4" />
												</Button>
												<AlertDialog>
													<AlertDialogTrigger asChild>
														<Button
															variant="ghost"
															size="sm"
															disabled={editingNoteId !== null || isCreating}
														>
															<Trash2 className="h-4 w-4" />
														</Button>
													</AlertDialogTrigger>
													<AlertDialogContent>
														<AlertDialogHeader>
															<AlertDialogTitle>Delete Note</AlertDialogTitle>
															<AlertDialogDescription>
																Are you sure you want to delete this note? This
																action cannot be undone.
															</AlertDialogDescription>
														</AlertDialogHeader>
														<AlertDialogFooter>
															<AlertDialogCancel>Cancel</AlertDialogCancel>
															<AlertDialogAction
																onClick={() => handleDeleteNote(note.id)}
																className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
															>
																Delete
															</AlertDialogAction>
														</AlertDialogFooter>
													</AlertDialogContent>
												</AlertDialog>
											</div>
										</div>
										<div className="prose prose-sm max-w-none">
											<p className="whitespace-pre-wrap text-sm leading-relaxed">
												{note.content}
											</p>
										</div>
									</>
								)}
							</div>
						))}
					</div>
				)}
			</CardContent>
		</Card>
	)
}
