import type { donorWithStatsDto } from "@donorcare/backend/schemas"
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@donorcare/ui/components/ui/alert-dialog"
import { Button } from "@donorcare/ui/components/ui/button"

import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuTrigger,
} from "@donorcare/ui/components/ui/dropdown-menu"
import { Input } from "@donorcare/ui/components/ui/input"
import { Label } from "@donorcare/ui/components/ui/label"
import {
	Pagination,
	PaginationContent,
	PaginationItem,
} from "@donorcare/ui/components/ui/pagination"
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@donorcare/ui/components/ui/select"
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@donorcare/ui/components/ui/table"
import { cn } from "@donorcare/ui/lib/utils"
import {
	type ColumnFiltersState,
	flexRender,
	getCoreRowModel,
	getFacetedUniqueValues,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	type PaginationState,
	type SortingState,
	useReactTable,
	type VisibilityState,
} from "@tanstack/react-table"
import type { Static } from "elysia"
import {
	ChevronDownIcon,
	ChevronFirstIcon,
	ChevronLastIcon,
	ChevronLeftIcon,
	ChevronRightIcon,
	ChevronUpIcon,
	CircleAlertIcon,
	CircleXIcon,
	Columns3Icon,
	DownloadIcon,
	ListFilterIcon,
	PlusIcon,
	TrashIcon,
} from "lucide-react"
import { memo, useCallback, useId, useMemo, useRef, useState } from "react"
import { DonorAnalytics } from "./DonorAnalytics"
import { DonorErrorBoundary } from "./DonorErrorBoundary"
import { DonorTableSkeleton } from "./DonorTableSkeleton"
import { donorColumns } from "./utils/columns"

type DonorWithStats = Static<typeof donorWithStatsDto>

export interface DonorsTableProps {
	donors: DonorWithStats[]
	isLoading?: boolean
	error?: Error | null
	onView: (donorId: string) => void
	onEdit: (donorId: string) => void
	onDelete: (donorId: string) => void
	onExport: (selectedIds?: string[]) => void
	onRetry?: () => void
	onCreateNew?: () => void
}

// Memoized table component for performance optimization
const DonorsTableComponent = memo(function DonorsTableComponent({
	donors,
	isLoading = false,
	error,
	onView,
	onEdit,
	onDelete,
	onExport,
	onRetry,
	onCreateNew,
}: DonorsTableProps) {
	const id = useId()
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
	const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})
	const [pagination, setPagination] = useState<PaginationState>({
		pageIndex: 0,
		pageSize: 10,
	})
	const inputRef = useRef<HTMLInputElement>(null)

	const [sorting, setSorting] = useState<SortingState>([
		{
			id: "name",
			desc: false,
		},
	])

	// Memoized handlers for better performance
	const handleDeleteRows = useCallback(() => {
		const selectedRows = table.getSelectedRowModel().rows
		selectedRows.forEach((row) => {
			onDelete(row.original.id)
		})
		table.resetRowSelection()
	}, [onDelete])

	const handleExportSelected = useCallback(() => {
		const selectedRows = table.getSelectedRowModel().rows
		const selectedIds = selectedRows.map((row) => row.original.id)
		onExport(selectedIds.length > 0 ? selectedIds : undefined)
	}, [onExport])

	// Memoized data to prevent unnecessary re-renders
	const memoizedDonors = useMemo(() => donors, [donors])

	const table = useReactTable({
		data: memoizedDonors,
		columns: donorColumns,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange: setSorting,
		enableSortingRemoval: false,
		getPaginationRowModel: getPaginationRowModel(),
		onPaginationChange: setPagination,
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: setColumnVisibility,
		getFilteredRowModel: getFilteredRowModel(),
		getFacetedUniqueValues: getFacetedUniqueValues(),
		state: {
			sorting,
			pagination,
			columnFilters,
			columnVisibility,
		},
		meta: {
			onView,
			onEdit,
			onDelete,
		},
	})

	// Show loading skeleton for better UX
	if (isLoading && donors.length === 0) {
		return <DonorTableSkeleton rows={pagination.pageSize} />
	}

	// Show error state
	if (error && !isLoading) {
		return (
			<div className="flex flex-col items-center justify-center py-12 space-y-4">
				<CircleAlertIcon className="h-12 w-12 text-muted-foreground" />
				<div className="text-center">
					<h3 className="text-lg font-semibold">Failed to load donors</h3>
					<p className="text-sm text-muted-foreground mt-1">{error.message}</p>
				</div>
				{onRetry && (
					<Button onClick={onRetry} variant="outline">
						Try Again
					</Button>
				)}
			</div>
		)
	}

	return (
		<div className="space-y-4">
			{/* Analytics */}
			<DonorAnalytics
				donors={donors}
				filteredDonors={table
					.getFilteredRowModel()
					.rows.map((row) => row.original)}
				selectedDonors={table
					.getSelectedRowModel()
					.rows.map((row) => row.original)}
				isLoading={isLoading}
				error={error}
			/>

			{/* Filters and Actions */}
			<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
				<div className="flex flex-col gap-3 sm:flex-row sm:items-center">
					{/* Filter by donor name or email */}
					<div className="relative">
						<Input
							id={`${id}-input`}
							ref={inputRef}
							className={cn(
								"peer w-full sm:min-w-60 ps-9",
								Boolean(table.getColumn("name")?.getFilterValue()) && "pe-9",
							)}
							value={
								(table.getColumn("name")?.getFilterValue() ?? "") as string
							}
							onChange={(e) =>
								table.getColumn("name")?.setFilterValue(e.target.value)
							}
							placeholder="Filter by donor name or email..."
							type="text"
							aria-label="Filter by donor name or email"
							disabled={isLoading}
						/>
						<div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
							<ListFilterIcon size={16} aria-hidden="true" />
						</div>
						{Boolean(table.getColumn("name")?.getFilterValue()) && (
							<button
								className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
								aria-label="Clear filter"
								onClick={() => {
									table.getColumn("name")?.setFilterValue("")
									if (inputRef.current) {
										inputRef.current.focus()
									}
								}}
								disabled={isLoading}
								type="button"
							>
								<CircleXIcon size={16} aria-hidden="true" />
							</button>
						)}
					</div>

					{/* Toggle columns visibility */}
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								disabled={isLoading}
								className="w-full sm:w-auto"
							>
								<Columns3Icon
									className="-ms-1 opacity-60"
									size={16}
									aria-hidden="true"
								/>
								<span className="sm:hidden">Columns</span>
								<span className="hidden sm:inline">View</span>
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
							{table
								.getAllColumns()
								.filter((column) => column.getCanHide())
								.map((column) => {
									return (
										<DropdownMenuCheckboxItem
											key={column.id}
											className="capitalize"
											checked={column.getIsVisible()}
											onCheckedChange={(value) =>
												column.toggleVisibility(!!value)
											}
											onSelect={(event) => event.preventDefault()}
										>
											{column.id === "name"
												? "Name"
												: column.id === "email"
													? "Email"
													: column.id === "totalDonated"
														? "Total Donated"
														: column.id === "donationCount"
															? "Donations"
															: column.id === "campaignsSupported"
																? "Campaigns"
																: column.id === "lastDonationDate"
																	? "Last Donation"
																	: column.id}
										</DropdownMenuCheckboxItem>
									)
								})}
						</DropdownMenuContent>
					</DropdownMenu>
				</div>

				<div className="flex flex-col gap-3 sm:flex-row sm:items-center">
					{/* Create New Donor Button */}
					{onCreateNew && (
						<Button
							onClick={onCreateNew}
							disabled={isLoading}
							className="w-full sm:w-auto"
						>
							<PlusIcon
								className="-ms-1 opacity-60"
								size={16}
								aria-hidden="true"
							/>
							Add Donor
						</Button>
					)}

					{/* Delete button */}
					{table.getSelectedRowModel().rows.length > 0 && (
						<AlertDialog>
							<AlertDialogTrigger asChild>
								<Button
									variant="outline"
									disabled={isLoading}
									className="w-full sm:w-auto"
								>
									<TrashIcon
										className="-ms-1 opacity-60"
										size={16}
										aria-hidden="true"
									/>
									Delete
									<span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
										{table.getSelectedRowModel().rows.length}
									</span>
								</Button>
							</AlertDialogTrigger>
							<AlertDialogContent className="sm:max-w-md">
								<div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
									<div
										className="flex size-9 shrink-0 items-center justify-center rounded-full border"
										aria-hidden="true"
									>
										<CircleAlertIcon className="opacity-80" size={16} />
									</div>
									<AlertDialogHeader>
										<AlertDialogTitle>
											Are you absolutely sure?
										</AlertDialogTitle>
										<AlertDialogDescription>
											This action cannot be undone. This will permanently delete{" "}
											{table.getSelectedRowModel().rows.length} selected{" "}
											{table.getSelectedRowModel().rows.length === 1
												? "donor"
												: "donors"}
											.
										</AlertDialogDescription>
									</AlertDialogHeader>
								</div>
								<AlertDialogFooter className="flex-col-reverse sm:flex-row">
									<AlertDialogCancel className="w-full sm:w-auto">
										Cancel
									</AlertDialogCancel>
									<AlertDialogAction
										onClick={handleDeleteRows}
										className="w-full sm:w-auto"
									>
										Delete
									</AlertDialogAction>
								</AlertDialogFooter>
							</AlertDialogContent>
						</AlertDialog>
					)}

					{/* Export button */}
					<Button
						variant="outline"
						onClick={handleExportSelected}
						disabled={isLoading}
						className="w-full sm:w-auto"
					>
						<DownloadIcon
							className="-ms-1 opacity-60"
							size={16}
							aria-hidden="true"
						/>
						Export
						{table.getSelectedRowModel().rows.length > 0 && (
							<span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
								{table.getSelectedRowModel().rows.length}
							</span>
						)}
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="bg-background overflow-hidden rounded-md border">
				<div className="overflow-x-auto">
					<Table className="table-fixed min-w-full">
						<TableHeader>
							{table.getHeaderGroups().map((headerGroup) => (
								<TableRow key={headerGroup.id} className="hover:bg-transparent">
									{headerGroup.headers.map((header) => {
										return (
											<TableHead
												key={header.id}
												style={{
													width: `${header.getSize()}px`,
													minWidth: `${header.getSize()}px`,
												}}
												className="h-11 sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
											>
												{header.isPlaceholder ? null : header.column.getCanSort() ? (
													<div
														className={cn(
															header.column.getCanSort() &&
																"flex h-full cursor-pointer items-center justify-between gap-2 select-none",
														)}
														onClick={header.column.getToggleSortingHandler()}
														onKeyDown={(e) => {
															// Enhanced keyboard handling for sorting
															if (
																header.column.getCanSort() &&
																(e.key === "Enter" || e.key === " ")
															) {
																e.preventDefault()
																header.column.getToggleSortingHandler()?.(e)
															}
														}}
														tabIndex={
															header.column.getCanSort() ? 0 : undefined
														}
														role={
															header.column.getCanSort() ? "button" : undefined
														}
														aria-label={
															header.column.getCanSort()
																? `Sort by ${header.column.columnDef.header}`
																: undefined
														}
													>
														{flexRender(
															header.column.columnDef.header,
															header.getContext(),
														)}
														{{
															asc: (
																<ChevronUpIcon
																	className="shrink-0 opacity-60"
																	size={16}
																	aria-hidden="true"
																/>
															),
															desc: (
																<ChevronDownIcon
																	className="shrink-0 opacity-60"
																	size={16}
																	aria-hidden="true"
																/>
															),
														}[header.column.getIsSorted() as string] ?? null}
													</div>
												) : (
													flexRender(
														header.column.columnDef.header,
														header.getContext(),
													)
												)}
											</TableHead>
										)
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody>
							{isLoading && donors.length > 0 ? (
								// Show existing data with loading overlay for better UX
								table
									.getRowModel()
									.rows.map((row) => (
										<TableRow
											key={row.id}
											data-state={row.getIsSelected() && "selected"}
											className="hover:bg-muted/50 transition-colors opacity-60"
										>
											{row.getVisibleCells().map((cell) => (
												<TableCell key={cell.id} className="last:py-0">
													{flexRender(
														cell.column.columnDef.cell,
														cell.getContext(),
													)}
												</TableCell>
											))}
										</TableRow>
									))
							) : table.getRowModel().rows?.length ? (
								table.getRowModel().rows.map((row) => (
									<TableRow
										key={row.id}
										data-state={row.getIsSelected() && "selected"}
										className="hover:bg-muted/50 transition-colors"
									>
										{row.getVisibleCells().map((cell) => (
											<TableCell key={cell.id} className="last:py-0">
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext(),
												)}
											</TableCell>
										))}
									</TableRow>
								))
							) : (
								<TableRow>
									<TableCell
										colSpan={donorColumns.length}
										className="h-24 text-center"
									>
										<div className="flex flex-col items-center justify-center space-y-4 py-4">
											<p className="text-muted-foreground">No donors found.</p>
											{onCreateNew && (
												<Button
													size="sm"
													onClick={onCreateNew}
													disabled={isLoading}
												>
													<PlusIcon className="mr-2 h-4 w-4" />
													Add your first donor
												</Button>
											)}
										</div>
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</div>
			</div>

			{/* Pagination */}
			<div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
				{/* Results per page */}
				<div className="flex items-center gap-3">
					<Label htmlFor={id} className="text-sm font-medium">
						Rows per page
					</Label>
					<Select
						value={table.getState().pagination.pageSize.toString()}
						onValueChange={(value) => {
							table.setPageSize(Number(value))
						}}
						disabled={isLoading}
					>
						<SelectTrigger id={id} className="w-20">
							<SelectValue placeholder="Select number of results" />
						</SelectTrigger>
						<SelectContent className="[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2">
							{[5, 10, 25, 50, 100].map((pageSize) => (
								<SelectItem key={pageSize} value={pageSize.toString()}>
									{pageSize}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Page number information */}
				<div className="flex items-center justify-center sm:justify-end">
					<p className="text-muted-foreground text-sm" aria-live="polite">
						<span className="text-foreground">
							{table.getState().pagination.pageIndex *
								table.getState().pagination.pageSize +
								1}
							-
							{Math.min(
								Math.max(
									table.getState().pagination.pageIndex *
										table.getState().pagination.pageSize +
										table.getState().pagination.pageSize,
									0,
								),
								table.getRowCount(),
							)}
						</span>{" "}
						of{" "}
						<span className="text-foreground">
							{table.getRowCount().toLocaleString()}
						</span>
					</p>
				</div>

				{/* Pagination buttons */}
				<div className="flex justify-center sm:justify-end">
					<Pagination>
						<PaginationContent>
							{/* First page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.firstPage()}
									disabled={!table.getCanPreviousPage() || isLoading}
									aria-label="Go to first page"
								>
									<ChevronFirstIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
							{/* Previous page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.previousPage()}
									disabled={!table.getCanPreviousPage() || isLoading}
									aria-label="Go to previous page"
								>
									<ChevronLeftIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
							{/* Next page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.nextPage()}
									disabled={!table.getCanNextPage() || isLoading}
									aria-label="Go to next page"
								>
									<ChevronRightIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
							{/* Last page button */}
							<PaginationItem>
								<Button
									size="icon"
									variant="outline"
									className="disabled:pointer-events-none disabled:opacity-50"
									onClick={() => table.lastPage()}
									disabled={!table.getCanNextPage() || isLoading}
									aria-label="Go to last page"
								>
									<ChevronLastIcon size={16} aria-hidden="true" />
								</Button>
							</PaginationItem>
						</PaginationContent>
					</Pagination>
				</div>
			</div>
		</div>
	)
})

// Main export with error boundary
export function DonorsTable(props: DonorsTableProps) {
	return (
		<DonorErrorBoundary onReset={props.onRetry}>
			<DonorsTableComponent {...props} />
		</DonorErrorBoundary>
	)
}
