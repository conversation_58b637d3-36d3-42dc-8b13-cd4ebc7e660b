import type { donorWithStatsDto } from "@donorcare/backend/schemas"
import { Button } from "@donorcare/ui/components/ui/button"
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuShortcut,
	DropdownMenuTrigger,
} from "@donorcare/ui/components/ui/dropdown-menu"
import type { Row } from "@tanstack/react-table"
import type { Static } from "elysia"
import {
	EditIcon,
	EllipsisIcon,
	EyeIcon,
	LoaderIcon,
	TrashIcon,
} from "lucide-react"
import { useState } from "react"

type DonorWithStats = Static<typeof donorWithStatsDto>

export interface DonorRowActionsProps {
	donor: DonorWithStats
	onView: (donorId: string) => void
	onEdit: (donorId: string) => void
	onDelete: (donorId: string) => Promise<void>
	isDeleting?: boolean
}

export function DonorRowActions({
	donor,
	onView,
	onEdit,
	onDelete,
	isDeleting = false,
}: DonorRowActionsProps) {
	const [isActionLoading, setIsActionLoading] = useState<string | null>(null)

	const handleView = async () => {
		try {
			setIsActionLoading("view")
			onView(donor.id)
		} catch (error) {
			console.error("Error viewing donor:", error)
		} finally {
			setIsActionLoading(null)
		}
	}

	const handleEdit = async () => {
		try {
			setIsActionLoading("edit")
			onEdit(donor.id)
		} catch (error) {
			console.error("Error editing donor:", error)
		} finally {
			setIsActionLoading(null)
		}
	}

	const handleDelete = async () => {
		try {
			setIsActionLoading("delete")
			await onDelete(donor.id)
		} catch (error) {
			console.error("Error deleting donor:", error)
		} finally {
			setIsActionLoading(null)
		}
	}

	const isAnyActionLoading = isActionLoading !== null || isDeleting

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<div className="flex justify-end">
					<Button
						size="icon"
						variant="ghost"
						className="shadow-none"
						aria-label={`Actions for donor ${donor.name}`}
						disabled={isAnyActionLoading}
					>
						{isAnyActionLoading ? (
							<LoaderIcon
								size={16}
								className="animate-spin"
								aria-hidden="true"
							/>
						) : (
							<EllipsisIcon size={16} aria-hidden="true" />
						)}
					</Button>
				</div>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end" className="w-48">
				<DropdownMenuGroup>
					<DropdownMenuItem
						onClick={handleView}
						className="cursor-pointer"
						disabled={isAnyActionLoading}
						aria-describedby={`view-donor-${donor.id}`}
					>
						{isActionLoading === "view" ? (
							<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
						) : (
							<EyeIcon className="mr-2 h-4 w-4" />
						)}
						<span>View Details</span>
						<DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={handleEdit}
						className="cursor-pointer"
						disabled={isAnyActionLoading}
						aria-describedby={`edit-donor-${donor.id}`}
					>
						{isActionLoading === "edit" ? (
							<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
						) : (
							<EditIcon className="mr-2 h-4 w-4" />
						)}
						<span>Edit</span>
						<DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
					</DropdownMenuItem>
				</DropdownMenuGroup>

				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive cursor-pointer"
					onClick={handleDelete}
					disabled={isAnyActionLoading}
					aria-describedby={`delete-donor-${donor.id}`}
				>
					{isActionLoading === "delete" || isDeleting ? (
						<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
					) : (
						<TrashIcon className="mr-2 h-4 w-4" />
					)}
					<span>Delete</span>
					<DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	)
}

// For use with TanStack Table Row
export interface DonorRowActionsTableProps {
	row: Row<DonorWithStats>
	table: any
}

export function DonorRowActionsTable({
	row,
	table,
}: DonorRowActionsTableProps) {
	const { onView, onEdit, onDelete, deletingDonorId } = table.options.meta || {}

	const handleView = async (donorId: string) => {
		if (onView) {
			try {
				await onView(donorId)
			} catch (error) {
				console.error("Failed to view donor:", error)
				// Error handling could be enhanced with toast notifications
			}
		}
	}

	const handleEdit = async (donorId: string) => {
		if (onEdit) {
			try {
				await onEdit(donorId)
			} catch (error) {
				console.error("Failed to edit donor:", error)
				// Error handling could be enhanced with toast notifications
			}
		}
	}

	const handleDelete = async (donorId: string) => {
		if (onDelete) {
			try {
				await onDelete(donorId)
			} catch (error) {
				console.error("Failed to delete donor:", error)
				// Error handling could be enhanced with toast notifications
			}
		}
	}

	return (
		<DonorRowActions
			donor={row.original}
			onView={handleView}
			onEdit={handleEdit}
			onDelete={handleDelete}
			isDeleting={deletingDonorId === row.original.id}
		/>
	)
}
