import { Skeleton } from "@donorcare/ui/components/ui/skeleton"
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@donorcare/ui/components/ui/table"
import { donorColumns } from "./utils/columns"

interface DonorTableSkeletonProps {
	rows?: number
}

export function DonorTableSkeleton({ rows = 10 }: DonorTableSkeletonProps) {
	return (
		<div className="space-y-4">
			{/* Analytics Skeleton */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{Array.from({ length: 4 }).map((_, i) => (
					<div key={i} className="rounded-lg border p-6 space-y-2">
						<div className="flex items-center justify-between">
							<Skeleton className="h-4 w-24" />
							<Skeleton className="h-4 w-4" />
						</div>
						<Skeleton className="h-8 w-20" />
						<Skeleton className="h-3 w-16" />
					</div>
				))}
			</div>

			{/* Filters Skeleton */}
			<div className="flex items-center justify-between gap-3">
				<div className="flex items-center gap-3">
					<Skeleton className="h-10 w-60" />
					<Skeleton className="h-10 w-20" />
				</div>
				<div className="flex items-center gap-3">
					<Skeleton className="h-10 w-24" />
					<Skeleton className="h-10 w-20" />
				</div>
			</div>

			{/* Table Skeleton */}
			<div className="bg-background overflow-hidden rounded-md border">
				<Table className="table-fixed">
					<TableHeader>
						<TableRow className="hover:bg-transparent">
							{donorColumns.map((column) => (
								<TableHead
									key={column.id}
									style={{ width: `${column.size}px` }}
									className="h-11"
								>
									<Skeleton className="h-4 w-full" />
								</TableHead>
							))}
						</TableRow>
					</TableHeader>
					<TableBody>
						{Array.from({ length: rows }).map((_, index) => (
							<TableRow key={`skeleton-${index}`}>
								{donorColumns.map((column) => (
									<TableCell key={column.id} className="last:py-0">
										<div className="py-2">
											<Skeleton
												className={`h-4 ${
													column.id === "select"
														? "w-4"
														: column.id === "actions"
															? "w-8"
															: "w-full"
												}`}
											/>
										</div>
									</TableCell>
								))}
							</TableRow>
						))}
					</TableBody>
				</Table>
			</div>

			{/* Pagination Skeleton */}
			<div className="flex items-center justify-between gap-8">
				<div className="flex items-center gap-3">
					<Skeleton className="h-4 w-20" />
					<Skeleton className="h-10 w-16" />
				</div>
				<Skeleton className="h-4 w-32" />
				<div className="flex items-center gap-2">
					{Array.from({ length: 4 }).map((_, i) => (
						<Skeleton key={i} className="h-10 w-10" />
					))}
				</div>
			</div>
		</div>
	)
}
