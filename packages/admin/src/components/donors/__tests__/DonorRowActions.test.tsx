/**
 * @vitest-environment jsdom
 */

import type { donorWithStatsDto } from "@donorcare/backend/schemas"
import { render, screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import type { Static } from "elysia"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { DonorRowActions, DonorRowActionsTable } from "../DonorRowActions"

// Mock donor data
const mockDonor: Static<typeof donorWithStatsDto> = {
	id: "test-donor-id",
	name: "<PERSON>",
	email: "<EMAIL>",
	phone: "+60123456789",
	address: "123 Main St, Kuala Lumpur",
	notes: "VIP donor",
	createdAt: new Date("2024-01-15T10:00:00Z"),
	updatedAt: new Date("2024-01-15T10:00:00Z"),
	totalDonated: "500.00",
	donationCount: 5,
	averageDonation: "100.00",
	lastDonationDate: new Date("2024-01-20T10:00:00Z"),
	campaignsSupported: 3,
	firstDonationDate: new Date("2024-01-10T10:00:00Z"),
}

describe("DonorRowActions", () => {
	const mockOnView = vi.fn()
	const mockOnEdit = vi.fn()
	const mockOnDelete = vi.fn()

	const defaultProps = {
		donor: mockDonor,
		onView: mockOnView,
		onEdit: mockOnEdit,
		onDelete: mockOnDelete,
	}

	beforeEach(() => {
		vi.clearAllMocks()
		mockOnView.mockResolvedValue(undefined)
		mockOnEdit.mockResolvedValue(undefined)
		mockOnDelete.mockResolvedValue(undefined)
	})

	describe("Basic Rendering", () => {
		it("renders the dropdown trigger button", () => {
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button", {
				name: "Actions for donor John Doe",
			})
			expect(triggerButton).toBeInTheDocument()
			expect(triggerButton).toHaveAttribute(
				"aria-label",
				"Actions for donor John Doe",
			)
		})

		it("renders all action items when dropdown is opened", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button", {
				name: "Actions for donor John Doe",
			})
			await user.click(triggerButton)

			expect(screen.getByText("View Details")).toBeInTheDocument()
			expect(screen.getByText("Edit")).toBeInTheDocument()
			expect(screen.getByText("Delete")).toBeInTheDocument()
		})

		it("shows keyboard shortcuts for actions", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			expect(screen.getByText("⌘V")).toBeInTheDocument()
			expect(screen.getByText("⌘E")).toBeInTheDocument()
			expect(screen.getByText("⌘⌫")).toBeInTheDocument()
		})
	})

	describe("Action Handlers", () => {
		it("calls onView when View Details is clicked", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const viewItem = screen.getByText("View Details")
			await user.click(viewItem)

			expect(mockOnView).toHaveBeenCalledWith("test-donor-id")
		})

		it("calls onEdit when Edit is clicked", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const editItem = screen.getByText("Edit")
			await user.click(editItem)

			expect(mockOnEdit).toHaveBeenCalledWith("test-donor-id")
		})

		it("calls onDelete when Delete is clicked", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const deleteItem = screen.getByText("Delete")
			await user.click(deleteItem)

			expect(mockOnDelete).toHaveBeenCalledWith("test-donor-id")
		})
	})

	describe("Loading States", () => {
		it("shows loading spinner when isDeleting is true", () => {
			render(<DonorRowActions {...defaultProps} isDeleting={true} />)

			const triggerButton = screen.getByRole("button")
			expect(triggerButton).toBeDisabled()

			// Check for loading spinner
			const spinner = screen.getByRole("button").querySelector(".animate-spin")
			expect(spinner).toBeInTheDocument()
		})

		it("disables all actions when any action is loading", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			// Click view to trigger loading state
			const viewItem = screen.getByText("View Details")
			await user.click(viewItem)

			// Trigger button should be disabled during loading
			expect(triggerButton).toBeDisabled()
		})

		it("shows loading spinner for individual actions", async () => {
			const user = userEvent.setup()
			// Make onView take some time to resolve
			mockOnView.mockImplementation(
				() => new Promise((resolve) => setTimeout(resolve, 100)),
			)

			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const viewItem = screen.getByText("View Details")
			await user.click(viewItem)

			// Should show loading spinner for the view action
			await screen.findByText("View Details") // Wait for the element to be updated
			const viewMenuItem = screen
				.getByText("View Details")
				.closest('[role="menuitem"]')
			const spinner = viewMenuItem?.querySelector(".animate-spin")
			expect(spinner).toBeInTheDocument()
		})
	})

	describe("Error Handling", () => {
		it("handles view action errors gracefully", async () => {
			const user = userEvent.setup()
			const consoleError = vi
				.spyOn(console, "error")
				.mockImplementation(() => {})
			mockOnView.mockRejectedValue(new Error("View failed"))

			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const viewItem = screen.getByText("View Details")
			await user.click(viewItem)

			await vi.waitFor(() => {
				expect(consoleError).toHaveBeenCalledWith(
					"Error viewing donor:",
					expect.any(Error),
				)
			})

			consoleError.mockRestore()
		})

		it("handles edit action errors gracefully", async () => {
			const user = userEvent.setup()
			const consoleError = vi
				.spyOn(console, "error")
				.mockImplementation(() => {})
			mockOnEdit.mockRejectedValue(new Error("Edit failed"))

			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const editItem = screen.getByText("Edit")
			await user.click(editItem)

			await vi.waitFor(() => {
				expect(consoleError).toHaveBeenCalledWith(
					"Error editing donor:",
					expect.any(Error),
				)
			})

			consoleError.mockRestore()
		})

		it("handles delete action errors gracefully", async () => {
			const user = userEvent.setup()
			const consoleError = vi
				.spyOn(console, "error")
				.mockImplementation(() => {})
			mockOnDelete.mockRejectedValue(new Error("Delete failed"))

			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			await user.click(triggerButton)

			const deleteItem = screen.getByText("Delete")
			await user.click(deleteItem)

			await vi.waitFor(() => {
				expect(consoleError).toHaveBeenCalledWith(
					"Error deleting donor:",
					expect.any(Error),
				)
			})

			consoleError.mockRestore()
		})
	})

	describe("Accessibility", () => {
		it("has proper ARIA labels and descriptions", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			expect(triggerButton).toHaveAttribute(
				"aria-label",
				"Actions for donor John Doe",
			)

			await user.click(triggerButton)

			const viewItem = screen
				.getByText("View Details")
				.closest('[role="menuitem"]')
			const editItem = screen.getByText("Edit").closest('[role="menuitem"]')
			const deleteItem = screen.getByText("Delete").closest('[role="menuitem"]')

			expect(viewItem).toHaveAttribute(
				"aria-describedby",
				"view-donor-test-donor-id",
			)
			expect(editItem).toHaveAttribute(
				"aria-describedby",
				"edit-donor-test-donor-id",
			)
			expect(deleteItem).toHaveAttribute(
				"aria-describedby",
				"delete-donor-test-donor-id",
			)
		})

		it("properly disables actions when loading", async () => {
			const user = userEvent.setup()
			render(<DonorRowActions {...defaultProps} isDeleting={true} />)

			const triggerButton = screen.getByRole("button")
			expect(triggerButton).toBeDisabled()

			await user.click(triggerButton)

			const menuItems = screen.getAllByRole("menuitem")
			menuItems.forEach((item) => {
				expect(item).toHaveAttribute("aria-disabled", "true")
			})
		})
	})

	describe("Donor Information Display", () => {
		it("uses donor name in aria-label", () => {
			render(<DonorRowActions {...defaultProps} />)

			const triggerButton = screen.getByRole("button")
			expect(triggerButton).toHaveAttribute(
				"aria-label",
				"Actions for donor John Doe",
			)
		})

		it("handles donor with no name gracefully", () => {
			const donorWithoutName = { ...mockDonor, name: "" }
			render(<DonorRowActions {...defaultProps} donor={donorWithoutName} />)

			const triggerButton = screen.getByRole("button")
			expect(triggerButton).toHaveAttribute("aria-label", "Actions for donor ")
		})
	})

	describe("TanStack Table Integration", () => {
		it("renders with table row context", () => {
			const mockRow = {
				original: mockDonor,
			}

			const mockTable = {
				options: {
					meta: {
						onView: mockOnView,
						onEdit: mockOnEdit,
						onDelete: mockOnDelete,
						deletingDonorId: null,
					},
				},
			}

			render(<DonorRowActionsTable row={mockRow} table={mockTable} />)

			const triggerButton = screen.getByRole("button", {
				name: "Actions for donor John Doe",
			})
			expect(triggerButton).toBeInTheDocument()
		})
	})
})
