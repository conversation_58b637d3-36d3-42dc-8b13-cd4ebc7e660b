import type { DonorWithStats } from "@donorcare/backend/schemas"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { DeleteDonorDialog } from "../DeleteDonorDialog"

// Mock donor data for testing
const mockDonor: DonorWithStats = {
	id: "donor-123",
	name: "<PERSON>",
	email: "<EMAIL>",
	phone: "+60123456789",
	address: "123 Main St, Kuala Lumpur",
	notes: "Regular donor",
	createdAt: new Date("2024-01-01"),
	updatedAt: new Date("2024-01-15"),
	totalDonated: "500.00",
	donationCount: 3,
	averageDonation: "166.67",
	lastDonationDate: new Date("2024-01-10"),
	campaignsSupported: 2,
	firstDonationDate: new Date("2023-12-01"),
}

const mockDonorWithoutDonations: DonorWithStats = {
	...mockDonor,
	id: "donor-456",
	name: "<PERSON>",
	email: "<EMAIL>",
	totalDonated: "0.00",
	donationCount: 0,
	averageDonation: "0.00",
	lastDonationDate: null,
	campaignsSupported: 0,
	firstDonationDate: null,
}

describe("DeleteDonorDialog", () => {
	const mockOnClose = vi.fn()
	const mockOnConfirm = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("renders nothing when donor is null", () => {
		render(
			<DeleteDonorDialog
				donor={null}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.queryByText("Delete Donor")).not.toBeInTheDocument()
	})

	it("renders dialog when donor is provided and isOpen is true", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByText("Delete Donor")).toBeInTheDocument()
		expect(
			screen.getByText("This action cannot be undone."),
		).toBeInTheDocument()
		expect(
			screen.getByText('You are about to permanently delete donor "John Doe"'),
		).toBeInTheDocument()
	})

	it("shows donor name in confirmation input placeholder", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByText("John Doe")).toBeInTheDocument()
		expect(screen.getByPlaceholderText("Enter donor name")).toBeInTheDocument()
	})

	it("disables delete button when confirmation text doesn't match", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const deleteButton = screen.getByRole("button", { name: "Delete Donor" })
		expect(deleteButton).toBeDisabled()
	})

	it("enables delete button when confirmation text matches donor name", async () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donor name")
		const deleteButton = screen.getByRole("button", { name: "Delete Donor" })

		fireEvent.change(input, { target: { value: "John Doe" } })

		await waitFor(() => {
			expect(deleteButton).not.toBeDisabled()
		})
	})

	it("shows validation error when confirmation text is incorrect", async () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donor name")

		fireEvent.change(input, { target: { value: "Wrong Name" } })

		await waitFor(() => {
			expect(screen.getByText("Donor name does not match")).toBeInTheDocument()
		})
	})

	it("calls onConfirm when delete button is clicked with correct confirmation", async () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donor name")
		const deleteButton = screen.getByRole("button", { name: "Delete Donor" })

		fireEvent.change(input, { target: { value: "John Doe" } })

		await waitFor(() => {
			expect(deleteButton).not.toBeDisabled()
		})

		fireEvent.click(deleteButton)

		await waitFor(() => {
			expect(mockOnConfirm).toHaveBeenCalledWith("donor-123")
		})
	})

	it("shows loading state when isLoading is true", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={true}
			/>,
		)

		expect(screen.getByText("Deleting...")).toBeInTheDocument()
		expect(screen.getByRole("button", { name: "Cancel" })).toBeDisabled()
	})

	it("shows donations warning when donor has existing donations", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(
			screen.getByText("⚠️ This donor has existing donations"),
		).toBeInTheDocument()
		expect(
			screen.getByText(/This donor has made 3 donations totaling/),
		).toBeInTheDocument()
		expect(
			screen.getByText(/Consider marking the donor as inactive instead/),
		).toBeInTheDocument()
	})

	it("does not show donations warning for donors without donations", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonorWithoutDonations}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(
			screen.queryByText("⚠️ This donor has existing donations"),
		).not.toBeInTheDocument()
	})

	it("shows data preservation notice", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByText("📋 Data Preservation Notice")).toBeInTheDocument()
		expect(
			screen.getByText(
				/Donation records will be preserved but will no longer be associated/,
			),
		).toBeInTheDocument()
	})

	it("calls onClose when cancel button is clicked", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const cancelButton = screen.getByRole("button", { name: "Cancel" })
		fireEvent.click(cancelButton)

		expect(mockOnClose).toHaveBeenCalled()
	})

	it("resets confirmation text when dialog is closed and reopened", async () => {
		const { rerender } = render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donor name")
		fireEvent.change(input, { target: { value: "John Doe" } })

		// Close dialog by changing isOpen to false
		rerender(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={false}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		// Reopen dialog
		rerender(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const newInput = screen.getByPlaceholderText("Enter donor name")
		expect(newInput).toHaveValue("")
	})

	it("has proper accessibility attributes", () => {
		render(
			<DeleteDonorDialog
				donor={mockDonor}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter donor name")
		expect(input).toHaveAttribute("autoComplete", "off")
		expect(input).toHaveAttribute("autoFocus")

		const deleteButton = screen.getByRole("button", { name: "Delete Donor" })
		expect(deleteButton).toHaveClass("bg-destructive")
	})
})
