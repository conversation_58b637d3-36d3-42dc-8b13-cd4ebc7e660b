import type { DonorWithStats } from "@donorcare/backend/schemas"
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	Input,
	Label,
} from "@donorcare/ui"
import { AlertTriangle, Loader2 } from "lucide-react"
import { useId, useState } from "react"

export interface DeleteDonorDialogProps {
	/** The donor to delete, or null if dialog is closed */
	donor: DonorWithStats | null
	/** Whether the dialog is open */
	isOpen: boolean
	/** Function to close the dialog */
	onClose: () => void
	/** Function to confirm deletion */
	onConfirm: (donorId: string) => Promise<void>
	/** Whether deletion is in progress */
	isLoading: boolean
}

/**
 * Format currency amount for display
 */
function formatCurrency(amount: string, currency: string = "MYR"): string {
	const numAmount = parseFloat(amount)
	return new Intl.NumberFormat("en-MY", {
		style: "currency",
		currency: currency,
	}).format(numAmount)
}

/**
 * Confirmation dialog for donor deletion with safety measures
 * Requires donor name confirmation and shows warnings for donors with existing donations
 */
export function DeleteDonorDialog({
	donor,
	isOpen,
	onClose,
	onConfirm,
	isLoading,
}: DeleteDonorDialogProps) {
	const [confirmationText, setConfirmationText] = useState("")

	// Reset confirmation text when dialog opens/closes
	const handleOpenChange = (open: boolean) => {
		if (!open) {
			setConfirmationText("")
			onClose()
		}
	}

	const inputId = useId()

	const handleConfirm = async () => {
		if (!donor) return

		try {
			await onConfirm(donor.id)
			setConfirmationText("")
		} catch (error) {
			// Error handling is managed by the parent component
			console.error("Delete donor error:", error)
		}
	}

	// Check if confirmation text matches donor name
	const expectedName = donor?.name || ""
	const isConfirmationValid = donor && confirmationText === expectedName
	const hasConfirmationError =
		confirmationText.length > 0 && !isConfirmationValid

	// Check if donor has existing donations (should show additional warning)
	const hasDonations = donor && donor.donationCount > 0
	const totalDonated = donor ? formatCurrency(donor.totalDonated) : ""

	if (!donor) return null

	return (
		<AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
			<AlertDialogContent className="sm:max-w-md">
				<AlertDialogHeader>
					<div className="flex items-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
							<AlertTriangle className="h-5 w-5 text-destructive" />
						</div>
						<div className="flex-1">
							<AlertDialogTitle className="text-left">
								Delete Donor
							</AlertDialogTitle>
							<AlertDialogDescription className="text-left mt-1">
								This action cannot be undone.
							</AlertDialogDescription>
						</div>
					</div>
				</AlertDialogHeader>

				<div className="space-y-4">
					{/* Warning message */}
					<div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
						<p className="text-sm text-destructive font-medium mb-2">
							You are about to permanently delete donor "{donor.name}"
						</p>
						<p className="text-sm text-muted-foreground">
							This will remove the donor profile and all associated data from
							the system.
						</p>
					</div>

					{/* Donations warning - shows if donor has existing donations */}
					{hasDonations && (
						<div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
							<p className="text-sm text-amber-800 font-medium mb-1">
								⚠️ This donor has existing donations
							</p>
							<p className="text-sm text-amber-700 mb-2">
								This donor has made {donor.donationCount} donation
								{donor.donationCount !== 1 ? "s" : ""} totaling {totalDonated}.
								Deleting this donor may affect financial records and reporting.
							</p>
							<p className="text-sm text-amber-700">
								Consider marking the donor as inactive instead of deletion to
								preserve donation history and maintain data integrity.
							</p>
						</div>
					)}

					{/* Data preservation notice */}
					<div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
						<p className="text-sm text-blue-800 font-medium mb-1">
							📋 Data Preservation Notice
						</p>
						<p className="text-sm text-blue-700">
							{hasDonations
								? "Donation records will be preserved but will no longer be associated with this donor profile."
								: "All donor information including contact details and notes will be permanently removed."}
						</p>
					</div>

					{/* Confirmation input */}
					<div className="space-y-2">
						<Label htmlFor={inputId} className="text-sm font-medium">
							Type the donor name to confirm deletion:
						</Label>
						<div className="space-y-1">
							<p className="text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded">
								{expectedName}
							</p>
							<Input
								id={inputId}
								type="text"
								placeholder="Enter donor name"
								value={confirmationText}
								onChange={(e) => setConfirmationText(e.target.value)}
								disabled={isLoading}
								className={
									hasConfirmationError
										? "border-destructive focus-visible:ring-destructive"
										: ""
								}
								autoComplete="off"
								autoFocus
							/>
							{hasConfirmationError && (
								<p className="text-sm text-destructive">
									Donor name does not match
								</p>
							)}
						</div>
					</div>
				</div>

				<AlertDialogFooter>
					<AlertDialogCancel
						disabled={isLoading}
						onClick={() => handleOpenChange(false)}
					>
						Cancel
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={handleConfirm}
						disabled={!isConfirmationValid || isLoading}
						className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-destructive"
					>
						{isLoading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Deleting...
							</>
						) : (
							"Delete Donor"
						)}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	)
}
