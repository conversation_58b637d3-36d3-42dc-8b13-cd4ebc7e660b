import type { donorWithStatsDto } from "@donorcare/backend/schemas"
import { Badge } from "@donorcare/ui/components/ui/badge"
import {
	<PERSON>,
	Card<PERSON>ontent,
	CardHeader,
	CardTitle,
} from "@donorcare/ui/components/ui/card"
import { Skeleton } from "@donorcare/ui/components/ui/skeleton"
import { cn } from "@donorcare/ui/lib/utils"
import type { Static } from "elysia"
import {
	AlertCircleIcon,
	DollarSignIcon,
	HeartIcon,
	TrendingUpIcon,
	UsersIcon,
} from "lucide-react"
import { useMemo } from "react"
import { formatAmount } from "./utils/formatters"

type DonorWithStats = Static<typeof donorWithStatsDto>

export interface DonorAnalyticsProps {
	donors: DonorWithStats[]
	filteredDonors: DonorWithStats[]
	selectedDonors: DonorWithStats[]
	isLoading: boolean
	error?: Error | null
	className?: string
}

interface AnalyticsData {
	totalAmount: string
	totalCount: number
	averageAmount: string
	totalDonations: number
	averageDonationsPerDonor: number
}

export function DonorAnalytics({
	donors,
	filteredDonors,
	selectedDonors,
	isLoading,
	error,
	className,
}: DonorAnalyticsProps) {
	// Calculate analytics based on the current context (selected > filtered > all)
	const analyticsData = useMemo((): AnalyticsData => {
		const dataToAnalyze =
			selectedDonors.length > 0
				? selectedDonors
				: filteredDonors.length > 0
					? filteredDonors
					: donors

		if (!dataToAnalyze || dataToAnalyze.length === 0) {
			return {
				totalAmount: "0.00",
				totalCount: 0,
				averageAmount: "0.00",
				totalDonations: 0,
				averageDonationsPerDonor: 0,
			}
		}

		console.log(dataToAnalyze)

		// Calculate total amount donated by all donors
		const totalAmountNum = dataToAnalyze.reduce((sum, donor) => {
			return sum + parseFloat(donor.totalDonated)
		}, 0)

		// Calculate total number of donations across all donors
		const totalDonations = dataToAnalyze.reduce((sum, donor) => {
			return sum + donor.donationCount
		}, 0)

		// Calculate average donation amount per donor
		const averageAmountNum = totalAmountNum / dataToAnalyze.length

		// Calculate average donations per donor
		const averageDonationsPerDonor = totalDonations / dataToAnalyze.length

		return {
			totalAmount: totalAmountNum.toFixed(2),
			totalCount: dataToAnalyze.length,
			averageAmount: averageAmountNum.toFixed(2),
			totalDonations,
			averageDonationsPerDonor,
		}
	}, [donors, filteredDonors, selectedDonors])

	// Determine the context for display
	const context = useMemo(() => {
		if (selectedDonors.length > 0) {
			return {
				type: "selected" as const,
				count: selectedDonors.length,
				label: `${selectedDonors.length} selected`,
			}
		}
		if (filteredDonors.length !== donors.length) {
			return {
				type: "filtered" as const,
				count: filteredDonors.length,
				label: `${filteredDonors.length} filtered`,
			}
		}
		return {
			type: "all" as const,
			count: donors.length,
			label: "All donors",
		}
	}, [donors.length, filteredDonors.length, selectedDonors.length])

	// Show error state
	if (error && !isLoading) {
		return (
			<Card className={cn("border-destructive/50", className)}>
				<CardContent className="flex items-center justify-center py-6">
					<div className="flex items-center gap-2 text-destructive">
						<AlertCircleIcon className="h-4 w-4" />
						<span className="text-sm">Failed to load analytics</span>
					</div>
				</CardContent>
			</Card>
		)
	}

	return (
		<div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
			{/* Total Amount */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">Total Donated</CardTitle>
					<DollarSignIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-8 w-24" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div>
							<div className="text-2xl font-bold">
								{formatAmount(analyticsData.totalAmount)}
							</div>
							<p className="text-xs text-muted-foreground">{context.label}</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Total Donors */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">Total Donors</CardTitle>
					<UsersIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-8 w-16" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div>
							<div className="text-2xl font-bold">
								{analyticsData.totalCount.toLocaleString()}
							</div>
							<p className="text-xs text-muted-foreground">
								{context.type === "selected"
									? "donors selected"
									: context.type === "filtered"
										? "donors match filters"
										: "total donors"}
							</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Average per Donor */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">
						Average per Donor
					</CardTitle>
					<TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-8 w-20" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div>
							<div className="text-2xl font-bold">
								{analyticsData.totalCount > 0
									? formatAmount(analyticsData.averageAmount)
									: formatAmount("0.00")}
							</div>
							<p className="text-xs text-muted-foreground">per donor</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Total Donations */}
			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="text-sm font-medium">Total Donations</CardTitle>
					<HeartIcon className="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="space-y-2">
							<Skeleton className="h-6 w-full" />
							<Skeleton className="h-4 w-16" />
						</div>
					) : (
						<div className="space-y-2">
							<div className="text-2xl font-bold">
								{analyticsData.totalDonations.toLocaleString()}
							</div>
							<div className="flex items-center gap-2">
								<Badge variant="secondary" className="text-xs">
									{analyticsData.averageDonationsPerDonor.toFixed(1)} avg per
									donor
								</Badge>
							</div>
							<p className="text-xs text-muted-foreground">total donations</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	)
}
