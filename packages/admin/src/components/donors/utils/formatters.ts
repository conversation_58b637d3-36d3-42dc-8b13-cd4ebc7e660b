/**
 * Format currency amount with proper locale and currency symbol
 * @param amount - The amount as a string
 * @param currency - The currency code (default: MYR)
 * @returns Formatted currency string
 */
export const formatAmount = (
	amount: string,
	currency: string = "MYR",
): string => {
	const numAmount = parseFloat(amount)

	// Handle invalid amounts
	if (Number.isNaN(numAmount)) {
		return `${currency} 0.00`
	}

	// Use appropriate locale based on currency
	const locale = getLocaleForCurrency(currency)

	return new Intl.NumberFormat(locale, {
		style: "currency",
		currency: currency,
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(numAmount)
}

/**
 * Format date for display in table
 * @param dateString - Date string or Date object
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | Date): string => {
	const date =
		typeof dateString === "string" ? new Date(dateString) : dateString

	// Handle invalid dates
	if (Number.isNaN(date.getTime())) {
		return "Invalid Date"
	}

	return date.toLocaleDateString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
	})
}

/**
 * Format date and time for detailed views
 * @param dateString - Date string or Date object
 * @returns Formatted date and time string
 */
export const formatDateTime = (dateString: string | Date): string => {
	const date =
		typeof dateString === "string" ? new Date(dateString) : dateString

	// Handle invalid dates
	if (Number.isNaN(date.getTime())) {
		return "Invalid Date"
	}

	return date.toLocaleString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
		hour12: true,
	})
}

/**
 * Format large numbers with appropriate suffixes (K, M, B)
 * @param amount - The amount as a string or number
 * @param currency - The currency code (optional)
 * @returns Formatted amount with suffix
 */
export const formatCompactAmount = (
	amount: string | number,
	currency?: string,
): string => {
	const numAmount = typeof amount === "string" ? parseFloat(amount) : amount

	if (Number.isNaN(numAmount)) {
		return currency ? `${currency} 0` : "0"
	}

	const formatter = new Intl.NumberFormat("en-US", {
		notation: "compact",
		compactDisplay: "short",
		minimumFractionDigits: 0,
		maximumFractionDigits: 1,
	})

	const formatted = formatter.format(numAmount)
	return currency ? `${currency} ${formatted}` : formatted
}

/**
 * Parse amount string to number for calculations
 * @param amount - Amount string
 * @returns Parsed number or 0 if invalid
 */
export const parseAmount = (amount: string): number => {
	const parsed = parseFloat(amount)
	return Number.isNaN(parsed) ? 0 : parsed
}

/**
 * Format donor statistics for display
 * @param donationCount - Number of donations
 * @param campaignsSupported - Number of campaigns supported
 * @returns Formatted statistics string
 */
export const formatDonorStats = (
	donationCount: number,
	campaignsSupported: number,
): string => {
	const donations = donationCount === 1 ? "donation" : "donations"
	const campaigns = campaignsSupported === 1 ? "campaign" : "campaigns"
	return `${donationCount} ${donations} across ${campaignsSupported} ${campaigns}`
}

/**
 * Format average donation amount
 * @param totalDonated - Total amount donated as string
 * @param donationCount - Number of donations
 * @param currency - Currency code
 * @returns Formatted average donation string
 */
export const formatAverageDonation = (
	totalDonated: string,
	donationCount: number,
	currency: string = "MYR",
): string => {
	if (donationCount === 0) {
		return formatAmount("0", currency)
	}

	const total = parseFloat(totalDonated)
	const average = total / donationCount

	return formatAmount(average.toString(), currency)
}

/**
 * Get appropriate locale for currency formatting
 * @param currency - Currency code
 * @returns Locale string
 */
function getLocaleForCurrency(currency: string): string {
	switch (currency) {
		case "MYR":
			return "en-MY"
		case "USD":
			return "en-US"
		case "EUR":
			return "en-EU"
		case "GBP":
			return "en-GB"
		case "SGD":
			return "en-SG"
		default:
			return "en-US"
	}
}
