import type { donorWithStatsDto } from "@donorcare/backend/schemas"
import { Checkbox } from "@donorcare/ui/components/ui/checkbox"
import type { ColumnDef } from "@tanstack/react-table"
import type { Static } from "elysia"
import { DonorRowActionsTable } from "../DonorRowActions"
import {
	amountRangeFilterFn,
	dateRangeFilterFn,
	multiColumnFilterFn,
} from "./filters"
import { formatAmount, formatDate } from "./formatters"

type DonorWithStats = Static<typeof donorWithStatsDto>

/**
 * Column definitions for the donors table
 * Follows TanStack Table patterns with proper sizing, formatting, and filtering
 */
export const donorColumns: ColumnDef<DonorWithStats>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && "indeterminate")
				}
				onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
				aria-label="Select all donors"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label={`Select donor ${row.original.name}`}
			/>
		),
		size: 28,
		enableSorting: false,
		enableHiding: false,
	},
	{
		header: "Name",
		accessorKey: "name",
		cell: ({ row }) => (
			<div className="font-medium">{row.getValue("name")}</div>
		),
		size: 180,
		filterFn: multiColumnFilterFn,
		enableHiding: false,
		enableSorting: true,
	},
	{
		header: "Email",
		accessorKey: "email",
		cell: ({ row }) => (
			<div className="text-sm text-muted-foreground">
				{row.getValue("email")}
			</div>
		),
		size: 220,
		enableSorting: true,
	},
	{
		header: "Total Donated",
		accessorKey: "totalDonated",
		cell: ({ row }) => (
			<div className="font-medium">
				{formatAmount(row.getValue("totalDonated"), "MYR")}
			</div>
		),
		size: 140,
		enableSorting: true,
		filterFn: amountRangeFilterFn,
	},
	{
		header: "Donations",
		accessorKey: "donationCount",
		cell: ({ row }) => (
			<div className="text-center">{row.getValue("donationCount")}</div>
		),
		size: 100,
		enableSorting: true,
	},
	{
		header: "Campaigns",
		accessorKey: "campaignsSupported",
		cell: ({ row }) => (
			<div className="text-center">{row.getValue("campaignsSupported")}</div>
		),
		size: 100,
		enableSorting: true,
	},
	{
		header: "Last Donation",
		accessorKey: "lastDonationDate",
		cell: ({ row }) => {
			const lastDonation = row.getValue("lastDonationDate") as Date | null
			return (
				<div className="text-sm">
					{lastDonation ? formatDate(lastDonation) : "Never"}
				</div>
			)
		},
		size: 140,
		enableSorting: true,
		filterFn: dateRangeFilterFn,
	},
	{
		id: "actions",
		header: () => <span className="sr-only">Actions</span>,
		cell: ({ row, table }) => <DonorRowActionsTable row={row} table={table} />,
		size: 60,
		enableHiding: false,
		enableSorting: false,
	},
]
