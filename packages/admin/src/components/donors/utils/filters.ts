import type { donorWithStatsDto } from "@donorcare/backend/schemas"
import type { FilterFn } from "@tanstack/react-table"
import type { Static } from "elysia"

type DonorWithStats = Static<typeof donorWithStatsDto>

/**
 * Custom filter function for multi-column searching
 * Searches across donor name and email
 */
export const multiColumnFilterFn: FilterFn<DonorWithStats> = (
	row,
	_columnId,
	filterValue,
) => {
	const searchableRowContent =
		`${row.original.name} ${row.original.email}`.toLowerCase()
	const searchTerm = (filterValue ?? "").toLowerCase()
	return searchableRowContent.includes(searchTerm)
}

/**
 * Custom filter function for date range filtering
 * Filters donors by last donation date within a specific date range
 */
export const dateRangeFilterFn: FilterFn<DonorWithStats> = (
	row,
	_columnId,
	filterValue: { from?: Date; to?: Date },
) => {
	if (!filterValue?.from && !filterValue?.to) return true

	const lastDonationDate = row.original.lastDonationDate
	if (!lastDonationDate) return false // No donations, exclude from date range filter

	const donationDate = new Date(lastDonationDate)

	if (filterValue.from && donationDate < filterValue.from) return false
	if (filterValue.to && donationDate > filterValue.to) return false

	return true
}

/**
 * Custom filter function for amount range filtering
 * Filters donors by total donated amount within a specific range
 */
export const amountRangeFilterFn: FilterFn<DonorWithStats> = (
	row,
	_columnId,
	filterValue: { min?: number; max?: number },
) => {
	if (!filterValue?.min && !filterValue?.max) return true

	const totalDonated = parseFloat(row.original.totalDonated)

	if (filterValue.min && totalDonated < filterValue.min) return false
	if (filterValue.max && totalDonated > filterValue.max) return false

	return true
}

/**
 * Custom filter function for donation count filtering
 * Filters donors by number of donations within a specific range
 */
export const donationCountFilterFn: FilterFn<DonorWithStats> = (
	row,
	_columnId,
	filterValue: { min?: number; max?: number },
) => {
	if (!filterValue?.min && !filterValue?.max) return true

	const donationCount = row.original.donationCount

	if (filterValue.min && donationCount < filterValue.min) return false
	if (filterValue.max && donationCount > filterValue.max) return false

	return true
}

/**
 * Custom filter function for campaigns supported filtering
 * Filters donors by number of campaigns they've supported
 */
export const campaignsSupportedFilterFn: FilterFn<DonorWithStats> = (
	row,
	_columnId,
	filterValue: { min?: number; max?: number },
) => {
	if (!filterValue?.min && !filterValue?.max) return true

	const campaignsSupported = row.original.campaignsSupported

	if (filterValue.min && campaignsSupported < filterValue.min) return false
	if (filterValue.max && campaignsSupported > filterValue.max) return false

	return true
}
