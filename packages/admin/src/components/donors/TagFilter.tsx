import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>over<PERSON>ontent,
	<PERSON>over<PERSON>rigger,
	Separator,
} from "@donorcare/ui"
import { Check, Tag as TagIcon, X } from "lucide-react"
import { useTags } from "@/hooks/useDonors"

interface TagFilterProps {
	selectedTagIds: string[]
	onTagsChange: (tagIds: string[]) => void
}

/**
 * Tag Filter Component for DonorsTable
 */
export function TagFilter({ selectedTagIds, onTagsChange }: TagFilterProps) {
	const { data: allTags = [], isLoading } = useTags()

	const handleTagToggle = (tagId: string) => {
		const newSelectedTags = selectedTagIds.includes(tagId)
			? selectedTagIds.filter((id) => id !== tagId)
			: [...selectedTagIds, tagId]

		onTagsChange(newSelectedTags)
	}

	const handleClearAll = () => {
		onTagsChange([])
	}

	const selectedTags = allTags.filter((tag: any) =>
		selectedTagIds.includes(tag.id),
	)

	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button variant="outline" size="sm" className="h-8 border-dashed">
					<TagIcon className="mr-2 h-4 w-4" />
					Tags
					{selectedTagIds.length > 0 && (
						<>
							<Separator orientation="vertical" className="mx-2 h-4" />
							<Badge
								variant="secondary"
								className="rounded-sm px-1 font-normal lg:hidden"
							>
								{selectedTagIds.length}
							</Badge>
							<div className="hidden space-x-1 lg:flex">
								{selectedTagIds.length > 2 ? (
									<Badge
										variant="secondary"
										className="rounded-sm px-1 font-normal"
									>
										{selectedTagIds.length} selected
									</Badge>
								) : (
									selectedTags.map((tag: any) => (
										<Badge
											variant="secondary"
											key={tag.id}
											className="rounded-sm px-1 font-normal"
											style={{
												backgroundColor: tag.color + "20",
												borderColor: tag.color,
												color: tag.color,
											}}
										>
											{tag.name}
										</Badge>
									))
								)}
							</div>
						</>
					)}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[200px] p-0" align="start">
				<div className="p-2">
					<div className="flex items-center justify-between mb-2">
						<span className="text-sm font-medium">Filter by Tags</span>
						{selectedTagIds.length > 0 && (
							<Button
								variant="ghost"
								size="sm"
								onClick={handleClearAll}
								className="h-6 px-2 text-xs"
							>
								Clear
							</Button>
						)}
					</div>

					{isLoading ? (
						<div className="py-2 text-sm text-muted-foreground">
							Loading tags...
						</div>
					) : allTags.length === 0 ? (
						<div className="py-2 text-sm text-muted-foreground">
							No tags available
						</div>
					) : (
						<div className="space-y-1">
							{allTags.map((tag: any) => {
								const isSelected = selectedTagIds.includes(tag.id)
								return (
									<div
										key={tag.id}
										className="flex items-center space-x-2 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-sm p-1"
										onClick={() => handleTagToggle(tag.id)}
									>
										<div className="flex items-center space-x-2 flex-1">
											<div
												className="w-3 h-3 rounded-full border"
												style={{
													backgroundColor: tag.color,
													borderColor: tag.color,
												}}
											/>
											<span className="text-sm">{tag.name}</span>
										</div>
										{isSelected && <Check className="h-4 w-4 text-primary" />}
									</div>
								)
							})}
						</div>
					)}
				</div>
			</PopoverContent>
		</Popover>
	)
}

/**
 * Selected Tags Display Component
 */
interface SelectedTagsDisplayProps {
	selectedTagIds: string[]
	onRemoveTag: (tagId: string) => void
	onClearAll: () => void
}

export function SelectedTagsDisplay({
	selectedTagIds,
	onRemoveTag,
	onClearAll,
}: SelectedTagsDisplayProps) {
	const { data: allTags = [] } = useTags()

	if (selectedTagIds.length === 0) return null

	const selectedTags = allTags.filter((tag: any) =>
		selectedTagIds.includes(tag.id),
	)

	return (
		<div className="flex items-center gap-2 flex-wrap">
			<span className="text-sm text-muted-foreground">Filtered by:</span>
			{selectedTags.map((tag: any) => (
				<Badge
					key={tag.id}
					variant="secondary"
					className="gap-1"
					style={{
						backgroundColor: tag.color + "20",
						borderColor: tag.color,
						color: tag.color,
					}}
				>
					{tag.name}
					<X
						className="h-3 w-3 cursor-pointer hover:opacity-70"
						onClick={() => onRemoveTag(tag.id)}
					/>
				</Badge>
			))}
			<Button
				variant="ghost"
				size="sm"
				onClick={onClearAll}
				className="h-6 px-2 text-xs"
			>
				Clear all
			</Button>
		</div>
	)
}
