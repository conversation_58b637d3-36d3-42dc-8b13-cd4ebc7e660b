import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@donorcare/ui/components/ui/alert-dialog"
import { Button } from "@donorcare/ui/components/ui/button"
import { LogOut, ShieldAlert } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import { useAuthStore } from "@/lib/auth-store"

export interface StopImpersonationButtonProps {
	onImpersonationStop?: () => void
	onImpersonationError?: (error: Error) => void
}

/**
 * StopImpersonationButton component that allows users to stop impersonating
 * This component provides a way to end an impersonation session and return
 * to the original admin account.
 */
export function StopImpersonationButton({
	onImpersonationStop,
	onImpersonationError,
}: StopImpersonationButtonProps) {
	const [isStopping, setIsStopping] = useState(false)
	const { stopImpersonation, isImpersonating } = useAuthStore()

	/**
	 * Check if the current user is impersonating someone
	 */
	const isCurrentlyImpersonating = () => {
		// Check the auth store state
		return isImpersonating
	}

	/**
	 * Handle stopping impersonation
	 */
	const handleStopImpersonation = async () => {
		try {
			setIsStopping(true)

			// Call the stop impersonation method from auth store
			await stopImpersonation()

			toast.success("You have returned to your admin account")

			onImpersonationStop?.()
		} catch (error) {
			console.error("Stop impersonation error:", error)
			const errorMessage =
				error instanceof Error ? error.message : "An unknown error occurred"

			toast.error(errorMessage)

			onImpersonationError?.(error as Error)
		} finally {
			setIsStopping(false)
		}
	}

	// Don't render if user is not impersonating
	if (!isCurrentlyImpersonating()) {
		return null
	}

	return (
		<AlertDialog>
			<AlertDialogTrigger asChild>
				<Button
					variant="outline"
					size="sm"
					className="flex items-center gap-2 text-destructive hover:text-destructive"
				>
					<LogOut className="h-4 w-4" />
					<span>Stop Impersonating</span>
				</Button>
			</AlertDialogTrigger>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle className="flex items-center gap-2">
						<ShieldAlert className="h-5 w-5 text-destructive" />
						Stop Impersonating?
					</AlertDialogTitle>
					<AlertDialogDescription>
						Are you sure you want to stop impersonating this organizer and
						return to your admin account?
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter className="gap-2 sm:gap-0">
					<AlertDialogCancel disabled={isStopping}>Cancel</AlertDialogCancel>
					<AlertDialogAction
						onClick={handleStopImpersonation}
						disabled={isStopping}
						className="bg-destructive hover:bg-destructive/90"
					>
						{isStopping ? (
							<>
								<span className="animate-spin mr-2">●</span>
								Stopping...
							</>
						) : (
							"Stop Impersonating"
						)}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	)
}
