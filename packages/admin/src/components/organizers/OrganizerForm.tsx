import type { Organizer } from "@donorcare/backend/schemas"
import { createOrganizerDto } from "@donorcare/backend/schemas"
import {
	Button,
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
} from "@donorcare/ui"
import type { Static } from "elysia"
import { Loader2, Mail, User } from "lucide-react"
import { useForm } from "react-hook-form"
import { createTypeboxResolver } from "@/lib/typebox-resolver"

// Form data types
export type CreateOrganizerFormData = Static<typeof createOrganizerDto>

// Component props
export interface OrganizerFormProps {
	mode: "create" | "edit"
	initialData?: Partial<Organizer>
	onSubmit: (data: CreateOrganizerFormData) => Promise<void>
	onCancel: () => void
	isLoading?: boolean
}

export function OrganizerForm({
	mode,
	initialData,
	onSubmit,
	onCancel,
	isLoading = false,
}: OrganizerFormProps) {
	// Form setup with validation
	const form = useForm<CreateOrganizerFormData>({
		resolver: createTypeboxResolver(createOrganizerDto),
		defaultValues: {
			name: initialData?.name || "",
			email: initialData?.email || "",
		},
	})

	// Handle form submission
	const handleSubmit = async (data: CreateOrganizerFormData) => {
		try {
			await onSubmit(data)
		} catch (error) {
			// Error handling is done by the parent component via react-query
			console.error("Form submission error:", error)
		}
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="space-y-1">
				<h2 className="text-2xl font-semibold tracking-tight">
					{mode === "create" ? "Create Organizer" : "Edit Organizer"}
				</h2>
				<p className="text-muted-foreground">
					{mode === "create"
						? "Add a new organizer to the platform. They'll be able to create and manage campaigns."
						: "Update the organizer's information."}
				</p>
			</div>

			{/* Form */}
			<Form {...form}>
				<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
					{/* Basic Information */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Basic Information</h3>

						<div className="grid gap-4 sm:grid-cols-2">
							{/* Name Field */}
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-2">
											<User className="h-4 w-4" />
											Full Name
										</FormLabel>
										<FormControl>
											<Input
												placeholder="Enter organizer's full name"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Email Field */}
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-2">
											<Mail className="h-4 w-4" />
											Email Address
										</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="Enter email address"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>

					{/* Actions */}
					<div className="flex gap-3 pt-6 border-t">
						<Button
							type="submit"
							disabled={isLoading}
							className="min-w-[100px]"
						>
							{isLoading ? (
								<>
									<Loader2 className="h-4 w-4 animate-spin" />
									{mode === "create" ? "Creating..." : "Saving..."}
								</>
							) : (
								<>{mode === "create" ? "Create Organizer" : "Save Changes"}</>
							)}
						</Button>
						<Button
							type="button"
							variant="outline"
							onClick={onCancel}
							disabled={isLoading}
						>
							Cancel
						</Button>
					</div>
				</form>
			</Form>
		</div>
	)
}
