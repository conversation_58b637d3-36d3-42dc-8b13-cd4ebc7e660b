import type { Organizer } from "@donorcare/backend/schemas"
import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@donorcare/ui"
import {
	Calendar,
	Edit,
	Eye,
	Mail,
	MoreHorizontal,
	Shield,
	ShieldAlert,
	Trash2,
	User,
} from "lucide-react"

export interface OrganizerCardProps {
	organizer: Organizer
	onView?: (organizerId: string) => void
	onEdit?: (organizerId: string) => void
	onDelete?: (organizerId: string) => void
	onImpersonate?: (organizerId: string) => void
	isLoading?: boolean
}

/**
 * Individual organizer card component for displaying organizer information
 * with action buttons for view, edit, delete, and impersonate
 */
export function OrganizerCard({
	organizer,
	onView,
	onEdit,
	onDelete,
	onImpersonate,
	isLoading = false,
}: OrganizerCardProps) {
	const handleView = () => {
		onView?.(organizer.id)
	}

	const handleEdit = () => {
		onEdit?.(organizer.id)
	}

	const handleDelete = () => {
		onDelete?.(organizer.id)
	}

	const handleImpersonate = () => {
		onImpersonate?.(organizer.id)
	}

	// Format date for display
	const formatDate = (date: Date) => {
		return new Intl.DateTimeFormat("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		}).format(new Date(date))
	}

	return (
		<Card className={`transition-all duration-200 hover:shadow-md ${isLoading ? "opacity-50" : ""}`}>
			<CardHeader className="pb-4">
				<div className="flex items-start justify-between">
					<div className="flex items-start gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
							<User className="h-5 w-5 text-primary" />
						</div>
						<div className="space-y-1">
							<CardTitle className="text-lg">{organizer.name}</CardTitle>
							<CardDescription className="flex items-center gap-1">
								<Mail className="h-3 w-3" />
								{organizer.email}
							</CardDescription>
						</div>
					</div>
					<div className="flex items-center gap-2">
						{/* Status Badge */}
						<Badge variant={organizer.banned ? "destructive" : "default"}>
							{organizer.banned ? (
								<>
									<ShieldAlert className="h-3 w-3 mr-1" />
									Banned
								</>
							) : (
								<>
									<Shield className="h-3 w-3 mr-1" />
									Active
								</>
							)}
						</Badge>
						{/* Actions Dropdown */}
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									disabled={isLoading}
									className="h-8 w-8 p-0"
								>
									<MoreHorizontal className="h-4 w-4" />
									<span className="sr-only">Open menu</span>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end" className="w-40">
								{onView && (
									<DropdownMenuItem onClick={handleView}>
										<Eye className="h-4 w-4 mr-2" />
										View
									</DropdownMenuItem>
								)}
								{onEdit && (
									<DropdownMenuItem onClick={handleEdit}>
										<Edit className="h-4 w-4 mr-2" />
										Edit
									</DropdownMenuItem>
								)}
								{onImpersonate && !organizer.banned && (
									<>
										<DropdownMenuSeparator />
										<DropdownMenuItem onClick={handleImpersonate}>
											<User className="h-4 w-4 mr-2" />
											Impersonate
										</DropdownMenuItem>
									</>
								)}
								{onDelete && (
									<>
										<DropdownMenuSeparator />
										<DropdownMenuItem
											onClick={handleDelete}
											className="text-destructive focus:text-destructive"
										>
											<Trash2 className="h-4 w-4 mr-2" />
											Delete
										</DropdownMenuItem>
									</>
								)}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			</CardHeader>

			<CardContent className="pt-0">
				<div className="grid gap-4 text-sm">
					{/* Role Information */}
					<div className="flex items-center justify-between">
						<span className="text-muted-foreground">Role:</span>
						<span className="font-medium capitalize">{organizer.role}</span>
					</div>

					{/* Registration Date */}
					<div className="flex items-center justify-between">
						<span className="text-muted-foreground flex items-center gap-1">
							<Calendar className="h-3 w-3" />
							Registered:
						</span>
						<span className="font-medium">{formatDate(organizer.createdAt)}</span>
					</div>

					{/* Last Updated */}
					{organizer.updatedAt && organizer.updatedAt !== organizer.createdAt && (
						<div className="flex items-center justify-between">
							<span className="text-muted-foreground">Last Updated:</span>
							<span className="font-medium">{formatDate(organizer.updatedAt)}</span>
						</div>
					)}
				</div>
			</CardContent>

			{/* Quick Actions Footer */}
			<CardFooter className="pt-4">
				<div className="flex w-full gap-2">
					{onView && (
						<Button
							variant="outline"
							size="sm"
							onClick={handleView}
							disabled={isLoading}
							className="flex-1"
						>
							<Eye className="h-4 w-4 mr-2" />
							View Details
						</Button>
					)}
					{onEdit && (
						<Button
							variant="outline"
							size="sm"
							onClick={handleEdit}
							disabled={isLoading}
							className="flex-1"
						>
							<Edit className="h-4 w-4 mr-2" />
							Edit
						</Button>
					)}
				</div>
			</CardFooter>
		</Card>
	)
}