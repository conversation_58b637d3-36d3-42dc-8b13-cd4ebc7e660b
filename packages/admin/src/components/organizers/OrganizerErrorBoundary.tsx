import { Button } from "@donorcare/ui/components/ui/button"
import {
	Card,
	CardContent,
	CardDescription,
	Card<PERSON>ooter,
	CardHeader,
	CardTitle,
} from "@donorcare/ui/components/ui/card"
import { AlertTriangleIcon, RefreshCwIcon } from "lucide-react"
import type { ErrorInfo, ReactNode } from "react"
import { Component } from "react"

interface Props {
	children: ReactNode
	fallback?: ReactNode
	onReset?: () => void
}

interface State {
	hasError: boolean
	error?: Error
}

export class OrganizerErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props)
		this.state = { hasError: false }
	}

	static getDerivedStateFromError(error: Error): State {
		return { hasError: true, error }
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		console.error("OrganizerErrorBoundary caught an error:", error, errorInfo)
	}

	handleReset = () => {
		this.setState({ hasError: false, error: undefined })
		this.props.onReset?.()
	}

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				return this.props.fallback
			}

			return (
				<Card className="w-full max-w-md mx-auto mt-8">
					<CardHeader className="text-center">
						<div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
							<AlertTriangleIcon className="h-6 w-6 text-destructive" />
						</div>
						<CardTitle className="text-xl">Something went wrong</CardTitle>
						<CardDescription>
							An error occurred while loading the organizer information. This
							might be a temporary issue.
						</CardDescription>
					</CardHeader>
					<CardContent className="text-center">
						{this.state.error && (
							<details className="mt-4 text-left">
								<summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
									Error details
								</summary>
								<pre className="mt-2 text-xs text-muted-foreground bg-muted p-2 rounded overflow-auto">
									{this.state.error.message}
								</pre>
							</details>
						)}
					</CardContent>
					<CardFooter className="flex justify-center">
						<Button onClick={this.handleReset} variant="outline">
							<RefreshCwIcon className="mr-2 h-4 w-4" />
							Try again
						</Button>
					</CardFooter>
				</Card>
			)
		}

		return this.props.children
	}
}

// Hook-based error boundary for functional components
export function withOrganizerErrorBoundary<T extends object>(
	Component: React.ComponentType<T>,
	fallback?: ReactNode,
) {
	return function WrappedComponent(props: T) {
		return (
			<OrganizerErrorBoundary fallback={fallback}>
				<Component {...props} />
			</OrganizerErrorBoundary>
		)
	}
}
