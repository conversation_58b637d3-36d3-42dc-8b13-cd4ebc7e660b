import { render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { ImpersonationButton } from "../ImpersonationButton"

// Mock donor data
const mockDonor = {
	id: "donor-123",
	name: "<PERSON>",
	email: "<EMAIL>",
	phone: "+1234567890",
	address: "123 Main St",
	notes: "Test donor",
	createdAt: new Date("2024-01-01"),
	updatedAt: new Date("2024-01-01"),
	totalDonated: "100.00",
	donationCount: 5,
	averageDonation: "20.00",
	lastDonationDate: new Date("2024-01-15"),
	campaignsSupported: 3,
	firstDonationDate: new Date("2023-12-01"),
}

// Mock auth store
const mockUseAuthStore = vi.fn()

vi.mock("@/lib/auth-store", () => ({
	useAuthStore: () => mockUseAuthStore(),
}))

describe("ImpersonationButton", () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("renders null when user is not admin", () => {
		mockUseAuthStore.mockReturnValue({
			user: {
				id: "user-123",
				name: "Regular User",
				email: "<EMAIL>",
				role: "donor",
			},
		})

		const { container } = render(<ImpersonationButton donor={mockDonor} />)

		expect(container.firstChild).toBeNull()
	})

	it("renders button when user is admin", () => {
		mockUseAuthStore.mockReturnValue({
			user: {
				id: "admin-123",
				name: "Admin User",
				email: "<EMAIL>",
				role: "admin",
			},
		})

		render(<ImpersonationButton donor={mockDonor} />)

		expect(screen.getByText("Impersonate")).toBeInTheDocument()
	})

	it("opens confirmation dialog when clicked", async () => {
		mockUseAuthStore.mockReturnValue({
			user: {
				id: "admin-123",
				name: "Admin User",
				email: "<EMAIL>",
				role: "admin",
			},
		})

		const user = require("@testing-library/user-event").default.setup()

		render(<ImpersonationButton donor={mockDonor} />)

		const button = screen.getByText("Impersonate")
		await user.click(button)

		expect(screen.getByText("Impersonate Donor")).toBeInTheDocument()
		expect(
			screen.getByText(`You are about to impersonate ${mockDonor.name}`),
		).toBeInTheDocument()
	})
})
