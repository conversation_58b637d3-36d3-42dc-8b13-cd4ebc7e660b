import { render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { StopImpersonationButton } from "../StopImpersonationButton"

// Mock auth store
const mockUseAuthStore = vi.fn()

vi.mock("@/lib/auth-store", () => ({
	useAuthStore: () => mockUseAuthStore(),
}))

describe("StopImpersonationButton", () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("renders null when user is not impersonating", () => {
		mockUseAuthStore.mockReturnValue({
			user: {
				id: "admin-123",
				name: "Admin User",
				email: "<EMAIL>",
				role: "admin",
			},
			isImpersonating: false,
		})

		const { container } = render(<StopImpersonationButton />)

		expect(container.firstChild).toBeNull()
	})

	it("renders button when user is impersonating", () => {
		mockUseAuthStore.mockReturnValue({
			user: {
				id: "admin-123",
				name: "Admin User",
				email: "<EMAIL>",
				role: "admin",
			},
			isImpersonating: true,
		})

		render(<StopImpersonationButton />)

		expect(screen.getByText("Stop Impersonating")).toBeInTheDocument()
	})

	it("opens confirmation dialog when clicked", async () => {
		mockUseAuthStore.mockReturnValue({
			user: {
				id: "admin-123",
				name: "Admin User",
				email: "<EMAIL>",
				role: "admin",
			},
			isImpersonating: true,
		})

		const user = require("@testing-library/user-event").default.setup()

		render(<StopImpersonationButton />)

		const button = screen.getByText("Stop Impersonating")
		await user.click(button)

		expect(screen.getByText("Stop Impersonating?")).toBeInTheDocument()
		expect(
			screen.getByText(
				"Are you sure you want to stop impersonating this donor and return to your admin account?",
			),
		).toBeInTheDocument()
	})
})
