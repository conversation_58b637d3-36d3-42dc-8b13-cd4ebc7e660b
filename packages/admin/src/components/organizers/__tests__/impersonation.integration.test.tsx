import { <PERSON><PERSON>er<PERSON>outer } from "@tanstack/react-router"
import { render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { ImpersonationButton } from "./ImpersonationButton"
import { StopImpersonationButton } from "./StopImpersonationButton"

// Mock donor data
const mockDonor = {
	id: "donor-123",
	name: "<PERSON>",
	email: "<EMAIL>",
	phone: "+1234567890",
	address: "123 Main St",
	notes: "Test donor",
	createdAt: new Date("2024-01-01"),
	updatedAt: new Date("2024-01-01"),
	totalDonated: "100.00",
	donationCount: 5,
	averageDonation: "20.00",
	lastDonationDate: new Date("2024-01-15"),
	campaignsSupported: 3,
	firstDonationDate: new Date("2023-12-01"),
}

// Mock auth store
const mockUseAuthStore = vi.fn()

vi.mock("@/lib/auth-store", () => ({
	useAuthStore: () => mockUseAuthStore(),
}))

describe("Impersonation Components Integration", () => {
	beforeEach(() => {
		vi.clearAllMocks()
	})

	describe("ImpersonationButton", () => {
		it("renders correctly for admin users", () => {
			mockUseAuthStore.mockReturnValue({
				user: {
					id: "admin-123",
					name: "Admin User",
					email: "<EMAIL>",
					role: "admin",
				},
			})

			render(
				<BrowserRouter>
					<ImpersonationButton donor={mockDonor} />
				</BrowserRouter>,
			)

			expect(screen.getByText("Impersonate")).toBeInTheDocument()
		})

		it("does not render for non-admin users", () => {
			mockUseAuthStore.mockReturnValue({
				user: {
					id: "user-123",
					name: "Regular User",
					email: "<EMAIL>",
					role: "donor",
				},
			})

			const { container } = render(
				<BrowserRouter>
					<ImpersonationButton donor={mockDonor} />
				</BrowserRouter>,
			)

			expect(container.firstChild).toBeNull()
		})
	})

	describe("StopImpersonationButton", () => {
		it("renders when user is impersonating", () => {
			mockUseAuthStore.mockReturnValue({
				user: {
					id: "admin-123",
					name: "Admin User",
					email: "<EMAIL>",
					role: "admin",
				},
				isImpersonating: true,
			})

			render(
				<BrowserRouter>
					<StopImpersonationButton />
				</BrowserRouter>,
			)

			expect(screen.getByText("Stop Impersonating")).toBeInTheDocument()
		})

		it("does not render when user is not impersonating", () => {
			mockUseAuthStore.mockReturnValue({
				user: {
					id: "admin-123",
					name: "Admin User",
					email: "<EMAIL>",
					role: "admin",
				},
				isImpersonating: false,
			})

			const { container } = render(
				<BrowserRouter>
					<StopImpersonationButton />
				</BrowserRouter>,
			)

			expect(container.firstChild).toBeNull()
		})
	})
})
