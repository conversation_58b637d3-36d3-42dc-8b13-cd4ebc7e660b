# Donor Impersonation Feature

This document describes the implementation of the donor impersonation feature for DonorCARE administrators.

## Overview

The impersonation feature allows admin users to temporarily assume the identity of a donor account for troubleshooting and support purposes. This provides administrators with the ability to view the application exactly as a donor would see it, making it easier to diagnose issues and provide accurate support.

## Implementation Details

### 1. Authentication System Integration

The impersonation feature is built on top of the better-auth admin plugin, which provides:

- Secure impersonation endpoints
- Session management for impersonated users
- Proper role-based access control
- Audit logging of impersonation activities

### 2. Key Components

#### ImpersonationButton
A component that allows administrators to initiate impersonation of a specific donor. It provides:
- Confirmation dialog with important information about impersonation
- Visual feedback during the impersonation process
- Error handling for failed impersonation attempts

#### StopImpersonationButton
A component that allows administrators to end their impersonation session and return to their original admin account. It provides:
- Clear indication when a user is currently impersonating
- Confirmation dialog before stopping impersonation
- Visual feedback during the process

#### Auth Store Updates
The authentication store has been enhanced to:
- Track impersonation state
- Provide dedicated methods for starting and stopping impersonation
- Maintain proper session state throughout the impersonation process

### 3. Security Considerations

- Only users with the "admin" role can initiate impersonation
- All impersonation activities are logged for audit purposes
- Impersonation sessions automatically expire after a configurable duration
- Administrators can stop impersonation at any time
- Proper session cleanup occurs when impersonation ends

### 4. User Interface

#### User Menu
The user menu now displays:
- An indicator when a user is currently impersonating a donor
- A "Stop Impersonating" option when in an impersonation session

#### Donor List
The donor list actions now include:
- An "Impersonate" option for each donor (visible only to admins)

## Usage

### Starting Impersonation
1. Navigate to the donors list
2. Find the donor you wish to impersonate
3. Click the "Actions" button for that donor
4. Select "Impersonate" from the dropdown menu
5. Confirm the impersonation in the dialog
6. The application will reload with the donor's session

### Stopping Impersonation
1. While impersonating, click your user avatar in the top right
2. Select "Stop Impersonating" from the menu
3. Confirm you want to end the impersonation session
4. The application will reload with your original admin session

## Technical Implementation

### Auth Store Enhancements
The `useAuthStore` has been updated with:
- `isImpersonating` state flag
- `startImpersonation` method
- `stopImpersonation` method
- `setImpersonating` setter

### API Integration
The impersonation feature uses the better-auth admin plugin endpoints:
- `POST /admin/impersonate-user` - Start impersonation
- `POST /admin/stop-impersonating` - Stop impersonation

### Component Integration
The feature integrates with existing components:
- DonorRowActions - Added impersonation option
- UserMenu - Added impersonation status and stop button

## Configuration

The impersonation feature can be configured through the better-auth admin plugin options:

```typescript
admin({
  impersonationSessionDuration: 60 * 60 * 24, // 1 day
  // ... other admin options
})
```

## Error Handling

The impersonation feature includes comprehensive error handling for:
- Network connectivity issues
- Permission denied errors
- Session validation failures
- Unexpected server responses

Errors are displayed to users through toast notifications with user-friendly messages.

## Testing

The impersonation feature includes unit tests for:
- Component rendering and behavior
- Auth store state management
- API integration
- Error scenarios

## Future Enhancements

Potential future enhancements for the impersonation feature include:
- Time-limited impersonation sessions
- Activity logging during impersonation
- Enhanced audit trails
- Multi-level impersonation (nested sessions)