import type { Organizer } from "@donorcare/backend/schemas"
import { But<PERSON> } from "@donorcare/ui/components/ui/button"
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@donorcare/ui/components/ui/dropdown-menu"
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@donorcare/ui/components/ui/table"
import { Download, MoreHorizontal, Plus } from "lucide-react"
import { ImpersonationButton } from "./ImpersonationButton"
import { OrganizerErrorBoundary } from "./OrganizerErrorBoundary"

export interface OrganizerTableProps {
	organizers: Organizer[]
	isLoading: boolean
	error: Error | null
	onView: (organizerId: string) => void
	onEdit: (organizerId: string) => void
	onDelete: (organizerId: string) => void
	onExport: (selectedIds?: string[]) => void
	onRetry: () => void
	onCreateNew: () => void
}

export function OrganizerTableComponent({
	organizers,
	isLoading,
	error,
	onView,
	onEdit,
	onDelete,
	onExport,
	onRetry,
	onCreateNew,
}: OrganizerTableProps) {
	if (error) {
		return (
			<div className="rounded-md border p-8 text-center">
				<p className="text-sm text-muted-foreground mb-4">
					Failed to load organizers: {error.message}
				</p>
				<Button onClick={onRetry} variant="outline">
					Try Again
				</Button>
			</div>
		)
	}

	return (
		<div className="space-y-4">
			{/* Actions Bar */}
			<div className="flex items-center justify-end space-x-2">
				<Button
					onClick={() => onExport()}
					variant="outline"
					className="gap-2"
					disabled={isLoading || organizers.length === 0}
				>
					<Download className="h-4 w-4" />
					Export All
				</Button>
			</div>

			{/* Table */}
			<div className="rounded-md border">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Name</TableHead>
							<TableHead>Email</TableHead>
							<TableHead>Phone</TableHead>
							<TableHead>Address</TableHead>
							<TableHead className="w-[120px]">Impersonate</TableHead>
							<TableHead className="w-[70px]">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{isLoading ? (
							// Loading state
							Array.from({ length: 5 }).map((_, index) => (
								<TableRow key={index}>
									<TableCell>
										<div className="h-4 w-32 bg-muted rounded animate-pulse" />
									</TableCell>
									<TableCell>
										<div className="h-4 w-40 bg-muted rounded animate-pulse" />
									</TableCell>
									<TableCell>
										<div className="h-4 w-24 bg-muted rounded animate-pulse" />
									</TableCell>
									<TableCell>
										<div className="h-4 w-48 bg-muted rounded animate-pulse" />
									</TableCell>
									<TableCell>
										<div className="h-8 w-20 bg-muted rounded animate-pulse" />
									</TableCell>
									<TableCell>
										<div className="h-6 w-6 bg-muted rounded animate-pulse" />
									</TableCell>
								</TableRow>
							))
						) : organizers.length === 0 ? (
							// Empty state
							<TableRow>
								<TableCell colSpan={6} className="text-center py-8">
									<p className="text-muted-foreground">No organizers found.</p>
									<Button onClick={onCreateNew} variant="link" className="mt-2">
										Add your first organizer
									</Button>
								</TableCell>
							</TableRow>
						) : (
							// Data rows
							organizers.map((organizer) => (
								<TableRow key={organizer.id}>
									<TableCell className="font-medium">
										{organizer.name}
									</TableCell>
									<TableCell>{organizer.email}</TableCell>
									<TableCell>{organizer.phone}</TableCell>
									<TableCell className="max-w-xs truncate">
										{organizer.address}
									</TableCell>
									<TableCell>
										<ImpersonationButton organizer={organizer} />
									</TableCell>
									<TableCell>
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button variant="ghost" className="h-8 w-8 p-0">
													<span className="sr-only">Open menu</span>
													<MoreHorizontal className="h-4 w-4" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												<DropdownMenuLabel>Actions</DropdownMenuLabel>
												<DropdownMenuItem onClick={() => onView(organizer.id)}>
													View details
												</DropdownMenuItem>
												<DropdownMenuItem onClick={() => onEdit(organizer.id)}>
													Edit organizer
												</DropdownMenuItem>
												<DropdownMenuSeparator />
												<DropdownMenuItem
													onClick={() => onDelete(organizer.id)}
													className="text-destructive"
												>
													Delete organizer
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									</TableCell>
								</TableRow>
							))
						)}
					</TableBody>
				</Table>
			</div>
		</div>
	)
}

// Main export with error boundary
export function OrganizerTable(props: OrganizerTableProps) {
	return (
		<OrganizerErrorBoundary onReset={props.onRetry}>
			<OrganizerTableComponent {...props} />
		</OrganizerErrorBoundary>
	)
}
