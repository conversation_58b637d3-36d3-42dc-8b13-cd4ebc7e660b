import type { Organizer } from "@donorcare/backend/schemas"
import { But<PERSON> } from "@donorcare/ui/components/ui/button"
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	<PERSON>alogTitle,
	DialogTrigger,
} from "@donorcare/ui/components/ui/dialog"
import { ShieldAlert, User } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import { useAuthStore } from "@/lib/auth-store"

export interface ImpersonationButtonProps {
	organizer: Organizer
	onImpersonationStart?: () => void
	onImpersonationError?: (error: Error) => void
}

/**
 * ImpersonationButton component that allows admin users to impersonate organizers
 * This component provides a secure way for admins to view the application
 * from a organizer's perspective for troubleshooting and support purposes.
 */
export function ImpersonationButton({
	organizer,
	onImpersonationStart,
	onImpersonationError,
}: ImpersonationButtonProps) {
	const [isOpen, setIsOpen] = useState(false)
	const [isImpersonating, setIsImpersonating] = useState(false)
	const { user, startImpersonation } = useAuthStore()

	/**
	 * Check if the current user has permission to impersonate
	 */
	const canImpersonate = () => {
		// Only admin users can impersonate
		return user?.role === "admin"
	}

	/**
	 * Handle the impersonation action
	 */
	const handleImpersonate = async () => {
		if (!canImpersonate()) {
			toast.error("You don't have permission to impersonate users")
			return
		}

		try {
			setIsImpersonating(true)
			onImpersonationStart?.()

			// Call the impersonation method from auth store
			await startImpersonation(organizer.id)
		} catch (error) {
			console.error("Impersonation error:", error)
			const errorMessage =
				error instanceof Error ? error.message : "An unknown error occurred"

			toast.error(errorMessage)

			onImpersonationError?.(error as Error)
		} finally {
			setIsImpersonating(false)
			setIsOpen(false)
		}
	}

	// Don't render if user doesn't have permission to impersonate
	if (!canImpersonate()) {
		return null
	}

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				<Button variant="outline" size="sm" className="flex items-center gap-2">
					<User className="h-4 w-4" />
					<span>Impersonate</span>
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<ShieldAlert className="h-5 w-5 text-warning" />
						Impersonate Organizer
					</DialogTitle>
					<DialogDescription>
						You are about to impersonate <strong>{organizer.name}</strong> (
						{organizer.email}). This will allow you to view the application
						exactly as this organizer sees it.
					</DialogDescription>
				</DialogHeader>

				<div className="rounded-lg border bg-muted p-4">
					<h4 className="text-sm font-medium mb-2">Important Information</h4>
					<ul className="text-sm space-y-1 list-disc pl-4">
						<li>You will temporarily become this organizer in the system</li>
						<li>All actions will be logged and attributed to this organizer</li>
						<li>Your admin session will be paused during impersonation</li>
						<li>You can stop impersonating at any time using the user menu</li>
					</ul>
				</div>

				<DialogFooter className="gap-2">
					<Button
						variant="outline"
						onClick={() => setIsOpen(false)}
						disabled={isImpersonating}
					>
						Cancel
					</Button>
					<Button
						onClick={handleImpersonate}
						disabled={isImpersonating}
						variant="destructive"
					>
						{isImpersonating ? (
							<>
								<span className="animate-spin mr-2">●</span>
								Starting Impersonation...
							</>
						) : (
							"Start Impersonation"
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	)
}
