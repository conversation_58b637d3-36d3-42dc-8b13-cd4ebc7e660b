import { Alert, AlertDescription } from "@donorcare/ui/components/ui/alert"
import { Button } from "@donorcare/ui/components/ui/button"
import { ShieldAlert, X } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import { useAuthStore } from "@/lib/auth-store"

/**
 * ImpersonationBanner component that shows a prominent banner when an admin is impersonating a user
 * This provides clear visual feedback and easy access to stop impersonation
 */
export function ImpersonationBanner() {
	const [isLoading, setIsLoading] = useState(false)
	const { stopImpersonation, isImpersonating, user } = useAuthStore()

	// Don't render if not impersonating
	if (!isImpersonating || !user) {
		return null
	}

	/**
	 * Handle stopping impersonation
	 */
	const handleStopImpersonation = async () => {
		try {
			setIsLoading(true)
			await stopImpersonation()
			toast.success("Impersonation ended - returned to admin session")
		} catch (error) {
			console.error("Failed to stop impersonation:", error)
			const errorMessage =
				error instanceof Error ? error.message : "Failed to stop impersonation"
			toast.error(errorMessage)
		} finally {
			setIsLoading(false)
		}
	}

	return (
		<Alert className="rounded-none border-l-0 border-r-0 border-t-0 bg-warning/10 border-warning bg-green-50 text-warning-foreground">
			<AlertDescription className="flex items-center justify-between w-full">
			  <div className="flex items-center space-x-2">
					<ShieldAlert className="h-4 w-4" />
  				<span className="font-medium flex-1">
  					You are currently impersonating {user.name} ({user.email})
  				</span>
				</div>
				<Button
					onClick={handleStopImpersonation}
					disabled={isLoading}
					size="sm"
				>
					<X className="mr-1 h-3 w-3" />
					{isLoading ? "Stopping..." : "Stop Impersonation"}
				</Button>
			</AlertDescription>
		</Alert>
	)
}
