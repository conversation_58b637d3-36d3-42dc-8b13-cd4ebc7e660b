import {
	Button,
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
} from "@donorcare/ui"
import { useNavigate } from "@tanstack/react-router"
import * as React from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { useAuthStore } from "@/lib/auth-store"
import type { LoginFormData } from "@/types/auth"

// Form validation schema
const loginFormSchema = z.object({
	email: z
		.email("Please enter a valid email address")
		.min(1, "Email is required"),
	password: z
		.string()
		.min(1, "Password is required")
		.min(6, "Password must be at least 6 characters"),
})

interface LoginFormProps {
	redirectTo?: string
	onSuccess?: () => void
}

export function LoginForm({ redirectTo, onSuccess }: LoginFormProps) {
	const navigate = useNavigate()
	const { signIn, isLoading, error, clearError, isAuthenticated } =
		useAuthStore()
	const [isLoggingIn, setIsLoggingIn] = React.useState(false)

	const form = useForm<LoginFormData>({
		defaultValues: {
			email: "",
			password: "",
		},
		mode: "onBlur",
	})

	// Clear errors when form values change
	React.useEffect(() => {
		const subscription = form.watch(() => {
			if (error) {
				clearError()
			}
		})
		return () => {
			if (subscription && typeof subscription.unsubscribe === "function") {
				subscription.unsubscribe()
			}
		}
	}, [form, error, clearError])

	// Handle navigation after authentication state changes
	React.useEffect(() => {
		console.log("🔍 LoginForm auth effect:", {
			isLoggingIn,
			isAuthenticated,
			redirectTo,
			timestamp: new Date().toISOString(),
		})

		// TODO: stuck at LoginForm even though navigate executed
		// Only navigate if we were in the process of logging in and now we're authenticated
		if (isLoggingIn && isAuthenticated) {
			console.log("✅ Login successful, preparing to navigate...")
			setIsLoggingIn(false)
			// Use a longer delay to ensure the router context has been updated
			// and all auth guards have had a chance to re-evaluate
			setTimeout(() => {
				const destination = redirectTo || "/"
				console.log("🎯 Executing navigation to:", destination)

				// Navigate to root path, which will handle the proper redirect
				// based on the updated auth state via the index route logic
				navigate({ to: destination, replace: true })
			}, 500)
		}
	}, [isLoggingIn, isAuthenticated, redirectTo, navigate])

	const onSubmit = async (data: LoginFormData) => {
		// Manual validation using Zod
		const validationResult = loginFormSchema.safeParse(data)

		if (!validationResult.success) {
			// Set form errors manually
			const errors = validationResult.error.issues
			for (const error of errors) {
				const fieldName = error.path[0] as keyof LoginFormData
				form.setError(fieldName, {
					type: "manual",
					message: error.message,
				})
			}
			return
		}

		try {
			console.log("🔄 Starting login process...")
			setIsLoggingIn(true)
			await signIn(data.email, data.password)
			console.log("✅ signIn completed successfully")

			// Call success callback if provided
			if (onSuccess) {
				onSuccess()
			}

			// Navigation will be handled by the useEffect above
			console.log(
				"⏳ Waiting for auth state to update and trigger navigation...",
			)
		} catch (error) {
			console.error("❌ Login failed:", error)
			setIsLoggingIn(false)
			// Error is already handled by the auth store
		}
	}

	return (
		<div className="w-full max-w-md space-y-6">
			<div className="text-center space-y-2">
				<h1 className="text-2xl font-bold tracking-tight">
					Sign in to DonorCARE
				</h1>
			</div>

			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
					{/* Global error display */}
					{error && (
						<div
							className="rounded-md bg-destructive/10 border border-destructive/20 p-3"
							role="alert"
							aria-live="polite"
						>
							<p className="text-sm text-destructive font-medium">
								{error.message}
							</p>
						</div>
					)}

					{/* Email field */}
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input
										{...field}
										type="email"
										placeholder="Enter your email"
										autoComplete="email"
										aria-describedby={
											form.formState.errors.email
												? `${field.name}-error`
												: undefined
										}
									/>
								</FormControl>
								<FormMessage id={`${field.name}-error`} />
							</FormItem>
						)}
					/>

					{/* Password field */}
					<FormField
						control={form.control}
						name="password"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Password</FormLabel>
								<FormControl>
									<Input
										{...field}
										type="password"
										placeholder="Enter your password"
										autoComplete="current-password"
										aria-describedby={
											form.formState.errors.password
												? `${field.name}-error`
												: undefined
										}
									/>
								</FormControl>
								<FormMessage id={`${field.name}-error`} />
							</FormItem>
						)}
					/>

					{/* Submit button */}
					<Button
						type="submit"
						className="w-full"
						disabled={isLoading}
						aria-describedby={isLoading ? "loading-message" : undefined}
					>
						{isLoading ? (
							<>
								<span
									className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-current"
									aria-hidden="true"
								/>
								<span id="loading-message">Signing in...</span>
							</>
						) : (
							"Sign In"
						)}
					</Button>
				</form>
			</Form>

			{/* Redirect information */}
			{/* {redirectTo && (
				<div className="text-center">
					<p className="text-sm text-muted-foreground">
						You will be redirected to:{' '}
						<span className="font-medium text-foreground">{redirectTo}</span>
					</p>
				</div>
			)} */}
		</div>
	)
}
