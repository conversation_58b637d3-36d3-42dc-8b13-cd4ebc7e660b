import type { Campaign } from "@donorcare/backend/schemas"
import {
	createCampaignDto,
	updateCampaignDto,
} from "@donorcare/backend/schemas"
import {
	Button,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	Input,
	Switch,
	Textarea,
} from "@donorcare/ui"
import type { Static } from "elysia"
import { Loader2 } from "lucide-react"
import * as React from "react"
import { useForm } from "react-hook-form"
import { createTypeboxResolver } from "@/lib/typebox-resolver"

// Form data types
export type CreateCampaignFormData = Static<typeof createCampaignDto>
export type EditCampaignFormData = Static<typeof updateCampaignDto>

// Component props
export interface CampaignFormProps {
	mode: "create" | "edit"
	initialData?: Partial<Campaign>
	onSubmit: (data: CreateCampaignFormData | EditCampaignFormData) => void
	onCancel: () => void
	isLoading?: boolean
}

/**
 * Generates a URL-friendly slug from a given name
 */
function generateSlug(name: string): string {
	return name
		.toLowerCase()
		.trim()
		.replace(/[^\w\s-]/g, "") // Remove special characters
		.replace(/[\s_-]+/g, "-") // Replace spaces and underscores with hyphens
		.replace(/^-+|-+$/g, "") // Remove leading/trailing hyphens
}

/**
 * Reusable campaign form component for both create and edit modes
 */
export function CampaignForm({
	mode,
	initialData,
	onSubmit,
	onCancel,
	isLoading = false,
}: CampaignFormProps) {
	const isCreateMode = mode === "create"
	const isEditMode = mode === "edit"

	// Form setup with appropriate schema based on mode
	const form = useForm<CreateCampaignFormData | EditCampaignFormData>({
		resolver: createTypeboxResolver(
			isCreateMode ? createCampaignDto : updateCampaignDto,
		),
		defaultValues: isCreateMode
			? {
					name: initialData?.name || "",
					description: initialData?.description || "",
					slug: initialData?.slug || "",
				}
			: {
					name: initialData?.name || "",
					description: initialData?.description || "",
					isActive: initialData?.isActive ?? true,
				},
	})

	const { watch, setValue } = form

	// Watch name field for real-time slug generation in create mode
	const nameValue = watch("name")

	// Generate slug from name in create mode
	React.useEffect(() => {
		if (isCreateMode && nameValue) {
			const generatedSlug = generateSlug(nameValue)
			setValue("slug", generatedSlug, { shouldValidate: true })
		}
	}, [nameValue, setValue, isCreateMode])

	// Handle form submission
	const handleSubmit = (
		data: CreateCampaignFormData | EditCampaignFormData,
	) => {
		if (isCreateMode) {
			const createData = data as CreateCampaignFormData
			onSubmit({
				name: createData.name,
				description: createData.description || undefined,
				slug: createData.slug,
			})
		} else {
			const editData = data as EditCampaignFormData
			onSubmit({
				name: editData.name,
				description: editData.description || undefined,
				isActive: editData.isActive,
			})
		}
	}

	return (
		<div className="space-y-6">
			<div>
				<h2 className="text-2xl font-bold tracking-tight">
					{isCreateMode ? "Create New Campaign" : "Edit Campaign"}
				</h2>
				<p className="text-muted-foreground">
					{isCreateMode
						? "Set up a new fundraising campaign with all the necessary details."
						: "Update your campaign information and settings."}
				</p>
			</div>

			<Form {...form}>
				<form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
					{/* Campaign Name Field */}
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Campaign Name *</FormLabel>
								<FormControl>
									<Input
										placeholder="Enter campaign name"
										disabled={isLoading}
										{...field}
									/>
								</FormControl>
								<FormDescription>
									A clear, descriptive name for your fundraising campaign.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Slug Field (Create Mode Only) */}
					{isCreateMode && (
						<FormField
							control={form.control}
							name="slug"
							render={({ field }) => (
								<FormItem>
									<FormLabel>URL Slug *</FormLabel>
									<FormControl>
										<Input
											placeholder="campaign-url-slug"
											disabled={isLoading}
											{...field}
										/>
									</FormControl>
									<FormDescription>
										This will be used in your campaign URL. It's automatically
										generated from the name but can be customized.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					)}

					{/* Description Field */}
					<FormField
						control={form.control}
						name="description"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Description</FormLabel>
								<FormControl>
									<Textarea
										placeholder="Describe your campaign goals and purpose..."
										className="min-h-[100px]"
										disabled={isLoading}
										{...field}
									/>
								</FormControl>
								<FormDescription>
									Optional description to help donors understand your campaign.
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Active Status Field (Edit Mode Only) */}
					{isEditMode && (
						<FormField
							control={form.control}
							name="isActive"
							render={({ field }) => (
								<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
									<div className="space-y-0.5">
										<FormLabel className="text-base">Campaign Status</FormLabel>
										<FormDescription>
											When active, your campaign will be visible to donors and
											accept donations.
										</FormDescription>
									</div>
									<FormControl>
										<Switch
											checked={field.value}
											onCheckedChange={field.onChange}
											disabled={isLoading}
										/>
									</FormControl>
								</FormItem>
							)}
						/>
					)}

					{/* Form Actions */}
					<div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
						<Button
							type="button"
							variant="outline"
							onClick={onCancel}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button type="submit" disabled={isLoading}>
							{isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
							{isCreateMode ? "Create Campaign" : "Save Changes"}
						</Button>
					</div>
				</form>
			</Form>
		</div>
	)
}
