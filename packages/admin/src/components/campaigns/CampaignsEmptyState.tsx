import { <PERSON><PERSON>, <PERSON>, CardContent } from "@donorcare/ui"
import { Megaphone, Plus } from "lucide-react"

export interface CampaignsEmptyStateProps {
	onCreateCampaign: () => void
}

/**
 * Empty state component displayed when no campaigns exist
 */
export function CampaignsEmptyState({
	onCreateCampaign,
}: CampaignsEmptyStateProps) {
	return (
		<Card className="border-dashed">
			<CardContent className="flex flex-col items-center justify-center py-12 px-6 text-center">
				<div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
					<Megaphone className="h-8 w-8 text-muted-foreground" />
				</div>
				<div className="mt-6 space-y-2">
					<h3 className="text-lg font-semibold">No campaigns yet</h3>
					<p className="text-sm text-muted-foreground max-w-sm">
						Get started by creating your first fundraising campaign. You can set
						up donation goals, customize your campaign page, and start accepting
						donations.
					</p>
				</div>
				<Button onClick={onCreateCampaign} className="mt-6">
					<Plus className="mr-2 h-4 w-4" />
					Create Your First Campaign
				</Button>
			</CardContent>
		</Card>
	)
}
