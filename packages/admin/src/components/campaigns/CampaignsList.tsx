import {
	Alert,
	AlertDescription,
	Button,
	Input,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Skeleton,
} from "@donorcare/ui"
import { AlertCircle, Plus, RefreshCw, Search } from "lucide-react"
import * as React from "react"
import {
	useCampaigns,
	useDeleteCampaign,
	useToggleCampaignStatus,
} from "@/hooks/useCampaigns"
import { CampaignCard } from "./CampaignCard"
import { CampaignsEmptyState } from "./CampaignsEmptyState"

export interface CampaignsListProps {
	onEdit: (campaignId: string) => void
	onCreateCampaign: () => void
}

export interface CampaignFilters {
	search: string
	status: "all" | "active" | "inactive"
	sortBy: "name" | "createdAt"
	sortOrder: "asc" | "desc"
}

/**
 * Main campaigns list component with filtering, sorting, and responsive layout
 */
export function CampaignsList({
	onEdit,
	onCreateCampaign,
}: CampaignsListProps) {
	// Use TanStack Query hooks
	const { data: campaigns = [], isLoading, error, refetch } = useCampaigns()
	const deleteCampaignMutation = useDeleteCampaign()
	const toggleStatusMutation = useToggleCampaignStatus()

	// Handle delete campaign
	const handleDelete = async (campaignId: string) => {
		if (window.confirm("Are you sure you want to delete this campaign?")) {
			deleteCampaignMutation.mutate(campaignId)
		}
	}

	// Handle status toggle
	const handleStatusToggle = (campaignId: string, isActive: boolean) => {
		toggleStatusMutation.mutate({ campaignId, isActive })
	}

	// Handle retry
	const handleRetry = () => {
		refetch()
	}
	const [filters, setFilters] = React.useState<CampaignFilters>({
		search: "",
		status: "all",
		sortBy: "createdAt",
		sortOrder: "desc",
	})

	// Filter and sort campaigns based on current filters
	const filteredAndSortedCampaigns = React.useMemo(() => {
		let filtered = campaigns

		// Apply search filter
		if (filters.search) {
			const searchLower = filters.search.toLowerCase()
			filtered = filtered.filter(
				(campaign) =>
					campaign.name.toLowerCase().includes(searchLower) ||
					campaign.slug.toLowerCase().includes(searchLower) ||
					campaign.description?.toLowerCase().includes(searchLower),
			)
		}

		// Apply status filter
		if (filters.status !== "all") {
			filtered = filtered.filter((campaign) =>
				filters.status === "active" ? campaign.isActive : !campaign.isActive,
			)
		}

		// Apply sorting
		filtered.sort((a, b) => {
			let aValue: string | Date
			let bValue: string | Date

			if (filters.sortBy === "name") {
				aValue = a.name.toLowerCase()
				bValue = b.name.toLowerCase()
			} else {
				aValue = new Date(a.createdAt)
				bValue = new Date(b.createdAt)
			}

			if (aValue < bValue) {
				return filters.sortOrder === "asc" ? -1 : 1
			}
			if (aValue > bValue) {
				return filters.sortOrder === "asc" ? 1 : -1
			}
			return 0
		})

		return filtered
	}, [campaigns, filters])

	const handleSearchChange = (value: string) => {
		setFilters((prev) => ({ ...prev, search: value }))
	}

	const handleStatusFilterChange = (value: string) => {
		setFilters((prev) => ({
			...prev,
			status: value as CampaignFilters["status"],
		}))
	}

	const handleSortChange = (value: string) => {
		const [sortBy, sortOrder] = value.split("-") as [
			CampaignFilters["sortBy"],
			CampaignFilters["sortOrder"],
		]
		setFilters((prev) => ({ ...prev, sortBy, sortOrder }))
	}

	// Show error state
	if (error && !isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold tracking-tight">Campaigns</h1>
						<p className="text-muted-foreground">
							Manage your fundraising campaigns
						</p>
					</div>
					<Button onClick={onCreateCampaign}>
						<Plus className="mr-2 h-4 w-4" />
						Create Campaign
					</Button>
				</div>

				<Alert variant="destructive">
					<AlertCircle className="h-4 w-4" />
					<AlertDescription className="flex items-center justify-between">
						<span>
							{error instanceof Error
								? error.message
								: "Failed to load campaigns"}
						</span>
						<Button
							variant="outline"
							size="sm"
							onClick={handleRetry}
							className="ml-4"
						>
							<RefreshCw className="mr-2 h-4 w-4" />
							Retry
						</Button>
					</AlertDescription>
				</Alert>
			</div>
		)
	}

	// Show loading state
	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold tracking-tight">Campaigns</h1>
						<p className="text-muted-foreground">
							Manage your fundraising campaigns
						</p>
					</div>
					<Button disabled>
						<Plus className="mr-2 h-4 w-4" />
						Create Campaign
					</Button>
				</div>

				<div className="flex flex-col sm:flex-row gap-4">
					<Skeleton className="h-10 flex-1" />
					<Skeleton className="h-10 w-32" />
					<Skeleton className="h-10 w-48" />
				</div>

				<div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
					{Array.from({ length: 6 }).map((_, i) => (
						// biome-ignore lint/suspicious/noArrayIndexKey: skeleton keys
						<div key={i} className="space-y-3">
							<Skeleton className="h-48 w-full rounded-lg" />
						</div>
					))}
				</div>
			</div>
		)
	}

	// Show empty state
	if (campaigns.length === 0) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold tracking-tight">Campaigns</h1>
						<p className="text-muted-foreground">
							Manage your fundraising campaigns
						</p>
					</div>
					<Button onClick={onCreateCampaign}>
						<Plus className="mr-2 h-4 w-4" />
						Create Campaign
					</Button>
				</div>

				<CampaignsEmptyState onCreateCampaign={onCreateCampaign} />
			</div>
		)
	}

	// Show campaigns list
	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Campaigns</h1>
					<p className="text-muted-foreground">
						Manage your fundraising campaigns
					</p>
				</div>
				<Button onClick={onCreateCampaign}>
					<Plus className="mr-2 h-4 w-4" />
					Create Campaign
				</Button>
			</div>

			{/* Filters and Search */}
			<div className="flex flex-col sm:flex-row gap-4">
				<div className="relative flex-1">
					<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
					<Input
						placeholder="Search campaigns..."
						value={filters.search}
						onChange={(e) => handleSearchChange(e.target.value)}
						className="pl-10"
					/>
				</div>
				<Select value={filters.status} onValueChange={handleStatusFilterChange}>
					<SelectTrigger className="w-32">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">All Status</SelectItem>
						<SelectItem value="active">Active</SelectItem>
						<SelectItem value="inactive">Inactive</SelectItem>
					</SelectContent>
				</Select>
				<Select
					value={`${filters.sortBy}-${filters.sortOrder}`}
					onValueChange={handleSortChange}
				>
					<SelectTrigger className="w-48">
						<SelectValue />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="name-asc">Name (A-Z)</SelectItem>
						<SelectItem value="name-desc">Name (Z-A)</SelectItem>
						<SelectItem value="createdAt-desc">Newest First</SelectItem>
						<SelectItem value="createdAt-asc">Oldest First</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Results Summary */}
			<div className="text-sm text-muted-foreground">
				{filteredAndSortedCampaigns.length === campaigns.length ? (
					<span>Showing {campaigns.length} campaigns</span>
				) : (
					<span>
						Showing {filteredAndSortedCampaigns.length} of {campaigns.length}{" "}
						campaigns
					</span>
				)}
			</div>

			{/* Campaigns Grid */}
			{filteredAndSortedCampaigns.length === 0 ? (
				<Alert>
					<AlertCircle className="h-4 w-4" />
					<AlertDescription>
						No campaigns match your current filters. Try adjusting your search
						or filter criteria.
					</AlertDescription>
				</Alert>
			) : (
				<div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
					{filteredAndSortedCampaigns.map((campaign) => (
						<CampaignCard
							key={campaign.id}
							campaign={campaign}
							onEdit={onEdit}
							onDelete={handleDelete}
							onStatusToggle={handleStatusToggle}
							isLoading={
								deleteCampaignMutation.isPending ||
								toggleStatusMutation.isPending
							}
						/>
					))}
				</div>
			)}
		</div>
	)
}
