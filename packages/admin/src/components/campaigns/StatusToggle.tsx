import { Label, Switch } from "@donorcare/ui"
import { Loader2 } from "lucide-react"
import { useToggleCampaignStatus } from "@/hooks/useCampaigns"

export interface StatusToggleProps {
	campaignId: string
	isActive: boolean
	disabled?: boolean
	size?: "sm" | "default"
	showLabel?: boolean
	className?: string
}

/**
 * Campaign status toggle component with optimistic updates and error rollback
 * Provides immediate visual feedback and proper accessibility attributes
 */
export function StatusToggle({
	campaignId,
	isActive,
	disabled = false,
	size = "default",
	showLabel = true,
	className,
}: StatusToggleProps) {
	const toggleStatusMutation = useToggleCampaignStatus()

	const handleToggle = (checked: boolean) => {
		// Prevent multiple simultaneous requests
		if (toggleStatusMutation.isPending || disabled) {
			return
		}

		toggleStatusMutation.mutate({ campaignId, isActive: checked })
	}

	// Use optimistic state during loading, otherwise use actual state
	const displayState = toggleStatusMutation.isPending
		? (toggleStatusMutation.variables?.isActive ?? isActive)
		: isActive

	const toggleId = `status-toggle-${campaignId}`
	const labelId = `status-label-${campaignId}`

	return (
		<div className={`flex items-center gap-2 ${className || ""}`}>
			<div className="relative">
				<Switch
					id={toggleId}
					checked={displayState}
					onCheckedChange={handleToggle}
					disabled={disabled || toggleStatusMutation.isPending}
					className={size === "sm" ? "h-4 w-6" : undefined}
					aria-labelledby={showLabel ? labelId : undefined}
					aria-describedby={`${toggleId}-description`}
				/>
				{toggleStatusMutation.isPending && (
					<div className="absolute inset-0 flex items-center justify-center">
						<Loader2
							className="h-3 w-3 animate-spin text-muted-foreground"
							data-testid="loading-spinner"
						/>
					</div>
				)}
			</div>
			{showLabel && (
				<Label
					id={labelId}
					htmlFor={toggleId}
					className={`text-sm font-medium cursor-pointer ${
						disabled || toggleStatusMutation.isPending ? "opacity-50" : ""
					}`}
				>
					{displayState ? "Active" : "Inactive"}
				</Label>
			)}
			{/* Screen reader description */}
			<span id={`${toggleId}-description`} className="sr-only">
				{displayState
					? "Campaign is currently active and accepting donations"
					: "Campaign is currently inactive and not accepting donations"}
				{toggleStatusMutation.isPending && ". Status is being updated."}
			</span>
		</div>
	)
}
