import type { Campaign } from "@donorcare/backend/schemas"
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@donorcare/ui"
import { Edit, MoreHorizontal, Trash2 } from "lucide-react"
import { StatusToggle } from "./StatusToggle"

export interface CampaignCardProps {
	campaign: Campaign
	onEdit: (campaignId: string) => void
	onDelete: (campaignId: string) => void
	onStatusToggle: (campaignId: string, isActive: boolean) => void
	isLoading?: boolean
}

/**
 * Individual campaign card component for displaying campaign information
 * with action buttons for edit, delete, and status toggle
 */
export function CampaignCard({
	campaign,
	onEdit,
	onDelete,
	onStatusToggle,
	isLoading = false,
}: CampaignCardProps) {
	const handleEdit = () => {
		onEdit(campaign.id)
	}

	const handleDelete = () => {
		onDelete(campaign.id)
	}

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		})
	}

	return (
		<Card className="h-full">
			<CardHeader>
				<div className="flex items-center justify-between">
					<div className="flex flex-col flex-1 min-w-0">
						<CardTitle className="text-lg truncate">{campaign.name}</CardTitle>
						<span className="text-xs text-muted-foreground">
							/{campaign.slug}
						</span>
					</div>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="ghost"
								size="sm"
								className="h-8 w-8 p-0"
								disabled={isLoading}
							>
								<MoreHorizontal className="h-4 w-4" />
								<span className="sr-only">Open menu</span>
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-48">
							<DropdownMenuItem onClick={handleEdit}>
								<Edit className="mr-2 h-4 w-4" />
								Edit Campaign
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								onClick={handleDelete}
								className="text-destructive focus:text-destructive"
							>
								<Trash2 className="mr-2 h-4 w-4" />
								Delete Campaign
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</CardHeader>
			<CardContent className="flex-grow">
				<div className="space-y-3">
					{campaign.description && (
						<CardDescription className="text-sm text-muted-foreground overflow-hidden">
							<div className="line-clamp-3">{campaign.description}</div>
						</CardDescription>
					)}
				</div>
			</CardContent>
			<CardFooter className="flex items-center justify-between gap-2">
				<div className="text-xs text-muted-foreground">
					Created {formatDate(campaign.createdAt)}
				</div>
				<StatusToggle
					campaignId={campaign.id}
					isActive={campaign.isActive}
					onToggle={(campaignId, isActive) => {
						onStatusToggle(campaignId, isActive)
					}}
					size="sm"
					disabled={isLoading}
				/>
			</CardFooter>
		</Card>
	)
}
