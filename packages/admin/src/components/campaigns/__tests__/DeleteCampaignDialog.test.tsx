import type { Campaign } from "@donorcare/backend/schemas"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { DeleteCampaignDialog } from "../DeleteCampaignDialog"

// Mock campaign data
const mockCampaign: Campaign = {
	id: "campaign-1",
	organizerId: "organizer-1",
	name: "Test Campaign",
	description: "A test campaign for deletion",
	slug: "test-campaign",
	isActive: true,
	createdAt: "2024-01-01T00:00:00Z",
	updatedAt: "2024-01-01T00:00:00Z",
}

describe("DeleteCampaignDialog", () => {
	const mockOnClose = vi.fn()
	const mockOnConfirm = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("renders nothing when campaign is null", () => {
		render(
			<DeleteCampaignDialog
				campaign={null}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.queryByText("Delete Campaign")).not.toBeInTheDocument()
	})

	it("renders dialog when campaign is provided and isOpen is true", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByRole("alertdialog")).toBeInTheDocument()
		expect(
			screen.getByText("This action cannot be undone."),
		).toBeInTheDocument()
		expect(
			screen.getByText(
				`You are about to permanently delete "${mockCampaign.name}"`,
			),
		).toBeInTheDocument()
	})

	it("shows campaign name in confirmation input placeholder", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		expect(screen.getByText(mockCampaign.name)).toBeInTheDocument()
		expect(
			screen.getByPlaceholderText("Enter campaign name"),
		).toBeInTheDocument()
	})

	it("disables delete button when confirmation text doesn't match", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const deleteButton = screen.getByRole("button", { name: /Delete Campaign/ })
		expect(deleteButton).toBeDisabled()
	})

	it("enables delete button when confirmation text matches campaign name", async () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter campaign name")
		const deleteButton = screen.getByRole("button", { name: /Delete Campaign/ })

		fireEvent.change(input, { target: { value: mockCampaign.name } })

		await waitFor(() => {
			expect(deleteButton).not.toBeDisabled()
		})
	})

	it("shows validation error when confirmation text is incorrect", async () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter campaign name")
		fireEvent.change(input, { target: { value: "Wrong Name" } })

		await waitFor(() => {
			expect(
				screen.getByText("Campaign name does not match"),
			).toBeInTheDocument()
		})
	})

	it("calls onConfirm when delete button is clicked with correct confirmation", async () => {
		mockOnConfirm.mockResolvedValue(undefined)

		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter campaign name")
		const deleteButton = screen.getByRole("button", { name: /Delete Campaign/ })

		fireEvent.change(input, { target: { value: mockCampaign.name } })

		await waitFor(() => {
			expect(deleteButton).not.toBeDisabled()
		})

		fireEvent.click(deleteButton)

		await waitFor(() => {
			expect(mockOnConfirm).toHaveBeenCalledWith(mockCampaign.id)
		})
	})

	it("shows loading state when isLoading is true", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={true}
			/>,
		)

		expect(screen.getByText("Deleting...")).toBeInTheDocument()
		expect(screen.getByRole("button", { name: /Deleting/ })).toBeDisabled()
	})

	it("shows donation count warning when donationCount is provided", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
				donationCount={5}
			/>,
		)

		expect(
			screen.getByText("⚠️ This campaign has 5 donations"),
		).toBeInTheDocument()
		expect(
			screen.getByText(
				/Deleting this campaign will also remove all donation records/,
			),
		).toBeInTheDocument()
	})

	it("shows singular donation text when donationCount is 1", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
				donationCount={1}
			/>,
		)

		expect(
			screen.getByText("⚠️ This campaign has 1 donation"),
		).toBeInTheDocument()
	})

	it("calls onClose when cancel button is clicked", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const cancelButton = screen.getByRole("button", { name: "Cancel" })
		fireEvent.click(cancelButton)

		expect(mockOnClose).toHaveBeenCalled()
	})

	it("resets confirmation text when dialog is closed and reopened", async () => {
		const { rerender } = render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter campaign name")
		fireEvent.change(input, { target: { value: "Some text" } })
		expect(input).toHaveValue("Some text")

		// Close dialog by changing isOpen to false
		rerender(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={false}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		// Reopen dialog
		rerender(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		// The input should be reset when dialog reopens
		const newInput = screen.getByPlaceholderText("Enter campaign name")
		expect(newInput).toHaveValue("")
	})

	it("has proper accessibility attributes", () => {
		render(
			<DeleteCampaignDialog
				campaign={mockCampaign}
				isOpen={true}
				onClose={mockOnClose}
				onConfirm={mockOnConfirm}
				isLoading={false}
			/>,
		)

		const input = screen.getByPlaceholderText("Enter campaign name")
		expect(input).toHaveAttribute("id")

		const label = screen.getByText(
			"Type the campaign name to confirm deletion:",
		)
		expect(label).toHaveAttribute("for", input.id)
	})
})
