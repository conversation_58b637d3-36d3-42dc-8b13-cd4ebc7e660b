import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { StatusToggle } from "../StatusToggle"

// Mock the useToggleCampaignStatus hook
const mockUseToggleCampaignStatus = vi.fn()

vi.mock("@/hooks/useCampaigns", () => ({
	useToggleCampaignStatus: () => mockUseToggleCampaignStatus(),
}))

// Helper function to render with QueryClient
function renderWithQueryClient(ui: React.ReactElement) {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	})

	return render(
		<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
	)
}

describe("StatusToggle", () => {
	const mockMutate = vi.fn()
	const defaultProps = {
		campaignId: "test-campaign-id",
		isActive: true,
	}

	beforeEach(() => {
		vi.clearAllMocks()

		// Default mock implementation
		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockMutate,
			isPending: false,
			variables: undefined,
		})
	})

	describe("Rendering", () => {
		it("renders with active state", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const toggle = screen.getByRole("switch")
			const label = screen.getByText("Active")

			expect(toggle).toBeInTheDocument()
			expect(toggle).toBeChecked()
			expect(label).toBeInTheDocument()
		})

		it("renders with inactive state", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} isActive={false} />)

			const toggle = screen.getByRole("switch")
			const label = screen.getByText("Inactive")

			expect(toggle).toBeInTheDocument()
			expect(toggle).not.toBeChecked()
			expect(label).toBeInTheDocument()
		})

		it("renders without label when showLabel is false", () => {
			renderWithQueryClient(
				<StatusToggle {...defaultProps} showLabel={false} />,
			)

			expect(screen.queryByText("Active")).not.toBeInTheDocument()
			expect(screen.queryByText("Inactive")).not.toBeInTheDocument()
		})

		it("applies custom className", () => {
			const { container } = renderWithQueryClient(
				<StatusToggle {...defaultProps} className="custom-class" />,
			)

			expect(container.firstChild).toHaveClass("custom-class")
		})

		it("renders with small size", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} size="sm" />)

			const toggle = screen.getByRole("switch")
			expect(toggle).toHaveClass("h-4", "w-6")
		})
	})

	describe("Accessibility", () => {
		it("has proper ARIA attributes", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const toggle = screen.getByRole("switch")
			const label = screen.getByText("Active")

			expect(toggle).toHaveAttribute("id", "status-toggle-test-campaign-id")
			expect(toggle).toHaveAttribute(
				"aria-labelledby",
				"status-label-test-campaign-id",
			)
			expect(toggle).toHaveAttribute(
				"aria-describedby",
				"status-toggle-test-campaign-id-description",
			)
			expect(label).toHaveAttribute("id", "status-label-test-campaign-id")
			expect(label).toHaveAttribute("for", "status-toggle-test-campaign-id")
		})

		it("has screen reader description", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const description = screen.getByText(
				"Campaign is currently active and accepting donations",
			)
			expect(description).toHaveClass("sr-only")
		})

		it("updates screen reader description for inactive state", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} isActive={false} />)

			const description = screen.getByText(
				"Campaign is currently inactive and not accepting donations",
			)
			expect(description).toHaveClass("sr-only")
		})

		it("does not have aria-labelledby when showLabel is false", () => {
			renderWithQueryClient(
				<StatusToggle {...defaultProps} showLabel={false} />,
			)

			const toggle = screen.getByRole("switch")
			expect(toggle).not.toHaveAttribute("aria-labelledby")
		})
	})

	describe("User Interactions", () => {
		it("calls mutate when clicked", async () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const toggle = screen.getByRole("switch")
			fireEvent.click(toggle)

			expect(mockMutate).toHaveBeenCalledWith({
				campaignId: "test-campaign-id",
				isActive: false,
			})
		})

		it("calls mutate with correct state when toggling from inactive", async () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} isActive={false} />)

			const toggle = screen.getByRole("switch")
			fireEvent.click(toggle)

			expect(mockMutate).toHaveBeenCalledWith({
				campaignId: "test-campaign-id",
				isActive: true,
			})
		})

		it("does not call mutate when disabled", () => {
			renderWithQueryClient(<StatusToggle {...defaultProps} disabled />)

			const toggle = screen.getByRole("switch")
			fireEvent.click(toggle)

			expect(mockMutate).not.toHaveBeenCalled()
		})
	})

	describe("Optimistic Updates", () => {
		it("shows optimistic state immediately", async () => {
			mockUseToggleCampaignStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: { campaignId: "test-campaign-id", isActive: false },
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} isActive={true} />)

			const toggle = screen.getByRole("switch")

			// During pending state with variables.isActive = false, should show optimistic state
			expect(toggle).not.toBeChecked()
			expect(screen.getByText("Inactive")).toBeInTheDocument()
		})

		it("handles error state correctly", () => {
			// The error handling is now managed by the hook/mutation
			// This test can focus on the component behavior
			renderWithQueryClient(<StatusToggle {...defaultProps} isActive={true} />)

			const toggle = screen.getByRole("switch")
			expect(toggle).toBeChecked()
		})
	})

	describe("Loading States", () => {
		it("shows loading spinner during mutation", () => {
			mockUseToggleCampaignStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			// Should show loading spinner
			const loader = screen.getByTestId("loading-spinner")
			expect(loader).toBeInTheDocument()
			expect(loader).toHaveClass("animate-spin")
		})

		it("disables toggle during mutation", () => {
			mockUseToggleCampaignStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const toggle = screen.getByRole("switch")

			// Toggle should be disabled during loading
			expect(toggle).toBeDisabled()

			// Try to click - should not call mutate
			fireEvent.click(toggle)
			expect(mockMutate).not.toHaveBeenCalled()
		})

		it("updates screen reader description during mutation", () => {
			mockUseToggleCampaignStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			// Should include loading message in description
			expect(
				screen.getByText(/Status is being updated/, { exact: false }),
			).toBeInTheDocument()
		})

		it("applies opacity to label during mutation", () => {
			mockUseToggleCampaignStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const label = screen.getByText("Active")

			// Label should have opacity during loading
			expect(label).toHaveClass("opacity-50")
		})
	})

	describe("Edge Cases", () => {
		it("prevents multiple simultaneous requests", () => {
			mockUseToggleCampaignStatus.mockReturnValue({
				mutate: mockMutate,
				isPending: true,
				variables: undefined,
			})

			renderWithQueryClient(<StatusToggle {...defaultProps} />)

			const toggle = screen.getByRole("switch")

			// Try to click multiple times - should be disabled
			fireEvent.click(toggle)
			fireEvent.click(toggle)
			fireEvent.click(toggle)

			// Should not call mutate because toggle is disabled during pending state
			expect(mockMutate).not.toHaveBeenCalled()
		})

		it("handles state changes from parent", () => {
			const { rerender } = renderWithQueryClient(
				<StatusToggle {...defaultProps} isActive={true} />,
			)

			let toggle = screen.getByRole("switch")
			expect(toggle).toBeChecked()

			// Parent updates the state
			rerender(<StatusToggle {...defaultProps} isActive={false} />)

			toggle = screen.getByRole("switch")
			expect(toggle).not.toBeChecked()
		})
	})
})
