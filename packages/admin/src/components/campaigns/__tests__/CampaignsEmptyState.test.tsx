import { fireEvent, render, screen } from "@testing-library/react"
import { describe, expect, it, vi } from "vitest"
import { CampaignsEmptyState } from "../CampaignsEmptyState"

describe("CampaignsEmptyState", () => {
	const mockOnCreateCampaign = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	it("renders empty state content correctly", () => {
		render(<CampaignsEmptyState onCreateCampaign={mockOnCreateCampaign} />)

		expect(screen.getByText("No campaigns yet")).toBeInTheDocument()
		expect(
			screen.getByText(
				/Get started by creating your first fundraising campaign/,
			),
		).toBeInTheDocument()
		expect(
			screen.getByRole("button", { name: /create your first campaign/i }),
		).toBeInTheDocument()
	})

	it("calls onCreateCampaign when create button is clicked", () => {
		render(<CampaignsEmptyState onCreateCampaign={mockOnCreateCampaign} />)

		const createButton = screen.getByRole("button", {
			name: /create your first campaign/i,
		})
		fireEvent.click(createButton)

		expect(mockOnCreateCampaign).toHaveBeenCalledTimes(1)
	})

	it("displays megaphone icon", () => {
		render(<CampaignsEmptyState onCreateCampaign={mockOnCreateCampaign} />)

		// The icon should be present (we can't easily test the specific icon, but we can check the container)
		const iconContainer = screen.getByText("No campaigns yet").closest("div")
		expect(iconContainer).toBeInTheDocument()
	})

	it("has proper styling classes for dashed border", () => {
		const { container } = render(
			<CampaignsEmptyState onCreateCampaign={mockOnCreateCampaign} />,
		)

		const card = container.querySelector(".border-dashed")
		expect(card).toBeInTheDocument()
	})
})
