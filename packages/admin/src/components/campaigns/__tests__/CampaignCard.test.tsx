import type { Campaign } from "@donorcare/backend/schemas"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { describe, expect, it, vi } from "vitest"
import { CampaignCard } from "../CampaignCard"

// Mock the useToggleCampaignStatus hook
const mockUseToggleCampaignStatus = vi.fn()

vi.mock("@/hooks/useCampaigns", () => ({
	useToggleCampaignStatus: () => mockUseToggleCampaignStatus(),
}))

// Helper function to render with QueryClient
function renderWithQueryClient(ui: React.ReactElement) {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	})

	return render(
		<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
	)
}

// Mock campaign data
const mockCampaign: Campaign = {
	id: "1",
	organizerId: "org-1",
	name: "Test Campaign",
	description: "This is a test campaign description",
	slug: "test-campaign",
	isActive: true,
	createdAt: "2024-01-15T10:00:00Z",
}

const mockInactiveCampaign: Campaign = {
	...mockCampaign,
	id: "2",
	name: "Inactive Campaign",
	isActive: false,
}

const mockCampaignWithoutDescription: Campaign = {
	...mockCampaign,
	id: "3",
	name: "No Description Campaign",
	description: null,
}

describe("CampaignCard", () => {
	const mockOnEdit = vi.fn()
	const mockOnDelete = vi.fn()
	const mockOnStatusToggle = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()

		// Default mock implementation
		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: vi.fn(),
			isPending: false,
		})
	})

	it("renders campaign information correctly", () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		expect(screen.getByText("Test Campaign")).toBeInTheDocument()
		expect(
			screen.getByText("This is a test campaign description"),
		).toBeInTheDocument()
		expect(screen.getByText("/test-campaign")).toBeInTheDocument()
		expect(screen.getByText("Active")).toBeInTheDocument()
		expect(screen.getByText("Created Jan 15, 2024")).toBeInTheDocument()
	})

	it("renders inactive campaign with correct badge", () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockInactiveCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		expect(screen.getByText("Inactive")).toBeInTheDocument()
	})

	it("handles campaign without description", () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockCampaignWithoutDescription}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		expect(screen.getByText("No Description Campaign")).toBeInTheDocument()
		expect(
			screen.queryByText("This is a test campaign description"),
		).not.toBeInTheDocument()
	})

	it("calls onEdit when edit menu item is clicked", async () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		// Open dropdown menu
		const menuButton = screen.getByRole("button", { name: /open menu/i })
		fireEvent.click(menuButton)

		// Wait for dropdown to open and find edit option
		await waitFor(() => {
			const editButton = screen.getByText("Edit Campaign")
			fireEvent.click(editButton)
		})

		expect(mockOnEdit).toHaveBeenCalledWith("1")
	})

	it("calls onDelete when delete menu item is clicked", async () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		// Open dropdown menu
		const menuButton = screen.getByRole("button", { name: /open menu/i })
		fireEvent.click(menuButton)

		// Wait for dropdown to open and find delete option
		await waitFor(() => {
			const deleteButton = screen.getByText("Delete Campaign")
			fireEvent.click(deleteButton)
		})

		expect(mockOnDelete).toHaveBeenCalledWith("1")
	})

	it("renders StatusToggle component", () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		// StatusToggle should be present
		expect(screen.getByRole("switch")).toBeInTheDocument()
		expect(screen.getByText("Active")).toBeInTheDocument()
	})

	it("renders StatusToggle for inactive campaign", () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockInactiveCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		// StatusToggle should be present with inactive state
		expect(screen.getByRole("switch")).toBeInTheDocument()
		expect(screen.getByText("Inactive")).toBeInTheDocument()
	})

	it("disables menu button when loading", () => {
		renderWithQueryClient(
			<CampaignCard
				campaign={mockCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
				isLoading={true}
			/>,
		)

		const menuButton = screen.getByRole("button", { name: /open menu/i })
		expect(menuButton).toBeDisabled()
	})

	it("truncates long campaign names", () => {
		const longNameCampaign: Campaign = {
			...mockCampaign,
			name: "This is a very long campaign name that should be truncated when displayed in the card component",
		}

		renderWithQueryClient(
			<CampaignCard
				campaign={longNameCampaign}
				onEdit={mockOnEdit}
				onDelete={mockOnDelete}
				onStatusToggle={mockOnStatusToggle}
			/>,
		)

		const titleElement = screen.getByText(longNameCampaign.name)
		expect(titleElement).toHaveClass("truncate")
	})
})
