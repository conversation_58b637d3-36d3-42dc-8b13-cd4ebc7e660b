import { beforeEach } from "node:test"
import type { Campaign } from "@donorcare/backend/schemas"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { fireEvent, render, screen, waitFor } from "@testing-library/react"
import { describe, expect, it, vi } from "vitest"
import { CampaignsList } from "../CampaignsList"

// Mock the useCampaigns hook
const mockUseCampaigns = vi.fn()
const mockUseDeleteCampaign = vi.fn()
const mockUseToggleCampaignStatus = vi.fn()

vi.mock("@/hooks/useCampaigns", () => ({
	useCampaigns: () => mockUseCampaigns(),
	useDeleteCampaign: () => mockUseDeleteCampaign(),
	useToggleCampaignStatus: () => mockUseToggleCampaignStatus(),
}))

// Mock campaign data
const mockCampaigns: Campaign[] = [
	{
		id: "1",
		organizerId: "org-1",
		name: "Active Campaign",
		description: "This is an active campaign",
		slug: "active-campaign",
		isActive: true,
		createdAt: "2024-01-15T10:00:00Z",
		updatedAt: "2024-01-15T10:00:00Z",
	},
	{
		id: "2",
		organizerId: "org-1",
		name: "Inactive Campaign",
		description: "This is an inactive campaign",
		slug: "inactive-campaign",
		isActive: false,
		createdAt: "2024-01-10T10:00:00Z",
		updatedAt: "2024-01-10T10:00:00Z",
	},
	{
		id: "3",
		organizerId: "org-1",
		name: "Another Active Campaign",
		description: null,
		slug: "another-active-campaign",
		isActive: true,
		createdAt: "2024-01-20T10:00:00Z",
		updatedAt: "2024-01-20T10:00:00Z",
	},
]

// Helper function to render with QueryClient
function renderWithQueryClient(ui: React.ReactElement) {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	})

	return render(
		<QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>,
	)
}

describe("CampaignsList", () => {
	const mockOnEdit = vi.fn()
	const mockOnCreateCampaign = vi.fn()
	const mockDeleteMutate = vi.fn()
	const mockToggleStatusMutate = vi.fn()
	const mockRefetch = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()

		// Default mock implementations - return empty array by default
		mockUseCampaigns.mockReturnValue({
			data: [],
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})
	})

	it("renders loading state correctly", () => {
		mockUseCampaigns.mockReturnValue({
			data: [],
			isLoading: true,
			error: null,
			refetch: mockRefetch,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		expect(screen.getByText("Campaigns")).toBeInTheDocument()
		expect(
			screen.getByText("Manage your fundraising campaigns"),
		).toBeInTheDocument()

		// Create button should be disabled during loading
		const createButton = screen.getByRole("button", {
			name: /create campaign/i,
		})
		expect(createButton).toBeDisabled()
	})

	it("renders error state correctly", () => {
		const error = new Error("Failed to fetch campaigns")
		mockUseCampaigns.mockReturnValue({
			data: [],
			isLoading: false,
			error,
			refetch: mockRefetch,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		expect(screen.getByText("Failed to fetch campaigns")).toBeInTheDocument()
		expect(screen.getByRole("button", { name: /retry/i })).toBeInTheDocument()
	})

	it("calls refetch when retry button is clicked", () => {
		const error = new Error("Failed to fetch campaigns")
		mockUseCampaigns.mockReturnValue({
			data: [],
			isLoading: false,
			error,
			refetch: mockRefetch,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		const retryButton = screen.getByRole("button", { name: /retry/i })
		fireEvent.click(retryButton)

		expect(mockRefetch).toHaveBeenCalledTimes(1)
	})

	it("renders empty state when no campaigns exist", () => {
		mockUseCampaigns.mockReturnValue({
			data: [],
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		expect(screen.getByText("No campaigns yet")).toBeInTheDocument()
		expect(
			screen.getByRole("button", { name: /create your first campaign/i }),
		).toBeInTheDocument()
	})

	it("renders campaigns list correctly", () => {
		// Override the default mock to return campaigns
		mockUseCampaigns.mockReturnValue({
			data: mockCampaigns,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		expect(screen.getByText("Active Campaign")).toBeInTheDocument()
		expect(screen.getByText("Inactive Campaign")).toBeInTheDocument()
		expect(screen.getByText("Another Active Campaign")).toBeInTheDocument()
		expect(screen.getByText("Showing 3 campaigns")).toBeInTheDocument()
	})

	it("filters campaigns by search term", async () => {
		// Override the default mock to return campaigns
		mockUseCampaigns.mockReturnValue({
			data: mockCampaigns,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		const searchInput = screen.getByPlaceholderText("Search campaigns...")
		fireEvent.change(searchInput, { target: { value: "Active" } })

		await waitFor(() => {
			expect(screen.getByText("Showing 2 of 3 campaigns")).toBeInTheDocument()
			expect(screen.getByText("Active Campaign")).toBeInTheDocument()
			expect(screen.getByText("Another Active Campaign")).toBeInTheDocument()
			expect(screen.queryByText("Inactive Campaign")).not.toBeInTheDocument()
		})
	})

	it("filters campaigns by status", async () => {
		// Override the default mock to return campaigns
		mockUseCampaigns.mockReturnValue({
			data: mockCampaigns,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		// Open status filter dropdown
		const statusFilter = screen.getByDisplayValue("All Status")
		fireEvent.click(statusFilter)

		// Select "Active" filter
		const activeOption = screen.getByText("Active")
		fireEvent.click(activeOption)

		await waitFor(() => {
			expect(screen.getByText("Showing 2 of 3 campaigns")).toBeInTheDocument()
			expect(screen.getByText("Active Campaign")).toBeInTheDocument()
			expect(screen.getByText("Another Active Campaign")).toBeInTheDocument()
			expect(screen.queryByText("Inactive Campaign")).not.toBeInTheDocument()
		})
	})

	it("sorts campaigns by name", async () => {
		// Override the default mock to return campaigns
		mockUseCampaigns.mockReturnValue({
			data: mockCampaigns,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		// Open sort dropdown
		const sortFilter = screen.getByDisplayValue("Newest First")
		fireEvent.click(sortFilter)

		// Select "Name (A-Z)" sort
		const nameAscOption = screen.getByText("Name (A-Z)")
		fireEvent.click(nameAscOption)

		await waitFor(() => {
			const campaignCards = screen.getAllByText(/Campaign/)
			// Should be sorted alphabetically: Active, Another Active, Inactive
			expect(campaignCards[0]).toHaveTextContent("Active Campaign")
			expect(campaignCards[1]).toHaveTextContent("Another Active Campaign")
			expect(campaignCards[2]).toHaveTextContent("Inactive Campaign")
		})
	})

	it("shows no results message when filters return empty", async () => {
		// Override the default mock to return campaigns
		mockUseCampaigns.mockReturnValue({
			data: mockCampaigns,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		const searchInput = screen.getByPlaceholderText("Search campaigns...")
		fireEvent.change(searchInput, { target: { value: "nonexistent" } })

		await waitFor(() => {
			expect(
				screen.getByText(/No campaigns match your current filters/),
			).toBeInTheDocument()
		})
	})

	it("calls onCreateCampaign when create button is clicked", () => {
		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		const createButton = screen.getByRole("button", {
			name: /create campaign/i,
		})
		fireEvent.click(createButton)

		expect(mockOnCreateCampaign).toHaveBeenCalledTimes(1)
	})

	it("passes correct props to CampaignCard components", () => {
		// Override the default mock to return campaigns
		mockUseCampaigns.mockReturnValue({
			data: mockCampaigns,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		})

		mockUseDeleteCampaign.mockReturnValue({
			mutate: mockDeleteMutate,
			isPending: false,
		})

		mockUseToggleCampaignStatus.mockReturnValue({
			mutate: mockToggleStatusMutate,
			isPending: false,
		})

		renderWithQueryClient(
			<CampaignsList
				onEdit={mockOnEdit}
				onCreateCampaign={mockOnCreateCampaign}
			/>,
		)

		// Verify that campaign cards are rendered with correct data
		expect(screen.getByText("Active Campaign")).toBeInTheDocument()
		expect(screen.getByText("/active-campaign")).toBeInTheDocument()
		expect(screen.getByText("This is an active campaign")).toBeInTheDocument()
	})
})
