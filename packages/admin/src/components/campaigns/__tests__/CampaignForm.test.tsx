import type { Campaign } from "@donorcare/backend/schemas"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"
import { CampaignForm } from "../CampaignForm"

describe("CampaignForm", () => {
	const mockOnSubmit = vi.fn()
	const mockOnCancel = vi.fn()

	beforeEach(() => {
		vi.clearAllMocks()
	})

	describe("Create Mode", () => {
		it("renders create form with correct title", () => {
			render(
				<CampaignForm
					mode="create"
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("Create New Campaign")).toBeInTheDocument()
			expect(screen.getByText("Create Campaign")).toBeInTheDocument()
		})

		it("generates slug from campaign name", async () => {
			const user = userEvent.setup()
			render(
				<CampaignForm
					mode="create"
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const nameInput = screen.getByPlaceholderText("Enter campaign name")
			const slugInput = screen.getByPlaceholderText("campaign-url-slug")

			await user.type(nameInput, "My Test Campaign")

			await waitFor(() => {
				expect(slugInput).toHaveValue("my-test-campaign")
			})
		})

		it("handles special characters in slug generation", async () => {
			const user = userEvent.setup()
			render(
				<CampaignForm
					mode="create"
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const nameInput = screen.getByPlaceholderText("Enter campaign name")
			const slugInput = screen.getByPlaceholderText("campaign-url-slug")

			await user.type(nameInput, "Help & Support 2024!")

			await waitFor(() => {
				expect(slugInput).toHaveValue("help-support-2024")
			})
		})
	})

	describe("Edit Mode", () => {
		const mockCampaign: Campaign = {
			id: "1",
			organizerId: "org-1",
			name: "Existing Campaign",
			description: "Existing description",
			slug: "existing-campaign",
			isActive: true,
			createdAt: "2024-01-01T00:00:00Z",
		}

		it("renders edit form with correct title", () => {
			render(
				<CampaignForm
					mode="edit"
					initialData={mockCampaign}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByText("Edit Campaign")).toBeInTheDocument()
			expect(screen.getByText("Save Changes")).toBeInTheDocument()
		})

		it("populates form with campaign data", () => {
			render(
				<CampaignForm
					mode="edit"
					initialData={mockCampaign}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(screen.getByDisplayValue("Existing Campaign")).toBeInTheDocument()
			expect(
				screen.getByDisplayValue("Existing description"),
			).toBeInTheDocument()
		})

		it("does not show slug field in edit mode", () => {
			render(
				<CampaignForm
					mode="edit"
					initialData={mockCampaign}
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			expect(
				screen.queryByPlaceholderText("campaign-url-slug"),
			).not.toBeInTheDocument()
		})
	})

	describe("Common Functionality", () => {
		it("calls onCancel when cancel button is clicked", async () => {
			const user = userEvent.setup()
			render(
				<CampaignForm
					mode="create"
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
				/>,
			)

			const cancelButton = screen.getByText("Cancel")
			await user.click(cancelButton)

			expect(mockOnCancel).toHaveBeenCalled()
		})

		it("shows loading state when isLoading is true", () => {
			render(
				<CampaignForm
					mode="create"
					onSubmit={mockOnSubmit}
					onCancel={mockOnCancel}
					isLoading={true}
				/>,
			)

			const nameInput = screen.getByPlaceholderText("Enter campaign name")
			expect(nameInput).toBeDisabled()

			const submitButton = screen.getByText("Create Campaign")
			expect(submitButton).toBeDisabled()
		})
	})
})
