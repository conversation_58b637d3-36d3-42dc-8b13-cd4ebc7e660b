import type { Campaign } from "@donorcare/backend/schemas"
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	Input,
	Label,
} from "@donorcare/ui"
import { AlertTriangle, Loader2 } from "lucide-react"
import { useId, useState } from "react"

export interface DeleteCampaignDialogProps {
	/** The campaign to delete, or null if dialog is closed */
	campaign: Campaign | null
	/** Whether the dialog is open */
	isOpen: boolean
	/** Function to close the dialog */
	onClose: () => void
	/** Function to confirm deletion */
	onConfirm: (campaignId: string) => Promise<void>
	/** Whether deletion is in progress */
	isLoading: boolean
	/** Optional donation count for the campaign */
	donationCount?: number
}

/**
 * Confirmation dialog for campaign deletion with safety measures
 * Requires campaign name confirmation and shows warnings for campaigns with donations
 */
export function DeleteCampaignDialog({
	campaign,
	isOpen,
	onClose,
	onConfirm,
	isLoading,
	donationCount = 0,
}: DeleteCampaignDialogProps) {
	const [confirmationText, setConfirmationText] = useState("")

	// Reset confirmation text when dialog opens/closes
	const handleOpenChange = (open: boolean) => {
		if (!open) {
			setConfirmationText("")
			onClose()
		}
	}

	const inputId = useId()

	const handleConfirm = async () => {
		if (!campaign) return

		try {
			await onConfirm(campaign.id)
			setConfirmationText("")
		} catch (error) {
			// Error handling is managed by the parent component
			console.error("Delete campaign error:", error)
		}
	}

	// Check if confirmation text matches campaign name
	const isConfirmationValid = campaign && confirmationText === campaign.name
	const hasConfirmationError =
		confirmationText.length > 0 && !isConfirmationValid

	if (!campaign) return null

	return (
		<AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
			<AlertDialogContent className="sm:max-w-md">
				<AlertDialogHeader>
					<div className="flex items-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
							<AlertTriangle className="h-5 w-5 text-destructive" />
						</div>
						<div className="flex-1">
							<AlertDialogTitle className="text-left">
								Delete Campaign
							</AlertDialogTitle>
							<AlertDialogDescription className="text-left mt-1">
								This action cannot be undone.
							</AlertDialogDescription>
						</div>
					</div>
				</AlertDialogHeader>

				<div className="space-y-4">
					{/* Warning message */}
					<div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
						<p className="text-sm text-destructive font-medium mb-2">
							You are about to permanently delete "{campaign.name}"
						</p>
						<p className="text-sm text-muted-foreground">
							This will remove the campaign and all associated data from the
							system.
						</p>
					</div>

					{/* Donation count warning */}
					{donationCount > 0 && (
						<div className="rounded-lg border border-amber-200 bg-amber-50 p-4">
							<p className="text-sm text-amber-800 font-medium mb-1">
								⚠️ This campaign has {donationCount} donation
								{donationCount !== 1 ? "s" : ""}
							</p>
							<p className="text-sm text-amber-700">
								Deleting this campaign will also remove all donation records and
								cannot be recovered.
							</p>
						</div>
					)}

					{/* Confirmation input */}
					<div className="space-y-2">
						<Label htmlFor={inputId} className="text-sm font-medium">
							Type the campaign name to confirm deletion:
						</Label>
						<div className="space-y-1">
							<p className="text-sm text-muted-foreground font-mono bg-muted px-2 py-1 rounded">
								{campaign.name}
							</p>
							<Input
								id={inputId}
								type="text"
								placeholder="Enter campaign name"
								value={confirmationText}
								onChange={(e) => setConfirmationText(e.target.value)}
								disabled={isLoading}
								className={
									hasConfirmationError
										? "border-destructive focus-visible:ring-destructive"
										: ""
								}
								autoComplete="off"
								autoFocus
							/>
							{hasConfirmationError && (
								<p className="text-sm text-destructive">
									Campaign name does not match
								</p>
							)}
						</div>
					</div>
				</div>

				<AlertDialogFooter>
					<AlertDialogCancel
						disabled={isLoading}
						onClick={() => handleOpenChange(false)}
					>
						Cancel
					</AlertDialogCancel>
					<AlertDialogAction
						onClick={handleConfirm}
						disabled={!isConfirmationValid || isLoading}
						className="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-destructive"
					>
						{isLoading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Deleting...
							</>
						) : (
							"Delete Campaign"
						)}
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	)
}
