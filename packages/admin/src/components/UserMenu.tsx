import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
	Avatar,
	AvatarFallback,
	AvatarImage,
	Button,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@donorcare/ui"
import { LogOut, Settings, User } from "lucide-react"
import { useState } from "react"
import { useAuthStore, useUser } from "@/lib/auth-store"

/**
 * UserMenu component that displays user information and authentication actions
 * Fulfills requirements 5.1, 5.2, 5.3, 5.4 for user interface display
 */
export function UserMenu() {
	const user = useUser()
	const { signOut, isLoading } = useAuthStore()
	const [isLogoutDialogOpen, setIsLogoutDialogOpen] = useState(false)

	// Don't render if no user is authenticated
	if (!user) {
		return null
	}

	/**
	 * Generate user initials from name for avatar fallback
	 * Requirement 5.3: Show profile image or initials
	 */
	const getUserInitials = (name: string): string => {
		return name
			.split(" ")
			.map((part) => part.charAt(0).toUpperCase())
			.slice(0, 2)
			.join("")
	}

	/**
	 * Handle logout with confirmation
	 * Requirement 5.4: Add logout functionality with confirmation
	 */
	const handleLogout = async () => {
		try {
			await signOut()
			setIsLogoutDialogOpen(false)
		} catch (error) {
			console.error("Logout failed:", error)
			// Even if logout fails, close the dialog for security
			setIsLogoutDialogOpen(false)
		}
	}

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button
					variant="ghost"
					className="relative h-10 w-10 rounded-full p-0"
					aria-label={`User menu for ${user.name}`}
				>
					<Avatar className="h-10 w-10">
						<AvatarImage
							src={user.image}
							alt={`${user.name}'s avatar`}
							className="object-cover"
						/>
						<AvatarFallback className="bg-primary text-primary-foreground font-medium">
							{getUserInitials(user.name)}
						</AvatarFallback>
					</Avatar>
				</Button>
			</DropdownMenuTrigger>

			<DropdownMenuContent className="w-64" align="end" forceMount>
				{/* User information display - Requirements 5.1, 5.2 */}
				<DropdownMenuLabel className="font-normal">
					<div className="flex flex-col space-y-1">
						<p className="text-sm font-medium leading-none">{user.name}</p>
						<p className="text-xs leading-none text-muted-foreground">
							{user.email}
						</p>
						{user.emailVerified && (
							<p className="text-xs leading-none text-green-600">
								✓ Email verified
							</p>
						)}
					</div>
				</DropdownMenuLabel>

				<DropdownMenuSeparator />

				{/* Profile and settings options */}
				<DropdownMenuItem className="cursor-pointer">
					<User className="mr-2 h-4 w-4" />
					<span>Profile</span>
				</DropdownMenuItem>

				<DropdownMenuItem className="cursor-pointer">
					<Settings className="mr-2 h-4 w-4" />
					<span>Settings</span>
				</DropdownMenuItem>

				<DropdownMenuSeparator />

				{/* Logout with confirmation dialog */}
				<AlertDialog
					open={isLogoutDialogOpen}
					onOpenChange={setIsLogoutDialogOpen}
				>
					<AlertDialogTrigger asChild>
						<DropdownMenuItem
							className="cursor-pointer text-red-600 focus:text-red-600"
							onSelect={(e) => {
								e.preventDefault()
								setIsLogoutDialogOpen(true)
							}}
						>
							<LogOut className="mr-2 h-4 w-4" />
							<span>Sign out</span>
						</DropdownMenuItem>
					</AlertDialogTrigger>

					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle>Sign out of your account?</AlertDialogTitle>
							<AlertDialogDescription>
								You will be signed out of your account and redirected to the
								login page. Any unsaved changes may be lost.
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>Cancel</AlertDialogCancel>
							<AlertDialogAction
								onClick={handleLogout}
								disabled={isLoading}
								className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
							>
								{isLoading ? "Signing out..." : "Sign out"}
							</AlertDialogAction>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
			</DropdownMenuContent>
		</DropdownMenu>
	)
}
