import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>readcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
	Separator,
	SidebarTrigger,
} from "@donorcare/ui"
import { Link, useRouterState } from "@tanstack/react-router"
import { Fragment } from "react/jsx-runtime"

interface BreadcrumbConfig {
	[key: string]: string
}

const breadcrumbConfig: BreadcrumbConfig = {
	dashboard: "Dashboard",
	profile: "Profile",
	settings: "Settings",
}

export default function Header() {
	const routerState = useRouterState()
	const pathSegments = routerState.location.pathname
		.split("/")
		.filter(Boolean)
		.filter((segment) => segment !== "_authenticated")

	const breadcrumbItems = pathSegments.map((segment, index) => {
		const path = "/" + pathSegments.slice(0, index + 1).join("/")
		const label =
			breadcrumbConfig[segment] ||
			segment.charAt(0).toUpperCase() + segment.slice(1)
		const isLast = index === pathSegments.length - 1

		return {
			path,
			label,
			isLast,
		}
	})

	// Handle root dashboard page
	const isRootDashboard =
		pathSegments.length === 1 && pathSegments[0] === "dashboard"
	const isRootPath = pathSegments.length === 0

	return (
		<header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
			<div className="flex items-center gap-2 px-4">
				<SidebarTrigger className="-ml-1" />
				<Separator orientation="vertical" className="mr-2 h-4" />
				<Breadcrumb>
					<BreadcrumbList>
						{isRootPath || isRootDashboard ? (
							<BreadcrumbItem>
								<BreadcrumbPage>Dashboard</BreadcrumbPage>
							</BreadcrumbItem>
						) : (
							<>
								<BreadcrumbItem className="hidden md:block">
									<BreadcrumbLink asChild>
										<Link to="/">Dashboard</Link>
									</BreadcrumbLink>
								</BreadcrumbItem>
								{breadcrumbItems.map(({ path, label, isLast }, index) => (
									<Fragment key={path}>
										<BreadcrumbSeparator className="hidden md:block" />
										<BreadcrumbItem>
											{isLast ? (
												<BreadcrumbPage>{label}</BreadcrumbPage>
											) : (
												<BreadcrumbLink asChild>
													<Link to={path}>{label}</Link>
												</BreadcrumbLink>
											)}
										</BreadcrumbItem>
									</Fragment>
								))}
							</>
						)}
					</BreadcrumbList>
				</Breadcrumb>
			</div>
		</header>
	)
}
