import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@donorcare/ui/components/animate-ui/radix/collapsible"
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@donorcare/ui/components/animate-ui/radix/dropdown-menu"
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuAction,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	SidebarRail,
} from "@donorcare/ui/components/animate-ui/radix/sidebar"
import {
	Avatar,
	AvatarFallback,
	AvatarImage,
} from "@donorcare/ui/components/ui/avatar"
import { useIsMobile } from "@donorcare/ui/lib/use-mobile"
import { Link, useLocation } from "@tanstack/react-router"
import {
	BookOpen,
	Building,
	ChevronRight,
	ChevronsUpDown,
	Folder,
	Forward,
	Heart,
	Home,
	LogOut,
	MoreHorizontal,
	PieChart,
	Settings,
	SquareTerminal,
	Trash2,
	User,
	Users,
} from "lucide-react"
import { useAuthStore, useUser } from "../lib/auth-store"

// Navigation data for regular organizers
const ORGANIZER_DATA = {
	navMain: [
		{
			title: "Dashboard",
			url: "/dashboard",
			icon: Home,
		},
		{
			title: "Campaigns",
			url: "/campaigns",
			icon: SquareTerminal,
			items: [
				{
					title: "All Campaigns",
					url: "/campaigns",
				},
				{
					title: "Active",
					url: "/campaigns",
				},
				{
					title: "Completed",
					url: "/campaigns",
				},
				{
					title: "Create New",
					url: "/campaigns/create",
				},
			],
		},
		{
			title: "Donations",
			url: "#",
			icon: Heart,
			items: [
				{
					title: "All Donations",
					url: "/donations",
				},
				{
					title: "Pending",
					url: "/donations",
				},
				{
					title: "Processed",
					url: "/donations",
				},
				{
					title: "Refunds",
					url: "/donations",
				},
			],
		},
		{
			title: "Donors",
			url: "/donors",
			icon: Users,
			items: [
				{
					title: "All Donors",
					url: "/donors",
				},
				{
					title: "Create New",
					url: "/donors/create",
				},
			],
		},
		{
			title: "Settings",
			url: "/settings",
			icon: Settings,
		},
	],
	projects: [
		{
			name: "Emergency Relief",
			url: "#",
			icon: Heart,
		},
		{
			name: "Education Fund",
			url: "#",
			icon: BookOpen,
		},
		{
			name: "Healthcare Initiative",
			url: "#",
			icon: PieChart,
		},
	],
}

// Navigation data for admin users
const ADMIN_DATA = {
	navMain: [
		{
			title: "Dashboard",
			url: "/dashboard",
			icon: Home,
		},
		{
			title: "Organizers",
			url: "/organizers",
			icon: Building,
			items: [
				{
					title: "All Organizers",
					url: "/organizers",
				},
				{
					title: "Create New",
					url: "/organizers/create",
				},
			],
		},
		{
			title: "Settings",
			url: "/settings",
			icon: Settings,
		},
	],
	projects: [], // Admin users don't need to see active campaigns in sidebar
}

export const AppSidebar = () => {
	const isMobile = useIsMobile()
	const user = useUser()
	const { signOut } = useAuthStore()
	const location = useLocation()

	if (!user) return null

	// Select navigation data based on user role
	const isAdmin = user.role === "admin"
	const DATA = isAdmin ? ADMIN_DATA : ORGANIZER_DATA

	// Helper function to check if a route is active
	const isRouteActive = (url: string) => {
		if (url === "#") return false
		return location.pathname === url || location.pathname.startsWith(url + "/")
	}

	return (
		<Sidebar collapsible="icon">
			<SidebarHeader>
				{/* App Logo/Title */}
				<SidebarMenu>
					<SidebarMenuItem>
						<SidebarMenuButton
							size="lg"
							className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
						>
							<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
								<Heart className="size-4" />
							</div>
							<div className="grid flex-1 text-left text-sm leading-tight">
								<span className="truncate font-semibold">DonorCARE</span>
								<span className="truncate text-xs">Admin Panel</span>
							</div>
						</SidebarMenuButton>
					</SidebarMenuItem>
				</SidebarMenu>
			</SidebarHeader>

			<SidebarContent>
				{/* Nav Main */}
				<SidebarGroup>
					<SidebarGroupLabel>Platform</SidebarGroupLabel>
					<SidebarMenu>
						{DATA.navMain.map((item) => {
							const isActive = isRouteActive(item.url)
							const hasActiveSubItem = item.items?.some((subItem) =>
								isRouteActive(subItem.url),
							)

							return item.items ? (
								<Collapsible
									key={item.title}
									asChild
									defaultOpen={isActive || hasActiveSubItem}
									className="group/collapsible"
								>
									<SidebarMenuItem>
										<CollapsibleTrigger asChild>
											<SidebarMenuButton
												tooltip={item.title}
												isActive={isActive || hasActiveSubItem}
											>
												{item.icon && <item.icon />}
												<span>{item.title}</span>
												<ChevronRight className="ml-auto transition-transform duration-300 group-data-[state=open]/collapsible:rotate-90" />
											</SidebarMenuButton>
										</CollapsibleTrigger>
										<CollapsibleContent>
											<SidebarMenuSub>
												{item.items?.map((subItem) => (
													<SidebarMenuSubItem key={subItem.title}>
														<SidebarMenuSubButton
															asChild
															isActive={isRouteActive(subItem.url)}
														>
															{subItem.url === "#" ? (
																<span className="cursor-not-allowed opacity-50">
																	{subItem.title}
																</span>
															) : (
																<Link to={subItem.url}>{subItem.title}</Link>
															)}
														</SidebarMenuSubButton>
													</SidebarMenuSubItem>
												))}
											</SidebarMenuSub>
										</CollapsibleContent>
									</SidebarMenuItem>
								</Collapsible>
							) : (
								<SidebarMenuItem key={item.title}>
									<SidebarMenuButton
										asChild
										tooltip={item.title}
										isActive={isActive}
									>
										{item.url === "#" ? (
											<div className="cursor-not-allowed opacity-50">
												{item.icon && <item.icon />}
												<span>{item.title}</span>
											</div>
										) : (
											<Link to={item.url}>
												{item.icon && <item.icon />}
												<span>{item.title}</span>
											</Link>
										)}
									</SidebarMenuButton>
								</SidebarMenuItem>
							)
						})}
					</SidebarMenu>
				</SidebarGroup>
				{/* Nav Main */}

				{/* Nav Project - Only show for organizers */}
				{!isAdmin && DATA.projects.length > 0 && (
					<SidebarGroup className="group-data-[collapsible=icon]:hidden">
						<SidebarGroupLabel>Active Campaigns</SidebarGroupLabel>
						<SidebarMenu>
							{DATA.projects.map((item) => (
								<SidebarMenuItem key={item.name}>
									<SidebarMenuButton asChild>
										{item.url === "#" ? (
											<div className="cursor-not-allowed opacity-50">
												<item.icon />
												<span>{item.name}</span>
											</div>
										) : (
											<Link to={item.url}>
												<item.icon />
												<span>{item.name}</span>
											</Link>
										)}
									</SidebarMenuButton>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<SidebarMenuAction showOnHover>
												<MoreHorizontal />
												<span className="sr-only">More</span>
											</SidebarMenuAction>
										</DropdownMenuTrigger>
										<DropdownMenuContent
											className="w-48 rounded-lg"
											side={isMobile ? "bottom" : "right"}
											align={isMobile ? "end" : "start"}
										>
											<DropdownMenuItem>
												<Folder className="text-muted-foreground" />
												<span>View Campaign</span>
											</DropdownMenuItem>
											<DropdownMenuItem>
												<Forward className="text-muted-foreground" />
												<span>Share Campaign</span>
											</DropdownMenuItem>
											<DropdownMenuSeparator />
											<DropdownMenuItem>
												<Trash2 className="text-muted-foreground" />
												<span>Archive Campaign</span>
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</SidebarMenuItem>
							))}
							<SidebarMenuItem>
								<SidebarMenuButton className="text-sidebar-foreground/70">
									<MoreHorizontal className="text-sidebar-foreground/70" />
									<span>More</span>
								</SidebarMenuButton>
							</SidebarMenuItem>
						</SidebarMenu>
					</SidebarGroup>
				)}
				{/* Nav Project */}
			</SidebarContent>
			<SidebarFooter>
				{/* Nav User */}
				<SidebarMenu>
					<SidebarMenuItem>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<SidebarMenuButton
									size="lg"
									className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
								>
									<Avatar className="h-8 w-8 rounded-lg">
										<AvatarImage
											src={user.image || "/api/placeholder/32/32"}
											alt={user.name}
										/>
										<AvatarFallback className="rounded-lg">
											{user.name
												.split(" ")
												.map((n) => n[0])
												.join("")
												.toUpperCase()}
										</AvatarFallback>
									</Avatar>
									<div className="grid flex-1 text-left text-sm leading-tight">
										<span className="truncate font-semibold">{user.name}</span>
										<span className="truncate text-xs">{user.email}</span>
									</div>
									<ChevronsUpDown className="ml-auto size-4" />
								</SidebarMenuButton>
							</DropdownMenuTrigger>
							<DropdownMenuContent
								className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
								side={isMobile ? "bottom" : "right"}
								align="end"
								sideOffset={4}
							>
								<DropdownMenuLabel className="p-0 font-normal">
									<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
										<Avatar className="h-8 w-8 rounded-lg">
											<AvatarImage
												src={user.image || "/api/placeholder/32/32"}
												alt={user.name}
											/>
											<AvatarFallback className="rounded-lg">
												{user.name
													.split(" ")
													.map((n) => n[0])
													.join("")
													.toUpperCase()}
											</AvatarFallback>
										</Avatar>
										<div className="grid flex-1 text-left text-sm leading-tight">
											<span className="truncate font-semibold">
												{user.name}
											</span>
											<span className="truncate text-xs">{user.email}</span>
										</div>
									</div>
								</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuGroup>
									<Link to="/profile">
										<DropdownMenuItem>
											<User />
											Profile
										</DropdownMenuItem>
									</Link>
								</DropdownMenuGroup>
								<DropdownMenuSeparator />
								<DropdownMenuGroup>
									<Link to="/settings">
										<DropdownMenuItem>
											<Settings />
											Settings
										</DropdownMenuItem>
									</Link>
								</DropdownMenuGroup>
								<DropdownMenuSeparator />
								<DropdownMenuItem onClick={() => signOut()}>
									<LogOut />
									Log out
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</SidebarMenuItem>
				</SidebarMenu>
				{/* Nav User */}
			</SidebarFooter>
			<SidebarRail />
		</Sidebar>
	)
}
