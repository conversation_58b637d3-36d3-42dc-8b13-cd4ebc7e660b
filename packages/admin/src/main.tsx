import { createRouter, RouterProvider } from "@tanstack/react-router"
import { StrictMode, useEffect, useState } from "react"
import ReactDOM from "react-dom/client"
import { AuthProvider } from "./lib/auth/context"
import { useAuthStore } from "./lib/auth-store"
// Import the generated route tree
import { routeTree } from "./routeTree.gen"

import "./styles.css"
import { QueryProvider } from "./lib/query-client"
import reportWebVitals from "./reportWebVitals"

// Create a new router instance
const router = createRouter({
	routeTree,
	context: {
		auth: {
			user: null,
			isAuthenticated: false,
			isLoading: false,
			error: null,
			sessionChecked: false,
		},
	},
	defaultPreload: "intent",
	scrollRestoration: true,
	defaultStructuralSharing: true,
	defaultPreloadStaleTime: 0,
})

// Register the router instance for type safety
declare module "@tanstack/react-router" {
	interface Register {
		router: typeof router
	}
}

// Inner app component that has access to auth store
function InnerApp() {
	const authState = useAuthStore()

	// Update router context whenever auth state changes
	useEffect(() => {
		router.update({
			context: {
				auth: authState,
			},
		})

		// Force router to re-evaluate routes when auth state changes
		if (authState.sessionChecked) {
			router.invalidate()
		}
	}, [
		authState.isAuthenticated,
		authState.sessionChecked,
		authState.isLoading,
		authState.user,
		authState.error,
	])

	return <RouterProvider router={router} />
}

// Main app component with auth provider
function App() {
	const [authInitialized, setAuthInitialized] = useState(false)

	// Initialize auth before rendering the router
	useEffect(() => {
		const initAuth = async () => {
			try {
				const authStore = useAuthStore.getState()
				await authStore.initializeAuth()
				setAuthInitialized(true)
			} catch (error) {
				console.error("Failed to initialize auth:", error)
				// Still set as initialized to prevent infinite loading
				setAuthInitialized(true)
			}
		}

		initAuth()
	}, [])

	// Handle app-level cleanup
	useEffect(() => {
		return () => {
			// Only cleanup when the entire app is unmounting
			// This preserves session across page refreshes
			if (process.env.NODE_ENV === "development") {
				// In development, we might want to cleanup on hot reload
				// but preserve session in production
				console.log("App unmounting - preserving session")
			}
		}
	}, [])

	// Show loading until auth is initialized
	if (!authInitialized) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
					<p className="text-muted-foreground">Initializing...</p>
				</div>
			</div>
		)
	}

	return (
		<QueryProvider>
			<AuthProvider>
				<InnerApp />
			</AuthProvider>
		</QueryProvider>
	)
}

// Render the app
const rootElement = document.getElementById("app")
if (rootElement && !rootElement.innerHTML) {
	const root = ReactDOM.createRoot(rootElement)
	root.render(
		<StrictMode>
			<App />
		</StrictMode>,
	)
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals()
