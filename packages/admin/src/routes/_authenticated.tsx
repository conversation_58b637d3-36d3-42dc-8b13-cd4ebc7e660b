import { SidebarInset, SidebarProvider, Toaster } from "@donorcare/ui"
import { TanstackDevtools } from "@tanstack/react-devtools"
import { createFileRoute, Outlet } from "@tanstack/react-router"
import { TanStackRouterDevtoolsPanel } from "@tanstack/react-router-devtools"
import { AppSidebar } from "@/components/AppSidebar"
import Header from "@/components/Header"
import { ImpersonationBanner } from "@/components/ImpersonationBanner"
import { authGuards } from "../lib/auth/route-guards"

export const Route = createFileRoute("/_authenticated")({
	beforeLoad: authGuards.requireAuth,
	component: AuthenticatedLayout,
})

function AuthenticatedLayout() {
	return (
		<SidebarProvider defaultOpen>
			<div className="flex h-screen w-full flex-col">
				<div className="flex flex-1 h-full">
					<AppSidebar />
					<SidebarInset>
						<ImpersonationBanner />
						<Header />
						<main className="flex-1 overflow-y-auto bg-background">
							<Outlet />
						</main>
					</SidebarInset>
				</div>
			</div>
			<Toaster />
			<TanstackDevtools
				config={{
					position: "bottom-right",
				}}
				plugins={[
					{
						name: "Tanstack Router",
						render: <TanStackRouterDevtoolsPanel />,
					},
				]}
			/>
		</SidebarProvider>
	)
}
