import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { CampaignsList } from "@/components/campaigns"

export const Route = createFileRoute("/_authenticated/campaigns/")({
	component: RouteComponent,
})

function RouteComponent() {
	const navigate = useNavigate()

	const handleEdit = (campaignId: string) => {
		navigate({
			to: "/campaigns/$campaignId",
			params: { campaignId },
		})
	}

	return (
		<div className="p-6">
			<CampaignsList
				onEdit={handleEdit}
				onCreateCampaign={() => navigate({ to: "/campaigns/create" })}
			/>
		</div>
	)
}
