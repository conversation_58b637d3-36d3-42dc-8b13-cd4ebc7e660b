import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { CampaignForm } from "@/components/campaigns/CampaignForm"
import { useCreateCampaign } from "@/hooks/useCampaigns"

export const Route = createFileRoute("/_authenticated/campaigns/create")({
	component: CreateCampaignPage,
})

function CreateCampaignPage() {
	const navigate = useNavigate()
	const createMutation = useCreateCampaign()

	// Handle form submission
	const handleSubmit = (data: any) => {
		createMutation.mutate(data)
	}

	// Handle cancel action
	const handleCancel = () => {
		navigate({ to: "/campaigns" })
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<CampaignForm
					mode="create"
					onSubmit={handleSubmit}
					onCancel={handleCancel}
					isLoading={createMutation.isPending}
				/>
			</div>
		</div>
	)
}
