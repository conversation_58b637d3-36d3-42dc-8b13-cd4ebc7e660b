import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { Loader2 } from "lucide-react"
import { CampaignForm } from "@/components/campaigns/CampaignForm"
import { useCampaign, useUpdateCampaign } from "@/hooks/useCampaigns"

export const Route = createFileRoute("/_authenticated/campaigns/$campaignId/")({
	component: EditCampaignPage,
})

function EditCampaignPage() {
	const navigate = useNavigate()
	const { campaignId } = Route.useParams()

	// Query hooks
	const {
		data: campaign,
		isLoading: isLoadingCampaign,
		error,
	} = useCampaign(campaignId)

	// Mutation hooks
	const updateMutation = useUpdateCampaign(campaignId)

	// Handle form submission
	const handleSubmit = (data: any) => {
		updateMutation.mutate(data)
	}

	// Handle cancel action
	const handleCancel = () => {
		navigate({ to: "/campaigns" })
	}

	// Loading state
	if (isLoadingCampaign) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="flex flex-col items-center gap-4">
						<Loader2 className="h-8 w-8 animate-spin" />
						<p className="text-muted-foreground">Loading campaign...</p>
					</div>
				</div>
			</div>
		)
	}

	// Error state
	if (error || !campaign) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="text-center space-y-4">
						<h2 className="text-2xl font-semibold">Unable to Load Campaign</h2>
						<p className="text-muted-foreground max-w-md">
							{error?.message || "Campaign not found"}
						</p>
						<button
							type="button"
							onClick={() => navigate({ to: "/campaigns" })}
							className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
						>
							Back to Campaigns
						</button>
					</div>
				</div>
			</div>
		)
	}

	// Main content
	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<CampaignForm
					mode="edit"
					initialData={campaign}
					onSubmit={handleSubmit}
					onCancel={handleCancel}
					isLoading={updateMutation.isPending}
				/>
			</div>
		</div>
	)
}
