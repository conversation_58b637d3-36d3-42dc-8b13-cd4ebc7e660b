import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { Loader2 } from "lucide-react"
import { DonorCard } from "@/components/donors"
import { useDonor } from "@/hooks/useDonors"

export const Route = createFileRoute("/_authenticated/donors/$donorId/")({
	component: DonorDetailPage,
})

function DonorDetailPage() {
	const navigate = useNavigate()
	const { donorId } = Route.useParams()

	// Query hooks
	const { data: donor, isLoading: isLoadingDonor, error } = useDonor(donorId)

	// Handle edit action
	const handleEdit = () => {
		navigate({
			to: "/donors/$donorId/edit",
			params: { donorId },
		})
	}

	// Handle back action
	const handleBack = () => {
		navigate({ to: "/donors" })
	}

	// Loading state
	if (isLoadingDonor) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="flex flex-col items-center gap-4">
						<Loader2 className="h-8 w-8 animate-spin" />
						<p className="text-muted-foreground">Loading donor...</p>
					</div>
				</div>
			</div>
		)
	}

	// Error state
	if (error || !donor) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="text-center space-y-4">
						<h2 className="text-2xl font-semibold">Unable to Load Donor</h2>
						<p className="text-muted-foreground max-w-md">
							{error?.message || "Donor not found"}
						</p>
						<button
							type="button"
							onClick={handleBack}
							className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
						>
							Back to Donors
						</button>
					</div>
				</div>
			</div>
		)
	}

	// Main content
	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<button
							type="button"
							onClick={handleBack}
							className="text-sm text-muted-foreground hover:text-foreground"
						>
							← Back to Donors
						</button>
						<button
							type="button"
							onClick={handleEdit}
							className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2"
						>
							Edit Donor
						</button>
					</div>
					<DonorCard
						donor={donor}
						onView={() => {}}
						onEdit={handleEdit}
						onDelete={() => {}}
					/>
				</div>
			</div>
		</div>
	)
}
