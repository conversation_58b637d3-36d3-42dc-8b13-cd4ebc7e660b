import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { DonorsTable } from "@/components/donors"
import { useDeleteDonor, useDonors, useExportDonors } from "@/hooks/useDonors"

export const Route = createFileRoute("/_authenticated/donors/")({
	component: RouteComponent,
})

function RouteComponent() {
	const navigate = useNavigate()

	// Query hooks
	const { data: donorsResponse, isLoading, error, refetch } = useDonors()
	const donors = donorsResponse?.donors ?? []

	// Mutation hooks
	const deleteMutation = useDeleteDonor()
	const exportMutation = useExportDonors()

	const handleView = (donorId: string) => {
		navigate({
			to: "/donors/$donorId",
			params: { donorId },
		})
	}

	const handleEdit = (donorId: string) => {
		navigate({
			to: "/donors/$donorId/edit",
			params: { donorId },
		})
	}

	const handleDelete = async (donorId: string) => {
		deleteMutation.mutate(donorId)
	}

	const handleExport = async (selectedIds?: string[]) => {
		exportMutation.mutate(selectedIds)
	}

	const handleCreate = () => {
		navigate({ to: "/donors/create" })
	}

	return (
		<div className="p-6 space-y-2">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Donors</h1>
					<p className="text-muted-foreground">
						Manage your donor relationships and track supporter engagement.
					</p>
				</div>
			</div>

			<DonorsTable
				donors={donors}
				isLoading={isLoading}
				error={error}
				onView={handleView}
				onEdit={handleEdit}
				onDelete={handleDelete}
				onExport={handleExport}
				onRetry={refetch}
				onCreateNew={handleCreate}
			/>
		</div>
	)
}
