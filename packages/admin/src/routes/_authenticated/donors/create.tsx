import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { DonorForm } from "@/components/donors/DonorForm"
import { useCreateDonor } from "@/hooks/useDonors"

export const Route = createFileRoute("/_authenticated/donors/create")({
	component: CreateDonorPage,
})

function CreateDonorPage() {
	const navigate = useNavigate()
	const createMutation = useCreateDonor()

	// Handle form submission
	const handleSubmit = async (data: any) => {
		return new Promise<void>((resolve, reject) => {
			createMutation.mutate(data, {
				onSuccess: () => {
					navigate({ to: "/donors" })
					resolve()
				},
				onError: (error) => {
					reject(error)
				},
			})
		})
	}

	// Handle cancel action
	const handleCancel = () => {
		navigate({ to: "/donors" })
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<DonorForm
					mode="create"
					onSubmit={handleSubmit}
					onCancel={handleCancel}
					isLoading={createMutation.isPending}
				/>
			</div>
		</div>
	)
}
