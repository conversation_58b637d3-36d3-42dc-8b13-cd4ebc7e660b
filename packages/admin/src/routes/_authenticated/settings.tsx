import { createFileRoute } from "@tanstack/react-router"
import { createAuthGuard } from "../../lib/auth/route-guards"

// Custom auth guard that checks if user has verified email for settings access
const settingsAuthGuard = createAuthGuard({
	customAuthCheck: async ({ auth }) => {
		// Require authenticated user with verified email for settings
		return auth.isAuthenticated && auth.user?.emailVerified === true
	},
	// Custom redirect path for unverified users
	loginPath: "/profile", // Redirect to profile where they can see verification status
})

export const Route = createFileRoute("/_authenticated/settings")({
	beforeLoad: settingsAuthGuard,
	component: SettingsPage,
})

function SettingsPage() {
	const { user } = Route.useRouteContext().auth

	return (
		<div className="container mx-auto py-6">
			<div className="max-w-2xl mx-auto">
				<h1 className="text-2xl font-bold mb-6">Account Settings</h1>

				<div className="bg-card border rounded-lg p-6">
					<div className="space-y-6">
						<div>
							<h2 className="text-lg font-semibold mb-4">
								Account Information
							</h2>
							<p className="text-muted-foreground mb-4">
								Manage your account settings and preferences.
							</p>

							<div className="space-y-4">
								<div>
									<label className="text-sm font-medium">Display Name</label>
									<input
										type="text"
										defaultValue={user?.name || ""}
										className="w-full mt-1 px-3 py-2 border rounded-md"
										placeholder="Enter your display name"
									/>
								</div>

								<div>
									<label className="text-sm font-medium">Email</label>
									<input
										type="email"
										defaultValue={user?.email || ""}
										className="w-full mt-1 px-3 py-2 border rounded-md bg-muted"
										disabled
									/>
									<p className="text-xs text-muted-foreground mt-1">
										Email cannot be changed. Contact support if needed.
									</p>
								</div>
							</div>
						</div>

						<div>
							<h2 className="text-lg font-semibold mb-4">Security</h2>
							<div className="space-y-4">
								<button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
									Change Password
								</button>

								<div>
									<p className="text-sm text-muted-foreground">
										Last updated:{" "}
										{user?.updatedAt
											? new Date(user.updatedAt).toLocaleDateString()
											: "Unknown"}
									</p>
								</div>
							</div>
						</div>

						<div className="pt-4 border-t">
							<button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
								Save Changes
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
