import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { DonationsTable } from "@/components/donations"
import {
	useDeleteDonation,
	useDonations,
	useExportDonations,
	useUpdateDonationStatus,
} from "@/hooks/useDonations"

export const Route = createFileRoute("/_authenticated/donations/")({
	component: RouteComponent,
})

function RouteComponent() {
	const navigate = useNavigate()

	// Query hooks
	const { data: donations = [], isLoading, error, refetch } = useDonations()

	// Mutation hooks
	const deleteMutation = useDeleteDonation()
	const updateStatusMutation = useUpdateDonationStatus()
	const exportMutation = useExportDonations()

	const handleView = (donationId: string) => {
		navigate({
			to: "/donations/$donationId",
			params: { donationId },
		})
	}

	const handleEdit = (donationId: string) => {
		navigate({
			to: "/donations/$donationId/edit",
			params: { donationId },
		})
	}

	const handleDelete = async (donationId: string) => {
		deleteMutation.mutate(donationId)
	}

	const handleStatusChange = async (
		donationId: string,
		status: "pending" | "completed" | "failed",
		failureReason?: string,
	) => {
		updateStatusMutation.mutate({ donationId, status, failureReason })
	}

	const handleExport = async () => {
		exportMutation.mutate(undefined)
	}

	return (
		<div className="p-6">
			<DonationsTable
				donations={donations}
				isLoading={isLoading}
				error={error}
				onView={handleView}
				onEdit={handleEdit}
				onDelete={handleDelete}
				onStatusChange={handleStatusChange}
				onExport={handleExport}
				onRetry={refetch}
				// isExporting={exportMutation.isPending}
			/>
		</div>
	)
}
