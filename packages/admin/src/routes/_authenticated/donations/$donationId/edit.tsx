import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { Loader2 } from "lucide-react"
import { DonationForm } from "@/components/donations"
import { useDonation, useUpdateDonation } from "@/hooks/useDonations"

export const Route = createFileRoute(
	"/_authenticated/donations/$donationId/edit",
)({
	component: EditDonationPage,
})

function EditDonationPage() {
	const navigate = useNavigate()
	const { donationId } = Route.useParams()

	// Query hooks
	const {
		data: donation,
		isLoading: isLoadingDonation,
		error,
	} = useDonation(donationId)

	// Mutation hooks
	const updateMutation = useUpdateDonation(donationId)

	// Handle form submission
	const handleSubmit = async (data: any) => {
		return new Promise<void>((resolve, reject) => {
			updateMutation.mutate(data, {
				onSuccess: () => {
					navigate({
						to: "/donations/$donationId",
						params: { donationId },
					})
					resolve()
				},
				onError: (error) => {
					reject(error)
				},
			})
		})
	}

	// Handle cancel action
	const handleCancel = () => {
		navigate({
			to: "/donations/$donationId",
			params: { donationId },
		})
	}

	// Loading state
	if (isLoadingDonation) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="flex flex-col items-center gap-4">
						<Loader2 className="h-8 w-8 animate-spin" />
						<p className="text-muted-foreground">Loading donation...</p>
					</div>
				</div>
			</div>
		)
	}

	// Error state
	if (error || !donation) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="text-center space-y-4">
						<h2 className="text-2xl font-semibold">Unable to Load Donation</h2>
						<p className="text-muted-foreground max-w-md">
							{error?.message || "Donation not found"}
						</p>
						<button
							type="button"
							onClick={() => navigate({ to: "/donations" })}
							className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
						>
							Back to Donations
						</button>
					</div>
				</div>
			</div>
		)
	}

	// Main content
	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<DonationForm
					mode="edit"
					initialData={donation}
					onSubmit={handleSubmit}
					onCancel={handleCancel}
					isLoading={updateMutation.isPending}
				/>
			</div>
		</div>
	)
}
