import { createFileRoute } from "@tanstack/react-router"

export const Route = createFileRoute("/_authenticated/profile")({
	component: ProfilePage,
})

function ProfilePage() {
	const { user } = Route.useRouteContext().auth

	return (
		<div className="container mx-auto py-6">
			<div className="max-w-2xl mx-auto">
				<h1 className="text-2xl font-bold mb-6">User Profile</h1>

				<div className="bg-card border rounded-lg p-6">
					<div className="space-y-4">
						<div>
							<span className="text-sm font-medium text-muted-foreground">
								Name
							</span>
							<p className="text-lg">{user?.name || "Not provided"}</p>
						</div>

						<div>
							<span className="text-sm font-medium text-muted-foreground">
								Email
							</span>
							<p className="text-lg">{user?.email || "Not provided"}</p>
						</div>

						<div>
							<span className="text-sm font-medium text-muted-foreground">
								Email Verified
							</span>
							<p className="text-lg">
								{user?.emailVerified ? (
									<span className="text-green-600">✓ Verified</span>
								) : (
									<span className="text-yellow-600">⚠ Not verified</span>
								)}
							</p>
						</div>

						<div>
							<span className="text-sm font-medium text-muted-foreground">
								Member Since
							</span>
							<p className="text-lg">
								{user?.createdAt
									? new Date(user.createdAt).toLocaleDateString()
									: "Unknown"}
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}
