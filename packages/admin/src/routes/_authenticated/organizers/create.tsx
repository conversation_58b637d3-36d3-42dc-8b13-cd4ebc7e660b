import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { OrganizerForm } from "@/components/organizers/OrganizerForm"
import { useCreateOrganizer } from "@/hooks/useOrganizers"
import { authGuards } from "@/lib/auth/route-guards"

export const Route = createFileRoute("/_authenticated/organizers/create")({
	component: CreateOrganizerPage,
	beforeLoad: authGuards.requireAdmin,
})

function CreateOrganizerPage() {
	const navigate = useNavigate()
	const createMutation = useCreateOrganizer()

	// Handle form submission
	const handleSubmit = async (data: any) => {
		return new Promise<void>((resolve, reject) => {
			createMutation.mutate(data, {
				onSuccess: () => {
					navigate({ to: "/organizers" })
					resolve()
				},
				onError: (error) => {
					reject(error)
				},
			})
		})
	}

	// Handle cancel action
	const handleCancel = () => {
		navigate({ to: "/organizers" })
	}

	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<OrganizerForm
					mode="create"
					onSubmit={handleSubmit}
					onCancel={handleCancel}
					isLoading={createMutation.isPending}
				/>
			</div>
		</div>
	)
}
