import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { Loader2 } from "lucide-react"
import { OrganizerCard } from "@/components/organizers"
import { useOrganizer, useDeleteOrganizer, useImpersonateOrganizer } from "@/hooks/useOrganizers"
import { authGuards } from "@/lib/auth/route-guards"

export const Route = createFileRoute("/_authenticated/organizers/$organizerId/")({
	component: OrganizerDetailPage,
	beforeLoad: authGuards.requireAdmin,
})

function OrganizerDetailPage() {
	const navigate = useNavigate()
	const { organizerId } = Route.useParams()

	// Query hooks
	const { data: organizer, isLoading: isLoadingOrganizer, error } = useOrganizer(organizerId)

	// Mutation hooks
	const deleteMutation = useDeleteOrganizer()
	const impersonateMutation = useImpersonateOrganizer()

	// Handle back action
	const handleBack = () => {
		navigate({ to: "/organizers" })
	}

	// Handle delete action
	const handleDelete = () => {
		if (!organizer) return
		
		const confirmDelete = window.confirm(
			`Are you sure you want to delete "${organizer.name}"?\n\nThis action cannot be undone.`
		)
		
		if (confirmDelete) {
			deleteMutation.mutate(organizerId, {
				onSuccess: () => {
					navigate({ to: "/organizers" })
				},
			})
		}
	}

	// Handle impersonate action
	const handleImpersonate = () => {
		if (!organizer) return
		
		const confirmImpersonate = window.confirm(
			`Are you sure you want to impersonate "${organizer.name}"?\n\nYou will be logged in as this organizer and can view their campaigns and donations.`
		)
		
		if (confirmImpersonate) {
			impersonateMutation.mutate(organizerId)
		}
	}

	// Loading state
	if (isLoadingOrganizer) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="flex flex-col items-center gap-4">
						<Loader2 className="h-8 w-8 animate-spin" />
						<p className="text-muted-foreground">Loading organizer...</p>
					</div>
				</div>
			</div>
		)
	}

	// Error state
	if (error || !organizer) {
		return (
			<div className="flex flex-1 flex-col gap-4 p-6">
				<div className="flex-1 flex items-center justify-center">
					<div className="text-center space-y-4">
						<h2 className="text-2xl font-semibold">Unable to Load Organizer</h2>
						<p className="text-muted-foreground max-w-md">
							{error?.message || "Organizer not found"}
						</p>
						<button
							type="button"
							onClick={handleBack}
							className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
						>
							Back to Organizers
						</button>
					</div>
				</div>
			</div>
		)
	}

	// Main content
	return (
		<div className="flex flex-1 flex-col gap-4 p-6">
			<div className="flex-1 max-w-2xl mx-auto w-full">
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<button
							type="button"
							onClick={handleBack}
							className="text-sm text-muted-foreground hover:text-foreground"
						>
							← Back to Organizers
						</button>
					</div>
					<OrganizerCard
						organizer={organizer}
						onView={() => {}}
						onDelete={handleDelete}
						onImpersonate={handleImpersonate}
						isLoading={deleteMutation.isPending || impersonateMutation.isPending}
					/>
				</div>
			</div>
		</div>
	)
}