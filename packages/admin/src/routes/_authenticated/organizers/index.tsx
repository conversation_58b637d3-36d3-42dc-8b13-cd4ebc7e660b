import { Button } from "@donorcare/ui/components/ui/button"
import { createFileRoute, useNavigate } from "@tanstack/react-router"
import { Plus } from "lucide-react"
import { OrganizerTable } from "@/components/organizers"
import {
	useDeleteOrganizer,
	useExportOrganizers,
	useOrganizers,
} from "@/hooks/useOrganizers"
import { authGuards } from "@/lib/auth/route-guards"

export const Route = createFileRoute("/_authenticated/organizers/")({
	component: RouteComponent,
	beforeLoad: authGuards.requireAdmin,
})

function RouteComponent() {
	const navigate = useNavigate()

	// Query hooks
	const {
		data: organizersResponse,
		isLoading,
		error,
		refetch,
	} = useOrganizers()
	const organizers = organizersResponse?.data ?? []

	// Mutation hooks
	const deleteMutation = useDeleteOrganizer()
	const exportMutation = useExportOrganizers()

	const handleView = (organizerId: string) => {
		navigate({
			to: "/organizers/$organizerId",
			params: { organizerId },
		})
	}

	const handleEdit = (organizerId: string) => {
		navigate({
			to: "/organizers/$organizerId/edit",
			params: { organizerId },
		})
	}

	const handleDelete = async (organizerId: string) => {
		deleteMutation.mutate(organizerId)
	}

	const handleExport = async (selectedIds?: string[]) => {
		exportMutation.mutate(selectedIds)
	}

	const handleCreate = () => {
		navigate({ to: "/organizers/create" })
	}

	return (
		<div className="p-6 space-y-2">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">Organizers</h1>
					<p className="text-muted-foreground">
						Manage platform organizers and their account settings.
					</p>
				</div>
				<Button onClick={handleCreate} className="gap-2">
					<Plus className="h-4 w-4" />
					Add Organizer
				</Button>
			</div>

			<OrganizerTable
				organizers={organizers}
				isLoading={isLoading}
				error={error}
				onView={handleView}
				onEdit={handleEdit}
				onDelete={handleDelete}
				onExport={handleExport}
				onRetry={refetch}
				onCreateNew={handleCreate}
			/>
		</div>
	)
}
