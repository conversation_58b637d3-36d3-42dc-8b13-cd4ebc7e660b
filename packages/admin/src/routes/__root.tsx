import { TanstackDevtools } from "@tanstack/react-devtools"
import { createRootRoute, Outlet } from "@tanstack/react-router"
import { TanStackRouterDevtoolsPanel } from "@tanstack/react-router-devtools"
import { useEffect } from "react"
import { useAuthStore } from "@/lib/auth-store"
import type { RouterContext } from "@/types/auth"

export const Route = createRootRoute({
	context: (): RouterContext => {
		const authState = useAuthStore.getState()
		return {
			auth: {
				user: authState.user,
				isAuthenticated: authState.isAuthenticated,
				isLoading: authState.isLoading,
				error: authState.error,
				sessionChecked: authState.sessionChecked,
			},
		}
	},
	component: RootComponent,
})

function RootComponent() {
	const { initializeAuth } = useAuthStore()

	// Initialize auth on mount
	useEffect(() => {
		initializeAuth()
	}, [initializeAuth])

	return (
		<>
			<Outlet />
			<TanstackDevtools
				config={{
					position: "bottom-left",
				}}
				plugins={[
					{
						name: "Tanstack Router",
						render: <TanStackRouterDevtoolsPanel />,
					},
				]}
			/>
		</>
	)
}

// Export the router context type for use in other routes
export type { RouterContext as RootRouterContext }
