import { createFileRoute, redirect } from "@tanstack/react-router"
import type { RouterContext } from "@/types/auth"

export const Route = createFileRoute("/")({
	beforeLoad: ({ context }: { context: RouterContext }) => {
		console.log("Index route beforeLoad:", {
			isAuthenticated: context.auth.isAuthenticated,
			sessionChecked: context.auth.sessionChecked,
		})

		// If session hasn't been checked yet, don't redirect
		if (!context.auth.sessionChecked) {
			return
		}

		// If user is authenticated, redirect to dashboard
		if (context.auth.isAuthenticated) {
			console.log("Redirecting authenticated user to dashboard")
			throw redirect({
				to: "/dashboard",
			})
		} else {
			// If user is not authenticated, redirect to login
			console.log("Redirecting unauthenticated user to login")
			throw redirect({
				to: "/login",
			})
		}
	},
})
