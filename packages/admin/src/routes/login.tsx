import { createFileRoute } from "@tanstack/react-router"
import { z } from "zod"
import { LoginForm } from "../components/LoginForm"
import { authGuards } from "../lib/auth/route-guards"

// Search params validation schema
const loginSearchSchema = z.object({
	redirect: z.string().optional(),
})

export const Route = createFileRoute("/login")({
	validateSearch: loginSearchSchema,
	beforeLoad: authGuards.guestOnly,
	component: LoginPage,
})

function LoginPage() {
	const { redirect: redirectTo } = Route.useSearch()

	return (
		<div className="min-h-screen flex items-center justify-center bg-background">
			<div className="w-full max-w-md p-6">
				<div className="bg-card border rounded-lg shadow-sm p-6">
					<LoginForm redirectTo={redirectTo} />
				</div>
			</div>
		</div>
	)
}
