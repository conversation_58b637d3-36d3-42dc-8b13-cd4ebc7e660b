import type {
	createDonorDto,
	createNoteDto,
	createTagDto,
	donorFiltersDto,
	updateDonorDto,
	updateNoteDto,
	updateTagDto,
} from "@donorcare/backend/schemas"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import type { Static } from "elysia"
import { toast } from "sonner"
import {
	assignTags,
	createDonor,
	createDonorNote,
	createTag,
	deleteDonor,
	deleteDonorNote,
	deleteTag,
	exportDonors,
	getDonor,
	getDonorNotes,
	getDonors,
	getTags,
	updateDonor,
	updateDonorNote,
	updateTag,
} from "../lib/donors.api"
import {
	invalidateDonor,
	invalidateDonors,
	mutationKeys,
	queryKeys,
} from "../lib/query-client"

// Helper function to extract error message from treaty client response
function getErrorMessage(
	error: any,
	fallback: string = "An error occurred",
): string {
	if (typeof error === "string") return error
	if (error?.value && typeof error.value === "string") return error.value
	if (error?.message && typeof error.message === "string") return error.message
	return fallback
}

// Query hooks
export function useDonors(filters?: Static<typeof donorFiltersDto>) {
	return useQuery({
		queryKey: queryKeys.donors.list(filters),
		queryFn: async () => {
			const result = await getDonors(filters)

			if (result.error) {
				throw new Error(getErrorMessage(result.error, "Failed to fetch donors"))
			}

			return result.data
		},
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

export function useDonor(donorId: string, enabled = true) {
	return useQuery({
		queryKey: queryKeys.donors.detail(donorId),
		queryFn: async () => {
			const result = await getDonor(donorId)

			if (result.error) {
				if (result.error.status === 404) {
					throw new Error(
						"Donor not found or you don't have permission to view them.",
					)
				}
				if (result.error.status === 403) {
					throw new Error("You don't have permission to view this donor.")
				}
				throw new Error(getErrorMessage(result.error, "Failed to fetch donor"))
			}

			return result.data?.donor
		},
		enabled: enabled && !!donorId,
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

// Mutation hooks
export function useCreateDonor() {
	const navigate = useNavigate()

	return useMutation({
		mutationKey: mutationKeys.donors.create,
		mutationFn: async (data: Static<typeof createDonorDto>) => {
			const result = await createDonor(data)

			if (result.error) {
				const error = result.error
				if (error.status === 400) {
					throw new Error(
						getErrorMessage(error, "Please check your input and try again."),
					)
				}
				if (error.status === 409) {
					throw new Error(
						"A donor with this email already exists. Please use a different email.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to create donors.")
				}
				throw new Error(getErrorMessage(error, "Failed to create donor."))
			}

			return result.data
		},
		onSuccess: () => {
			// Invalidate donors list to show the new donor
			invalidateDonors()

			toast.success("Donor created successfully!")
			navigate({ to: "/dashboard" })
		},
		onError: (error) => {
			if (error.message.includes("session has expired")) {
				navigate({ to: "/login" })
			}
			toast.error(error.message)
		},
	})
}

export function useUpdateDonor(donorId: string) {
	const queryClient = useQueryClient()
	const navigate = useNavigate()

	return useMutation({
		mutationKey: mutationKeys.donors.update(donorId),
		mutationFn: async (data: Static<typeof updateDonorDto>) => {
			const result = await updateDonor(donorId, data)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Donor not found or you don't have permission to edit them.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to edit this donor.")
				}
				throw new Error(getErrorMessage(error, "Failed to update donor."))
			}

			return result.data
		},
		onMutate: async (newData) => {
			// Cancel any outgoing refetches (so they don't overwrite our optimistic update)
			await queryClient.cancelQueries({
				queryKey: queryKeys.donors.detail(donorId),
			})

			// Snapshot the previous value
			const previousDonor = queryClient.getQueryData(
				queryKeys.donors.detail(donorId),
			)

			// Optimistically update to the new value
			if (previousDonor) {
				queryClient.setQueryData(queryKeys.donors.detail(donorId), {
					...previousDonor,
					...newData,
				})
			}

			// Return a context object with the snapshot value
			return { previousDonor }
		},
		onError: (error, _variables, context) => {
			// If the mutation fails, use the context returned from onMutate to roll back
			if (context?.previousDonor) {
				queryClient.setQueryData(
					queryKeys.donors.detail(donorId),
					context.previousDonor,
				)
			}

			if (error.message.includes("not found")) {
				navigate({ to: "/dashboard" })
			}
			toast.error(error.message)
		},
		onSuccess: (data) => {
			// Update the specific donor data
			if (data?.donor) {
				queryClient.setQueryData(queryKeys.donors.detail(donorId), data.donor)
			}

			// Invalidate donors list to ensure consistency
			invalidateDonors()

			toast.success("Donor updated successfully!")
			navigate({ to: "/dashboard" })
		},
		// Always refetch after error or success to ensure we have the latest data
		onSettled: () => {
			invalidateDonor(donorId)
		},
	})
}

export function useDeleteDonor() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (donorId: string) => {
			const result = await deleteDonor(donorId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Donor not found or you don't have permission to delete them.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to delete this donor.")
				}
				if (error.status === 409) {
					throw new Error(
						"Cannot delete donor with existing donations. Please mark as inactive instead.",
					)
				}
				throw new Error(getErrorMessage(error, "Failed to delete donor."))
			}

			return { donorId }
		},
		onMutate: async (donorId) => {
			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: queryKeys.donors.lists(),
			})

			// Snapshot the previous value
			const previousDonors = queryClient.getQueryData(queryKeys.donors.lists())

			// Optimistically remove the donor from the list
			if (
				previousDonors &&
				typeof previousDonors === "object" &&
				"donors" in previousDonors
			) {
				const donorsData = previousDonors as { donors: any[] }
				if (Array.isArray(donorsData.donors)) {
					queryClient.setQueryData(queryKeys.donors.lists(), {
						...donorsData,
						donors: donorsData.donors.filter(
							(donor: any) => donor.id !== donorId,
						),
					})
				}
			}

			return { previousDonors }
		},
		onError: (error, _donorId, context) => {
			// Rollback on error
			if (context?.previousDonors) {
				queryClient.setQueryData(
					queryKeys.donors.lists(),
					context.previousDonors,
				)
			}
			toast.error(error.message)
		},
		onSuccess: () => {
			toast.success("Donor deleted successfully")
		},
		onSettled: () => {
			// Always refetch to ensure consistency
			invalidateDonors()
		},
	})
}

export function useExportDonors() {
	return useMutation({
		mutationKey: mutationKeys.donors.export,
		mutationFn: async (filters?: Static<typeof donorFiltersDto>) => {
			const result = await exportDonors(filters)

			if (result.error) {
				const error = result.error
				if (error.status === 403) {
					throw new Error("You don't have permission to export donors.")
				}
				throw new Error(getErrorMessage(error, "Failed to export donors."))
			}

			return result.data
		},
		onSuccess: (data) => {
			const exportData = data
			if (exportData?.csvData && exportData?.filename) {
				// Create a blob and download the CSV file
				const blob = new Blob([exportData.csvData], { type: "text/csv" })
				const url = window.URL.createObjectURL(blob)
				const link = document.createElement("a")
				link.href = url
				link.download = exportData.filename
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
				window.URL.revokeObjectURL(url)

				toast.success("Donors exported successfully!")
			}
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

// Tag Management Hooks
export function useTags() {
	return useQuery({
		queryKey: queryKeys.donors.tags(),
		queryFn: async () => {
			const result = await getTags()

			if (result.error) {
				throw new Error(getErrorMessage(result.error, "Failed to fetch tags"))
			}

			return result.data?.tags || []
		},
		staleTime: 1000 * 60 * 10, // 10 minutes
	})
}

export function useCreateTag() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationKey: mutationKeys.donors.createTag,
		mutationFn: async (data: Static<typeof createTagDto>) => {
			const result = await createTag(data)

			if (result.error) {
				const error = result.error
				if (error.status === 409) {
					throw new Error("A tag with this name already exists.")
				}
				throw new Error(getErrorMessage(error, "Failed to create tag."))
			}

			return result.data
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: queryKeys.donors.tags() })
			toast.success("Tag created successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

export function useUpdateTag() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			tagId,
			data,
		}: {
			tagId: string
			data: Static<typeof updateTagDto>
		}) => {
			const result = await updateTag(tagId, data)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error("Tag not found.")
				}
				if (error.status === 409) {
					throw new Error("A tag with this name already exists.")
				}
				throw new Error(getErrorMessage(error, "Failed to update tag."))
			}

			return result.data
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: queryKeys.donors.tags() })
			invalidateDonors() // Refresh donor lists to show updated tags
			toast.success("Tag updated successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

export function useDeleteTag() {
	return useMutation({
		mutationFn: async (tagId: string) => {
			const result = await deleteTag(tagId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error("Tag not found.")
				}
				throw new Error(getErrorMessage(error, "Failed to delete tag."))
			}

			return { tagId }
		},
		onSuccess: () => {
			invalidateDonors() // Refresh donor lists to remove deleted tags
			toast.success("Tag deleted successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

export function useAssignTags() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			donorId,
			tagIds,
		}: {
			donorId: string
			tagIds: string[]
		}) => {
			const result = await assignTags(donorId, { tagIds })

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error("Donor not found.")
				}
				if (error.status === 400) {
					throw new Error("Invalid tag IDs provided.")
				}
				throw new Error(getErrorMessage(error, "Failed to assign tags."))
			}

			return result.data
		},
		onSuccess: (_, { donorId }) => {
			invalidateDonor(donorId)
			invalidateDonors()
			toast.success("Tags assigned successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

// Note Management Hooks
export function useDonorNotes(donorId: string, enabled = true) {
	return useQuery({
		queryKey: queryKeys.donors.notes(donorId),
		queryFn: async () => {
			const result = await getDonorNotes(donorId)

			if (result.error) {
				if (result.error.status === 404) {
					throw new Error("Donor not found or access denied.")
				}
				throw new Error(getErrorMessage(result.error, "Failed to fetch notes"))
			}

			return result.data?.notes || []
		},
		enabled: enabled && !!donorId,
		staleTime: 1000 * 60 * 2, // 2 minutes
	})
}

export function useCreateDonorNote() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			donorId,
			data,
		}: {
			donorId: string
			data: Static<typeof createNoteDto>
		}) => {
			const result = await createDonorNote(donorId, data)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error("Donor not found or access denied.")
				}
				throw new Error(getErrorMessage(error, "Failed to create note."))
			}

			return result.data
		},
		onSuccess: (_, { donorId }) => {
			queryClient.invalidateQueries({
				queryKey: queryKeys.donors.notes(donorId),
			})
			invalidateDonor(donorId) // Refresh donor details to show new note
			toast.success("Note created successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

export function useUpdateDonorNote() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			donorId,
			noteId,
			data,
		}: {
			donorId: string
			noteId: string
			data: Static<typeof updateNoteDto>
		}) => {
			const result = await updateDonorNote(donorId, noteId, data)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error("Note not found.")
				}
				throw new Error(getErrorMessage(error, "Failed to update note."))
			}

			return result.data
		},
		onSuccess: (_, { donorId }) => {
			queryClient.invalidateQueries({
				queryKey: queryKeys.donors.notes(donorId),
			})
			invalidateDonor(donorId)
			toast.success("Note updated successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

export function useDeleteDonorNote() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			donorId,
			noteId,
		}: {
			donorId: string
			noteId: string
		}) => {
			const result = await deleteDonorNote(donorId, noteId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error("Note not found.")
				}
				throw new Error(getErrorMessage(error, "Failed to delete note."))
			}

			return { donorId, noteId }
		},
		onSuccess: (_, { donorId }) => {
			queryClient.invalidateQueries({
				queryKey: queryKeys.donors.notes(donorId),
			})
			invalidateDonor(donorId)
			toast.success("Note deleted successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

// Utility hook for checking if any donor mutations are in progress
export function useIsDonorMutating() {
	const queryClient = useQueryClient()

	return queryClient.isMutating({
		predicate: (mutation) => {
			return (
				mutation.options.mutationKey?.some(
					(key) => typeof key === "string" && key.includes("donors"),
				) ?? false
			)
		},
	})
}
