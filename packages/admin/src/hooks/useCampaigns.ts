import type {
	createCampaignDto,
	updateCampaignDto,
} from "@donorcare/backend/src/campaigns/campaigns.schema"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import type { Static } from "elysia"
import { toast } from "sonner"
import {
	createCampaign,
	deleteCampaign,
	getCampaign,
	getMyCampaigns,
	getPublicCampaign,
	toggleCampaignStatus,
	updateCampaign,
} from "@/lib/campaigns.api"
import {
	invalidateCampaign,
	invalidateCampaigns,
	mutationKeys,
	queryKeys,
} from "@/lib/query-client"

// Helper function to extract error message from treaty client response
function getErrorMessage(
	error: any,
	fallback: string = "An error occurred",
): string {
	if (typeof error === "string") return error
	if (error?.value && typeof error.value === "string") return error.value
	if (error?.message && typeof error.message === "string") return error.message
	return fallback
}

// Query hooks
export function useCampaigns() {
	return useQuery({
		queryKey: queryKeys.campaigns.lists(),
		queryFn: async () => {
			const result = await getMyCampaigns()

			if (result.error) {
				throw new Error(
					getErrorMessage(result.error, "Failed to fetch campaigns"),
				)
			}

			return result.data?.campaigns || []
		},
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

export function useCampaign(campaignId: string, enabled = true) {
	return useQuery({
		queryKey: queryKeys.campaigns.detail(campaignId),
		queryFn: async () => {
			const result = await getCampaign(campaignId)

			if (result.error) {
				if (result.error.status === 404) {
					throw new Error(
						"Campaign not found or you don't have permission to view it.",
					)
				}
				if (result.error.status === 403) {
					throw new Error("You don't have permission to view this campaign.")
				}
				throw new Error(
					getErrorMessage(result.error, "Failed to fetch campaign"),
				)
			}

			return result.data?.campaign
		},
		enabled: enabled && !!campaignId,
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

export function usePublicCampaign(slug: string, enabled = true) {
	return useQuery({
		queryKey: queryKeys.campaigns.public(slug),
		queryFn: async () => {
			const result = await getPublicCampaign(slug)

			if (result.error) {
				if (result.error.status === 404) {
					throw new Error("Campaign not found.")
				}
				throw new Error(
					getErrorMessage(result.error, "Failed to fetch campaign"),
				)
			}

			return result.data?.campaign
		},
		enabled: enabled && !!slug,
		staleTime: 1000 * 60 * 2, // 2 minutes for public campaigns
	})
}

// Mutation hooks
export function useCreateCampaign() {
	const navigate = useNavigate()

	return useMutation({
		mutationKey: mutationKeys.campaigns.create,
		mutationFn: async (data: Static<typeof createCampaignDto>) => {
			const result = await createCampaign(data)

			if (result.error) {
				const error = result.error
				if (error.status === 400) {
					throw new Error(
						getErrorMessage(error, "Please check your input and try again."),
					)
				}
				if (error.status === 409) {
					throw new Error(
						"A campaign with this slug already exists. Please choose a different name.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to create campaigns.")
				}
				throw new Error(getErrorMessage(error, "Failed to create campaign."))
			}

			return result.data
		},
		onSuccess: () => {
			// Invalidate campaigns list to show the new campaign
			invalidateCampaigns()

			toast.success("Campaign created successfully!")
			navigate({ to: "/campaigns" })
		},
		onError: (error) => {
			if (error.message.includes("session has expired")) {
				navigate({ to: "/login" })
			}
			toast.error(error.message)
		},
	})
}

export function useUpdateCampaign(campaignId: string) {
	const queryClient = useQueryClient()
	const navigate = useNavigate()

	return useMutation({
		mutationKey: mutationKeys.campaigns.update(campaignId),
		mutationFn: async (data: Static<typeof updateCampaignDto>) => {
			const result = await updateCampaign(campaignId, data)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Campaign not found or you don't have permission to edit it.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to edit this campaign.")
				}
				throw new Error(getErrorMessage(error, "Failed to update campaign."))
			}

			return result.data
		},
		onMutate: async (newData) => {
			// Cancel any outgoing refetches (so they don't overwrite our optimistic update)
			await queryClient.cancelQueries({
				queryKey: queryKeys.campaigns.detail(campaignId),
			})

			// Snapshot the previous value
			const previousCampaign = queryClient.getQueryData(
				queryKeys.campaigns.detail(campaignId),
			)

			// Optimistically update to the new value
			if (previousCampaign) {
				queryClient.setQueryData(queryKeys.campaigns.detail(campaignId), {
					...previousCampaign,
					...newData,
				})
			}

			// Return a context object with the snapshot value
			return { previousCampaign }
		},
		onError: (error, _variables, context) => {
			// If the mutation fails, use the context returned from onMutate to roll back
			if (context?.previousCampaign) {
				queryClient.setQueryData(
					queryKeys.campaigns.detail(campaignId),
					context.previousCampaign,
				)
			}

			if (error.message.includes("not found")) {
				navigate({ to: "/campaigns" })
			}
			toast.error(error.message)
		},
		onSuccess: (data) => {
			// Update the specific campaign data
			if (data?.campaign) {
				queryClient.setQueryData(
					queryKeys.campaigns.detail(campaignId),
					data.campaign,
				)
			}

			// Invalidate campaigns list to ensure consistency
			invalidateCampaigns()

			toast.success("Campaign updated successfully!")
			navigate({ to: "/campaigns" })
		},
		// Always refetch after error or success to ensure we have the latest data
		onSettled: () => {
			invalidateCampaign(campaignId)
		},
	})
}

export function useDeleteCampaign() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (campaignId: string) => {
			const result = await deleteCampaign(campaignId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Campaign not found or you don't have permission to delete it.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to delete this campaign.")
				}
				if (error.status === 409) {
					throw new Error(
						"Cannot delete campaign with existing donations. Please deactivate instead.",
					)
				}
				throw new Error(getErrorMessage(error, "Failed to delete campaign."))
			}

			return { campaignId }
		},
		onMutate: async (campaignId) => {
			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: queryKeys.campaigns.lists(),
			})

			// Snapshot the previous value
			const previousCampaigns = queryClient.getQueryData(
				queryKeys.campaigns.lists(),
			)

			// Optimistically remove the campaign from the list
			if (previousCampaigns && Array.isArray(previousCampaigns)) {
				queryClient.setQueryData(
					queryKeys.campaigns.lists(),
					previousCampaigns.filter(
						(campaign: any) => campaign.id !== campaignId,
					),
				)
			}

			return { previousCampaigns }
		},
		onError: (error, _campaignId, context) => {
			// Rollback on error
			if (context?.previousCampaigns) {
				queryClient.setQueryData(
					queryKeys.campaigns.lists(),
					context.previousCampaigns,
				)
			}
			toast.error(error.message)
		},
		onSuccess: () => {
			toast.success("Campaign deleted successfully")
		},
		onSettled: () => {
			// Always refetch to ensure consistency
			invalidateCampaigns()
		},
	})
}

export function useToggleCampaignStatus() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			campaignId,
			isActive,
		}: {
			campaignId: string
			isActive: boolean
		}) => {
			const result = await toggleCampaignStatus(campaignId, isActive)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Campaign not found or you don't have permission to modify it.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to modify this campaign.")
				}
				throw new Error(
					getErrorMessage(error, "Failed to update campaign status."),
				)
			}

			return result.data
		},
		onMutate: async ({ campaignId, isActive }) => {
			// Cancel any outgoing refetches
			await Promise.all([
				queryClient.cancelQueries({
					queryKey: queryKeys.campaigns.detail(campaignId),
				}),
				queryClient.cancelQueries({ queryKey: queryKeys.campaigns.lists() }),
			])

			// Snapshot previous values
			const previousCampaign = queryClient.getQueryData(
				queryKeys.campaigns.detail(campaignId),
			)
			const previousCampaigns = queryClient.getQueryData(
				queryKeys.campaigns.lists(),
			)

			// Optimistically update campaign detail
			if (previousCampaign) {
				queryClient.setQueryData(queryKeys.campaigns.detail(campaignId), {
					...previousCampaign,
					isActive,
				})
			}

			// Optimistically update campaigns list
			if (previousCampaigns && Array.isArray(previousCampaigns)) {
				queryClient.setQueryData(
					queryKeys.campaigns.lists(),
					previousCampaigns.map((campaign: any) =>
						campaign.id === campaignId ? { ...campaign, isActive } : campaign,
					),
				)
			}

			return { previousCampaign, previousCampaigns }
		},
		onError: (error, { campaignId }, context) => {
			// Rollback on error
			if (context?.previousCampaign) {
				queryClient.setQueryData(
					queryKeys.campaigns.detail(campaignId),
					context.previousCampaign,
				)
			}
			if (context?.previousCampaigns) {
				queryClient.setQueryData(
					queryKeys.campaigns.lists(),
					context.previousCampaigns,
				)
			}
			toast.error(error.message)
		},
		onSuccess: (data, { isActive }) => {
			// Update with server response
			if (data?.campaign) {
				queryClient.setQueryData(
					queryKeys.campaigns.detail(data.campaign.id),
					data.campaign,
				)
			}

			toast.success(
				`Campaign ${isActive ? "activated" : "deactivated"} successfully`,
			)
		},
		onSettled: (_data, _error, { campaignId }) => {
			// Always refetch to ensure consistency
			invalidateCampaign(campaignId)
		},
	})
}

// Utility hook for checking if any campaign mutations are in progress
export function useIsCampaignMutating() {
	const queryClient = useQueryClient()

	return queryClient.isMutating({
		predicate: (mutation) => {
			return (
				mutation.options.mutationKey?.some(
					(key) => typeof key === "string" && key.includes("campaigns"),
				) ?? false
			)
		},
	})
}
