import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import { toast } from "sonner"
import {
	createOrganizer,
	deleteOrganizer,
	exportOrganizers,
	getOrganizer,
	getOrganizers,
	impersonateOrganizer,
} from "../lib/organizers.api"
import type { CreateOrganizer } from "../lib/organizers.api"
import {
	invalidateOrganizer,
	invalidateOrganizers,
	mutationKeys,
	queryKeys,
} from "../lib/query-client"

// Helper function to extract error message from treaty client response
function getErrorMessage(
	error: any,
	fallback: string = "An error occurred",
): string {
	if (typeof error === "string") return error
	if (error?.value && typeof error.value === "string") return error.value
	if (error?.message && typeof error.message === "string") return error.message
	return fallback
}

// Query hooks
export function useOrganizers() {
	return useQuery({
		queryKey: queryKeys.organizers.lists(),
		queryFn: async () => {
			const result = await getOrganizers()

			if (result.error) {
				if (result.error.status === 403) {
					throw new Error("You don't have permission to view organizers.")
				}
				throw new Error(getErrorMessage(result.error, "Failed to fetch organizers"))
			}

			return result.data
		},
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

export function useOrganizer(organizerId: string, enabled = true) {
	return useQuery({
		queryKey: queryKeys.organizers.detail(organizerId),
		queryFn: async () => {
			const result = await getOrganizer(organizerId)

			if (result.error) {
				if (result.error.status === 404) {
					throw new Error(
						"Organizer not found or you don't have permission to view them.",
					)
				}
				if (result.error.status === 403) {
					throw new Error("You don't have permission to view this organizer.")
				}
				throw new Error(getErrorMessage(result.error, "Failed to fetch organizer"))
			}

			return result.data?.organizer
		},
		enabled: enabled && !!organizerId,
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

// Mutation hooks
export function useCreateOrganizer() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationKey: mutationKeys.organizers.create,
		mutationFn: async (data: CreateOrganizer) => {
			const result = await createOrganizer(data)

			if (result.error) {
				const error = result.error
				if (error.status === 403) {
					throw new Error("You don't have permission to create organizers.")
				}
				if (error.status === 409) {
					throw new Error("A user with this email already exists.")
				}
				throw new Error(getErrorMessage(error, "Failed to create organizer."))
			}

			return result.data
		},
		onSuccess: (data) => {
			// Add the new organizer to the list optimistically
			const queryKey = queryKeys.organizers.lists()
			const previousData = queryClient.getQueryData(queryKey)
			
			if (previousData && typeof previousData === "object" && "data" in previousData) {
				const organizersData = previousData as { data: any[] }
				if (Array.isArray(organizersData.data)) {
					queryClient.setQueryData(queryKey, {
						...organizersData,
						data: [data?.organizer, ...organizersData.data].filter(Boolean),
					})
				}
			}
			
			toast.success("Organizer created successfully!")
		},
		onError: (error) => {
			toast.error(error.message)
		},
		onSettled: () => {
			// Always refetch to ensure consistency
			invalidateOrganizers()
		},
	})
}

export function useDeleteOrganizer() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationKey: mutationKeys.organizers.delete(""),
		mutationFn: async (organizerId: string) => {
			const result = await deleteOrganizer(organizerId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Organizer not found or you don't have permission to delete them.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to delete this organizer.")
				}
				if (error.status === 409) {
					throw new Error(
						"Cannot delete organizer with existing data. Please contact support.",
					)
				}
				throw new Error(getErrorMessage(error, "Failed to delete organizer."))
			}

			return { organizerId }
		},
		onMutate: async (organizerId) => {
			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: queryKeys.organizers.lists(),
			})

			// Snapshot the previous value
			const previousOrganizers = queryClient.getQueryData(queryKeys.organizers.lists())

			// Optimistically remove the organizer from the list
			if (
				previousOrganizers &&
				typeof previousOrganizers === "object" &&
				"data" in previousOrganizers
			) {
				const organizersData = previousOrganizers as { data: any[] }
				if (Array.isArray(organizersData.data)) {
					queryClient.setQueryData(queryKeys.organizers.lists(), {
						...organizersData,
						data: organizersData.data.filter(
							(organizer: any) => organizer.id !== organizerId,
						),
					})
				}
			}

			return { previousOrganizers }
		},
		onError: (error, _organizerId, context) => {
			// Rollback on error
			if (context?.previousOrganizers) {
				queryClient.setQueryData(
					queryKeys.organizers.lists(),
					context.previousOrganizers,
				)
			}
			toast.error(error.message)
		},
		onSuccess: () => {
			toast.success("Organizer deleted successfully")
		},
		onSettled: () => {
			// Always refetch to ensure consistency
			invalidateOrganizers()
		},
	})
}

export function useExportOrganizers() {
	return useMutation({
		mutationKey: mutationKeys.organizers.export,
		mutationFn: async (selectedIds?: string[]) => {
			const result = await exportOrganizers(selectedIds)

			if (result.error) {
				const error = result.error
				if (error.status === 403) {
					throw new Error("You don't have permission to export organizers.")
				}
				throw new Error(getErrorMessage(error, "Failed to export organizers."))
			}

			return result.data
		},
		onSuccess: (data) => {
			const exportData = data
			if (exportData?.csvData && exportData?.filename) {
				// Create a blob and download the CSV file
				const blob = new Blob([exportData.csvData], { type: "text/csv" })
				const url = window.URL.createObjectURL(blob)
				const link = document.createElement("a")
				link.href = url
				link.download = exportData.filename
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
				window.URL.revokeObjectURL(url)

				toast.success("Organizers exported successfully!")
			}
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

export function useImpersonateOrganizer() {
	const navigate = useNavigate()

	return useMutation({
		mutationKey: mutationKeys.organizers.impersonate(""),
		mutationFn: async (organizerId: string) => {
			const result = await impersonateOrganizer(organizerId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Organizer not found or you don't have permission to impersonate them.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to impersonate this organizer.")
				}
				if (error.status === 409) {
					throw new Error(
						"Cannot impersonate banned organizer.",
					)
				}
				throw new Error(getErrorMessage(error, "Failed to impersonate organizer."))
			}

			return result.data
		},
		onSuccess: (data) => {
			toast.success("Now impersonating organizer. You can view their campaigns and donations.")
			// Refresh the page to reflect the impersonation state
			window.location.reload()
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

// Utility hook for checking if any organizer mutations are in progress
export function useIsOrganizerMutating() {
	const queryClient = useQueryClient()

	return queryClient.isMutating({
		predicate: (mutation) => {
			return (
				mutation.options.mutationKey?.some(
					(key) => typeof key === "string" && key.includes("organizers"),
				) ?? false
			)
		},
	})
}