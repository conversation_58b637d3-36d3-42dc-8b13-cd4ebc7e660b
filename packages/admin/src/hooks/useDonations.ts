import type {
	donationFiltersDto,
	updateDonationDto,
} from "@donorcare/backend/schemas"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useNavigate } from "@tanstack/react-router"
import type { Static } from "elysia"
import { toast } from "sonner"
import {
	deleteDonation,
	exportDonations,
	getDonation,
	getDonationAnalytics,
	getDonations,
	updateDonation,
	updateDonationStatus,
} from "@/lib/donations.api"
import {
	invalidateDonation,
	invalidateDonations,
	mutationKeys,
	queryKeys,
} from "@/lib/query-client"

// Helper function to extract error message from treaty client response
function getErrorMessage(
	error: any,
	fallback: string = "An error occurred",
): string {
	if (typeof error === "string") return error
	if (error?.value && typeof error.value === "string") return error.value
	if (error?.message && typeof error.message === "string") return error.message
	return fallback
}

// Query hooks
export function useDonations(filters?: Static<typeof donationFiltersDto>) {
	return useQuery({
		queryKey: queryKeys.donations.list(filters),
		queryFn: async () => {
			const result = await getDonations(filters)

			if (result.error) {
				throw new Error(
					getErrorMessage(result.error, "Failed to fetch donations"),
				)
			}

			return result.data?.donations || []
		},
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

export function useDonation(donationId: string, enabled = true) {
	return useQuery({
		queryKey: queryKeys.donations.detail(donationId),
		queryFn: async () => {
			const result = await getDonation(donationId)

			if (result.error) {
				if (result.error.status === 404) {
					throw new Error(
						"Donation not found or you don't have permission to view it.",
					)
				}
				if (result.error.status === 403) {
					throw new Error("You don't have permission to view this donation.")
				}
				throw new Error(
					getErrorMessage(result.error, "Failed to fetch donation"),
				)
			}

			return result.data?.donation
		},
		enabled: enabled && !!donationId,
		staleTime: 1000 * 60 * 5, // 5 minutes
	})
}

export function useDonationAnalytics(
	filters?: Static<typeof donationFiltersDto>,
) {
	return useQuery({
		queryKey: queryKeys.donations.analytics(filters),
		queryFn: async () => {
			const result = await getDonationAnalytics(filters)

			if (result.error) {
				throw new Error(
					getErrorMessage(result.error, "Failed to fetch donation analytics"),
				)
			}

			return result.data?.analytics
		},
		staleTime: 1000 * 60 * 2, // 2 minutes for analytics
	})
}

// Mutation hooks
export function useUpdateDonation(donationId: string) {
	const queryClient = useQueryClient()

	return useMutation({
		mutationKey: mutationKeys.donations.update(donationId),
		mutationFn: async (data: Static<typeof updateDonationDto>) => {
			const result = await updateDonation(donationId, data)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Donation not found or you don't have permission to edit it.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to edit this donation.")
				}
				throw new Error(getErrorMessage(error, "Failed to update donation."))
			}

			return result.data
		},
		onMutate: async (newData) => {
			// Cancel any outgoing refetches (so they don't overwrite our optimistic update)
			await queryClient.cancelQueries({
				queryKey: queryKeys.donations.detail(donationId),
			})

			// Snapshot the previous value
			const previousDonation = queryClient.getQueryData(
				queryKeys.donations.detail(donationId),
			)

			// Optimistically update to the new value
			if (previousDonation) {
				queryClient.setQueryData(queryKeys.donations.detail(donationId), {
					...previousDonation,
					...newData,
				})
			}

			// Return a context object with the snapshot value
			return { previousDonation }
		},
		onError: (error, _variables, context) => {
			// If the mutation fails, use the context returned from onMutate to roll back
			if (context?.previousDonation) {
				queryClient.setQueryData(
					queryKeys.donations.detail(donationId),
					context.previousDonation,
				)
			}

			toast.error(error.message)
		},
		onSuccess: (data) => {
			// Update the specific donation data
			if (data?.donation) {
				queryClient.setQueryData(
					queryKeys.donations.detail(donationId),
					data.donation,
				)
			}

			// Invalidate donations list to ensure consistency
			invalidateDonations()

			toast.success("Donation updated successfully!")
		},
		// Always refetch after error or success to ensure we have the latest data
		onSettled: () => {
			invalidateDonation(donationId)
		},
	})
}

export function useDeleteDonation() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (donationId: string) => {
			const result = await deleteDonation(donationId)

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Donation not found or you don't have permission to delete it.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to delete this donation.")
				}
				// Note: 409 status may not be available in current backend implementation
				// This is a placeholder for future business logic restrictions
				throw new Error(getErrorMessage(error, "Failed to delete donation."))
			}

			return { donationId }
		},
		onMutate: async (donationId) => {
			// Cancel any outgoing refetches
			await queryClient.cancelQueries({
				queryKey: queryKeys.donations.lists(),
			})

			// Snapshot the previous value
			const previousDonations = queryClient.getQueryData(
				queryKeys.donations.lists(),
			)

			// Optimistically remove the donation from the list
			if (previousDonations && Array.isArray(previousDonations)) {
				queryClient.setQueryData(
					queryKeys.donations.lists(),
					previousDonations.filter(
						(donation: any) => donation.id !== donationId,
					),
				)
			}

			return { previousDonations }
		},
		onError: (error, _donationId, context) => {
			// Rollback on error
			if (context?.previousDonations) {
				queryClient.setQueryData(
					queryKeys.donations.lists(),
					context.previousDonations,
				)
			}
			toast.error(error.message)
		},
		onSuccess: () => {
			toast.success("Donation deleted successfully")
		},
		onSettled: () => {
			// Always refetch to ensure consistency
			invalidateDonations()
		},
	})
}

export function useUpdateDonationStatus() {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			donationId,
			status,
			failureReason,
		}: {
			donationId: string
			status: "pending" | "completed" | "failed"
			failureReason?: string
		}) => {
			const result = await updateDonationStatus(donationId, {
				status,
				failureReason,
			})

			if (result.error) {
				const error = result.error
				if (error.status === 404) {
					throw new Error(
						"Donation not found or you don't have permission to modify it.",
					)
				}
				if (error.status === 403) {
					throw new Error("You don't have permission to modify this donation.")
				}
				throw new Error(
					getErrorMessage(error, "Failed to update donation status."),
				)
			}

			return result.data
		},
		onMutate: async ({ donationId, status }) => {
			// Cancel any outgoing refetches
			await Promise.all([
				queryClient.cancelQueries({
					queryKey: queryKeys.donations.detail(donationId),
				}),
				queryClient.cancelQueries({ queryKey: queryKeys.donations.lists() }),
			])

			// Snapshot previous values
			const previousDonation = queryClient.getQueryData(
				queryKeys.donations.detail(donationId),
			)
			const previousDonations = queryClient.getQueryData(
				queryKeys.donations.lists(),
			)

			// Optimistically update donation detail
			if (previousDonation) {
				queryClient.setQueryData(queryKeys.donations.detail(donationId), {
					...previousDonation,
					status,
				})
			}

			// Optimistically update donations list
			if (previousDonations && Array.isArray(previousDonations)) {
				queryClient.setQueryData(
					queryKeys.donations.lists(),
					previousDonations.map((donation: any) =>
						donation.id === donationId ? { ...donation, status } : donation,
					),
				)
			}

			return { previousDonation, previousDonations }
		},
		onError: (error, { donationId }, context) => {
			// Rollback on error
			if (context?.previousDonation) {
				queryClient.setQueryData(
					queryKeys.donations.detail(donationId),
					context.previousDonation,
				)
			}
			if (context?.previousDonations) {
				queryClient.setQueryData(
					queryKeys.donations.lists(),
					context.previousDonations,
				)
			}
			toast.error(error.message)
		},
		onSuccess: (data, { status }) => {
			// Update with server response
			if (data?.donation) {
				queryClient.setQueryData(
					queryKeys.donations.detail(data.donation.id),
					data.donation,
				)
			}

			const statusText = {
				pending: "marked as pending",
				completed: "marked as completed",
				failed: "marked as failed",
			}[status]

			toast.success(`Donation ${statusText} successfully`)
		},
		onSettled: (_data, _error, { donationId }) => {
			// Always refetch to ensure consistency
			invalidateDonation(donationId)
		},
	})
}

export function useExportDonations() {
	return useMutation({
		mutationKey: mutationKeys.donations.export,
		mutationFn: async (filters?: Static<typeof donationFiltersDto>) => {
			const result = await exportDonations(filters)

			if (result.error) {
				throw new Error(
					getErrorMessage(result.error, "Failed to export donations."),
				)
			}

			// The result should be a blob for CSV download
			return result.data
		},
		onSuccess: (blob) => {
			// Create download link for the CSV file
			if (blob && typeof blob === "object") {
				const url = window.URL.createObjectURL(blob as Blob)
				const link = document.createElement("a")
				link.href = url
				link.download = `donations-export-${new Date().toISOString().split("T")[0]}.csv`
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
				window.URL.revokeObjectURL(url)

				toast.success("Donations exported successfully!")
			}
		},
		onError: (error) => {
			toast.error(error.message)
		},
	})
}

// Utility hook for checking if any donation mutations are in progress
export function useIsDonationMutating() {
	const queryClient = useQueryClient()

	return queryClient.isMutating({
		predicate: (mutation) => {
			return (
				mutation.options.mutationKey?.some(
					(key) => typeof key === "string" && key.includes("donations"),
				) ?? false
			)
		},
	})
}
