# TanStack Query Integration - DonorCARE Admin

This document outlines the TanStack Query integration implemented for the DonorCARE admin panel to improve data fetching, caching, and state management.

## Overview

We've successfully integrated TanStack Query (React Query) v5 to replace manual state management in campaign routes with:

- **Automatic caching** and background refetching
- **Optimistic updates** for better UX
- **Error handling** with retry logic
- **Loading states** management
- **Query invalidation** for data consistency

## Files Updated

### Core Setup

1. **`src/lib/query-client.tsx`** - QueryClient configuration and provider
2. **`src/hooks/useCampaigns.ts`** - Custom hooks for campaign operations
3. **`src/main.tsx`** - Added QueryProvider wrapper

### Route Components Updated

1. **`src/routes/_authenticated/campaigns/index.tsx`** - Campaigns list page
2. **`src/routes/_authenticated/campaigns/create.tsx`** - Create campaign page  
3. **`src/routes/_authenticated/campaigns/$campaignId/index.tsx`** - Edit campaign page

### Form Component Updated

1. **`src/components/campaigns/CampaignForm.tsx`** - Updated interface to work with mutations

## Key Features Implemented

### 1. Query Client Configuration

```typescript
// Optimized defaults
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,     // 5 minutes
      gcTime: 1000 * 60 * 10,       // 10 minutes
      retry: (failureCount, error) => {
        // Don't retry on 4xx client errors
        if (error?.status >= 400 && error?.status < 500) {
          return false
        }
        return failureCount < 3
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
})
```

### 2. Query Keys Factory

Organized query keys for consistent cache management:

```typescript
export const queryKeys = {
  campaigns: {
    all: ['campaigns'] as const,
    lists: () => [...queryKeys.campaigns.all, 'list'] as const,
    details: () => [...queryKeys.campaigns.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.campaigns.details(), id] as const,
    public: (slug: string) => [...queryKeys.campaigns.all, 'public', slug] as const,
  },
}
```

### 3. Custom Hooks

#### Query Hooks
- **`useCampaigns()`** - Fetch campaigns list
- **`useCampaign(id)`** - Fetch single campaign
- **`usePublicCampaign(slug)`** - Fetch public campaign

#### Mutation Hooks  
- **`useCreateCampaign()`** - Create new campaign
- **`useUpdateCampaign(id)`** - Update existing campaign
- **`useDeleteCampaign()`** - Delete campaign
- **`useToggleCampaignStatus()`** - Toggle campaign active status

### 4. Optimistic Updates

Implemented optimistic updates for mutations:

```typescript
onMutate: async (variables) => {
  // Cancel outgoing refetches
  await queryClient.cancelQueries({ queryKey })
  
  // Snapshot previous value
  const previous = queryClient.getQueryData(queryKey)
  
  // Optimistically update
  queryClient.setQueryData(queryKey, newData)
  
  return { previous }
},
onError: (err, variables, context) => {
  // Rollback on error
  if (context?.previous) {
    queryClient.setQueryData(queryKey, context.previous)
  }
},
```

### 5. Error Handling

Consistent error handling with user-friendly messages:

```typescript
function getErrorMessage(error: any, fallback: string = 'An error occurred'): string {
  if (typeof error === 'string') return error
  if (error?.value && typeof error.value === 'string') return error.value
  if (error?.message && typeof error.message === 'string') return error.message
  return fallback
}
```

## Usage Examples

### Fetching Campaigns

```typescript
function CampaignsList() {
  const { 
    data: campaigns = [], 
    isLoading, 
    error, 
    refetch 
  } = useCampaigns()

  if (isLoading) return <Loading />
  if (error) return <Error message={error.message} onRetry={refetch} />
  
  return <CampaignGrid campaigns={campaigns} />
}
```

### Creating a Campaign

```typescript
function CreateCampaign() {
  const createMutation = useCreateCampaign()

  const handleSubmit = (data) => {
    createMutation.mutate(data)
    // Automatically handles success navigation and error display
  }

  return (
    <CampaignForm
      onSubmit={handleSubmit}
      isLoading={createMutation.isPending}
    />
  )
}
```

## Benefits Achieved

### 1. **Better User Experience**
- Instant feedback with optimistic updates
- Background data synchronization
- Smart retry logic for failed requests
- Consistent loading and error states

### 2. **Improved Performance**
- Automatic caching reduces API calls
- Background refetching keeps data fresh
- Request deduplication prevents duplicate calls
- Efficient memory management with garbage collection

### 3. **Simplified Code**
- Eliminated manual state management
- Reduced boilerplate code
- Centralized error handling
- Type-safe API layer

### 4. **Developer Experience**
- React Query DevTools in development
- Consistent patterns across components
- Easy testing with predictable behavior
- Better debugging capabilities

## Development Tools

### React Query DevTools
Automatically enabled in development mode for debugging queries and mutations.

### Query Invalidation Helpers
```typescript
// Invalidate all campaigns
await invalidateCampaigns()

// Invalidate specific campaign
await invalidateCampaign(campaignId)
```

## Migration Notes

### Before (Manual State Management)
```typescript
const [campaigns, setCampaigns] = useState([])
const [isLoading, setIsLoading] = useState(true)
const [error, setError] = useState(null)

useEffect(() => {
  fetchCampaigns()
    .then(setCampaigns)
    .catch(setError)
    .finally(() => setIsLoading(false))
}, [])
```

### After (TanStack Query)
```typescript
const { data: campaigns = [], isLoading, error } = useCampaigns()
```

## Best Practices

1. **Use Query Keys Factory** - Ensures consistent cache keys
2. **Implement Optimistic Updates** - For better UX in mutations  
3. **Handle Errors Gracefully** - With user-friendly messages
4. **Leverage Background Refetching** - Keep data fresh automatically
5. **Use DevTools** - For debugging in development

## Future Enhancements

1. **Infinite Queries** - For paginated campaign lists
2. **Prefetching** - Preload related data
3. **Offline Support** - Cache mutations for offline scenarios
4. **Real-time Updates** - WebSocket integration
5. **Advanced Caching** - Custom cache strategies

## Dependencies Added

```json
{
  "@tanstack/react-query": "^5.85.9",
  "@tanstack/react-query-devtools": "^5.85.9"
}
```

The integration is complete and all campaign routes now use TanStack Query for optimal data management and user experience.