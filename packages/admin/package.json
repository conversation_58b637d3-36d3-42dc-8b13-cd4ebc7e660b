{"name": "@donorcare/admin", "private": true, "type": "module", "scripts": {"dev": "bunx --bun vite --port 3001", "start": "bunx --bun vite --port 3001", "build": "bunx --bun vite build && tsc", "serve": "bunx --bun vite preview", "test": "vitest run", "format": "cd ../.. && bun x biome format --write packages/admin/src", "lint": "cd ../.. && bun x biome lint packages/admin/src", "check": "cd ../.. && bun x biome check --write packages/admin/src"}, "dependencies": {"@donorcare/backend": "workspace:*", "@donorcare/ui": "workspace:*", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-devtools": "^0.2.2", "@tanstack/react-query": "^5.85.9", "@tanstack/react-query-devtools": "^5.85.9", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.131.5", "@tanstack/router-plugin": "^1.121.2", "better-auth": "^1.3.7", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.542.0", "motion": "^12.23.12", "next-themes": "^0.4.6", "radix-ui": "^1.4.3", "react": "^19.0.0", "react-day-picker": "^9.9.0", "react-dom": "^19.0.0", "react-resizable-panels": "^3.0.4", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwindcss": "^4.0.6", "tw-animate-css": "^1.3.6", "vaul": "^1.1.2", "zustand": "^5.0.8"}, "devDependencies": {}}