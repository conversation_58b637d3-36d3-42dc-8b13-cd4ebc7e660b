import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"
import type { ComponentProps } from "react"

const Toaster = ({ ...props }: ComponentProps<typeof Sonner>) => {
	const { theme = "system" } = useTheme()

	return (
		<Sonner
			theme={theme as ComponentProps<typeof Sonner>["theme"]}
			className="toaster group"
			style={
				{
					"--normal-bg": "var(--popover)",
					"--normal-text": "var(--popover-foreground)",
					"--normal-border": "var(--border)",
				} as React.CSSProperties
			}
			{...props}
		/>
	)
}

export { Toaster }
