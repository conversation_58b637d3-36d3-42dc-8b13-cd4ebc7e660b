{"name": "@donorcare/backend", "version": "1.0.50", "main": "src/index.ts", "types": "src/model.ts", "exports": {".": null, "./types": {"types": "./src/model.ts", "default": "./src/model.ts"}, "./schemas": {"types": "./src/schemas.ts", "default": "./src/schemas.ts"}, "./auth": {"types": "./lib/auth.ts", "default": "./lib/auth.ts"}}, "scripts": {"test": "vitest run", "test:watch": "vitest", "dev": "bun run --watch src/index.ts", "build": "bun build --compile --minify-whitespace --minify-syntax --target bun --outfile server ./src/index.ts", "seed": "bun run drizzle/seed.ts", "migrate": "bun run drizzle/migrate.ts", "generate-migration": "drizzle-kit generate", "format": "cd ../.. && biome format --write packages/backend/src packages/backend/lib packages/backend/drizzle", "lint": "cd ../.. && biome lint packages/backend/src packages/backend/lib packages/backend/drizzle", "check": "cd ../.. && biome check --write packages/backend/src packages/backend/lib packages/backend/drizzle", "fix": "cd ../.. && biome check --write --unsafe packages/backend/src packages/backend/lib packages/backend/drizzle"}, "dependencies": {"@elysiajs/cors": "^1.3.3", "@elysiajs/eden": "^1.3.3", "@elysiajs/swagger": "^1.3.1", "better-auth": "^1.3.7", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "drizzle-typebox": "^0.3.3", "elysia": "latest", "http-status-codes": "^2.3.0", "nanoid": "^5.0.9", "pg": "^8.16.3"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@testing-library/jest-dom": "^6.8.0", "@types/pg": "^8.15.5", "bun-types": "latest", "drizzle-kit": "^0.31.4", "tsx": "^4.20.4", "vitest": "^3.2.4"}, "module": "src/index.js", "overrides": {"@sinclair/typebox": "0.34.33"}}