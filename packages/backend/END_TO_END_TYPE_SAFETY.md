# End-to-End Type Safety with Elysia and Eden

This document explains how the DonorCare backend implements end-to-end type safety using Elysia and Eden.

## Overview

End-to-end type safety ensures that types are consistent across the entire application stack, from the server API to the client consuming it. This prevents runtime errors and provides excellent developer experience with auto-completion and type checking.

## Implementation

### 1. Server-Side Type Safety (Elysia)

The backend uses Elysia with TypeBox for runtime validation and compile-time type safety:

```typescript
// Example endpoint with full type safety
.post('/campaigns', async ({ body }) => {
  // body is fully typed based on the schema
  return await createCampaign(body)
}, {
  body: t.Object({
    name: t.String({ minLength: 1, maxLength: 255 }),
    description: t.Optional(t.String()),
    slug: t.String({ pattern: "^[a-z0-9-]+$" }),
  }),
  response: {
    200: campaignResponseDto,
    400: errorResponseDto,
    500: errorResponseDto,
  }
})
```

### 2. Schema Definitions

All API schemas are defined using TypeBox in dedicated schema files:

- `src/campaigns/campaigns.schema.ts` - Campaign-related schemas
- `src/donations/donations.schema.ts` - Donation-related schemas
- `src/users/users.schema.ts` - User and database schemas

### 3. Type Export

The main server exports its type for client consumption:

```typescript
// src/index.ts
const app = new Elysia()
  .use(/* ... plugins ... */)
  .use(/* ... controllers ... */)
  .listen(3000)

// Export the app type for end-to-end type safety
export type App = typeof app
```

### 4. Client-Side Type Safety (Eden)

Clients can consume the API with full type safety using Eden Treaty:

```typescript
import { treaty } from '@elysiajs/eden'
import type { App } from '@donorcare/backend'

// Create type-safe client
const api = treaty<App>('http://localhost:3000')

// All requests are fully typed
const { data, error } = await api.api.campaigns.post({
  name: 'My Campaign',
  slug: 'my-campaign',
  // TypeScript will enforce the correct shape
})

if (error) {
  // Error handling with type narrowing
  switch (error.status) {
    case 400:
      console.error('Validation error:', error.value)
      break
    case 401:
      console.error('Unauthorized:', error.value)
      break
  }
} else {
  // data is fully typed
  console.log('Campaign created:', data.campaign.name)
}
```

## Benefits

### 1. Compile-Time Safety
- TypeScript catches type mismatches at compile time
- No more runtime errors due to API contract changes
- Refactoring is safer and more reliable

### 2. Developer Experience
- Full auto-completion in IDEs
- Inline documentation through TypeScript
- Immediate feedback on API changes

### 3. API Contract Enforcement
- Server and client must agree on types
- Breaking changes are caught immediately
- Documentation is always up-to-date

### 4. Runtime Validation
- TypeBox provides runtime validation
- Automatic request/response validation
- Type coercion where appropriate

## API Endpoints with Type Safety

### Campaigns

```typescript
// Create campaign
POST /api/campaigns
Body: CreateCampaignDto
Response: CreateCampaignResponseDto | ErrorResponseDto

// Get organizer's campaigns
GET /api/campaigns/my
Response: GetCampaignsResponseDto | ErrorResponseDto

// Get public campaign
GET /api/campaigns/:slug
Response: GetPublicCampaignResponseDto | ErrorResponseDto

// Update campaign
PUT /api/campaigns/:id
Body: UpdateCampaignDto
Response: UpdateCampaignResponseDto | ErrorResponseDto
```

### Donations

```typescript
// Initiate donation
POST /api/donations/initiate
Body: InitiateDonationDto
Response: InitiateDonationResponseDto | ErrorResponseDto

// Get donations (organizer)
GET /api/donations
Query: DonationFiltersDto
Response: DonationsListResponseDto | ErrorResponseDto

// Get donation details
GET /api/donations/:id
Response: DonationResponseDto | ErrorResponseDto

// Update donation
PUT /api/donations/:id
Body: UpdateDonationDto
Response: DonationResponseDto | ErrorResponseDto

// Get analytics
GET /api/donations/analytics
Query: DonationFiltersDto
Response: AnalyticsResponseDto | ErrorResponseDto
```

## Usage Examples

### Frontend Integration

```typescript
// In your React/Vue/etc. application
import { treaty } from '@elysiajs/eden'
import type { App } from '@donorcare/backend'

const api = treaty<App>(process.env.API_URL)

// Type-safe API calls
export const useCampaigns = () => {
  const [campaigns, setCampaigns] = useState([])
  
  useEffect(() => {
    const fetchCampaigns = async () => {
      const { data, error } = await api.api.campaigns.my.get({
        headers: { authorization: `Bearer ${token}` }
      })
      
      if (data) {
        setCampaigns(data.campaigns) // Fully typed
      }
    }
    
    fetchCampaigns()
  }, [])
  
  return campaigns
}
```

### Testing

```typescript
// Type-safe testing
import { treaty } from '@elysiajs/eden'
import type { App } from '../src/index'

const api = treaty<App>('http://localhost:3000')

test('should create campaign', async () => {
  const { data, error } = await api.api.campaigns.post({
    name: 'Test Campaign',
    slug: 'test-campaign'
  })
  
  expect(error).toBeNull()
  expect(data?.success).toBe(true)
  expect(data?.campaign.name).toBe('Test Campaign')
})
```

## Best Practices

### 1. Schema Organization
- Keep schemas in separate files by domain
- Use consistent naming conventions
- Document complex schemas with JSDoc

### 2. Error Handling
- Define comprehensive error response schemas
- Use appropriate HTTP status codes
- Provide meaningful error messages

### 3. Validation
- Validate all inputs with appropriate constraints
- Use TypeBox's built-in formats (email, uuid, etc.)
- Provide clear validation error messages

### 4. Type Exports
- Always export the app type from the main server file
- Use consistent import/export patterns
- Keep type definitions close to their usage

## Troubleshooting

### Common Issues

1. **Type inference not working**
   - Ensure `strict: true` in tsconfig.json
   - Check that method chaining is used correctly
   - Verify Eden version compatibility

2. **Runtime validation errors**
   - Check that request body matches schema
   - Verify content-type headers
   - Review TypeBox schema definitions

3. **Import/export issues**
   - Ensure proper path aliases are configured
   - Check that types are exported correctly
   - Verify module resolution settings

### Performance Considerations

- TypeBox schemas are compiled once at startup
- Eden client is lightweight (~2KB)
- Type checking happens at compile time, not runtime
- Consider using schema references for large, reused schemas

## Migration Guide

If migrating from a non-type-safe API:

1. **Add TypeBox schemas** to existing endpoints
2. **Export app type** from main server file
3. **Install Eden** in client applications
4. **Replace fetch calls** with Eden treaty calls
5. **Update tests** to use type-safe client
6. **Remove manual type definitions** that duplicate schemas

## Resources

- [Elysia Documentation](https://elysiajs.com)
- [Eden Documentation](https://elysiajs.com/eden/overview.html)
- [TypeBox Documentation](https://github.com/sinclairzx81/typebox)
- [Example Client Implementation](./src/client.example.ts)