import { spreads } from "../lib/database/utils"
import { campaigns } from "./campaigns/campaigns.schema"
import { donations } from "./donations/donations.schema"
import {
	donorNotes,
	donorTagAssignments,
	donorTags,
} from "./donors/donors-tags.schema"
import {
	recurringDonations,
	recurringPaymentAttempts,
} from "./recurring-donations/recurring-donations.schema"
import { users } from "./users/users.schema"

export const model = {
	insert: spreads(
		{
			users: users,
			campaigns: campaigns,
			donations: donations,
			recurringDonations: recurringDonations,
			recurringPaymentAttempts: recurringPaymentAttempts,
			donorTags: donorTags,
			donorTagAssignments: donorTagAssignments,
			donorNotes: donorNotes,
		},
		"insert",
	),
	select: spreads(
		{
			users: users,
			campaigns: campaigns,
			donations: donations,
			recurringDonations: recurringDonations,
			recurringPaymentAttempts: recurringPaymentAttempts,
			donorTags: donorTags,
			donorTagAssignments: donorTagAssignments,
			donorNotes: donorNotes,
		},
		"select",
	),
} as const
