import { eq } from "drizzle-orm"
import { db } from "@/db"
import { type CreateUser, users } from "./users.schema"

type FindFilters = {
	email?: string
}

type PaginationOptions = {
	offset?: number
	limit?: number
}

type FindOptions = {
	pagination?: PaginationOptions
}

// NOTE: use service class if it isn't tied to HTTP
// NOTE: use abstract class if it doesn't need to store a property
export abstract class UserService {
	static async findById(id: string) {
		return db.query.users.findFirst({
			where: eq(users.id, id),
		})
	}

	static async find(filters: FindFilters, options: FindOptions = {}) {
		const { pagination } = options
		const { offset = 0, limit = 20 } = pagination ?? {}

		let whereQuery: any

		if (filters.email) {
			whereQuery = eq(users.email, filters.email)
		}

		return db.query.users.findMany({
			where: whereQuery,
			offset,
			limit,
		})
	}

	static async create(data: CreateUser) {
		const results = await db.insert(users).values(data).returning()

		const newUser = results[0]
		if (!newUser) {
			return null
		}

		return await UserService.findById(newUser.id)
	}

	static async update(id: string, data: CreateUser) {
		return db.update(users).set(data).where(eq(users.id, id)).returning()
	}

	static async delete(id: string) {
		return db.delete(users).where(eq(users.id, id)).returning()
	}
}
