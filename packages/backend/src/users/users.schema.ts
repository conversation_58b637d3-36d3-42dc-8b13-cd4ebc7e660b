import { sql } from "drizzle-orm"
import {
	boolean,
	pgEnum,
	pgPolicy,
	pgRole,
	pgTable,
	text,
	timestamp,
} from "drizzle-orm/pg-core"
import type { auth } from "../../lib/auth"

// Define roles for RLS - create if they don't exist
export const pgOrganizerRole = pgRole("organizer")
export const pgAdminRole = pgRole("admin")
export const pgAuthenticatedRole = pgRole("authenticated")
export const pgAnonymousRole = pgRole("anonymous")

export const userRoleEnum = pgEnum("user_role", ["admin", "organizer", "user"])

export const users = pgTable(
	"users",
	{
		id: text("id").primaryKey(),
		name: text("name").notNull(),
		email: text("email").notNull().unique(),
		emailVerified: boolean("email_verified").notNull().default(false),
		image: text("image"),
		role: userRoleEnum("role").notNull().default("user"),
		banned: boolean("banned").notNull().default(false),
		banReason: text("ban_reason"),
		banExpires: timestamp("ban_expires"),
		createdAt: timestamp("created_at").notNull().defaultNow(),
		updatedAt: timestamp("updated_at").notNull().defaultNow(),
	},
	(t) => [
		// Admins can access all users
		pgPolicy("users_admin_access", {
			for: "all",
			to: [pgAdminRole],
			using: sql`current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`current_setting('app.current_user_role', true) = 'admin'`,
		}),
		// Organizers can read user data (for donor management)
		pgPolicy("users_organizer_read", {
			for: "select",
			to: [pgOrganizerRole],
			using: sql`current_setting('app.current_user_role', true) = 'organizer'`,
		}),
		// Users can access their own data
		pgPolicy("users_self_access", {
			for: "all",
			to: [pgAuthenticatedRole],
			using: sql`${t.id} = current_setting('app.current_user_id', true)`,
			withCheck: sql`${t.id} = current_setting('app.current_user_id', true)`,
		}),
	],
)

export const sessions = pgTable(
	"sessions",
	{
		id: text("id").primaryKey(),
		expiresAt: timestamp("expires_at").notNull(),
		token: text("token").notNull().unique(),
		createdAt: timestamp("created_at").notNull(),
		updatedAt: timestamp("updated_at").notNull(),
		ipAddress: text("ip_address"),
		userAgent: text("user_agent"),
		userId: text("user_id")
			.notNull()
			.references(() => users.id, { onDelete: "cascade" }),
		impersonatedBy: text("impersonated_by").references(() => users.id, {
			onDelete: "cascade",
		}),
	},
	(t) => [
		// Admins can access all sessions
		pgPolicy("sessions_admin_access", {
			for: "all",
			to: [pgAdminRole],
			using: sql`current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`current_setting('app.current_user_role', true) = 'admin'`,
		}),
		// Users can access their own sessions
		pgPolicy("sessions_self_access", {
			for: "all",
			to: [pgAuthenticatedRole],
			using: sql`${t.userId} = current_setting('app.current_user_id', true)`,
			withCheck: sql`${t.userId} = current_setting('app.current_user_id', true)`,
		}),
		// Admins can access sessions they impersonated
		pgPolicy("sessions_impersonation_access", {
			for: "all",
			to: [pgAdminRole],
			using: sql`${t.impersonatedBy} = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`${t.impersonatedBy} = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'admin'`,
		}),
	],
)

export const accounts = pgTable(
	"accounts",
	{
		id: text("id").primaryKey(),
		accountId: text("account_id").notNull(),
		providerId: text("provider_id").notNull(),
		userId: text("user_id")
			.notNull()
			.references(() => users.id, { onDelete: "cascade" }),
		accessToken: text("access_token"),
		refreshToken: text("refresh_token"),
		idToken: text("id_token"),
		accessTokenExpiresAt: timestamp("access_token_expires_at"),
		refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
		scope: text("scope"),
		password: text("password"),
		createdAt: timestamp("created_at").notNull(),
		updatedAt: timestamp("updated_at").notNull(),
	},
	(t) => [
		// Admins can access all accounts
		pgPolicy("accounts_admin_access", {
			for: "all",
			to: [pgAdminRole],
			using: sql`current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`current_setting('app.current_user_role', true) = 'admin'`,
		}),
		// Users can access their own accounts
		pgPolicy("accounts_self_access", {
			for: "all",
			to: [pgAuthenticatedRole],
			using: sql`${t.userId} = current_setting('app.current_user_id', true)`,
			withCheck: sql`${t.userId} = current_setting('app.current_user_id', true)`,
		}),
	],
)

export const verifications = pgTable(
	"verifications",
	{
		id: text("id").primaryKey(),
		identifier: text("identifier").notNull(),
		value: text("value").notNull(),
		expiresAt: timestamp("expires_at").notNull(),
		createdAt: timestamp("created_at").notNull().defaultNow(),
		updatedAt: timestamp("updated_at").notNull().defaultNow(),
	},
	(t) => [
		// Only admins can access verification records
		pgPolicy("verifications_admin_access", {
			for: "all",
			to: [pgAdminRole],
			using: sql`current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`current_setting('app.current_user_role', true) = 'admin'`,
		}),
		// System can access verifications for authentication flows
		pgPolicy("verifications_system_access", {
			for: "all",
			to: [pgAnonymousRole, pgAuthenticatedRole],
			using: sql`current_setting('app.current_user_id', true) IS NULL`,
			withCheck: sql`current_setting('app.current_user_id', true) IS NULL`,
		}),
	],
)

// Type exports
export type User = typeof auth.$Infer.Session.user
export type Session = typeof auth.$Infer.Session.session
export type SessionData = typeof auth.$Infer.Session
