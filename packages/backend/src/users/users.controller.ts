import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { authController } from "@/auth/auth.controller"
import { model } from "@/model"
import {
	DEFAULT_LIMIT,
	DEFAULT_OFFSET,
	MAX_LIMIT,
	MIN_LIMIT,
	MIN_OFFSET,
} from "../../lib/constants"
import { UserService } from "./users.service"

export const usersController = new Elysia({ prefix: "/api/users" })
	.use(authController)
	.get(
		"/",
		async ({ query }) => {
			const { offset = DEFAULT_OFFSET, limit = DEFAULT_LIMIT, email } = query

			// TODO: need to add pagination to response
			return await UserService.find(
				{ email },
				{ pagination: { offset: offset, limit: limit } },
			)
		},
		{
			requirePermission: { user: ["view"] },
			query: t.Object({
				limit: t.Optional(
					t.Integer({
						minimum: MIN_LIMIT,
						maximum: MAX_LIMIT,
						default: DEFAULT_LIMIT,
						description: `Number of items per request (between ${MIN_LIMIT} and ${MAX_LIMIT}, defaults to ${DEFAULT_LIMIT})`,
					}),
				),
				offset: t.Optional(
					t.Integer({
						minimum: MIN_OFFSET,
						default: DEFAULT_OFFSET,
						description: `Number of items to skip (at least ${MIN_OFFSET}, defaults to ${DEFAULT_OFFSET})`,
					}),
				),
				email: t.Optional(t.String({ description: "Email address" })),
			}),
			response: {
				[StatusCodes.OK]: t.Array(t.Object(model.select.users)),
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
			detail: {
				summary: "List Users",
				// description: '',
			},
		},
	)
	.get(
		"/:id",
		async ({ params, status }) => {
			const user = await UserService.findById(params.id)

			if (!user) {
				return status(StatusCodes.NOT_FOUND, "User not found")
			}

			return status(StatusCodes.OK, user)
		},
		{
			requirePermission: { user: ["view"] },
			params: t.Object({
				id: t.String({ description: "User ID" }),
			}),
			response: {
				[StatusCodes.OK]: t.Object(model.select.users),
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
			detail: {
				summary: "Get User",
			},
		},
	)
	.post(
		"/",
		async ({ body, status }) => {
			const newUser = await UserService.create(body)

			if (!newUser) {
				throw status(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to create user")
			}

			return status(StatusCodes.CREATED, newUser)
		},
		{
			requirePermission: { user: ["create"] },
			body: t.Object(model.insert.users),
			response: {
				[StatusCodes.CREATED]: t.Object(model.select.users),
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
			detail: {
				summary: "Create User",
				security: [
					{
						tokenAuth: [],
					},
				],
			},
		},
	)
