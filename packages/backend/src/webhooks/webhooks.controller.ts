import Elysia, { t } from "elysia"
import { paymentRouteHelpers, paymentWebhook } from "@/payments/payment.webhook"

// In-memory store for webhook idempotency (in production, use Redis or database)
const processedWebhooks = new Map<
	string,
	{ timestamp: number; status: string }
>()

// Clean up old entries every hour (1 hour = 3600000 ms)
const WEBHOOK_CACHE_TTL = 3600000
setInterval(() => {
	const now = Date.now()
	for (const [key, value] of processedWebhooks.entries()) {
		if (now - value.timestamp > WEBHOOK_CACHE_TTL) {
			processedWebhooks.delete(key)
		}
	}
}, WEBHOOK_CACHE_TTL)

interface WebhookAuditLog {
	webhookId: string
	paymentId: string
	status: string
	reference?: string
	timestamp: Date
	processingResult: "success" | "error" | "duplicate" | "skipped"
	errorMessage?: string
	donationId?: string
	processingTimeMs?: number
}

function logWebhookAudit(auditData: WebhookAuditLog): void {
	const logLevel = auditData.processingResult === "error" ? "error" : "info"

	const logData = {
		webhookId: auditData.webhookId,
		paymentId: auditData.paymentId,
		status: auditData.status,
		reference: auditData.reference,
		timestamp: auditData.timestamp.toISOString(),
		processingResult: auditData.processingResult,
		errorMessage: auditData.errorMessage,
		donationId: auditData.donationId,
		processingTimeMs: auditData.processingTimeMs,
	}

	if (logLevel === "error") {
		console.error("Webhook audit log - Error", logData)
	} else {
		console.info("Webhook audit log - Success", logData)
	}
}

export const webhooksController = new Elysia({ prefix: "/webhooks" })
	.onError(() => {})
	.use(paymentWebhook)
	.post(
		"/payments/chip",
		async ({ body, headers, handlePaymentWebhook }) => {
			// Validate webhook headers
			const { signature } = paymentRouteHelpers.validateWebhookHeaders(headers)

			// Handle the webhook - signature verification and routing is automatic
			const result = await handlePaymentWebhook(body, signature)

			return result
		},
		{
			// Accept raw body for signature verification
			body: t.String(),
			response: {
				200: t.Object({
					success: t.Boolean(),
					message: t.String(),
				}),
			},
		},
	)
	.get("/payments/test", async () => {
		// Test endpoint for webhook setup
		return {
			success: true,
			message: "Webhook endpoint is working",
			timestamp: new Date().toISOString(),
		}
	})
