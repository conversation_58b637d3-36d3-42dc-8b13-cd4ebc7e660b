import { StatusCodes } from "http-status-codes"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { AppError, ErrorCode } from "../../lib/error-handler"

// Mock the ChipService
const mockChipService = {
	verifyWebhookSignature: vi.fn(),
	handleDonationCompleted: vi.fn(),
	handleDonationFailed: vi.fn(),
	handleDonationCanceled: vi.fn(),
}

// Mock the database
const mockDb = {
	select: vi.fn().mockReturnThis(),
	from: vi.fn().mockReturnThis(),
	where: vi.fn().mockReturnThis(),
	limit: vi.fn().mockResolvedValue([]),
}

// Mock console methods
const mockConsole = {
	error: vi.fn(),
	warn: vi.fn(),
	info: vi.fn(),
}

beforeEach(() => {
	vi.clearAllMocks()

	// Mock modules
	vi.doMock("../../payments/payments.service", () => ({
		ChipService: mockChipService,
	}))

	vi.doMock("../../db", () => ({
		db: mockDb,
	}))

	// Mock console methods
	vi.spyOn(console, "error").mockImplementation(mockConsole.error)
	vi.spyOn(console, "warn").mockImplementation(mockConsole.warn)
	vi.spyOn(console, "info").mockImplementation(mockConsole.info)
})

afterEach(() => {
	vi.restoreAllMocks()
})

describe("Webhook Controller Error Handling", () => {
	describe("Request Body Validation", () => {
		it("should handle empty request body", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			const mockRequest = {
				text: vi.fn().mockResolvedValue(""),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.BAD_REQUEST,
				expect.objectContaining({
					error: expect.stringContaining("Request body is empty"),
				}),
			)
		})

		it("should handle request body read failure", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			const mockRequest = {
				text: vi.fn().mockRejectedValue(new Error("Failed to read body")),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.BAD_REQUEST,
				expect.objectContaining({
					error: expect.stringContaining("Failed to read webhook request body"),
				}),
			)
		})

		it("should handle missing signature header", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			const mockRequest = {
				text: vi.fn().mockResolvedValue('{"id": "test", "status": "paid"}'),
				headers: new Map(), // No signature header
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.BAD_REQUEST,
				expect.objectContaining({
					error: expect.stringContaining("Webhook signature missing"),
				}),
			)
		})

		it("should handle invalid JSON payload", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)

			const mockRequest = {
				text: vi.fn().mockResolvedValue("invalid json {"),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.BAD_REQUEST,
				expect.objectContaining({
					error: expect.stringContaining("Invalid JSON payload"),
				}),
			)
		})

		it("should handle missing required fields in payload", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)

			const mockRequest = {
				text: vi.fn().mockResolvedValue('{"status": "paid"}'), // Missing id
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.BAD_REQUEST,
				expect.objectContaining({
					error: expect.stringContaining("Webhook missing required fields"),
				}),
			)
		})
	})

	describe("Signature Verification", () => {
		it("should handle signature verification failure", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(false)

			const mockRequest = {
				text: vi.fn().mockResolvedValue('{"id": "test", "status": "paid"}'),
				headers: new Map([["x-signature", "invalid-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.UNAUTHORIZED,
				expect.objectContaining({
					error: expect.stringContaining(
						"Webhook signature verification failed",
					),
				}),
			)
		})

		it("should handle signature verification throwing error", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockRejectedValue(
				new AppError(
					"Signature verification error",
					ErrorCode.WEBHOOK_SIGNATURE_INVALID,
					StatusCodes.INTERNAL_SERVER_ERROR,
				),
			)

			const mockRequest = {
				text: vi.fn().mockResolvedValue('{"id": "test", "status": "paid"}'),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.UNAUTHORIZED,
				expect.objectContaining({
					error: expect.stringContaining(
						"Webhook signature verification failed",
					),
				}),
			)
		})
	})

	describe("Webhook Processing", () => {
		it("should handle duplicate webhook processing", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)

			const validPayload =
				'{"id": "payment_123", "status": "paid", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue({ success: true })
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			// First call should process normally
			await webhooksController.handle(context as any)

			// Second call with same payload should detect duplicate
			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenLastCalledWith(
				StatusCodes.OK,
				expect.objectContaining({
					success: true,
					message: "Webhook already processed",
				}),
			)
		})

		it("should handle donation processing errors", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)
			mockChipService.handleDonationCompleted.mockRejectedValue(
				new AppError(
					"Donation processing failed",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
				),
			)

			const validPayload =
				'{"id": "payment_456", "status": "paid", "reference": "donation_789"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(
				StatusCodes.INTERNAL_SERVER_ERROR,
				expect.objectContaining({
					error: "Webhook processing failed",
				}),
			)
		})

		it("should handle informational webhook statuses", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)

			const validPayload =
				'{"id": "payment_123", "status": "created", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue({ success: true })
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(StatusCodes.OK, { success: true })
			expect(mockConsole.info).toHaveBeenCalledWith(
				expect.stringContaining("Received informational webhook status"),
				expect.any(Object),
			)
		})

		it("should handle unknown webhook statuses", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)

			const validPayload =
				'{"id": "payment_123", "status": "unknown_status", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue({ success: true })
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockStatus).toHaveBeenCalledWith(StatusCodes.OK, { success: true })
			expect(mockConsole.warn).toHaveBeenCalledWith(
				expect.stringContaining("Received webhook with unhandled status"),
				expect.any(Object),
			)
		})
	})

	describe("Audit Logging", () => {
		it("should log successful webhook processing with timing", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)
			mockChipService.handleDonationCompleted.mockResolvedValue(undefined)

			const validPayload =
				'{"id": "payment_123", "status": "paid", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue({ success: true })
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockConsole.info).toHaveBeenCalledWith(
				"Webhook audit log - Success",
				expect.objectContaining({
					webhookId: expect.any(String),
					paymentId: "payment_123",
					status: "paid",
					reference: "donation_456",
					processingResult: "success",
					processingTimeMs: expect.any(Number),
				}),
			)
		})

		it("should log failed webhook processing with error details", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)
			mockChipService.handleDonationCompleted.mockRejectedValue(
				new Error("Processing failed"),
			)

			const validPayload =
				'{"id": "payment_123", "status": "paid", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue("error response")
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockConsole.error).toHaveBeenCalledWith(
				"Webhook audit log - Error",
				expect.objectContaining({
					webhookId: expect.any(String),
					paymentId: "payment_123",
					status: "paid",
					processingResult: "error",
					errorMessage: "Processing failed",
					processingTimeMs: expect.any(Number),
				}),
			)
		})

		it("should attempt to retrieve donation ID for audit logging", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)
			mockChipService.handleDonationCompleted.mockResolvedValue(undefined)

			// Mock successful donation lookup
			mockDb.limit.mockResolvedValue([{ id: "donation_456" }])

			const validPayload =
				'{"id": "payment_123", "status": "paid", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue({ success: true })
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			expect(mockConsole.info).toHaveBeenCalledWith(
				"Webhook audit log - Success",
				expect.objectContaining({
					donationId: "donation_456",
				}),
			)
		})

		it("should handle donation lookup failure gracefully in audit logging", async () => {
			const { webhooksController } = await import("../webhooks.controller")

			mockChipService.verifyWebhookSignature.mockResolvedValue(true)
			mockChipService.handleDonationCompleted.mockResolvedValue(undefined)

			// Mock donation lookup failure
			mockDb.limit.mockRejectedValue(new Error("Database error"))

			const validPayload =
				'{"id": "payment_123", "status": "paid", "reference": "donation_456"}'

			const mockRequest = {
				text: vi.fn().mockResolvedValue(validPayload),
				headers: new Map([["x-signature", "test-signature"]]),
			}

			const mockStatus = vi.fn().mockReturnValue({ success: true })
			const context = {
				request: mockRequest,
				status: mockStatus,
			}

			await webhooksController.handle(context as any)

			// Should still complete successfully despite donation lookup failure
			expect(mockStatus).toHaveBeenCalledWith(StatusCodes.OK, { success: true })

			// Should log warning about donation lookup failure
			expect(mockConsole.warn).toHaveBeenCalledWith(
				expect.stringContaining("Could not retrieve donation ID for audit log"),
				expect.any(Object),
			)
		})
	})
})
