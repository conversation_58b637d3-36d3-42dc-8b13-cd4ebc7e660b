import { Elysia } from "elysia"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import type { ChipPaymentResponse } from "@/payments/payments.schema"

describe("Webhooks Integration Tests", () => {
	let app: Elysia

	beforeEach(async () => {
		// Mock console methods to reduce test noise
		console.info = vi.fn()
		console.error = vi.fn()
		console.warn = vi.fn()

		// Set up environment variables
		process.env.CHIP_BASE_URL = "https://test.chip-in.asia/api/v1"
		process.env.CHIP_SECRET_KEY = "test_secret_key"
		process.env.CHIP_BRAND_ID = "test_brand_id"
		process.env.CHIP_WEBHOOK_SECRET = "test_public_key"

		// Import and setup app after environment is configured
		const { webhooksController } = await import("../webhooks.controller")
		app = new Elysia().use(webhooksController)
	})

	afterEach(() => {
		vi.restoreAllMocks()
	})

	const createValidWebhookPayload = (
		overrides: Partial<ChipPaymentResponse> = {},
	): ChipPaymentResponse =>
		({
			id: "payment_123",
			status: "paid",
			reference: "donation_123",
			purchase: {
				products: [
					{
						name: "Test Donation",
						price: 5000,
						category: "donation",
						quantity: "1",
					},
				],
			},
			...overrides,
		}) as ChipPaymentResponse

	describe("POST /webhooks/chip - Basic Request Validation", () => {
		it("should reject webhook without signature", async () => {
			const payload = createValidWebhookPayload()

			const response = await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(payload),
				}),
			)

			expect(response.status).toBe(400)
			const responseText = await response.text()
			expect(responseText).toContain("Signature not found")
		})

		it("should handle malformed request bodies gracefully", async () => {
			const response = await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "valid-signature",
					},
					body: null as any,
				}),
			)

			expect(response.status).toBe(400)
		})

		it("should process webhook endpoint and return appropriate response", async () => {
			const payload = createValidWebhookPayload()

			const response = await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "test-signature",
					},
					body: JSON.stringify(payload),
				}),
			)

			// Should process the request (even if signature verification fails)
			expect([400, 401, 500]).toContain(response.status)
		})
	})

	describe("Webhook Processing Flow", () => {
		it("should attempt signature verification for valid requests", async () => {
			const payload = createValidWebhookPayload({ status: "paid" })

			await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "valid-signature",
					},
					body: JSON.stringify(payload),
				}),
			)

			// Verify signature verification logging occurred
			expect(console.info).toHaveBeenCalledWith(
				expect.stringContaining("Verifying Chip webhook signature"),
			)
		})

		it("should handle webhook requests with proper structure", async () => {
			const statuses = ["paid", "failed", "error", "canceled"]

			for (const status of statuses) {
				const payload = createValidWebhookPayload({
					id: `payment_${status}`,
					status: status as any,
				})

				const response = await app.handle(
					new Request("http://localhost/webhooks/chip", {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							"x-signature": "test-signature",
						},
						body: JSON.stringify(payload),
					}),
				)

				// Should process the request structure
				expect(response.status).toBeGreaterThanOrEqual(200)
				expect(response.status).toBeLessThan(600)
			}
		})
	})

	describe("Request Processing", () => {
		it("should process webhook requests consistently", async () => {
			const payload = createValidWebhookPayload()

			// Make multiple requests
			const response1 = await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "signature1",
					},
					body: JSON.stringify(payload),
				}),
			)

			const response2 = await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "signature2",
					},
					body: JSON.stringify(payload),
				}),
			)

			// Both should be processed
			expect(response1.status).toBeGreaterThanOrEqual(200)
			expect(response2.status).toBeGreaterThanOrEqual(200)
		})

		it("should validate webhook payload structure before processing", async () => {
			const incompletePayload = {
				id: "payment_123",
				// Missing status field
			}

			const response = await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "valid-signature",
					},
					body: JSON.stringify(incompletePayload),
				}),
			)

			// Should attempt to process but may fail validation
			expect(response.status).toBeGreaterThanOrEqual(200)
		})
	})

	describe("Logging and Monitoring", () => {
		it("should log webhook processing attempts", async () => {
			const payload = createValidWebhookPayload({ status: "paid" })

			await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						"x-signature": "test-signature",
					},
					body: JSON.stringify(payload),
				}),
			)

			// Verify signature verification logging occurred
			expect(console.info).toHaveBeenCalledWith(
				expect.stringContaining("Verifying Chip webhook signature"),
			)
		})

		it("should log errors with proper context", async () => {
			const payload = createValidWebhookPayload()

			await app.handle(
				new Request("http://localhost/webhooks/chip", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						// Missing signature
					},
					body: JSON.stringify(payload),
				}),
			)

			// Verify error logging for missing signature
			expect(console.error).toHaveBeenCalledWith(
				expect.stringContaining("Webhook signature missing"),
				expect.objectContaining({
					webhookId: expect.any(String),
				}),
			)
		})
	})
})
