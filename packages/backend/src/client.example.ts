/**
 * Example client demonstrating end-to-end type safety with Eden
 * This file shows how to consume the DonorCare API with full type safety
 */

import { treaty } from "@elysiajs/eden"
import type { App } from "./index"

// Create a type-safe client
const api = treaty<App>("http://localhost:3000")

// Example usage with full type safety
export async function exampleUsage() {
	try {
		// 1. Health check - simple GET request
		const { data: health, error: healthError } = await api.health.get()
		if (healthError) {
			console.error("Health check failed:", healthError)
			return
		}
		console.log("Health status:", health)

		// 2. Create a campaign (requires authentication)
		const { data: campaign, error: campaignError } =
			await api.api.campaigns.post(
				{
					name: "Help Local Community",
					description: "Supporting families in need during difficult times",
					slug: "help-local-community-2024",
				},
				{
					headers: {
						authorization: "Bearer your-auth-token-here",
					},
				},
			)

		if (campaignError) {
			console.error("Campaign creation failed:", campaignError)
			return
		}
		console.log("Created campaign:", campaign)

		// 3. Get public campaign info
		const { data: publicCampaign, error: publicError } = await api.api
			.campaigns({ slug: "help-local-community-2024" })
			.get()
		if (publicError) {
			console.error("Failed to get public campaign:", publicError)
			return
		}
		console.log("Public campaign:", publicCampaign)

		// 4. Initiate a donation
		const { data: donation, error: donationError } =
			await api.api.donations.initiate.post({
				campaignSlug: "help-local-community-2024",
				amount: "50.00",
				donorName: "John Doe",
				donorEmail: "<EMAIL>",
				message: "Happy to help!",
			})

		if (donationError) {
			console.error("Donation initiation failed:", donationError)
			return
		}
		console.log("Donation initiated:", donation)

		// 5. Get donations list (organizer only)
		const { data: donations, error: donationsError } =
			await api.api.donations.get({
				query: {
					status: "completed",
					sortBy: "createdAt",
					sortOrder: "desc",
					page: 1,
					limit: 10,
				},
				headers: {
					authorization: "Bearer organizer-auth-token-here",
				},
			})

		if (donationsError) {
			console.error("Failed to get donations:", donationsError)
			return
		}
		console.log("Donations:", donations)

		// 6. Get donation analytics
		const { data: analytics, error: analyticsError } =
			await api.api.donations.analytics.get({
				query: {
					status: "all",
					dateFrom: "2024-01-01",
					dateTo: "2024-12-31",
				},
				headers: {
					authorization: "Bearer organizer-auth-token-here",
				},
			})

		if (analyticsError) {
			console.error("Failed to get analytics:", analyticsError)
			return
		}
		console.log("Analytics:", analytics)

		// 7. Update donation status
		if (donation?.donation?.id) {
			const { data: updatedDonation, error: updateError } = await api.api
				.donations({ id: donation.donation.id })
				.status.patch(
					{
						status: "completed",
					},
					{
						headers: {
							authorization: "Bearer organizer-auth-token-here",
						},
					},
				)

			if (updateError) {
				console.error("Failed to update donation:", updateError)
				return
			}
			console.log("Updated donation:", updatedDonation)
		}
	} catch (error) {
		console.error("Unexpected error:", error)
	}
}

// Type-safe error handling example
export async function handleErrorsExample() {
	const { data, error } = await api.api.campaigns.post({
		name: "Test Campaign",
		description: "Test description",
		slug: "test-campaign",
	})

	if (error) {
		// Error is fully typed based on the response schema
		switch (error.status) {
			case 400:
				console.error("Bad request:", error.value)
				break
			case 401:
				console.error("Unauthorized:", error.value)
				break
			case 403:
				console.error("Forbidden:", error.value)
				break
			case 500:
				console.error("Server error:", error.value)
				break
			default:
				console.error("Unknown error:", error.value)
		}
		return
	}

	// Data is fully typed when no error
	console.log("Campaign created successfully:", data.campaign.name)
}

// WebSocket example (if implemented)
export async function websocketExample() {
	// This would be used for real-time donation updates
	// const ws = api.ws.donations.subscribe()
	//
	// ws.subscribe((message) => {
	//   console.log('New donation:', message)
	// })
	//
	// ws.send({ type: 'subscribe', campaignId: 'campaign-id' })
}

// Export the typed client for use in other files
export { api }
export type { App }
