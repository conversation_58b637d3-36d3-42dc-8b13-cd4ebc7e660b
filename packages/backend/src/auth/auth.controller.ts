import Elysia from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { createDatabaseContext, setDatabaseContext } from "@/lib/db-context"
import { auth } from "../../lib/auth"

export const authController = new Elysia({ name: "better-auth" })
	.mount(auth.handler)
	.macro({
		auth: {
			async resolve({ status, request: { headers } }) {
				const session = await auth.api.getSession({
					headers,
				})

				if (!session) {
					return status(StatusCodes.UNAUTHORIZED, "Authentication required")
				}

				// Set database context for RLS policies
				const dbContext = createDatabaseContext(session.user)
				await setDatabaseContext(db, dbContext)

				return {
					user: session.user,
					session: session.session,
				}
			},
		},
		requireRole: (roles: string[]) => ({
			async resolve({ status, request: { headers } }) {
				const session = await auth.api.getSession({
					headers,
				})

				if (!session) {
					return status(StatusCodes.UNAUTHORIZED, "Authentication required")
				}

				const hasRole = roles.includes(session.user.role ?? "")

				if (!hasRole) {
					return status(
						StatusCodes.FORBIDDEN,
						`${roles.join(", ")} access required`,
					)
				}

				// Set database context for RLS policies
				const dbContext = createDatabaseContext(session.user)
				await setDatabaseContext(db, dbContext)

				return {
					user: session.user,
					session: session.session,
				}
			},
		}),
		requirePermission: (permissions: Record<string, string[]>) => ({
			async resolve({ status, request: { headers } }) {
				const session = await auth.api.getSession({
					headers,
				})

				if (!session) {
					return status(StatusCodes.UNAUTHORIZED, "Authentication required")
				}

				// Use Better Auth's permission checking
				const hasPermission = await auth.api.userHasPermission({
					body: {
						userId: session.user.id,
						permissions,
					},
				})

				if (!hasPermission) {
					return status(StatusCodes.FORBIDDEN, "Insufficient permissions")
				}

				// Set database context for RLS policies
				const dbContext = createDatabaseContext(session.user)
				await setDatabaseContext(db, dbContext)

				return {
					user: session.user,
					session: session.session,
				}
			},
		}),
	})
