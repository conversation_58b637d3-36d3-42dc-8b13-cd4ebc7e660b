import { StatusCodes } from "http-status-codes"
import { AppError, ErrorCode } from "./error-handler"

/**
 * Configuration for redirect URLs with campaign and donation context
 */
export interface RedirectUrlConfig {
	baseUrl: string
	successPath?: string
	failurePath?: string
	cancelPath?: string
	fallbackSuccessPath?: string
	fallbackFailurePath?: string
	fallbackCancelPath?: string
}

/**
 * Context information for generating redirect URLs
 */
export interface RedirectContext {
	campaignId: string
	campaignSlug: string
	donationId: string
	paymentId?: string
	errorCode?: string
	errorMessage?: string
	isRecurring?: boolean
}

/**
 * Generated redirect URLs for payment processing
 */
export interface RedirectUrls {
	successRedirectUrl: string
	failureRedirectUrl: string
	cancelRedirectUrl: string
}

/**
 * Default redirect URL configuration
 */
const DEFAULT_CONFIG: Required<Omit<RedirectUrlConfig, "baseUrl">> = {
	successPath: "/donation/success",
	failurePath: "/donation/failed",
	cancelPath: "/campaigns",
	fallbackSuccessPath: "/",
	fallbackFailurePath: "/",
	fallbackCancelPath: "/campaigns",
}

/**
 * Utility class for generating redirect URLs with campaign and donation context
 */
export class RedirectUrlGenerator {
	private config: RedirectUrlConfig

	constructor(config: RedirectUrlConfig) {
		this.config = {
			...DEFAULT_CONFIG,
			...config,
		}

		// Validate base URL
		if (!this.config.baseUrl || typeof this.config.baseUrl !== "string") {
			throw new AppError(
				"Base URL is required for redirect URL generation",
				ErrorCode.PAYMENT_VALIDATION_ERROR,
				StatusCodes.INTERNAL_SERVER_ERROR,
				{ providedBaseUrl: this.config.baseUrl },
			)
		}

		// Validate base URL format
		try {
			new URL(this.config.baseUrl)
		} catch {
			throw new AppError(
				"Base URL must be a valid URL",
				ErrorCode.PAYMENT_VALIDATION_ERROR,
				StatusCodes.INTERNAL_SERVER_ERROR,
				{ providedBaseUrl: this.config.baseUrl },
			)
		}
	}

	/**
	 * Generates all redirect URLs with campaign and donation context
	 */
	generateRedirectUrls(context: RedirectContext): RedirectUrls {
		return {
			successRedirectUrl: this.generateSuccessUrl(context),
			failureRedirectUrl: this.generateFailureUrl(context),
			cancelRedirectUrl: this.generateCancelUrl(context),
		}
	}

	/**
	 * Generates success redirect URL with donation confirmation context
	 */
	generateSuccessUrl(context: RedirectContext): string {
		const path = this.config.successPath ?? this.config.fallbackSuccessPath!
		const url = new URL(path, this.config.baseUrl)

		// Add campaign and donation context
		url.searchParams.set("campaign", context.campaignSlug)
		url.searchParams.set("donation", context.donationId)

		if (context.paymentId) {
			url.searchParams.set("payment", context.paymentId)
		}

		// Add success indicator
		url.searchParams.set("status", "success")

		// Add recurring indicator if applicable
		if (context.isRecurring) {
			url.searchParams.set("type", "recurring")
		}

		return url.toString()
	}

	/**
	 * Generates failure redirect URL with error information
	 */
	generateFailureUrl(context: RedirectContext): string {
		const path = this.config.failurePath ?? this.config.fallbackFailurePath!
		const url = new URL(path, this.config.baseUrl)

		// Add campaign and donation context
		url.searchParams.set("campaign", context.campaignSlug)
		url.searchParams.set("donation", context.donationId)

		if (context.paymentId) {
			url.searchParams.set("payment", context.paymentId)
		}

		// Add failure information
		url.searchParams.set("status", "failed")

		if (context.errorCode) {
			url.searchParams.set("error_code", context.errorCode)
		}

		if (context.errorMessage) {
			url.searchParams.set(
				"error_message",
				encodeURIComponent(context.errorMessage),
			)
		}

		// Add recurring indicator if applicable
		if (context.isRecurring) {
			url.searchParams.set("type", "recurring")
		}

		return url.toString()
	}

	/**
	 * Generates cancel redirect URL to return to campaign page
	 */
	generateCancelUrl(context: RedirectContext): string {
		const path = this.config.cancelPath ?? this.config.fallbackCancelPath!

		// For cancel, redirect to campaign page or campaigns list
		let url: URL
		if (path && (path === "/campaigns" || path.startsWith("/campaigns/"))) {
			// If path is campaigns list or specific campaign, use campaign slug
			url = new URL(`/campaigns/${context.campaignSlug}`, this.config.baseUrl)
			// Add cancel status
			url.searchParams.set("status", "canceled")
			if (context.donationId) {
				url.searchParams.set("donation", context.donationId)
			}
		} else {
			// Use configured cancel path or fallback
			const finalPath = path || "/"
			url = new URL(finalPath, this.config.baseUrl)
			url.searchParams.set("campaign", context.campaignSlug)
			// Add cancel status
			url.searchParams.set("status", "canceled")
			if (context.donationId) {
				url.searchParams.set("donation", context.donationId)
			}
		}

		// Add recurring indicator if applicable
		if (context.isRecurring) {
			url.searchParams.set("type", "recurring")
		}

		return url.toString()
	}

	/**
	 * Creates a redirect URL generator from environment variables
	 */
	static fromEnvironment(): RedirectUrlGenerator {
		const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3002"

		const config: RedirectUrlConfig = {
			baseUrl: frontendUrl,
		}

		// Only set properties if they are defined in environment
		if (process.env.REDIRECT_SUCCESS_PATH) {
			config.successPath = process.env.REDIRECT_SUCCESS_PATH
		}
		if (process.env.REDIRECT_FAILURE_PATH) {
			config.failurePath = process.env.REDIRECT_FAILURE_PATH
		}
		if (process.env.REDIRECT_CANCEL_PATH) {
			config.cancelPath = process.env.REDIRECT_CANCEL_PATH
		}
		if (process.env.REDIRECT_FALLBACK_SUCCESS_PATH) {
			config.fallbackSuccessPath = process.env.REDIRECT_FALLBACK_SUCCESS_PATH
		}
		if (process.env.REDIRECT_FALLBACK_FAILURE_PATH) {
			config.fallbackFailurePath = process.env.REDIRECT_FALLBACK_FAILURE_PATH
		}
		if (process.env.REDIRECT_FALLBACK_CANCEL_PATH) {
			config.fallbackCancelPath = process.env.REDIRECT_FALLBACK_CANCEL_PATH
		}

		return new RedirectUrlGenerator(config)
	}

	/**
	 * Validates that all required environment variables are present
	 */
	static validateEnvironmentConfiguration(): void {
		const frontendUrl = process.env.FRONTEND_URL

		// Allow fallback to default localhost for development
		if (!frontendUrl) {
			console.warn("No FRONTEND_URL configured, using default localhost:3002")
			return
		}

		// Validate URL format
		try {
			new URL(frontendUrl)
		} catch {
			throw new AppError(
				"FRONTEND_URL must be a valid URL",
				ErrorCode.PAYMENT_VALIDATION_ERROR,
				StatusCodes.INTERNAL_SERVER_ERROR,
				{ providedUrl: frontendUrl },
			)
		}
	}
}
