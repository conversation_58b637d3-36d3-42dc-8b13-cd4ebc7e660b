import { sql } from "drizzle-orm"
import type { Database } from "@/db"
import type { User } from "@/users/users.schema"

// Only the fields needed to build database context
// Allow role to be optional to accommodate session users without a role
type UserForContext = { id: User["id"]; role?: User["role"] }

export interface DatabaseContext {
	userId?: User["id"]
	userRole?: User["role"]
	organizerId?: string
}

/**
 * Sets the database session context for Row Level Security policies
 * This must be called before any database operations that need RLS protection
 */
export async function setDatabaseContext(
	db: Database,
	context: DatabaseContext,
): Promise<void> {
	const queries: Promise<any>[] = []

	// Set current user ID (for donor access to their own donations)
	if (context.userId) {
		queries.push(
			db.execute(
				sql`SELECT set_config('app.current_user_id', ${context.userId}, false)`,
			),
		)
	} else {
		queries.push(
			db.execute(sql`SELECT set_config('app.current_user_id', NULL, false)`),
		)
	}

	// Set current user role (for admin vs regular user access)
	if (context.userRole) {
		queries.push(
			db.execute(
				sql`SELECT set_config('app.current_user_role', ${context.userRole}, false)`,
			),
		)
	} else {
		queries.push(
			db.execute(sql`SELECT set_config('app.current_user_role', NULL, false)`),
		)
	}

	// Set current organizer ID (for organizer-specific data access)
	if (context.organizerId) {
		queries.push(
			db.execute(
				sql`SELECT set_config('app.current_organizer_id', ${context.organizerId}, false)`,
			),
		)
	} else {
		queries.push(
			db.execute(
				sql`SELECT set_config('app.current_organizer_id', NULL, false)`,
			),
		)
	}

	// Execute all context setting queries in parallel
	await Promise.all(queries)
}

/**
 * Creates database context from user session
 * Determines the appropriate context based on user role and operation
 */
export function createDatabaseContext(user: UserForContext): DatabaseContext {
	const context: DatabaseContext = {
		userId: user.id,
		userRole: user.role,
	}

	// For organizers, set organizer context to their own ID
	if (user.role === "organizer") {
		context.organizerId = user.id
	}

	// For admins, don't set organizerId so they can access all data
	// (RLS policies check is_admin() function)

	return context
}

/**
 * Clears the database context (sets all session variables to NULL)
 * Useful for public operations like donation initiation
 */
export async function clearDatabaseContext(db: Database): Promise<void> {
	await Promise.all([
		db.execute(sql`SELECT set_config('app.current_user_id', NULL, false)`),
		db.execute(sql`SELECT set_config('app.current_user_role', NULL, false)`),
		db.execute(sql`SELECT set_config('app.current_organizer_id', NULL, false)`),
	])
}

/**
 * Executes a database operation with the given context
 * Automatically sets and clears context around the operation
 */
export async function withDatabaseContext<T>(
	db: Database,
	context: DatabaseContext | null,
	operation: () => Promise<T>,
): Promise<T> {
	try {
		if (context) {
			await setDatabaseContext(db, context)
		} else {
			await clearDatabaseContext(db)
		}

		return await operation()
	} finally {
		// Always clear context after operation to prevent leakage
		await clearDatabaseContext(db)
	}
}

/**
 * Convenience wrapper for executing operations with user context
 */
export async function withUserContext<T>(
	db: Database,
	user: User | null,
	operation: () => Promise<T>,
): Promise<T> {
	const context = user ? createDatabaseContext(user) : null
	return withDatabaseContext(db, context, operation)
}

/**
 * Convenience wrapper for public operations (no user context)
 */
export async function withPublicContext<T>(
	db: Database,
	operation: () => Promise<T>,
): Promise<T> {
	return withDatabaseContext(db, null, operation)
}
