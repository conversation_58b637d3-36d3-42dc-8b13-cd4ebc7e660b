import { afterEach, beforeEach, describe, expect, it } from "vitest"
import { AppError } from "../error-handler"
import { type RedirectContext, RedirectUrlGenerator } from "../redirect-urls"

describe("RedirectUrlGenerator", () => {
	let originalEnv: NodeJS.ProcessEnv

	beforeEach(() => {
		originalEnv = { ...process.env }
	})

	afterEach(() => {
		process.env = originalEnv
	})

	describe("constructor", () => {
		it("should create generator with valid base URL", () => {
			const generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
			})
			expect(generator).toBeDefined()
		})

		it("should throw error for missing base URL", () => {
			expect(() => {
				new RedirectUrlGenerator({
					baseUrl: "",
				})
			}).toThrow(AppError)
		})

		it("should throw error for invalid base URL", () => {
			expect(() => {
				new RedirectUrlGenerator({
					baseUrl: "not-a-url",
				})
			}).toThrow(AppError)
		})

		it("should use default paths when not provided", () => {
			const generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
			})

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const urls = generator.generateRedirectUrls(context)

			expect(urls.successRedirectUrl).toContain("/donation/success")
			expect(urls.failureRedirectUrl).toContain("/donation/failed")
			expect(urls.cancelRedirectUrl).toContain("/campaigns/test-campaign")
		})
	})

	describe("generateSuccessUrl", () => {
		let generator: RedirectUrlGenerator

		beforeEach(() => {
			generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
				successPath: "/donation/success",
			})
		})

		it("should generate success URL with campaign and donation context", () => {
			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateSuccessUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.origin).toBe("https://example.com")
			expect(parsedUrl.pathname).toBe("/donation/success")
			expect(parsedUrl.searchParams.get("campaign")).toBe("test-campaign")
			expect(parsedUrl.searchParams.get("donation")).toBe("don-456")
			expect(parsedUrl.searchParams.get("status")).toBe("success")
		})

		it("should include payment ID when provided", () => {
			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
				paymentId: "pay-789",
			}

			const url = generator.generateSuccessUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.searchParams.get("payment")).toBe("pay-789")
		})

		it("should use fallback path when success path is not configured", () => {
			const generatorWithFallback = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
				successPath: undefined, // Explicitly set to undefined
				fallbackSuccessPath: "/home",
			})

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generatorWithFallback.generateSuccessUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.pathname).toBe("/home")
		})
	})

	describe("generateFailureUrl", () => {
		let generator: RedirectUrlGenerator

		beforeEach(() => {
			generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
				failurePath: "/donation/failed",
			})
		})

		it("should generate failure URL with error information", () => {
			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
				errorCode: "PAYMENT_FAILED",
				errorMessage: "Payment was declined",
			}

			const url = generator.generateFailureUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.origin).toBe("https://example.com")
			expect(parsedUrl.pathname).toBe("/donation/failed")
			expect(parsedUrl.searchParams.get("campaign")).toBe("test-campaign")
			expect(parsedUrl.searchParams.get("donation")).toBe("don-456")
			expect(parsedUrl.searchParams.get("status")).toBe("failed")
			expect(parsedUrl.searchParams.get("error_code")).toBe("PAYMENT_FAILED")
			expect(parsedUrl.searchParams.get("error_message")).toBe(
				"Payment%20was%20declined",
			)
		})

		it("should handle missing error information gracefully", () => {
			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateFailureUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.searchParams.get("status")).toBe("failed")
			expect(parsedUrl.searchParams.has("error_code")).toBe(false)
			expect(parsedUrl.searchParams.has("error_message")).toBe(false)
		})
	})

	describe("generateCancelUrl", () => {
		it("should generate cancel URL to campaign page", () => {
			const generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
				cancelPath: "/campaigns",
			})

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateCancelUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.origin).toBe("https://example.com")
			expect(parsedUrl.pathname).toBe("/campaigns/test-campaign")
			expect(parsedUrl.searchParams.get("status")).toBe("canceled")
			expect(parsedUrl.searchParams.get("donation")).toBe("don-456")
		})

		it("should use custom cancel path when configured", () => {
			const generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
				cancelPath: "/custom/cancel",
			})

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateCancelUrl(context)
			const parsedUrl = new URL(url)

			expect(parsedUrl.pathname).toBe("/custom/cancel")
			expect(parsedUrl.searchParams.get("campaign")).toBe("test-campaign")
		})
	})

	describe("fromEnvironment", () => {
		it("should create generator from FRONTEND_URL", () => {
			process.env.FRONTEND_URL = "https://frontend.example.com"

			const generator = RedirectUrlGenerator.fromEnvironment()

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateSuccessUrl(context)
			expect(url).toContain("https://frontend.example.com")
		})

		it("should fallback to BETTER_AUTH_URL when FRONTEND_URL is not set", () => {
			delete process.env.FRONTEND_URL
			process.env.BETTER_AUTH_URL = "https://auth.example.com"

			const generator = RedirectUrlGenerator.fromEnvironment()

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateSuccessUrl(context)
			expect(url).toContain("https://auth.example.com")
		})

		it("should use default localhost when no environment URLs are set", () => {
			delete process.env.FRONTEND_URL
			delete process.env.BETTER_AUTH_URL

			const generator = RedirectUrlGenerator.fromEnvironment()

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const url = generator.generateSuccessUrl(context)
			expect(url).toContain("http://localhost:3002")
		})

		it("should use custom redirect paths from environment", () => {
			process.env.FRONTEND_URL = "https://example.com"
			process.env.REDIRECT_SUCCESS_PATH = "/custom/success"
			process.env.REDIRECT_FAILURE_PATH = "/custom/failure"
			process.env.REDIRECT_CANCEL_PATH = "/custom/cancel"

			const generator = RedirectUrlGenerator.fromEnvironment()

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
			}

			const urls = generator.generateRedirectUrls(context)

			expect(urls.successRedirectUrl).toContain("/custom/success")
			expect(urls.failureRedirectUrl).toContain("/custom/failure")
			expect(urls.cancelRedirectUrl).toContain("/custom/cancel")
		})
	})

	describe("validateEnvironmentConfiguration", () => {
		it("should pass validation with FRONTEND_URL", () => {
			process.env.FRONTEND_URL = "https://example.com"

			expect(() => {
				RedirectUrlGenerator.validateEnvironmentConfiguration()
			}).not.toThrow()
		})

		it("should pass validation with BETTER_AUTH_URL", () => {
			delete process.env.FRONTEND_URL
			process.env.BETTER_AUTH_URL = "https://auth.example.com"

			expect(() => {
				RedirectUrlGenerator.validateEnvironmentConfiguration()
			}).not.toThrow()
		})

		it("should not throw error when no URL environment variables are set (allows fallback)", () => {
			delete process.env.FRONTEND_URL
			delete process.env.BETTER_AUTH_URL

			expect(() => {
				RedirectUrlGenerator.validateEnvironmentConfiguration()
			}).not.toThrow()
		})

		it("should throw error for invalid URL format", () => {
			process.env.FRONTEND_URL = "not-a-valid-url"

			expect(() => {
				RedirectUrlGenerator.validateEnvironmentConfiguration()
			}).toThrow(AppError)
		})
	})

	describe("generateRedirectUrls", () => {
		it("should generate all redirect URLs with consistent context", () => {
			const generator = new RedirectUrlGenerator({
				baseUrl: "https://example.com",
			})

			const context: RedirectContext = {
				campaignId: "camp-123",
				campaignSlug: "test-campaign",
				donationId: "don-456",
				paymentId: "pay-789",
			}

			const urls = generator.generateRedirectUrls(context)

			// All URLs should contain the campaign and donation context
			expect(urls.successRedirectUrl).toContain("campaign=test-campaign")
			expect(urls.successRedirectUrl).toContain("donation=don-456")
			expect(urls.successRedirectUrl).toContain("payment=pay-789")

			expect(urls.failureRedirectUrl).toContain("campaign=test-campaign")
			expect(urls.failureRedirectUrl).toContain("donation=don-456")
			expect(urls.failureRedirectUrl).toContain("payment=pay-789")

			expect(urls.cancelRedirectUrl).toContain("donation=don-456")

			// Each URL should have the correct status
			expect(urls.successRedirectUrl).toContain("status=success")
			expect(urls.failureRedirectUrl).toContain("status=failed")
			expect(urls.cancelRedirectUrl).toContain("status=canceled")
		})
	})
})
