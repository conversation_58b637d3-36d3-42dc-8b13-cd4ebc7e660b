import { StatusCodes } from "http-status-codes"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import {
	AppError,
	createErrorResponse,
	ErrorCode,
	logError,
	sanitizeForLogging,
	validateEnvironmentVariables,
	validatePaymentAmount,
	withErrorHandling,
} from "../error-handler"

// Mock console methods
const mockConsole = {
	error: vi.fn(),
	warn: vi.fn(),
	info: vi.fn(),
}

beforeEach(() => {
	vi.spyOn(console, "error").mockImplementation(mockConsole.error)
	vi.spyOn(console, "warn").mockImplementation(mockConsole.warn)
	vi.spyOn(console, "info").mockImplementation(mockConsole.info)
})

afterEach(() => {
	vi.restoreAllMocks()
	mockConsole.error.mockClear()
	mockConsole.warn.mockClear()
	mockConsole.info.mockClear()
})

describe("AppError", () => {
	it("should create an AppError with all properties", () => {
		const error = new AppError(
			"Test error",
			ErrorCode.PAYMENT_GATEWAY_ERROR,
			StatusCodes.BAD_REQUEST,
			{ test: "data" },
			true,
		)

		expect(error.message).toBe("Test error")
		expect(error.code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
		expect(error.statusCode).toBe(StatusCodes.BAD_REQUEST)
		expect(error.details).toEqual({ test: "data" })
		expect(error.isOperational).toBe(true)
		expect(error.name).toBe("AppError")
	})

	it("should use default values when not provided", () => {
		const error = new AppError("Test error", ErrorCode.VALIDATION_ERROR)

		expect(error.statusCode).toBe(StatusCodes.INTERNAL_SERVER_ERROR)
		expect(error.details).toBeUndefined()
		expect(error.isOperational).toBe(true)
	})
})

describe("createErrorResponse", () => {
	it("should create error response from AppError", () => {
		const appError = new AppError(
			"Test error",
			ErrorCode.PAYMENT_GATEWAY_ERROR,
			StatusCodes.BAD_REQUEST,
			{ test: "data" },
		)

		const response = createErrorResponse(appError, "req123")

		expect(response.error).toBe("Test error")
		expect(response.code).toBe(ErrorCode.PAYMENT_GATEWAY_ERROR)
		expect(response.details).toEqual({ test: "data" })
		expect(response.requestId).toBe("req123")
		expect(response.timestamp).toBeDefined()
	})

	it("should create error response from regular Error", () => {
		const error = new Error("Regular error")
		const response = createErrorResponse(error, "req123", { extra: "data" })

		expect(response.error).toBe("Regular error")
		expect(response.code).toBeUndefined()
		expect(response.details).toEqual({ extra: "data" })
		expect(response.requestId).toBe("req123")
	})

	it("should create error response from string", () => {
		const response = createErrorResponse("String error")

		expect(response.error).toBe("String error")
		expect(response.code).toBeUndefined()
		expect(response.requestId).toBeUndefined()
	})
})

describe("logError", () => {
	it("should log AppError with structured format", () => {
		const appError = new AppError(
			"Test error",
			ErrorCode.PAYMENT_GATEWAY_ERROR,
			StatusCodes.BAD_REQUEST,
		)

		logError(appError, { context: "test" })

		expect(mockConsole.error).toHaveBeenCalledWith(
			"Application Error:",
			expect.objectContaining({
				message: "Test error",
				code: ErrorCode.PAYMENT_GATEWAY_ERROR,
				statusCode: StatusCodes.BAD_REQUEST,
				context: { context: "test" },
			}),
		)
	})

	it("should log regular Error", () => {
		const error = new Error("Regular error")
		logError(error, { context: "test" }, "warn")

		expect(mockConsole.warn).toHaveBeenCalledWith(
			"Application Warning:",
			expect.objectContaining({
				message: "Regular error",
				context: { context: "test" },
			}),
		)
	})

	it("should log string message", () => {
		logError("String error", { context: "test" }, "info")

		expect(mockConsole.info).toHaveBeenCalledWith(
			"Application Info:",
			expect.objectContaining({
				message: "String error",
				context: { context: "test" },
			}),
		)
	})
})

describe("validateEnvironmentVariables", () => {
	const originalEnv = process.env

	beforeEach(() => {
		process.env = { ...originalEnv }
	})

	afterEach(() => {
		process.env = originalEnv
	})

	it("should pass when all required variables are set", () => {
		process.env.TEST_VAR1 = "value1"
		process.env.TEST_VAR2 = "value2"

		expect(() => {
			validateEnvironmentVariables(["TEST_VAR1", "TEST_VAR2"])
		}).not.toThrow()
	})

	it("should throw AppError when variables are missing", () => {
		delete process.env.TEST_VAR1
		process.env.TEST_VAR2 = "value2"

		expect(() => {
			validateEnvironmentVariables(["TEST_VAR1", "TEST_VAR2"])
		}).toThrow(AppError)

		try {
			validateEnvironmentVariables(["TEST_VAR1", "TEST_VAR2"])
		} catch (error) {
			expect(error).toBeInstanceOf(AppError)
			expect((error as AppError).code).toBe(
				ErrorCode.MISSING_ENVIRONMENT_VARIABLES,
			)
			expect((error as AppError).message).toContain("TEST_VAR1")
		}
	})

	it("should throw when variables are empty strings", () => {
		process.env.TEST_VAR1 = ""
		process.env.TEST_VAR2 = "   "

		expect(() => {
			validateEnvironmentVariables(["TEST_VAR1", "TEST_VAR2"])
		}).toThrow(AppError)
	})
})

describe("validatePaymentAmount", () => {
	it("should pass for valid positive amounts", () => {
		expect(() => validatePaymentAmount(10.5)).not.toThrow()
		expect(() => validatePaymentAmount(0.01)).not.toThrow()
		expect(() => validatePaymentAmount(1000)).not.toThrow()
	})

	it("should throw for invalid amounts", () => {
		expect(() => validatePaymentAmount(0)).toThrow(AppError)
		expect(() => validatePaymentAmount(-10)).toThrow(AppError)
		expect(() => validatePaymentAmount(NaN)).toThrow(AppError)
		expect(() => validatePaymentAmount(Number.POSITIVE_INFINITY)).toThrow(
			AppError,
		)
	})

	it("should throw for amounts outside reasonable limits", () => {
		expect(() => validatePaymentAmount(0.001)).toThrow(AppError)
		expect(() => validatePaymentAmount(2000000)).toThrow(AppError)
	})

	it("should throw AppError with correct error code", () => {
		try {
			validatePaymentAmount(-5)
		} catch (error) {
			expect(error).toBeInstanceOf(AppError)
			expect((error as AppError).code).toBe(ErrorCode.PAYMENT_AMOUNT_INVALID)
			expect((error as AppError).statusCode).toBe(StatusCodes.BAD_REQUEST)
		}
	})
})

describe("sanitizeForLogging", () => {
	it("should redact sensitive fields", () => {
		const data = {
			email: "<EMAIL>",
			password: "secret123",
			phone: "+1234567890",
			publicField: "public data",
			token: "abc123",
		}

		const sanitized = sanitizeForLogging(data)

		expect(sanitized.email).toBe("[REDACTED]")
		expect(sanitized.password).toBe("[REDACTED]")
		expect(sanitized.phone).toBe("[REDACTED]")
		expect(sanitized.token).toBe("[REDACTED]")
		expect(sanitized.publicField).toBe("public data")
	})

	it("should handle nested objects", () => {
		const data = {
			user: {
				email: "<EMAIL>",
				name: "John Doe",
			},
			publicData: "visible",
		}

		const sanitized = sanitizeForLogging(data)

		expect(sanitized.user).toEqual({
			email: "<EMAIL>", // Not redacted because it's nested
			name: "John Doe",
		})
		expect(sanitized.publicData).toBe("visible")
	})
})

describe("withErrorHandling", () => {
	it("should execute function successfully", async () => {
		const testFn = vi.fn().mockResolvedValue("success")
		const wrappedFn = withErrorHandling(testFn, "test context")

		const result = await wrappedFn("arg1", "arg2")

		expect(result).toBe("success")
		expect(testFn).toHaveBeenCalledWith("arg1", "arg2")
	})

	it("should log errors and re-throw", async () => {
		const testError = new Error("Test error")
		const testFn = vi.fn().mockRejectedValue(testError)
		const wrappedFn = withErrorHandling(testFn, "test context")

		await expect(wrappedFn("arg1")).rejects.toThrow("Test error")

		expect(mockConsole.error).toHaveBeenCalledWith(
			"Application Error:",
			expect.objectContaining({
				message: "Test error",
				context: expect.objectContaining({
					context: "test context",
				}),
			}),
		)
	})

	it("should sanitize arguments in error logs", async () => {
		const testError = new Error("Test error")
		const testFn = vi.fn().mockRejectedValue(testError)
		const wrappedFn = withErrorHandling(testFn, "test context")

		await expect(
			wrappedFn({ email: "<EMAIL>", data: "public" }),
		).rejects.toThrow()

		expect(mockConsole.error).toHaveBeenCalledWith(
			"Application Error:",
			expect.objectContaining({
				context: expect.objectContaining({
					context: "test context",
					args: expect.objectContaining({
						"0": expect.objectContaining({
							data: "public",
							// Note: email is not redacted in nested objects by current implementation
						}),
					}),
				}),
			}),
		)
	})
})
