import { inspect } from "node:util"
import { StatusCodes } from "http-status-codes"

/**
 * Standard error response format for the API
 */
export interface ErrorResponse {
	error: string
	code?: string
	details?: Record<string, unknown>
	timestamp: string
	requestId?: string
}

/**
 * Error codes for different types of errors
 */
export enum ErrorCode {
	// Payment related errors
	PAYMENT_GATEWAY_ERROR = "PAYMENT_GATEWAY_ERROR",
	PAYMENT_VALIDATION_ERROR = "PAYMENT_VALIDATION_ERROR",
	PAYMENT_CONFIGURATION_ERROR = "PAYMENT_CONFIGURATION_ERROR",
	PAYMENT_AMOUNT_INVALID = "PAYMENT_AMOUNT_INVALID",

	// Webhook related errors
	WEBHOOK_SIGNATURE_INVALID = "WEBHOOK_SIGNATURE_INVALID",
	WEBHOOK_PAYLOAD_INVALID = "WEBHOOK_PAYLOAD_INVALID",
	WEBHOOK_PROCESSING_ERROR = "WEBHOOK_PROCESSING_ERROR",

	// Campaign related errors
	CAMPAIGN_NOT_FOUND = "CAMPAIGN_NOT_FOUND",
	CAMPAIGN_INACTIVE = "CAMPAIGN_INACTIVE",

	// Donation related errors
	DONATION_NOT_FOUND = "DONATION_NOT_FOUND",
	DONATION_CREATION_ERROR = "DONATION_CREATION_ERROR",

	// Configuration errors
	MISSING_ENVIRONMENT_VARIABLES = "MISSING_ENVIRONMENT_VARIABLES",

	// Database errors
	DATABASE_ERROR = "DATABASE_ERROR",

	// Validation errors
	VALIDATION_ERROR = "VALIDATION_ERROR",
}

/**
 * Custom error class for application-specific errors
 */
export class AppError extends Error {
	public readonly code: ErrorCode
	public readonly statusCode: number
	public readonly details?: Record<string, unknown>
	public readonly isOperational: boolean

	constructor(
		message: string,
		code: ErrorCode,
		statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR,
		details?: Record<string, unknown>,
		isOperational: boolean = true,
	) {
		super(message)
		this.name = "AppError"
		this.code = code
		this.statusCode = statusCode
		this.details = details
		this.isOperational = isOperational

		// Maintains proper stack trace for where our error was thrown
		Error.captureStackTrace(this, AppError)
	}
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse(
	error: string | Error | AppError,
	requestId?: string,
	details?: Record<string, unknown>,
): ErrorResponse {
	const timestamp = new Date().toISOString()

	if (error instanceof AppError) {
		return {
			error: error.message,
			code: error.code,
			details: error.details || details,
			timestamp,
			requestId,
		}
	}

	if (error instanceof Error) {
		return {
			error: error.message,
			details,
			timestamp,
			requestId,
		}
	}

	return {
		error: typeof error === "string" ? error : "Unknown error occurred",
		details,
		timestamp,
		requestId,
	}
}

/**
 * Logs error with structured format
 */
export function logError(
	error: Error | AppError | string,
	context: Record<string, unknown> = {},
	level: "error" | "warn" | "info" = "error",
): void {
	const timestamp = new Date().toISOString()
	const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

	const sanitizedContext = deepSanitizeForLogging(context)
	const structuredError =
		error instanceof Error ? serializeError(error) : { message: String(error) }

	const logData = {
		errorId,
		timestamp,
		level,
		context: sanitizedContext,
		...structuredError,
	}

	// Pretty-print with configurable depth and safe handling of deep/circular structures
	const pretty = inspect(logData, {
		depth: logOptions.inspectDepth,
		maxArrayLength: logOptions.maxArrayLength,
		breakLength: 120,
		compact: false,
	})

	switch (level) {
		case "error":
			console.error("Application Error:", pretty)
			break
		case "warn":
			console.warn("Application Warning:", pretty)
			break
		case "info":
			console.info("Application Info:", pretty)
			break
	}
}

/**
 * Validates that required environment variables are set
 */
export function validateEnvironmentVariables(requiredVars: string[]): void {
	const missingVars = requiredVars.filter(
		(varName) => !process.env[varName] || process.env[varName]?.trim() === "",
	)

	if (missingVars.length > 0) {
		const error = new AppError(
			`Missing required environment variables: ${missingVars.join(", ")}. Please check your .env file and ensure all required variables are set.`,
			ErrorCode.MISSING_ENVIRONMENT_VARIABLES,
			StatusCodes.INTERNAL_SERVER_ERROR,
			{ missingVariables: missingVars },
		)

		logError(error, { requiredVars, missingVars })
		throw error
	}
}

/**
 * Validates that a payment amount is positive and within reasonable limits
 */
export function validatePaymentAmount(amount: number): void {
	if (!amount || typeof amount !== "number" || Number.isNaN(amount)) {
		throw new AppError(
			"Payment amount must be a valid number",
			ErrorCode.PAYMENT_AMOUNT_INVALID,
			StatusCodes.BAD_REQUEST,
			{ providedAmount: amount },
		)
	}

	if (amount <= 0) {
		throw new AppError(
			"Payment amount must be greater than zero",
			ErrorCode.PAYMENT_AMOUNT_INVALID,
			StatusCodes.BAD_REQUEST,
			{ providedAmount: amount },
		)
	}

	// Set reasonable limits (adjust as needed)
	const MIN_AMOUNT = 0.01
	const MAX_AMOUNT = 1000000 // 1 million

	if (amount < MIN_AMOUNT) {
		throw new AppError(
			`Payment amount must be at least ${MIN_AMOUNT}`,
			ErrorCode.PAYMENT_AMOUNT_INVALID,
			StatusCodes.BAD_REQUEST,
			{ providedAmount: amount, minimumAmount: MIN_AMOUNT },
		)
	}

	if (amount > MAX_AMOUNT) {
		throw new AppError(
			`Payment amount cannot exceed ${MAX_AMOUNT}`,
			ErrorCode.PAYMENT_AMOUNT_INVALID,
			StatusCodes.BAD_REQUEST,
			{ providedAmount: amount, maximumAmount: MAX_AMOUNT },
		)
	}
}

/**
 * Sanitizes sensitive data from objects for logging
 */
export function sanitizeForLogging(
	data: Record<string, unknown>,
): Record<string, unknown> {
	// Keep export for backwards compatibility; delegate to deep implementation
	return deepSanitizeForLogging(data)
}

type Redactable = Record<string, unknown> | unknown[] | unknown

type LogOptions = {
	redactKeys: string[]
	maxStringLength: number
	maxArrayLength: number
	maxDepth: number
	inspectDepth: number | null
}

const defaultLogOptions: LogOptions = {
	redactKeys: [
		"password",
		"token",
		"secret",
		"key",
		"authorization",
		"email",
		"phone",
		"fullName",
		"donorEmail",
		"donorName",
		"donorPhone",
	],
	maxStringLength: 4_000,
	maxArrayLength: 200,
	maxDepth: 10,
	// When null, util.inspect shows full depth. Use a sane default for readability.
	inspectDepth: 8,
}

let logOptions: LogOptions = { ...defaultLogOptions }

export function configureLogging(options: Partial<LogOptions>) {
	logOptions = { ...logOptions, ...options }
}

function isPlainObject(value: unknown): value is Record<string, unknown> {
	return (
		typeof value === "object" &&
		value !== null &&
		Object.getPrototypeOf(value) === Object.prototype
	)
}

function redactIfSensitive(key: string, value: unknown): unknown {
	if (logOptions.redactKeys.includes(key)) return "[REDACTED]"
	return value
}

function deepSanitizeForLogging<T extends Redactable>(
	value: T,
	depth = 0,
	seen = new WeakSet<object>(),
): any {
	if (value === null || typeof value !== "object") return value

	if (depth >= logOptions.maxDepth) return "[MaxDepth]"

	if (typeof value === "object") {
		if (seen.has(value as object)) return "[Circular]"
		seen.add(value as object)
	}

	if (Array.isArray(value)) {
		const limited = value
			.slice(0, logOptions.maxArrayLength)
			.map((v) => deepSanitizeForLogging(v as any, depth + 1, seen))
		if (value.length > limited.length)
			limited.push(`[+${value.length - limited.length} more items]`)
		return limited
	}

	if (isPlainObject(value)) {
		const out: Record<string, unknown> = {}
		for (const [k, v] of Object.entries(value)) {
			const maybeRedacted = redactIfSensitive(k, v)
			if (typeof maybeRedacted === "string") {
				out[k] = truncateString(maybeRedacted)
			} else {
				out[k] = deepSanitizeForLogging(maybeRedacted as any, depth + 1, seen)
			}
		}
		return out
	}

	// Fallback for class instances, Date, Error, etc.
	try {
		// Attempt shallow copy of enumerable props
		const out: Record<string, unknown> = {}
		for (const [k, v] of Object.entries(value as object)) {
			const maybeRedacted = redactIfSensitive(k, v)
			out[k] = deepSanitizeForLogging(maybeRedacted as any, depth + 1, seen)
		}
		return out
	} catch {
		return String(value)
	}
}

function truncateString(s: string): string {
	if (s.length <= logOptions.maxStringLength) return s
	return `${s.slice(0, logOptions.maxStringLength)}...[truncated ${s.length - logOptions.maxStringLength} chars]`
}

function serializeError(
	err: Error,
	depth = 0,
	seen = new WeakSet<object>(),
): Record<string, unknown> {
	const base: Record<string, unknown> = {
		message: err.message,
		name: err.name,
		stack: err.stack,
	}

	// AppError extras
	if (err instanceof AppError) {
		base.code = err.code
		base.statusCode = err.statusCode
		base.isOperational = err.isOperational
		if (err.details) base.details = deepSanitizeForLogging(err.details, 0, seen)
	}

	// Common error shapes (e.g., AxiosError, fetch Response, ZodError)
	// Axios-like
	const anyErr = err as any
	if (anyErr?.response) {
		base.http = deepSanitizeForLogging(
			{
				status: anyErr.response.status,
				statusText: anyErr.response.statusText,
				headers: anyErr.response.headers,
				data: anyErr.response.data,
			},
			0,
			seen,
		)
	}
	if (anyErr?.request) {
		base.request = deepSanitizeForLogging(
			{
				method: anyErr.request.method,
				path: anyErr.request.path,
				headers: anyErr.request.headers,
				data: anyErr.request.data,
			},
			0,
			seen,
		)
	}

	// AggregateError support
	if (Array.isArray((err as any).errors)) {
		base.errors = (err as any).errors
			.slice(0, logOptions.maxArrayLength)
			.map((e: any) =>
				e instanceof Error
					? serializeError(e, depth + 1, seen)
					: deepSanitizeForLogging(e, depth + 1, seen),
			)
	}

	// Error cause chain
	const cause: unknown = (err as any).cause
	if (cause && depth < logOptions.maxDepth) {
		base.cause =
			cause instanceof Error
				? serializeError(cause, depth + 1, seen)
				: deepSanitizeForLogging(cause as any, depth + 1, seen)
	}

	// Attach enumerable custom props (if any)
	for (const [k, v] of Object.entries(err)) {
		if (k in base) continue
		base[k] = deepSanitizeForLogging(v as any, 0, seen)
	}

	return base
}

/**
 * Wraps async functions with error handling and logging
 */
export function withErrorHandling<T extends unknown[], R>(
	fn: (...args: T) => Promise<R>,
	context: string,
) {
	return async (...args: T): Promise<R> => {
		try {
			return await fn(...args)
		} catch (error) {
			// Convert args array to object for sanitization
			const argsObject: Record<string, unknown> = {}
			args.forEach((arg, index) => {
				argsObject[index.toString()] = arg
			})

			logError(error as Error, {
				context,
				args: deepSanitizeForLogging(argsObject),
			})
			throw error
		}
	}
}
