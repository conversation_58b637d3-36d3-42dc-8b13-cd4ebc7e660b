import { cors } from "@elysiajs/cors"
import { swagger } from "@elysiajs/swagger"
import { <PERSON><PERSON> } from "elysia"
import { StatusCodes } from "http-status-codes"
import { authController } from "./auth/auth.controller"
import { campaignsController } from "./campaigns/campaigns.controller"
import { donationsController } from "./donations/donations.controller"
import { donorsController } from "./donors/donors.controller"
import {
	AppError,
	configureLogging,
	createErrorResponse,
	ErrorCode,
	logError,
} from "./lib/error-handler"
import { organizersController } from "./organizers/organizers.controller"
import { healthRoutes } from "./routes/health"
import { usersController } from "./users/users.controller"
import { webhooksController } from "./webhooks/webhooks.controller"

// Configure logging verbosity/redaction based on environment
configureLogging(
	process.env.NODE_ENV === "production"
		? {
				inspectDepth: 4,
				maxDepth: 8,
				maxArrayLength: 100,
				maxStringLength: 2000,
				// You can extend redact keys here if needed
			}
		: {
				inspectDepth: 10,
				maxDepth: 12,
				maxArrayLength: 300,
				maxStringLength: 8000,
			},
)

const app = new Elysia()
	.use(swagger())
	.onError(({ code, error, set }) => {
		console.error("onError")

		if (error instanceof AppError || error instanceof Error) {
			logError(error, { code, path: (error as any).path })
		} else {
			console.error(error)
		}

		// Handle different error types
		switch (code) {
			case "NOT_FOUND":
				set.status = StatusCodes.NOT_FOUND
				return createErrorResponse("Resource not found", undefined, {
					code: ErrorCode.CAMPAIGN_NOT_FOUND,
				})

			case "VALIDATION":
				set.status = StatusCodes.BAD_REQUEST
				return createErrorResponse(error.message, undefined, {
					code: ErrorCode.VALIDATION_ERROR,
					details: (error as any).all ? (error as any).all() : undefined,
				})

			default:
				// Handle custom AppErrors
				if (error instanceof AppError) {
					set.status = error.statusCode
					return createErrorResponse(error, undefined, error.details)
				}

				// Handle other errors
				set.status = StatusCodes.INTERNAL_SERVER_ERROR
				return createErrorResponse("Internal server error", undefined, {
					code: "INTERNAL_ERROR",
				})
		}
	})
	.use(
		cors({
			origin: [
				"http://localhost:3000",
				"http://localhost:3001",
				"http://localhost:3002",
				"http://localhost:3003",
			],
			methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
			credentials: true,
			allowedHeaders: [
				"Content-Type",
				"Authorization",
				"Accept",
				"X-Requested-With",
			],
		}),
	)
	.use(healthRoutes)
	.use(authController)
	.use(usersController)
	.use(campaignsController)
	.use(donationsController)
	.use(donorsController)
	.use(organizersController)
	.use(webhooksController)
	.get("/", () => "Hello Elysia")
	.listen(3000)

console.log(
	`🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`,
)

// Export the app type for end-to-end type safety with Eden
export type Backend = typeof app
