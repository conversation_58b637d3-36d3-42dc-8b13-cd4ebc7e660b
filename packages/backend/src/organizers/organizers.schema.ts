import { t } from "elysia"

// DTOs for organizers (based on users with role 'organizer')
export const organizerIdParamsDto = t.Object({
	id: t.String(),
})

// Create organizer DTO
export const createOrganizerDto = t.Object({
	name: t.String({ minLength: 1, maxLength: 255 }),
	email: t.String({ format: "email" }),
})

// Filters for organizing list
export const organizerFiltersDto = t.Object({
	page: t.Optional(t.Numeric()),
	limit: t.Optional(t.Numeric()),
	banned: t.Optional(t.Boolean()),
	search: t.Optional(t.String()),
})

// Response schema - matches the actual user data returned from the controller
export const organizerDto = t.Object({
	id: t.String(),
	name: t.String(),
	email: t.String(),
	role: t.String(),
	banned: t.Boolean(),
	createdAt: t.Date(),
	updatedAt: t.Date(),
})

export const organizerListResponseDto = t.Object({
	success: t.<PERSON>(),
	data: t.Array(organizerDto),
	pagination: t.Object({
		total: t.Number(),
		page: t.Number(),
		limit: t.Number(),
	}),
})

export const organizerResponseDto = t.Object({
	success: t.Boolean(),
	organizer: organizerDto,
})

export const deleteOrganizerResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})

export const createOrganizerResponseDto = t.Object({
	success: t.Boolean(),
	organizer: organizerDto,
})

export const exportOrganizersResponseDto = t.Object({
	success: t.Boolean(),
	csvData: t.String(),
	filename: t.String(),
})

// Type exports for frontend consumption
export type CreateOrganizer = typeof createOrganizerDto.static
export type Organizer = typeof organizerDto.static
export type OrganizerListResponse = typeof organizerListResponseDto.static
export type OrganizerResponse = typeof organizerResponseDto.static
export type CreateOrganizerResponse = typeof createOrganizerResponseDto.static
export type DeleteOrganizerResponse = typeof deleteOrganizerResponseDto.static
export type ExportOrganizersResponse = typeof exportOrganizersResponseDto.static
