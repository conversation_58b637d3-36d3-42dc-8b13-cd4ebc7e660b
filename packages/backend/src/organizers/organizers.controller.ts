import { and, eq } from "drizzle-orm"
import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import {
	campaigns,
	createOrganizerDto,
	createOrganizerResponseDto,
	deleteOrganizerResponseDto,
	donations,
	exportOrganizersResponseDto,
	organizerFiltersDto,
	organizerIdParamsDto,
	organizerListResponseDto,
	organizerResponseDto,
} from "@/schemas"
import { users } from "@/users/users.schema"
import { auth } from "../../lib/auth"
import { authController } from "../auth/auth.controller"

export const organizersController = new Elysia({ prefix: "/api/organizers" })
	.use(authController)
	.get(
		"/",
		async ({ query, status }) => {
			try {
				const page = query.page || 1
				const limit = query.limit || 50
				const offset = (page - 1) * limit

				// Build where conditions
				let whereConditions = eq(users.role, "organizer")

				if (query.banned !== undefined) {
					whereConditions = and(whereConditions, eq(users.banned, query.banned))
				}

				// Get organizers with filters
				const organizers = await db
					.select({
						id: users.id,
						name: users.name,
						email: users.email,
						role: users.role,
						banned: users.banned,
						createdAt: users.createdAt,
						updatedAt: users.updatedAt,
					})
					.from(users)
					.where(whereConditions)
					.limit(limit)
					.offset(offset)
					.orderBy(users.createdAt)

				// Get total count for pagination
				const totalResult = await db
					.select({ count: users.id })
					.from(users)
					.where(whereConditions)

				const total = totalResult.length

				return {
					success: true,
					data: organizers,
					pagination: {
						total,
						page,
						limit,
					},
				}
			} catch (error) {
				console.error("Error fetching organizers:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch organizers",
				)
			}
		},
		{
			requirePermission: { user: ["view"] },
			query: organizerFiltersDto,
			response: {
				[StatusCodes.OK]: organizerListResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.post(
		"/",
		async ({ body, status }) => {
			try {
				// Check if email already exists
				const existingUser = await db
					.select()
					.from(users)
					.where(eq(users.email, body.email))
					.limit(1)

				if (existingUser.length > 0) {
					return status(
						StatusCodes.CONFLICT,
						"A user with this email already exists",
					)
				}

				// Create new organizer user
				const [newOrganizer] = await db
					.insert(users)
					.values({
						id: crypto.randomUUID(),
						name: body.name,
						email: body.email,
						role: "organizer",
						emailVerified: false,
					})
					.returning({
						id: users.id,
						name: users.name,
						email: users.email,
						role: users.role,
						banned: users.banned,
						createdAt: users.createdAt,
						updatedAt: users.updatedAt,
					})

				return status(StatusCodes.CREATED, {
					success: true,
					organizer: newOrganizer,
				})
			} catch (error) {
				console.error("Error creating organizer:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to create organizer",
				)
			}
		},
		{
			requirePermission: { user: ["create"] },
			body: createOrganizerDto,
			response: {
				[StatusCodes.CREATED]: createOrganizerResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/:id",
		async ({ params, status }) => {
			try {
				// Get organizer by ID (must have role 'organizer')
				const organizer = await db
					.select({
						id: users.id,
						name: users.name,
						email: users.email,
						role: users.role,
						banned: users.banned,
						createdAt: users.createdAt,
						updatedAt: users.updatedAt,
					})
					.from(users)
					.where(and(eq(users.id, params.id), eq(users.role, "organizer")))
					.limit(1)

				if (organizer.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Organizer not found")
				}

				return {
					success: true,
					organizer: organizer[0],
				}
			} catch (error) {
				console.error("Error fetching organizer:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch organizer",
				)
			}
		},
		{
			requirePermission: { user: ["view"] },
			params: organizerIdParamsDto,
			response: {
				[StatusCodes.OK]: organizerResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.delete(
		"/:id",
		async ({ params, status }) => {
			try {
				// Check if organizer exists
				const existingOrganizer = await db
					.select()
					.from(users)
					.where(and(eq(users.id, params.id), eq(users.role, "organizer")))
					.limit(1)

				if (existingOrganizer.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Organizer not found")
				}

				// Check if organizer has associated campaigns
				const associatedCampaigns = await db
					.select()
					.from(campaigns)
					.where(eq(campaigns.organizerId, params.id))
					.limit(1)

				if (associatedCampaigns.length > 0) {
					return status(
						StatusCodes.CONFLICT,
						"Cannot delete organizer with existing campaigns. Please contact support to transfer campaigns first.",
					)
				}

				// Check if organizer has associated donations through campaigns
				const associatedDonations = await db
					.select({ donationId: donations.id })
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(campaigns.organizerId, params.id))
					.limit(1)

				if (associatedDonations.length > 0) {
					return status(
						StatusCodes.CONFLICT,
						"Cannot delete organizer with existing donations. Please contact support to transfer donations first.",
					)
				}

				// Delete organizer
				await db.delete(users).where(eq(users.id, params.id))

				return {
					success: true,
					message: "Organizer deleted successfully",
				}
			} catch (error) {
				console.error("Error deleting organizer:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to delete organizer",
				)
			}
		},
		{
			requirePermission: { user: ["delete"] },
			params: organizerIdParamsDto,
			response: {
				[StatusCodes.OK]: deleteOrganizerResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/export",
		async ({ query, status }) => {
			try {
				let whereConditions = eq(users.role, "organizer")

				// Apply filters if provided
				if (query.banned !== undefined) {
					whereConditions = and(whereConditions, eq(users.banned, query.banned))
				}

				// Get organizers for export
				const organizers = await db
					.select({
						id: users.id,
						name: users.name,
						email: users.email,
						role: users.role,
						banned: users.banned,
						createdAt: users.createdAt,
						updatedAt: users.updatedAt,
					})
					.from(users)
					.where(whereConditions)
					.orderBy(users.createdAt)

				// Generate CSV data
				const headers = [
					"ID",
					"Name",
					"Email",
					"Role",
					"Banned",
					"Created At",
					"Updated At",
				]
				const csvRows = [
					headers.join(","),
					...organizers.map((organizer) =>
						[
							organizer.id,
							`"${organizer.name}"`,
							organizer.email,
							organizer.role,
							organizer.banned,
							organizer.createdAt.toISOString(),
							organizer.updatedAt.toISOString(),
						].join(","),
					),
				]

				const csvData = csvRows.join("\n")
				const timestamp = new Date()
					.toISOString()
					.slice(0, 19)
					.replace(/:/g, "-")
				const filename = `organizers-export-${timestamp}.csv`

				return {
					success: true,
					csvData,
					filename,
				}
			} catch (error) {
				console.error("Error exporting organizers:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to export organizers",
				)
			}
		},
		{
			requirePermission: { user: ["view"] },
			query: organizerFiltersDto,
			response: {
				[StatusCodes.OK]: exportOrganizersResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	// Ban/Unban organizer endpoint
	.patch(
		"/:id/ban",
		async ({ params, body, status }) => {
			try {
				// Check if organizer exists
				const existingOrganizer = await db
					.select()
					.from(users)
					.where(and(eq(users.id, params.id), eq(users.role, "organizer")))
					.limit(1)

				if (existingOrganizer.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Organizer not found")
				}

				// Update ban status
				const [updatedOrganizer] = await db
					.update(users)
					.set({
						banned: body.banned,
						banReason: body.banned ? body.reason || null : null,
						banExpires:
							body.banned && body.expiresAt ? new Date(body.expiresAt) : null,
						updatedAt: new Date(),
					})
					.where(eq(users.id, params.id))
					.returning({
						id: users.id,
						name: users.name,
						email: users.email,
						role: users.role,
						banned: users.banned,
						createdAt: users.createdAt,
						updatedAt: users.updatedAt,
					})

				return {
					success: true,
					organizer: updatedOrganizer,
				}
			} catch (error) {
				console.error("Error updating organizer ban status:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update organizer ban status",
				)
			}
		},
		{
			requirePermission: { user: ["ban"] },
			params: organizerIdParamsDto,
			body: t.Object({
				banned: t.Boolean(),
				reason: t.Optional(t.String()),
				expiresAt: t.Optional(t.String()),
			}),
			response: {
				[StatusCodes.OK]: organizerResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	// Impersonate organizer endpoint
	.post(
		"/:id/impersonate",
		async ({ params, status, request }) => {
			try {
				// Check if organizer exists
				const existingOrganizer = await db
					.select()
					.from(users)
					.where(and(eq(users.id, params.id), eq(users.role, "organizer")))
					.limit(1)

				if (existingOrganizer.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Organizer not found")
				}

				// Check if organizer is banned
				if (existingOrganizer[0].banned) {
					return status(
						StatusCodes.CONFLICT,
						"Cannot impersonate banned organizer",
					)
				}

				// Use Better Auth admin API to impersonate the organizer
				const impersonateResult = await auth.api.impersonateUser({
					body: {
						userId: params.id,
					},
					headers: request.headers,
				})

				if (!impersonateResult) {
					return status(
						StatusCodes.INTERNAL_SERVER_ERROR,
						"Failed to create impersonation session",
					)
				}

				return {
					success: true,
					message: "Impersonation initiated successfully",
					organizer: {
						id: existingOrganizer[0].id,
						name: existingOrganizer[0].name,
						email: existingOrganizer[0].email,
					},
				}
			} catch (error) {
				console.error("Error impersonating organizer:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to impersonate organizer",
				)
			}
		},
		{
			requirePermission: { user: ["impersonate"] },
			params: organizerIdParamsDto,
			response: {
				[StatusCodes.OK]: t.Object({
					success: t.Boolean(),
					message: t.String(),
					organizer: t.Object({
						id: t.String(),
						name: t.String(),
						email: t.String(),
					}),
				}),
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)
