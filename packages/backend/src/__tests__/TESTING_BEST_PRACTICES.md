# Testing Best Practices for Better-Au<PERSON>, Dr<PERSON>zle, and Vitest

This document outlines the best practices for testing applications using better-auth, drizzle-orm, and vitest, based on Context7 documentation and TypeScript best practices.

## Key Issues Fixed

### 1. Better-Auth Headers Type Issue
**Problem**: Tests were passing `Record<string, string>` to `auth.api.getSession({ headers })`, but better-auth expects a `Headers` object.

**Solution**: Use the `createHeaders()` utility function:
```typescript
// ❌ Wrong - causes TypeScript errors
const headers = { authorization: "Bearer token_123" }
const session = await auth.api.getSession({ headers })

// ✅ Correct - uses proper Headers object
const headers = createHeaders({ authorization: "Bearer token_123" })
const session = await auth.api.getSession({ headers })
```

### 2. Drizzle ORM Mocking Issues
**Problem**: Tests were using mock objects instead of actual table schemas.

**Solution**: Import and use actual schemas:
```typescript
// ❌ Wrong - mock table objects
const mockCampaignsTable = {
  id: "id_column",
  slug: "slug_column",
  // ...
}

// ✅ Correct - use actual schemas
import { campaigns } from "../campaigns.schema"
const result = await db.select().from(campaigns).where(eq(campaigns.slug, slug))
```

### 3. Vitest Mocking Best Practices
**Problem**: Improper use of mocking functions and type safety.

**Solution**: Use `vi.mocked()` for type-safe mocking:
```typescript
// ✅ Type-safe mocking
const mockAuth = vi.mocked(auth, { deep: true })
const mockDb = vi.mocked(db)
```

## Test Utilities

### Headers Creation
```typescript
export function createHeaders(headers: Record<string, string> = {}): Headers {
  const headersObj = new Headers()
  Object.entries(headers).forEach(([key, value]) => {
    headersObj.set(key, value)
  })
  return headersObj
}
```

### Mock Data Creation
```typescript
export function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: "user_123",
    name: "Test User",
    email: "<EMAIL>",
    role: "organizer",
    // ... other required fields
    ...overrides,
  }
}
```

## Better-Auth Testing Patterns

### 1. Session Testing
```typescript
it("should authenticate user with valid session", async () => {
  const mockUser = createMockUser()
  const mockSession = createMockSession()
  const headers = createHeaders({ authorization: "Bearer token_123" })
  
  mockAuth.api.getSession.mockResolvedValue({
    user: mockUser,
    session: mockSession,
  })
  
  const result = await mockAuth.api.getSession({ headers })
  expect(result.user).toEqual(mockUser)
})
```

### 2. Permission Testing
```typescript
it("should check user permissions", async () => {
  mockAuth.api.userHasPermission.mockResolvedValue({ success: true })
  
  const result = await mockAuth.api.userHasPermission({
    body: {
      userId: "user_123",
      permissions: { campaigns: ["read", "write"] },
    },
  })
  
  expect(result.success).toBe(true)
})
```

## Drizzle ORM Testing Patterns

### 1. Database Operations
```typescript
it("should query campaigns using proper schema", async () => {
  // Use actual schema imports
  const result = await db
    .select()
    .from(campaigns)
    .where(eq(campaigns.slug, "test-campaign"))
    .limit(1)
    
  expect(result).toBeDefined()
})
```

### 2. Mock Database Context
```typescript
it("should use withPublicContext for public operations", async () => {
  const mockOperation = vi.fn().mockResolvedValue([mockCampaign])
  
  mockWithPublicContext.mockImplementation(async (_db, operation) => {
    return await operation()
  })
  
  const result = await mockWithPublicContext(db, mockOperation)
  expect(result).toEqual([mockCampaign])
})
```

## Common Patterns

### 1. Test Setup
```typescript
beforeEach(() => {
  vi.clearAllMocks()
  
  // Setup default mocks
  mockAuth.api.getSession.mockResolvedValue(null)
  mockWithPublicContext.mockImplementation(async (_db, operation) => {
    return await operation()
  })
})
```

### 2. Type Safety
```typescript
// Use vi.mocked for type safety
const mockAuth = vi.mocked(auth, { deep: true })
const mockDb = vi.mocked(db)

// Use proper types for mock data
const mockUser: User = createMockUser()
const mockSession: Session = createMockSession()
```

### 3. Cleanup
```typescript
afterEach(() => {
  vi.restoreAllMocks()
})
```

## Key Takeaways

1. **Always use proper Headers objects** for better-auth testing
2. **Import actual schemas** instead of creating mock objects
3. **Use vi.mocked()** for type-safe mocking
4. **Create reusable test utilities** for common patterns
5. **Follow TypeScript best practices** throughout tests
6. **Clean up mocks properly** between tests
7. **Use meaningful test descriptions** and organize tests logically

These patterns ensure type safety, maintainability, and reliability in your test suite.
