import { describe, expect, it } from "bun:test"
import { treaty } from "@elysiajs/eden"
import type { App } from "../index"

// This test verifies that end-to-end type safety is working correctly
describe("End-to-End Type Safety", () => {
	const api = treaty<App>("http://localhost:3000")

	it("should have correct types for campaign creation", async () => {
		// This test primarily checks compile-time type safety
		// The actual API call would require authentication and a running server

		// Type check: This should compile without errors
		const _campaignData = {
			name: "Test Campaign",
			description: "Test description",
			slug: "test-campaign",
		}

		// Verify the API structure exists and has correct method signatures
		expect(typeof api.api.campaigns.post).toBe("function")
		expect(typeof api.api.campaigns.my.get).toBe("function")

		// Type assertion to verify the structure
		const _typeCheck: typeof api.api.campaigns.post = api.api.campaigns.post
		expect(_typeCheck).toBeDefined()
	})

	it("should have correct types for donation operations", async () => {
		// Type check: Donation initiation
		const _donationData = {
			campaignSlug: "test-campaign",
			amount: "50.00",
			donorName: "<PERSON> Do<PERSON>",
			donorEmail: "<EMAIL>",
			message: "Test donation",
		}

		// Verify the API structure exists
		expect(typeof api.api.donations.initiate.post).toBe("function")
		expect(typeof api.api.donations.get).toBe("function")
		expect(typeof api.api.donations.analytics.get).toBe("function")

		// Type assertion to verify the structure
		const _typeCheck: typeof api.api.donations.initiate.post =
			api.api.donations.initiate.post
		expect(_typeCheck).toBeDefined()
	})

	it("should have correct query parameter types", () => {
		// This verifies that query parameters are properly typed
		const queryParams = {
			search: "test",
			status: "completed" as const,
			campaignId: "campaign-id",
			dateFrom: "2024-01-01",
			dateTo: "2024-12-31",
			sortBy: "createdAt" as const,
			sortOrder: "desc" as const,
			page: 1,
			limit: 10,
		}

		// These should compile without type errors
		expect(queryParams.status).toBe("completed")
		expect(queryParams.sortBy).toBe("createdAt")
		expect(queryParams.sortOrder).toBe("desc")
	})

	it("should have proper error response types", () => {
		// This test ensures error responses are properly typed
		// In a real scenario, you would handle errors like this:

		const handleError = (error: { status?: number; value?: unknown }) => {
			if (error?.status === 400) {
				// error.value should be typed as ErrorResponseDto
				expect(typeof error.value).toBe("object")
			}
		}

		// Mock error for type checking
		const mockError = {
			status: 400,
			value: { error: "Test error" },
		}

		handleError(mockError)
	})
})

// Integration test that would run against a real server
describe("Integration Type Safety", () => {
	// These tests would require a running server and proper authentication
	// They're commented out but show how you would test the actual API calls

	it.skip("should create and retrieve campaign with type safety", async () => {
		const api = treaty<App>("http://localhost:3000")

		// Create campaign
		const { data: createData, error: createError } =
			await api.api.campaigns.post(
				{
					name: "Integration Test Campaign",
					description: "Test description",
					slug: "integration-test-campaign",
				},
				{
					headers: {
						authorization: "Bearer test-token",
					},
				},
			)

		expect(createError).toBeNull()
		expect(createData?.success).toBe(true)
		expect(createData?.campaign.name).toBe("Integration Test Campaign")

		// Get campaign
		const { data: getData, error: getError } = await api.api
			.campaigns({ slug: "integration-test-campaign" })
			.get()

		expect(getError).toBeNull()
		expect(getData?.success).toBe(true)
		expect(getData?.campaign.name).toBe("Integration Test Campaign")
	})

	it.skip("should handle donation flow with type safety", async () => {
		const api = treaty<App>("http://localhost:3000")

		// Initiate donation
		const { data: donationData, error: donationError } =
			await api.api.donations.initiate.post({
				campaignSlug: "integration-test-campaign",
				amount: "25.00",
				donorName: "Test Donor",
				donorEmail: "<EMAIL>",
				message: "Test donation message",
			})

		expect(donationError).toBeNull()
		expect(donationData?.success).toBe(true)
		expect(donationData?.donation.amount).toBe("25.00")

		// Get donations list
		const { data: listData, error: listError } = await api.api.donations.get({
			query: {
				status: "pending",
				limit: 10,
			},
			headers: {
				authorization: "Bearer organizer-token",
			},
		})

		expect(listError).toBeNull()
		expect(listData?.success).toBe(true)
		expect(Array.isArray(listData?.donations)).toBe(true)
	})
})
