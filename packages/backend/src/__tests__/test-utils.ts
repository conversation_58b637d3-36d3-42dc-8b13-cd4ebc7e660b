import { drizzle } from "drizzle-orm/node-postgres"
import { vi } from "vitest"
import * as schema from "../schemas"

/**
 * Test utilities for better-auth, drizzle, and vitest
 * Following best practices from Context7 documentation
 */

// Create proper Headers object for better-auth testing
export function createHeaders(headers: Record<string, string> = {}): Headers {
	const headersObj = new Headers()
	Object.entries(headers).forEach(([key, value]) => {
		headersObj.set(key, value)
	})
	return headersObj
}

// Create mock user data with proper types
export function createMockUser(
	overrides: Partial<schema.User> = {},
): schema.User {
	return {
		id: "user_123",
		name: "Test User",
		email: "<EMAIL>",
		role: "organizer",
		emailVerified: true,
		banned: false,
		image: null,
		banReason: null,
		banExpires: null,
		createdAt: new Date(),
		updatedAt: new Date(),
		...overrides,
	}
}

// Create mock session data with proper types
export function createMockSession(
	overrides: Partial<schema.Session> = {},
): schema.Session {
	return {
		id: "session_123",
		userId: "user_123",
		expiresAt: new Date(Date.now() + 86400000),
		token: "token_123",
		createdAt: new Date(),
		updatedAt: new Date(),
		ipAddress: "127.0.0.1",
		userAgent: "test-agent",
		impersonatedBy: null,
		...overrides,
	}
}

// Create mock database context
export function createMockDbContext(overrides: Record<string, any> = {}) {
	return {
		userId: "user_123",
		userRole: "organizer",
		organizerId: "user_123",
		...overrides,
	}
}

// Create mock database using drizzle.mock() - best practice from Context7
export function createMockDatabase() {
	return drizzle.mock({ schema })
}

// Helper for creating mock auth responses
export function createMockAuthResponse(
	user: schema.User,
	session: schema.Session,
) {
	return {
		user,
		session,
	}
}

// Helper for creating authorization headers
export function createAuthHeaders(token: string = "Bearer token_123"): Headers {
	return createHeaders({
		authorization: token,
		"content-type": "application/json",
	})
}

// Mock cleanup helper
export function setupMockCleanup() {
	return {
		clearAllMocks: () => vi.clearAllMocks(),
		restoreAllMocks: () => vi.restoreAllMocks(),
	}
}
