import { sql } from "drizzle-orm"
import {
	pgPolicy,
	pgTable,
	text,
	timestamp,
	uniqueIndex,
	uuid,
} from "drizzle-orm/pg-core"
import { pgAdminRole, pgOrganizerRole, users } from "@/schemas"

// Donor tags table - for categorizing donors
export const donorTags = pgTable(
	"donor_tags",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		organizerId: text("organizer_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		name: text("name").notNull(),
		color: text("color").notNull().default("#3b82f6"), // Default blue color
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(t) => [
		// Ensure unique tag names per organizer
		uniqueIndex("unique_organizer_tag").on(t.organizerId, t.name),
		// Organizers can manage their own tags
		pgPolicy("donor_tags_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`${t.organizerId} = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`${t.organizerId} = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin'`,
		}),
	],
)

// Donor tag assignments - many-to-many relationship between donors and tags
export const donorTagAssignments = pgTable(
	"donor_tag_assignments",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		donorId: text("donor_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		tagId: uuid("tag_id")
			.references(() => donorTags.id, { onDelete: "cascade" })
			.notNull(),
		assignedAt: timestamp("assigned_at").defaultNow().notNull(),
	},
	(t) => [
		// Ensure unique donor-tag combinations
		uniqueIndex("unique_donor_tag").on(t.donorId, t.tagId),
		// Organizers can manage tag assignments for their tags
		pgPolicy("donor_tag_assignments_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`EXISTS (
				SELECT 1 FROM donor_tags dt
				WHERE dt.id = ${t.tagId}
				AND (dt.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)`,
			withCheck: sql`EXISTS (
				SELECT 1 FROM donor_tags dt
				WHERE dt.id = ${t.tagId}
				AND (dt.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)`,
		}),
	],
)

// Donor notes table - for timestamped notes with organizer attribution
export const donorNotes = pgTable(
	"donor_notes",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		donorId: text("donor_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		organizerId: text("organizer_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		content: text("content").notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(t) => [
		// Organizers can manage notes they created
		pgPolicy("donor_notes_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`${t.organizerId} = current_setting('app.current_organizer_id', true)
			OR current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`${t.organizerId} = current_setting('app.current_organizer_id', true)
			OR current_setting('app.current_user_role', true) = 'admin'`,
		}),
	],
)

// Type exports
export type DonorTag = typeof donorTags.$inferSelect
export type CreateDonorTag = typeof donorTags.$inferInsert
export type DonorTagAssignment = typeof donorTagAssignments.$inferSelect
export type CreateDonorTagAssignment = typeof donorTagAssignments.$inferInsert
export type DonorNote = typeof donorNotes.$inferSelect
export type CreateDonorNote = typeof donorNotes.$inferInsert
