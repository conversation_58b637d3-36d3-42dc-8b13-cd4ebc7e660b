import { and, count, desc, eq, ilike, inArray, or, sql } from "drizzle-orm"
import { db } from "@/db"
import { campaigns } from "../campaigns/campaigns.schema"
import { donations } from "../donations/donations.schema"
import { users } from "../users/users.schema"
import type { DonorFilters } from "./donors.schema"
import {
	donorNotes,
	donorTagAssignments,
	donorTags,
} from "./donors-tags.schema"

/**
 * Fetch tags for a specific donor
 */
export async function getDonorTags(donorId: string) {
	return await db
		.select({
			id: donorTags.id,
			name: donorTags.name,
			color: donorTags.color,
			createdAt: donorTags.createdAt,
			updatedAt: donorTags.updatedAt,
		})
		.from(donorTags)
		.innerJoin(donorTagAssignments, eq(donorTags.id, donorTagAssignments.tagId))
		.where(eq(donorTagAssignments.donorId, donorId))
		.orderBy(donorTags.name)
}

/**
 * Fetch notes for a specific donor
 */
export async function getDonorNotes(donorId: string) {
	return await db
		.select({
			id: donorNotes.id,
			content: donorNotes.content,
			organizerId: donorNotes.organizerId,
			organizerName: users.name,
			createdAt: donorNotes.createdAt,
			updatedAt: donorNotes.updatedAt,
		})
		.from(donorNotes)
		.leftJoin(users, eq(donorNotes.organizerId, users.id))
		.where(eq(donorNotes.donorId, donorId))
		.orderBy(desc(donorNotes.createdAt))
}

/**
 * Fetch tags and notes for multiple donors efficiently
 */
export async function getDonorsTagsAndNotes(donorIds: string[]) {
	if (donorIds.length === 0) {
		return { tags: {}, notes: {} }
	}

	// Fetch all tags for these donors
	const tagsData = await db
		.select({
			donorId: donorTagAssignments.donorId,
			id: donorTags.id,
			name: donorTags.name,
			color: donorTags.color,
			createdAt: donorTags.createdAt,
			updatedAt: donorTags.updatedAt,
		})
		.from(donorTags)
		.innerJoin(donorTagAssignments, eq(donorTags.id, donorTagAssignments.tagId))
		.where(inArray(donorTagAssignments.donorId, donorIds))
		.orderBy(donorTags.name)

	// Fetch all notes for these donors
	const notesData = await db
		.select({
			donorId: donorNotes.donorId,
			id: donorNotes.id,
			content: donorNotes.content,
			organizerId: donorNotes.organizerId,
			organizerName: users.name,
			createdAt: donorNotes.createdAt,
			updatedAt: donorNotes.updatedAt,
		})
		.from(donorNotes)
		.leftJoin(users, eq(donorNotes.organizerId, users.id))
		.where(inArray(donorNotes.donorId, donorIds))
		.orderBy(desc(donorNotes.createdAt))

	// Group by donor ID
	const tags: Record<string, any[]> = {}
	const notes: Record<string, any[]> = {}

	tagsData.forEach((tag) => {
		if (!tags[tag.donorId]) tags[tag.donorId] = []
		tags[tag.donorId].push({
			id: tag.id,
			name: tag.name,
			color: tag.color,
			createdAt: tag.createdAt,
			updatedAt: tag.updatedAt,
		})
	})

	notesData.forEach((note) => {
		if (!notes[note.donorId]) notes[note.donorId] = []
		notes[note.donorId].push({
			id: note.id,
			content: note.content,
			organizerId: note.organizerId,
			organizerName: note.organizerName,
			createdAt: note.createdAt,
			updatedAt: note.updatedAt,
		})
	})

	return { tags, notes }
}

/**
 * Optimized donor statistics query builder
 * This function creates reusable query components for donor statistics
 */
export function buildDonorStatsSelect() {
	return {
		id: users.id,
		name: users.name,
		email: users.email,
		phone: users.image, // Temporarily using image field for phone
		address: sql<string | null>`NULL`.as("address"),
		notes: sql<string | null>`NULL`.as("notes"),
		createdAt: users.createdAt,
		updatedAt: users.updatedAt,
		totalDonated: sql<string>`COALESCE(SUM(${donations.amount}), 0)::text`.as(
			"totalDonated",
		),
		donationCount: sql<number>`COUNT(${donations.id})::int`.as("donationCount"),
		averageDonation:
			sql<string>`COALESCE(ROUND(AVG(${donations.amount}), 2), 0)::text`.as(
				"averageDonation",
			),
		lastDonationDate: sql<Date | null>`MAX(${donations.createdAt})`.as(
			"lastDonationDate",
		),
		firstDonationDate: sql<Date | null>`MIN(${donations.createdAt})`.as(
			"firstDonationDate",
		),
		campaignsSupported:
			sql<number>`COUNT(DISTINCT ${donations.campaignId})::int`.as(
				"campaignsSupported",
			),
	}
}

/**
 * Build WHERE conditions for organizer-scoped donor access
 */
export function buildDonorWhereConditions(
	organizerId: string,
	filters: DonorFilters,
) {
	const conditions = [
		eq(campaigns.organizerId, organizerId),
		eq(users.role, "user"),
	]

	// Search filter - name or email
	if (filters.search) {
		conditions.push(
			or(
				ilike(users.name, `%${filters.search}%`),
				ilike(users.email, `%${filters.search}%`),
			),
		)
	}

	// Campaign filter
	if (filters.campaignId) {
		conditions.push(eq(donations.campaignId, filters.campaignId))
	}

	// Date range filters
	if (filters.dateFrom) {
		conditions.push(sql`${donations.createdAt} >= ${filters.dateFrom}`)
	}

	if (filters.dateTo) {
		conditions.push(sql`${donations.createdAt} <= ${filters.dateTo}`)
	}

	return conditions
}

/**
 * Build additional WHERE conditions for tag filtering
 * This needs to be applied as a separate filter since it involves different joins
 */
export async function applyTagFilter(donorIds: string[], tagIds: string[]) {
	if (tagIds.length === 0) return donorIds

	const taggedDonors = await db
		.selectDistinct({ donorId: donorTagAssignments.donorId })
		.from(donorTagAssignments)
		.where(inArray(donorTagAssignments.tagId, tagIds))

	const taggedDonorIds = taggedDonors.map((d) => d.donorId)
	return donorIds.filter((id) => taggedDonorIds.includes(id))
}

/**
 * Build additional WHERE conditions for note search
 * This needs to be applied as a separate filter since it involves different joins
 */
export async function applyNoteSearchFilter(
	donorIds: string[],
	noteSearch: string,
) {
	if (!noteSearch) return donorIds

	const donorsWithMatchingNotes = await db
		.selectDistinct({ donorId: donorNotes.donorId })
		.from(donorNotes)
		.where(
			and(
				inArray(donorNotes.donorId, donorIds),
				ilike(donorNotes.content, `%${noteSearch}%`),
			),
		)

	return donorsWithMatchingNotes.map((d) => d.donorId)
}

/**
 * Build HAVING conditions for amount-based filtering
 */
export function buildDonorHavingConditions(filters: DonorFilters) {
	const havingConditions = []

	if (filters.minAmount !== undefined) {
		havingConditions.push(sql`SUM(${donations.amount}) >= ${filters.minAmount}`)
	}

	if (filters.maxAmount !== undefined) {
		havingConditions.push(sql`SUM(${donations.amount}) <= ${filters.maxAmount}`)
	}

	return havingConditions
}

/**
 * Build ORDER BY clause for donor sorting
 */
export function buildDonorOrderBy(
	sortBy: string = "name",
	sortOrder: string = "asc",
) {
	switch (sortBy) {
		case "email":
			return sortOrder === "desc" ? desc(users.email) : users.email
		case "totalAmount":
			return sortOrder === "desc"
				? sql`SUM(${donations.amount}) DESC`
				: sql`SUM(${donations.amount}) ASC`
		case "lastDonation":
			return sortOrder === "desc"
				? sql`MAX(${donations.createdAt}) DESC`
				: sql`MAX(${donations.createdAt}) ASC`
		default:
			return sortOrder === "desc" ? desc(users.name) : users.name
	}
}

/**
 * Get GROUP BY fields for donor queries
 */
export function getDonorGroupByFields() {
	return [
		users.id,
		users.name,
		users.email,
		users.image, // Temporarily using image field for phone
		users.createdAt,
		users.updatedAt,
	]
}

/**
 * Optimized count query for donors with organizer scope and new filters
 */
export async function getDonorCount(
	organizerId: string,
	filters: DonorFilters,
): Promise<number> {
	const conditions = buildDonorWhereConditions(organizerId, filters)
	const havingConditions = buildDonorHavingConditions(filters)

	// Use a subquery to get distinct donors first, then count
	let baseQuery = db
		.selectDistinct({ donorId: users.id })
		.from(users)
		.innerJoin(donations, eq(users.id, donations.donorId))
		.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
		.where(and(...conditions))

	// If we have amount filters, we need to group and apply HAVING
	if (havingConditions.length > 0) {
		baseQuery = db
			.select({ donorId: users.id })
			.from(users)
			.innerJoin(donations, eq(users.id, donations.donorId))
			.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
			.where(and(...conditions))
			.groupBy(users.id)
			.having(and(...havingConditions))
	}

	// Get initial donor IDs
	const initialDonors = await baseQuery
	let donorIds = initialDonors.map((d) => d.donorId)

	// Apply tag and note filters if specified
	if (filters.tagIds && filters.tagIds.length > 0) {
		donorIds = await applyTagFilter(donorIds, filters.tagIds)
	}

	if (filters.noteSearch) {
		donorIds = await applyNoteSearchFilter(donorIds, filters.noteSearch)
	}

	return donorIds.length
}

/**
 * Main query for getting donors with statistics, tags, and notes
 */
export async function buildDonorsQuery(
	organizerId: string,
	filters: DonorFilters,
	page: number = 1,
	limit: number = 20,
) {
	const conditions = buildDonorWhereConditions(organizerId, filters)
	const havingConditions = buildDonorHavingConditions(filters)
	const orderBy = buildDonorOrderBy(filters.sortBy, filters.sortOrder)
	const groupByFields = getDonorGroupByFields()

	let query = db
		.select(buildDonorStatsSelect())
		.from(users)
		.innerJoin(donations, eq(users.id, donations.donorId))
		.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
		.where(and(...conditions))
		.groupBy(...groupByFields)

	// Apply HAVING conditions if any
	if (havingConditions.length > 0) {
		query = query.having(and(...havingConditions))
	}

	// Get initial donor data
	let donorsData = await query
		.orderBy(orderBy)
		.limit(limit)
		.offset((page - 1) * limit)

	// Apply tag and note filters if specified
	let filteredDonorIds = donorsData.map((d) => d.id)

	if (filters.tagIds && filters.tagIds.length > 0) {
		filteredDonorIds = await applyTagFilter(filteredDonorIds, filters.tagIds)
		donorsData = donorsData.filter((d) => filteredDonorIds.includes(d.id))
	}

	if (filters.noteSearch) {
		filteredDonorIds = await applyNoteSearchFilter(
			filteredDonorIds,
			filters.noteSearch,
		)
		donorsData = donorsData.filter((d) => filteredDonorIds.includes(d.id))
	}

	// Fetch tags and notes for the filtered donors
	const { tags, notes } = await getDonorsTagsAndNotes(filteredDonorIds)

	// Combine donor data with tags and notes
	return donorsData.map((donor) => ({
		...donor,
		tags: tags[donor.id] || [],
		donorNotes: notes[donor.id] || [],
	}))
}

/**
 * Query for getting a single donor with statistics, tags, and notes
 */
export async function buildSingleDonorQuery(
	organizerId: string,
	donorId: string,
) {
	const conditions = [
		eq(users.id, donorId),
		eq(campaigns.organizerId, organizerId),
		eq(users.role, "user"),
	]

	const donorData = await db
		.select(buildDonorStatsSelect())
		.from(users)
		.innerJoin(donations, eq(users.id, donations.donorId))
		.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
		.where(and(...conditions))
		.groupBy(...getDonorGroupByFields())
		.limit(1)

	if (donorData.length === 0) {
		return []
	}

	// Fetch tags and notes for this donor
	const [tags, notes] = await Promise.all([
		getDonorTags(donorId),
		getDonorNotes(donorId),
	])

	// Combine donor data with tags and notes
	return [
		{
			...donorData[0],
			tags,
			donorNotes: notes,
		},
	]
}

/**
 * Verify donor access for organizer (used in update/delete operations)
 */
export async function verifyDonorAccess(
	organizerId: string,
	donorId: string,
): Promise<boolean> {
	const result = await db
		.select({ id: users.id })
		.from(users)
		.innerJoin(donations, eq(users.id, donations.donorId))
		.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
		.where(
			and(
				eq(users.id, donorId),
				eq(campaigns.organizerId, organizerId),
				eq(users.role, "user"),
			),
		)
		.limit(1)

	return result.length > 0
}

/**
 * Check if donor has any donations (used before deletion)
 */
export async function donorHasDonations(donorId: string): Promise<boolean> {
	const result = await db
		.select({ id: donations.id })
		.from(donations)
		.where(eq(donations.donorId, donorId))
		.limit(1)

	return result.length > 0
}

/**
 * Database indexing recommendations for optimal performance
 * These should be added as database migrations
 */
export const RECOMMENDED_INDEXES = [
	// Composite index for organizer-scoped donor queries
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_campaigns_organizer_id ON campaigns(organizer_id);",

	// Composite index for donation lookups
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_donations_donor_campaign ON donations(donor_id, campaign_id);",

	// Index for donation date filtering
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_donations_created_at ON donations(created_at);",

	// Index for donation amount filtering
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_donations_amount ON donations(amount);",

	// Composite index for user role and email searches
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role_email ON users(role, email);",

	// Index for user name searches (using gin for better text search)
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_name_gin ON users USING gin(name gin_trgm_ops);",

	// Index for user email searches (using gin for better text search)
	"CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_gin ON users USING gin(email gin_trgm_ops);",
] as const

/**
 * Performance optimization notes:
 *
 * 1. The queries use INNER JOINs to ensure only donors with donations are returned
 * 2. All WHERE clauses include organizer_id filtering for security
 * 3. GROUP BY is optimized to include only necessary fields
 * 4. COUNT queries use subqueries to avoid expensive aggregations
 * 5. HAVING clauses are used for amount-based filtering after aggregation
 * 6. Proper indexing recommendations are provided above
 *
 * Query execution plan considerations:
 * - The organizer_id filter should be applied first (most selective)
 * - Date range filters are applied early in the query plan
 * - Text searches use ILIKE with proper indexing
 * - Amount filters use HAVING to work with aggregated data
 */
