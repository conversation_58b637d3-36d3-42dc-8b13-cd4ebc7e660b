import { and, eq, inArray, sql } from "drizzle-orm"
import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { authController } from "../auth/auth.controller"
import { verifyDonorAccess } from "./donors.queries"
import {
	assignTagsDto,
	assignTagsResponseDto,
	createTagDto,
	createTagResponseDto,
	deleteTagResponseDto,
	donorParamsDto,
	tagsListResponseDto,
	updateTagDto,
	updateTagResponseDto,
} from "./donors.schema"
import { donorTagAssignments, donorTags } from "./donors-tags.schema"

export const donorsController = new Elysia({ prefix: "/api/donors" })
	.use(authController)
	.get(
		"/tags",
		async ({ user, status }) => {
			try {
				const tags = await db
					.select()
					.from(donorTags)
					.where(eq(donorTags.organizerId, user.id))
					.orderBy(donorTags.name)

				return {
					success: true,
					tags,
				}
			} catch (error) {
				console.error("Error fetching tags:", error)
				return status(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to fetch tags")
			}
		},
		{
			requirePermission: { donor: ["view"] },
			response: {
				[StatusCodes.OK]: tagsListResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.post(
		"/tags",
		async ({ body, user, status }) => {
			try {
				// Check if tag name already exists for this organizer
				const existingTag = await db
					.select()
					.from(donorTags)
					.where(
						and(
							eq(donorTags.organizerId, user.id),
							eq(donorTags.name, body.name),
						),
					)
					.limit(1)

				if (existingTag.length > 0) {
					return status(
						StatusCodes.CONFLICT,
						"A tag with this name already exists",
					)
				}

				const [newTag] = await db
					.insert(donorTags)
					.values({
						organizerId: user.id,
						name: body.name,
						color: body.color || "#3b82f6",
					})
					.returning()

				return status(StatusCodes.CREATED, {
					success: true,
					tag: newTag,
				})
			} catch (error) {
				console.error("Error creating tag:", error)
				return status(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to create tag")
			}
		},
		{
			requirePermission: { donor: ["create"] },
			body: createTagDto,
			response: {
				[StatusCodes.CREATED]: createTagResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.put(
		"/tags/:tagId",
		async ({ params, body, user, status }) => {
			try {
				// Verify tag ownership
				const existingTag = await db
					.select()
					.from(donorTags)
					.where(
						and(
							eq(donorTags.id, params.tagId),
							eq(donorTags.organizerId, user.id),
						),
					)
					.limit(1)

				if (existingTag.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Tag not found")
				}

				// Check if new name conflicts with existing tags
				if (body.name !== existingTag[0].name) {
					const nameConflict = await db
						.select()
						.from(donorTags)
						.where(
							and(
								eq(donorTags.organizerId, user.id),
								eq(donorTags.name, body.name),
								sql`${donorTags.id} != ${params.tagId}`,
							),
						)
						.limit(1)

					if (nameConflict.length > 0) {
						return status(
							StatusCodes.CONFLICT,
							"A tag with this name already exists",
						)
					}
				}

				const [updatedTag] = await db
					.update(donorTags)
					.set({
						name: body.name,
						color: body.color,
						updatedAt: new Date(),
					})
					.where(eq(donorTags.id, params.tagId))
					.returning()

				return {
					success: true,
					tag: updatedTag,
				}
			} catch (error) {
				console.error("Error updating tag:", error)
				return status(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to update tag")
			}
		},
		{
			requirePermission: { donor: ["edit"] },
			params: t.Object({ tagId: t.String() }),
			body: updateTagDto,
			response: {
				[StatusCodes.OK]: updateTagResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.delete(
		"/tags/:tagId",
		async ({ params, user, status }) => {
			try {
				// Verify tag ownership
				const existingTag = await db
					.select()
					.from(donorTags)
					.where(
						and(
							eq(donorTags.id, params.tagId),
							eq(donorTags.organizerId, user.id),
						),
					)
					.limit(1)

				if (existingTag.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Tag not found")
				}

				// Delete tag (assignments will be cascade deleted)
				await db.delete(donorTags).where(eq(donorTags.id, params.tagId))

				return {
					success: true,
					message: "Tag deleted successfully",
				}
			} catch (error) {
				console.error("Error deleting tag:", error)
				return status(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to delete tag")
			}
		},
		{
			requirePermission: { donor: ["delete"] },
			params: t.Object({ tagId: t.String() }),
			response: {
				[StatusCodes.OK]: deleteTagResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.post(
		"/:id/tags",
		async ({ params, body, user, status }) => {
			try {
				// Verify donor access
				const hasAccess = await verifyDonorAccess(user.id, params.id)
				if (!hasAccess) {
					return status(
						StatusCodes.NOT_FOUND,
						"Donor not found or access denied",
					)
				}

				// Verify all tags belong to the organizer
				if (body.tagIds.length > 0) {
					const tagCheck = await db
						.select()
						.from(donorTags)
						.where(
							and(
								eq(donorTags.organizerId, user.id),
								inArray(donorTags.id, body.tagIds),
							),
						)

					if (tagCheck.length !== body.tagIds.length) {
						return status(StatusCodes.BAD_REQUEST, "Invalid tag IDs provided")
					}
				}

				// Remove existing tag assignments for this donor
				await db
					.delete(donorTagAssignments)
					.where(eq(donorTagAssignments.donorId, params.id))

				// Add new tag assignments
				if (body.tagIds.length > 0) {
					await db.insert(donorTagAssignments).values(
						body.tagIds.map((tagId) => ({
							donorId: params.id,
							tagId,
						})),
					)
				}

				return {
					success: true,
					message: "Tags assigned successfully",
				}
			} catch (error) {
				console.error("Error assigning tags:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to assign tags",
				)
			}
		},
		{
			requirePermission: { donor: ["edit"] },
			params: donorParamsDto,
			body: assignTagsDto,
			response: {
				[StatusCodes.OK]: assignTagsResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.BAD_REQUEST]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)
