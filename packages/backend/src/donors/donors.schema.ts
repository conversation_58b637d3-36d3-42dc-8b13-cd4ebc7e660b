import { t } from "elysia"

// Tag management schemas
export const createTagDto = t.Object({
	name: t.String({ minLength: 1, maxLength: 50 }),
	color: t.Optional(t.String({ pattern: "^#[0-9A-Fa-f]{6}$" })),
})

export const updateTagDto = t.Object({
	name: t.String({ minLength: 1, maxLength: 50 }),
	color: t.String({ pattern: "^#[0-9A-Fa-f]{6}$" }),
})

export const tagDto = t.Object({
	id: t.String(),
	name: t.String(),
	color: t.String(),
	createdAt: t.Date(),
	updatedAt: t.Date(),
})

export const assignTagsDto = t.Object({
	tagIds: t.Array(t.String()),
})

// Note management schemas
export const createNoteDto = t.Object({
	content: t.String({ minLength: 1, maxLength: 5000 }),
})

export const updateNoteDto = t.Object({
	content: t.String({ minLength: 1, maxLength: 5000 }),
})

export const noteDto = t.Object({
	id: t.String(),
	content: t.String(),
	organizerId: t.String(),
	organizerName: t.String(),
	createdAt: t.Date(),
	updatedAt: t.Date(),
})

// Request schemas
export const donorFiltersDto = t.Object({
	search: t.Optional(t.String()),
	minAmount: t.Optional(t.Number({ minimum: 0 })),
	maxAmount: t.Optional(t.Number({ minimum: 0 })),
	dateFrom: t.Optional(t.String({ format: "date" })),
	dateTo: t.Optional(t.String({ format: "date" })),
	campaignId: t.Optional(t.String({ format: "uuid" })),
	tagIds: t.Optional(t.Array(t.String())),
	noteSearch: t.Optional(t.String()),
	sortBy: t.Optional(
		t.Union([
			t.Literal("name"),
			t.Literal("email"),
			t.Literal("totalAmount"),
			t.Literal("lastDonation"),
		]),
	),
	sortOrder: t.Optional(t.Union([t.Literal("asc"), t.Literal("desc")])),
	page: t.Optional(t.Number({ minimum: 1 })),
	limit: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
})

export const createDonorDto = t.Object({
	name: t.String({ minLength: 1, maxLength: 255 }),
	email: t.String({ format: "email" }),
	phone: t.Optional(t.String({ maxLength: 50 })),
	address: t.Optional(t.String({ maxLength: 500 })),
	notes: t.Optional(t.String({ maxLength: 2000 })),
})

export const updateDonorDto = t.Object({
	name: t.String({ minLength: 1, maxLength: 255 }),
	email: t.String({ format: "email" }),
	phone: t.Optional(t.String({ maxLength: 50 })),
	address: t.Optional(t.String({ maxLength: 500 })),
	notes: t.Optional(t.String({ maxLength: 2000 })),
})

export const donorParamsDto = t.Object({
	id: t.String(),
})

// Response schemas
export const donorWithStatsDto = t.Object({
	id: t.String(),
	name: t.String(),
	email: t.String(),
	phone: t.Union([t.String(), t.Null()]),
	address: t.Union([t.String(), t.Null()]),
	notes: t.Union([t.String(), t.Null()]),
	createdAt: t.Date(),
	updatedAt: t.Date(),
	totalDonated: t.String(),
	donationCount: t.Number(),
	averageDonation: t.String(),
	lastDonationDate: t.Union([t.Date(), t.Null()]),
	campaignsSupported: t.Number(),
	firstDonationDate: t.Union([t.Date(), t.Null()]),
	tags: t.Array(tagDto),
	donorNotes: t.Array(noteDto),
})

export const donorsListResponseDto = t.Object({
	success: t.Boolean(),
	donors: t.Array(donorWithStatsDto),
	pagination: t.Object({
		total: t.Number(),
		page: t.Number(),
		limit: t.Number(),
		totalPages: t.Number(),
	}),
})

export const donorResponseDto = t.Object({
	success: t.Boolean(),
	donor: donorWithStatsDto,
})

export const createDonorResponseDto = t.Object({
	success: t.Boolean(),
	donor: donorWithStatsDto,
})

export const updateDonorResponseDto = t.Object({
	success: t.Boolean(),
	donor: donorWithStatsDto,
})

export const deleteDonorResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})

export const exportDonorsResponseDto = t.Object({
	success: t.Boolean(),
	csvData: t.String(),
	filename: t.String(),
})

// Tag management response DTOs
export const tagsListResponseDto = t.Object({
	success: t.Boolean(),
	tags: t.Array(tagDto),
})

export const createTagResponseDto = t.Object({
	success: t.Boolean(),
	tag: tagDto,
})

export const updateTagResponseDto = t.Object({
	success: t.Boolean(),
	tag: tagDto,
})

export const deleteTagResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})

export const assignTagsResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})

// Note management response DTOs
export const notesListResponseDto = t.Object({
	success: t.Boolean(),
	notes: t.Array(noteDto),
})

export const createNoteResponseDto = t.Object({
	success: t.Boolean(),
	note: noteDto,
})

export const updateNoteResponseDto = t.Object({
	success: t.Boolean(),
	note: noteDto,
})

export const deleteNoteResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})

// Type exports for frontend consumption
export type DonorFilters = typeof donorFiltersDto.static
export type CreateDonor = typeof createDonorDto.static
export type UpdateDonor = typeof updateDonorDto.static
export type DonorParams = typeof donorParamsDto.static
export type DonorWithStats = typeof donorWithStatsDto.static
export type DonorsListResponse = typeof donorsListResponseDto.static
export type DonorResponse = typeof donorResponseDto.static
export type CreateDonorResponse = typeof createDonorResponseDto.static
export type UpdateDonorResponse = typeof updateDonorResponseDto.static
export type DeleteDonorResponse = typeof deleteDonorResponseDto.static
export type ExportDonorsResponse = typeof exportDonorsResponseDto.static

// Tag management types
export type CreateTag = typeof createTagDto.static
export type UpdateTag = typeof updateTagDto.static
export type Tag = typeof tagDto.static
export type AssignTags = typeof assignTagsDto.static
export type TagsListResponse = typeof tagsListResponseDto.static
export type CreateTagResponse = typeof createTagResponseDto.static
export type UpdateTagResponse = typeof updateTagResponseDto.static
export type DeleteTagResponse = typeof deleteTagResponseDto.static
export type AssignTagsResponse = typeof assignTagsResponseDto.static

// Note management types
export type CreateNote = typeof createNoteDto.static
export type UpdateNote = typeof updateNoteDto.static
export type Note = typeof noteDto.static
export type NotesListResponse = typeof notesListResponseDto.static
export type CreateNoteResponse = typeof createNoteResponseDto.static
export type UpdateNoteResponse = typeof updateNoteResponseDto.static
export type DeleteNoteResponse = typeof deleteNoteResponseDto.static
