import { and, eq, sql } from "drizzle-orm"
import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { authController } from "../auth/auth.controller"
import { users } from "../users/users.schema"
import { verifyDonorAccess } from "./donors.queries"
import {
	createNoteDto,
	createNoteResponseDto,
	deleteNoteResponseDto,
	donorParamsDto,
	notesListResponseDto,
	updateNoteDto,
	updateNoteResponseDto,
} from "./donors.schema"
import { donorNotes } from "./donors-tags.schema"

export const donorsController = new Elysia({ prefix: "/api/donors/:id" })
	.use(authController)
	.get(
		"/notes",
		async ({ params, user, status }) => {
			try {
				// Verify donor access
				const hasAccess = await verifyDonorAccess(user.id, params.id)
				if (!hasAccess) {
					return status(
						StatusCodes.NOT_FOUND,
						"Donor not found or access denied",
					)
				}

				const notes = await db
					.select({
						id: donorNotes.id,
						content: donorNotes.content,
						organizerId: donorNotes.organizerId,
						organizerName: users.name,
						createdAt: donorNotes.createdAt,
						updatedAt: donorNotes.updatedAt,
					})
					.from(donorNotes)
					.leftJoin(users, eq(donorNotes.organizerId, users.id))
					.where(eq(donorNotes.donorId, params.id))
					.orderBy(sql`${donorNotes.createdAt} DESC`)

				return {
					success: true,
					notes,
				}
			} catch (error) {
				console.error("Error fetching notes:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch notes",
				)
			}
		},
		{
			requirePermission: { donor: ["view"] },
			params: donorParamsDto,
			response: {
				[StatusCodes.OK]: notesListResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.post(
		"/notes",
		async ({ params, body, user, status }) => {
			try {
				// Verify donor access
				const hasAccess = await verifyDonorAccess(user.id, params.id)
				if (!hasAccess) {
					return status(
						StatusCodes.NOT_FOUND,
						"Donor not found or access denied",
					)
				}

				const [newNote] = await db
					.insert(donorNotes)
					.values({
						donorId: params.id,
						organizerId: user.id,
						content: body.content,
					})
					.returning()

				// Get note with organizer name
				const noteWithOrganizer = await db
					.select({
						id: donorNotes.id,
						content: donorNotes.content,
						organizerId: donorNotes.organizerId,
						organizerName: users.name,
						createdAt: donorNotes.createdAt,
						updatedAt: donorNotes.updatedAt,
					})
					.from(donorNotes)
					.leftJoin(users, eq(donorNotes.organizerId, users.id))
					.where(eq(donorNotes.id, newNote.id))
					.limit(1)

				return status(StatusCodes.CREATED, {
					success: true,
					note: noteWithOrganizer[0],
				})
			} catch (error) {
				console.error("Error creating note:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to create note",
				)
			}
		},
		{
			requirePermission: { donor: ["edit"] },
			params: donorParamsDto,
			body: createNoteDto,
			response: {
				[StatusCodes.CREATED]: createNoteResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.put(
		"/notes/:noteId",
		async ({ params, body, user, status }) => {
			try {
				// Verify note ownership
				const existingNote = await db
					.select()
					.from(donorNotes)
					.where(
						and(
							eq(donorNotes.id, params.noteId),
							eq(donorNotes.donorId, params.id),
							eq(donorNotes.organizerId, user.id),
						),
					)
					.limit(1)

				if (existingNote.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Note not found")
				}

				const [updatedNote] = await db
					.update(donorNotes)
					.set({
						content: body.content,
						updatedAt: new Date(),
					})
					.where(eq(donorNotes.id, params.noteId))
					.returning()

				// Get note with organizer name
				const noteWithOrganizer = await db
					.select({
						id: donorNotes.id,
						content: donorNotes.content,
						organizerId: donorNotes.organizerId,
						organizerName: users.name,
						createdAt: donorNotes.createdAt,
						updatedAt: donorNotes.updatedAt,
					})
					.from(donorNotes)
					.leftJoin(users, eq(donorNotes.organizerId, users.id))
					.where(eq(donorNotes.id, updatedNote.id))
					.limit(1)

				return {
					success: true,
					note: noteWithOrganizer[0],
				}
			} catch (error) {
				console.error("Error updating note:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update note",
				)
			}
		},
		{
			requirePermission: { donor: ["edit"] },
			params: t.Object({ id: t.String(), noteId: t.String() }),
			body: updateNoteDto,
			response: {
				[StatusCodes.OK]: updateNoteResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.delete(
		"/notes/:noteId",
		async ({ params, user, status }) => {
			try {
				// Verify note ownership
				const existingNote = await db
					.select()
					.from(donorNotes)
					.where(
						and(
							eq(donorNotes.id, params.noteId),
							eq(donorNotes.donorId, params.id),
							eq(donorNotes.organizerId, user.id),
						),
					)
					.limit(1)

				if (existingNote.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Note not found")
				}

				await db.delete(donorNotes).where(eq(donorNotes.id, params.noteId))

				return {
					success: true,
					message: "Note deleted successfully",
				}
			} catch (error) {
				console.error("Error deleting note:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to delete note",
				)
			}
		},
		{
			requirePermission: { donor: ["delete"] },
			params: t.Object({ id: t.String(), noteId: t.String() }),
			response: {
				[StatusCodes.OK]: deleteNoteResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)
