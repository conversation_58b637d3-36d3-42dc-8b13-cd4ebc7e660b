import { and, eq, sql } from "drizzle-orm"
import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { authController } from "../auth/auth.controller"
import { users } from "../users/users.schema"
import {
	buildDonorsQuery,
	buildSingleDonorQuery,
	donorHasDonations,
	getDonorCount,
	verifyDonorAccess,
} from "./donors.queries"
import {
	createDonorDto,
	createDonorResponseDto,
	deleteDonorResponseDto,
	donorFiltersDto,
	donorParamsDto,
	donorResponseDto,
	donorsListResponseDto,
	exportDonorsResponseDto,
	updateDonorDto,
	updateDonorResponseDto,
} from "./donors.schema"

export const donorsController = new Elysia({ prefix: "/api/donors" })
	.use(authController)
	.get(
		"/",
		async ({ query, user, status }) => {
			try {
				const { page = 1, limit = 20, ...filters } = query

				// Get total count using optimized query
				const totalCount = await getDonorCount(user.id, filters)

				// Get donors data using optimized query
				const donorsData = await buildDonorsQuery(user.id, filters, page, limit)

				const totalPages = Math.ceil(totalCount / limit)

				return {
					success: true,
					donors: donorsData,
					pagination: {
						total: totalCount,
						page,
						limit,
						totalPages,
					},
				}
			} catch (error) {
				console.error("Error fetching donors:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch donors",
				)
			}
		},
		{
			requirePermission: { donor: ["view"] },
			query: donorFiltersDto,
			response: {
				[StatusCodes.OK]: donorsListResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/:id",
		async ({ params, user, status }) => {
			try {
				// Get donor with stats using optimized query
				const donorData = await buildSingleDonorQuery(user.id, params.id)

				if (donorData.length === 0) {
					return status(
						StatusCodes.NOT_FOUND,
						"Donor not found or access denied",
					)
				}

				return {
					success: true,
					donor: donorData[0],
				}
			} catch (error) {
				console.error("Error fetching donor:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch donor",
				)
			}
		},
		{
			requirePermission: { donor: ["view"] },
			params: donorParamsDto,
			response: {
				[StatusCodes.OK]: donorResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.post(
		"/",
		async ({ body, user, status }) => {
			try {
				// Check if email already exists
				const existingUser = await db
					.select()
					.from(users)
					.where(eq(users.email, body.email))
					.limit(1)

				if (existingUser.length > 0) {
					return status(
						StatusCodes.CONFLICT,
						"A user with this email already exists",
					)
				}

				// Create new donor user
				const [newDonor] = await db
					.insert(users)
					.values({
						id: crypto.randomUUID(),
						name: body.name,
						email: body.email,
						role: "user",
						emailVerified: false,
					})
					.returning()

				// Return donor with initial stats (all zeros since no donations yet)
				const donorWithStats = {
					...newDonor,
					phone: null,
					address: null,
					notes: null,
					totalDonated: "0",
					donationCount: 0,
					averageDonation: "0",
					lastDonationDate: null,
					firstDonationDate: null,
					campaignsSupported: 0,
				}

				return status(StatusCodes.CREATED, {
					success: true,
					donor: donorWithStats,
				})
			} catch (error) {
				console.error("Error creating donor:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to create donor",
				)
			}
		},
		{
			requirePermission: { donor: ["create"] },
			body: createDonorDto,
			response: {
				[StatusCodes.CREATED]: createDonorResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.put(
		"/:id",
		async ({ params, body, user, status }) => {
			try {
				// Verify donor access using optimized query
				const hasAccess = await verifyDonorAccess(user.id, params.id)
				if (!hasAccess) {
					return status(
						StatusCodes.NOT_FOUND,
						"Donor not found or access denied",
					)
				}

				// Check if email is being changed and if it conflicts with existing users
				if (body.email) {
					const existingUser = await db
						.select()
						.from(users)
						.where(
							and(
								eq(users.email, body.email),
								sql`${users.id} != ${params.id}`,
							),
						)
						.limit(1)

					if (existingUser.length > 0) {
						return status(
							StatusCodes.CONFLICT,
							"A user with this email already exists",
						)
					}
				}

				// Update donor
				await db
					.update(users)
					.set({
						name: body.name,
						email: body.email,
						updatedAt: new Date(),
					})
					.where(eq(users.id, params.id))

				// Get updated donor with stats using optimized query
				const donorWithStats = await buildSingleDonorQuery(user.id, params.id)

				return {
					success: true,
					donor: donorWithStats[0],
				}
			} catch (error) {
				console.error("Error updating donor:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update donor",
				)
			}
		},
		{
			requirePermission: { donor: ["edit"] },
			params: donorParamsDto,
			body: updateDonorDto,
			response: {
				[StatusCodes.OK]: updateDonorResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.delete(
		"/:id",
		async ({ params, user, status }) => {
			try {
				// Verify donor access using optimized query
				const hasAccess = await verifyDonorAccess(user.id, params.id)
				if (!hasAccess) {
					return status(
						StatusCodes.NOT_FOUND,
						"Donor not found or access denied",
					)
				}

				// Check if donor has any donations using optimized query
				const hasDonations = await donorHasDonations(params.id)
				if (hasDonations) {
					return status(
						StatusCodes.CONFLICT,
						"Cannot delete donor with existing donation history. Consider marking as inactive instead.",
					)
				}

				// Delete donor (this should rarely happen since donors are created through donations)
				await db.delete(users).where(eq(users.id, params.id))

				return {
					success: true,
					message: "Donor deleted successfully",
				}
			} catch (error) {
				console.error("Error deleting donor:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to delete donor",
				)
			}
		},
		{
			requirePermission: { donor: ["delete"] },
			params: donorParamsDto,
			response: {
				[StatusCodes.OK]: deleteDonorResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/export",
		async ({ query, user, status }) => {
			try {
				const filters = query

				// Get all donors for export (no pagination) using optimized query
				// Use a high limit to get all results
				const donorsData = await buildDonorsQuery(user.id, filters, 1, 10000)

				// Generate CSV
				const headers = [
					"ID",
					"Name",
					"Email",
					"Total Donated",
					"Donation Count",
					"Average Donation",
					"Campaigns Supported",
					"First Donation",
					"Last Donation",
					"Registered Date",
				]

				const csvRows = [
					headers.join(","),
					...donorsData.map((donor) =>
						[
							donor.id,
							`"${donor.name.replace(/"/g, '""')}"`,
							donor.email,
							donor.totalDonated,
							donor.donationCount,
							donor.averageDonation,
							donor.campaignsSupported,
							donor.firstDonationDate?.toISOString().split("T")[0] || "",
							donor.lastDonationDate?.toISOString().split("T")[0] || "",
							donor.createdAt.toISOString().split("T")[0],
						].join(","),
					),
				]

				const csv = csvRows.join("\n")
				const filename = `donors-export-${new Date().toISOString().split("T")[0]}.csv`

				return {
					success: true,
					csvData: csv,
					filename,
				}
			} catch (error) {
				console.error("Error exporting donors:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to export donors",
				)
			}
		},
		{
			requirePermission: { donor: ["view"] },
			query: donorFiltersDto,
			response: {
				[StatusCodes.OK]: exportDonorsResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)
