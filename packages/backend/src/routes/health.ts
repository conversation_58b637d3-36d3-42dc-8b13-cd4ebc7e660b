import { Elysia } from "elysia"
import { db } from "@/db"
import { users } from "@/users/users.schema"

export const healthRoutes = new Elysia({ prefix: "/health" }).get(
	"/",
	async () => {
		try {
			// Test database connection by querying a simple count
			await db.$count(users)

			return {
				status: "ok",
				database: "connected",
				timestamp: new Date().toISOString(),
			}
		} catch (error) {
			console.error("Health check failed:", error)

			return {
				status: "error",
				database: "disconnected",
				error: error.message,
				timestamp: new Date().toISOString(),
			}
		}
	},
)
