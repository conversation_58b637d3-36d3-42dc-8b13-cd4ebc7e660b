import { sql } from "drizzle-orm"
import {
	decimal,
	integer,
	pgEnum,
	pgPolicy,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core"
import { t } from "elysia"
import { campaigns } from "../campaigns/campaigns.schema"
import {
	pgAdminRole,
	pgAuthenticatedRole,
	pgOrganizerRole,
	users,
} from "../users/users.schema"

// Enums for recurring donations
export const recurringStatusEnum = pgEnum("recurring_status", [
	"active",
	"paused",
	"canceled",
	"expired",
])

export const recurringFrequencyEnum = pgEnum("recurring_frequency", [
	"monthly",
	"quarterly",
	"yearly",
])

// Recurring donations table
export const recurringDonations = pgTable(
	"recurring_donations",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		campaignId: uuid("campaign_id")
			.references(() => campaigns.id)
			.notNull(),
		donorId: text("donor_id")
			.references(() => users.id)
			.notNull(),
		amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
		currency: text("currency").default("MYR").notNull(),
		frequency: recurringFrequencyEnum("frequency").notNull().default("monthly"),
		status: recurringStatusEnum("status").notNull().default("active"),
		chipRecurringToken: text("chip_recurring_token").notNull(),
		chipTokenExpiresAt: timestamp("chip_token_expires_at"),
		donorName: text("donor_name").notNull(),
		donorEmail: text("donor_email").notNull(),
		donorPhone: text("donor_phone"),
		nextPaymentDate: timestamp("next_payment_date").notNull(),
		lastPaymentDate: timestamp("last_payment_date"),
		failedAttempts: integer("failed_attempts").default(0).notNull(),
		maxRetries: integer("max_retries").default(3).notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(t) => [
		// Organizers can access recurring donations for their campaigns
		pgPolicy("recurring_donations_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`EXISTS (
				SELECT 1 FROM campaigns c
				WHERE c.id = ${t.campaignId}
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)`,
			withCheck: sql`EXISTS (
				SELECT 1 FROM campaigns c
				WHERE c.id = ${t.campaignId}
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)`,
		}),
		// Donors can view their own recurring donations
		pgPolicy("recurring_donations_donor_access", {
			for: "select",
			to: [pgAuthenticatedRole],
			using: sql`${t.donorId} = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'user'`,
		}),
	],
)

// Recurring payment attempts table for tracking retry logic
export const recurringPaymentAttempts = pgTable(
	"recurring_payment_attempts",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		recurringDonationId: uuid("recurring_donation_id")
			.references(() => recurringDonations.id, { onDelete: "cascade" })
			.notNull(),
		attemptNumber: integer("attempt_number").notNull(),
		chipPaymentId: text("chip_payment_id"),
		status: text("status").notNull(), // "pending", "completed", "failed"
		errorMessage: text("error_message"),
		attemptedAt: timestamp("attempted_at").defaultNow().notNull(),
		completedAt: timestamp("completed_at"),
	},
	(t) => [
		// Organizers can access payment attempts for their recurring donations
		pgPolicy("recurring_payment_attempts_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`EXISTS (
				SELECT 1 FROM recurring_donations rd
				JOIN campaigns c ON c.id = rd.campaign_id
				WHERE rd.id = ${t.recurringDonationId}
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)`,
			withCheck: sql`EXISTS (
				SELECT 1 FROM recurring_donations rd
				JOIN campaigns c ON c.id = rd.campaign_id
				WHERE rd.id = ${t.recurringDonationId}
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)`,
		}),
		// Donors can view payment attempts for their own recurring donations
		pgPolicy("recurring_payment_attempts_donor_access", {
			for: "select",
			to: [pgAuthenticatedRole],
			using: sql`EXISTS (
				SELECT 1 FROM recurring_donations rd
				WHERE rd.id = ${t.recurringDonationId}
				AND rd.donor_id = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'user'
			)`,
		}),
	],
)

export type RecurringDonation = typeof recurringDonations.$inferSelect
export type CreateRecurringDonation = typeof recurringDonations.$inferInsert
export type RecurringPaymentAttempt =
	typeof recurringPaymentAttempts.$inferSelect
export type CreateRecurringPaymentAttempt =
	typeof recurringPaymentAttempts.$inferInsert

// Request schemas
export const createRecurringDonationDto = t.Object({
	campaignSlug: t.String({
		minLength: 1,
		error: "Campaign slug is required and cannot be empty",
	}),
	amount: t.String({
		pattern: "^\\d+(\\.\\d{1,2})?$",
		error:
			"Amount must be a valid number with up to 2 decimal places (e.g., 10.50)",
	}),
	frequency: t.Union(
		[t.Literal("monthly"), t.Literal("quarterly"), t.Literal("yearly")],
		{
			error: "Frequency must be monthly, quarterly, or yearly",
		},
	),
	donorName: t.String({
		minLength: 1,
		maxLength: 255,
		error: "Donor name is required and must be between 1-255 characters",
	}),
	donorEmail: t.String({
		format: "email",
		error: "Please provide a valid email address",
	}),
	donorPhone: t.Optional(
		t.String({
			pattern: "^\\+?[0-9]{10,15}$",
			error:
				"Phone number must be 10-15 digits and may include a + prefix (e.g., +60123456789)",
		}),
	),
	message: t.Optional(
		t.String({
			maxLength: 1000,
			error: "Message cannot exceed 1000 characters",
		}),
	),
})

export const updateRecurringDonationDto = t.Object({
	amount: t.Optional(
		t.String({
			pattern: "^\\d+(\\.\\d{1,2})?$",
			error:
				"Amount must be a valid number with up to 2 decimal places (e.g., 10.50)",
		}),
	),
	frequency: t.Optional(
		t.Union([
			t.Literal("monthly"),
			t.Literal("quarterly"),
			t.Literal("yearly"),
		]),
	),
	status: t.Optional(
		t.Union([t.Literal("active"), t.Literal("paused"), t.Literal("canceled")]),
	),
	donorName: t.Optional(
		t.String({
			minLength: 1,
			maxLength: 255,
			error: "Donor name is required and must be between 1-255 characters",
		}),
	),
	donorEmail: t.Optional(
		t.String({
			format: "email",
			error: "Please provide a valid email address",
		}),
	),
	donorPhone: t.Optional(
		t.String({
			pattern: "^\\+?[0-9]{10,15}$",
			error:
				"Phone number must be 10-15 digits and may include a + prefix (e.g., +60123456789)",
		}),
	),
})

export const recurringDonationParamsDto = t.Object({
	id: t.String({ format: "uuid" }),
})

// Response schemas
export const recurringDonationDto = t.Object({
	id: t.String(),
	campaignId: t.String(),
	donorId: t.String(),
	amount: t.String(),
	currency: t.String(),
	frequency: t.Union([
		t.Literal("monthly"),
		t.Literal("quarterly"),
		t.Literal("yearly"),
	]),
	status: t.Union([
		t.Literal("active"),
		t.Literal("paused"),
		t.Literal("canceled"),
		t.Literal("expired"),
	]),
	chipRecurringToken: t.String(),
	chipTokenExpiresAt: t.Union([t.Date(), t.Null()]),
	donorName: t.String(),
	donorEmail: t.String(),
	donorPhone: t.Union([t.String(), t.Null()]),
	nextPaymentDate: t.Date(),
	lastPaymentDate: t.Union([t.Date(), t.Null()]),
	failedAttempts: t.Number(),
	maxRetries: t.Number(),
	createdAt: t.Date(),
	updatedAt: t.Date(),
})

export const recurringPaymentAttemptDto = t.Object({
	id: t.String(),
	recurringDonationId: t.String(),
	attemptNumber: t.Number(),
	chipPaymentId: t.Union([t.String(), t.Null()]),
	status: t.String(),
	errorMessage: t.Union([t.String(), t.Null()]),
	attemptedAt: t.Date(),
	completedAt: t.Union([t.Date(), t.Null()]),
})

export const createRecurringDonationResponseDto = t.Object({
	success: t.Boolean(),
	recurringDonation: recurringDonationDto,
	paymentUrl: t.String(),
})

export const recurringDonationResponseDto = t.Object({
	success: t.Boolean(),
	recurringDonation: recurringDonationDto,
})

export const recurringDonationsListResponseDto = t.Object({
	success: t.Boolean(),
	recurringDonations: t.Array(recurringDonationDto),
	pagination: t.Object({
		total: t.Number(),
		page: t.Number(),
		limit: t.Number(),
		totalPages: t.Number(),
	}),
})
