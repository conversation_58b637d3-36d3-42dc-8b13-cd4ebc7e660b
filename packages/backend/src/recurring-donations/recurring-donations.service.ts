import { and, eq } from "drizzle-orm"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { donations } from "@/donations/donations.schema"
import {
	AppError,
	ErrorCode,
	logError,
	withErrorHandling,
} from "@/lib/error-handler"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "@/payments/payments.schema"
import { type Campaign, users } from "@/schemas"
import {
	type RecurringDonation,
	recurringDonations,
	recurringPaymentAttempts,
} from "./recurring-donations.schema"

// TODO: remove withErrorHandling
// TODO: need to accept universal payment response
export abstract class RecurringDonationService {
	/**
	 * Handles successful recurring token setup
	 */
	static async handleRecurringTokenSetup(
		data: ChipPaymentResponse,
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing successful recurring token setup", {
				paymentId: data.id,
				reference: data.reference,
				recurringToken: data.recurring_token ? "present" : "missing",
			})

			if (!data.recurring_token) {
				throw new AppError(
					"Recurring token missing from successful payment",
					ErrorCode.PAYMENT_VALIDATION_ERROR,
					StatusCodes.BAD_REQUEST,
					{
						paymentId: data.id,
						reference: data.reference,
					},
				)
			}

			// Find recurring donation by reference
			const [recurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, data.reference))
				.limit(1)

			if (!recurringDonation) {
				throw new AppError(
					"Recurring donation not found for token setup",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						paymentId: data.id,
						reference: data.reference,
					},
				)
			}

			// Update recurring donation with token and set as active
			const nextPaymentDate = RecurringDonationService.calculateNextPaymentDate(
				new Date(),
				recurringDonation.frequency,
			)

			await db
				.update(recurringDonations)
				.set({
					chipRecurringToken: data.recurring_token,
					status: "active",
					nextPaymentDate,
					updatedAt: new Date(),
				})
				.where(eq(recurringDonations.id, recurringDonation.id))

			// Also create a donation record for the initial payment
			const [initialDonation] = await db
				.insert(donations)
				.values({
					campaignId: recurringDonation.campaignId,
					donorId: recurringDonation.donorId,
					amount: recurringDonation.amount,
					currency: recurringDonation.currency,
					chipPaymentId: data.id,
					status: "completed",
					donorName: recurringDonation.donorName,
					donorEmail: recurringDonation.donorEmail,
					donorPhone: recurringDonation.donorPhone,
					donorMessage: `Initial payment for recurring ${recurringDonation.frequency} donation`,
				})
				.returning()

			console.info("Recurring token setup completed successfully", {
				recurringDonationId: recurringDonation.id,
				initialDonationId: initialDonation.id,
				paymentId: data.id,
				nextPaymentDate,
				frequency: recurringDonation.frequency,
			})
		}, "RecurringDonationService.handleRecurringTokenSetup")()
	}

	/**
	 * Handles failed recurring token setup
	 */
	static async handleRecurringTokenSetupFailed(
		data: ChipPaymentResponse,
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing failed recurring token setup", {
				paymentId: data.id,
				reference: data.reference,
				status: data.status,
			})

			// Find recurring donation by reference
			const [recurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, data.reference))
				.limit(1)

			if (!recurringDonation) {
				logError(
					new AppError(
						"Recurring donation not found for failed token setup",
						ErrorCode.DONATION_NOT_FOUND,
						StatusCodes.NOT_FOUND,
						{
							paymentId: data.id,
							reference: data.reference,
						},
					),
					{ paymentId: data.id },
					"warn",
				)
				return
			}

			// Mark recurring donation as failed
			await db
				.update(recurringDonations)
				.set({
					status: "canceled",
					updatedAt: new Date(),
				})
				.where(eq(recurringDonations.id, recurringDonation.id))

			logError(
				new AppError(
					"Recurring token setup failed",
					ErrorCode.WEBHOOK_PROCESSING_ERROR,
					StatusCodes.OK,
				),
				{
					recurringDonationId: recurringDonation.id,
					paymentId: data.id,
					donorEmail: "[REDACTED]",
					errorDescription: data.error_description,
				},
				"warn",
			)
		}, "RecurringDonationService.handleRecurringTokenSetupFailed")()
	}

	/**
	 * Handles completed recurring donation payments
	 */
	static async handleRecurringDonationCompleted(
		data: ChipPaymentResponse,
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing completed recurring donation payment", {
				paymentId: data.id,
				reference: data.reference,
				status: data.status,
			})

			// Extract recurring donation ID from reference
			const recurringDonationId = data.reference?.startsWith("recurring_")
				? data.reference.split("_")[1]
				: data.reference

			if (!recurringDonationId) {
				throw new AppError(
					"Invalid reference for recurring donation payment",
					ErrorCode.PAYMENT_VALIDATION_ERROR,
					StatusCodes.BAD_REQUEST,
					{
						paymentId: data.id,
						reference: data.reference,
					},
				)
			}

			// Find recurring donation
			const [recurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, recurringDonationId))
				.limit(1)

			if (!recurringDonation) {
				throw new AppError(
					"Recurring donation not found for completed payment",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						paymentId: data.id,
						recurringDonationId,
					},
				)
			}

			// Create a regular donation record for this recurring payment
			const [donor] = await db
				.select()
				.from(users)
				.where(eq(users.id, recurringDonation.donorId))
				.limit(1)

			if (!donor) {
				throw new AppError(
					"Donor not found for recurring donation",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						paymentId: data.id,
						donorId: recurringDonation.donorId,
					},
				)
			}

			// Create donation record for this recurring payment
			const [newDonation] = await db
				.insert(donations)
				.values({
					campaignId: recurringDonation.campaignId,
					donorId: recurringDonation.donorId,
					amount: recurringDonation.amount,
					currency: recurringDonation.currency,
					chipPaymentId: data.id,
					status: "completed",
					donorName: recurringDonation.donorName,
					donorEmail: recurringDonation.donorEmail,
					donorPhone: recurringDonation.donorPhone,
					donorMessage: `Recurring ${recurringDonation.frequency} donation`,
				})
				.returning()

			// Update recurring donation with last payment date and next payment date
			const nextPaymentDate = RecurringDonationService.calculateNextPaymentDate(
				new Date(),
				recurringDonation.frequency,
			)

			await db
				.update(recurringDonations)
				.set({
					lastPaymentDate: new Date(),
					nextPaymentDate,
					failedAttempts: 0, // Reset failed attempts on successful payment
					updatedAt: new Date(),
				})
				.where(eq(recurringDonations.id, recurringDonation.id))

			// Update payment attempt record if exists
			await db
				.update(recurringPaymentAttempts)
				.set({
					status: "completed",
					chipPaymentId: data.id,
					completedAt: new Date(),
				})
				.where(
					and(
						eq(
							recurringPaymentAttempts.recurringDonationId,
							recurringDonation.id,
						),
						eq(recurringPaymentAttempts.status, "pending"),
					),
				)

			console.info("Recurring donation payment successfully processed", {
				recurringDonationId: recurringDonation.id,
				donationId: newDonation.id,
				paymentId: data.id,
				amount: recurringDonation.amount,
				frequency: recurringDonation.frequency,
				nextPaymentDate,
			})
		}, "RecurringDonationsService.handleRecurringDonationCompleted")()
	}

	/**
	 * Handles failed recurring donation payments with retry logic
	 */
	static async handleRecurringDonationFailed(
		data: ChipPaymentResponse,
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing failed recurring donation payment", {
				paymentId: data.id,
				reference: data.reference,
				status: data.status,
			})

			// Extract recurring donation ID from reference
			const recurringDonationId = data.reference?.startsWith("recurring_")
				? data.reference.split("_")[1]
				: data.reference

			if (!recurringDonationId) {
				logError(
					new AppError(
						"Invalid reference for recurring donation payment",
						ErrorCode.PAYMENT_VALIDATION_ERROR,
						StatusCodes.BAD_REQUEST,
						{
							paymentId: data.id,
							reference: data.reference,
						},
					),
					{ paymentId: data.id },
					"warn",
				)
				return
			}

			// Find recurring donation
			const [recurringDonation] = await db
				.select()
				.from(recurringDonations)
				.where(eq(recurringDonations.id, recurringDonationId))
				.limit(1)

			if (!recurringDonation) {
				logError(
					new AppError(
						"Recurring donation not found for failed payment",
						ErrorCode.DONATION_NOT_FOUND,
						StatusCodes.NOT_FOUND,
						{
							paymentId: data.id,
							recurringDonationId,
						},
					),
					{ paymentId: data.id },
					"warn",
				)
				return
			}

			// Increment failed attempts
			const newFailedAttempts = recurringDonation.failedAttempts + 1
			const shouldPause = newFailedAttempts >= recurringDonation.maxRetries

			// Update recurring donation
			await db
				.update(recurringDonations)
				.set({
					failedAttempts: newFailedAttempts,
					status: shouldPause ? "paused" : recurringDonation.status,
					updatedAt: new Date(),
				})
				.where(eq(recurringDonations.id, recurringDonation.id))

			// Update payment attempt record
			await db
				.update(recurringPaymentAttempts)
				.set({
					status: "failed",
					chipPaymentId: data.id,
					errorMessage: data.error_description || "Payment failed",
					completedAt: new Date(),
				})
				.where(
					and(
						eq(
							recurringPaymentAttempts.recurringDonationId,
							recurringDonation.id,
						),
						eq(recurringPaymentAttempts.status, "pending"),
					),
				)

			if (shouldPause) {
				logError(
					new AppError(
						"Recurring donation paused due to max retry attempts reached",
						ErrorCode.WEBHOOK_PROCESSING_ERROR,
						StatusCodes.OK,
					),
					{
						recurringDonationId: recurringDonation.id,
						failedAttempts: newFailedAttempts,
						maxRetries: recurringDonation.maxRetries,
						paymentId: data.id,
					},
					"warn",
				)
			} else {
				logError(
					new AppError(
						"Recurring donation payment failed, will retry",
						ErrorCode.WEBHOOK_PROCESSING_ERROR,
						StatusCodes.OK,
					),
					{
						recurringDonationId: recurringDonation.id,
						failedAttempts: newFailedAttempts,
						maxRetries: recurringDonation.maxRetries,
						paymentId: data.id,
					},
					"warn",
				)
			}
		}, "Recu.handleRecurringDonationFailed")()
	}

	/**
	 * Handles canceled recurring donation payments
	 */
	static async handleRecurringDonationCanceled(
		data: ChipPaymentResponse,
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing canceled recurring donation payment", {
				paymentId: data.id,
				reference: data.reference,
				status: data.status,
			})

			// For initial token setup cancellation
			if (data.is_recurring_token) {
				const [recurringDonation] = await db
					.select()
					.from(recurringDonations)
					.where(eq(recurringDonations.id, data.reference))
					.limit(1)

				if (recurringDonation) {
					await db
						.update(recurringDonations)
						.set({
							status: "canceled",
							updatedAt: new Date(),
						})
						.where(eq(recurringDonations.id, recurringDonation.id))

					console.info("Recurring donation setup canceled", {
						recurringDonationId: recurringDonation.id,
						paymentId: data.id,
					})
				}
			} else {
				// For recurring payment cancellation (shouldn't happen often)
				console.warn("Recurring payment canceled", {
					paymentId: data.id,
					reference: data.reference,
				})
			}
		}, "RecurringDonationService.handleRecurringDonationCanceled")()
	}

	/**
	 * Calculates the next payment date based on frequency
	 */
	private static calculateNextPaymentDate(
		currentDate: Date,
		frequency: string,
	): Date {
		const nextDate = new Date(currentDate)

		switch (frequency) {
			case "monthly":
				nextDate.setMonth(nextDate.getMonth() + 1)
				break
			case "quarterly":
				nextDate.setMonth(nextDate.getMonth() + 3)
				break
			case "yearly":
				nextDate.setFullYear(nextDate.getFullYear() + 1)
				break
			default:
				throw new AppError(
					`Invalid frequency: ${frequency}`,
					ErrorCode.PAYMENT_VALIDATION_ERROR,
					StatusCodes.BAD_REQUEST,
					{ frequency },
				)
		}

		return nextDate
	}

	/**
	 * Fallback to one-time payment when recurring setup fails
	 */
	static async fallbackToOneTimePayment(
		recurringDonation: RecurringDonation,
		campaign: Campaign,
		params: CreatePaymentParams,
	): Promise<ChipPaymentResponse> {
		return withErrorHandling(async () => {
			console.info("Falling back to one-time payment for recurring donation", {
				recurringDonationId: recurringDonation.id,
				campaignId: campaign.id,
				amount: params.amount,
			})

			// Create a regular donation record
			const [donor] = await db
				.select()
				.from(users)
				.where(eq(users.id, recurringDonation.donorId))
				.limit(1)

			if (!donor) {
				throw new AppError(
					"Donor not found for fallback payment",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						donorId: recurringDonation.donorId,
					},
				)
			}

			const [fallbackDonation] = await db
				.insert(donations)
				.values({
					campaignId: recurringDonation.campaignId,
					donorId: recurringDonation.donorId,
					amount: recurringDonation.amount,
					currency: recurringDonation.currency,
					status: "pending",
					donorName: recurringDonation.donorName,
					donorEmail: recurringDonation.donorEmail,
					donorPhone: recurringDonation.donorPhone,
					donorMessage: `Fallback from recurring ${recurringDonation.frequency} donation`,
				})
				.returning()

			// Create one-time payment
			const fallbackParams: CreatePaymentParams = {
				...params,
				isRecurringToken: false, // Disable recurring token
				products: [
					{
						name: `One-time Donation to ${campaign.name} (Fallback)`,
						price: params.amount,
						category: "donation",
						quantity: "1",
					},
				],
				reference: fallbackDonation.id,
				notes: `Fallback one-time payment from recurring donation setup`,
			}

			const paymentResponse = await ChipService.createDonationPayment(
				fallbackDonation,
				campaign,
				fallbackParams,
			)

			// Mark recurring donation as canceled since we're falling back
			await db
				.update(recurringDonations)
				.set({
					status: "canceled",
					updatedAt: new Date(),
				})
				.where(eq(recurringDonations.id, recurringDonation.id))

			console.info("Successfully created fallback one-time payment", {
				recurringDonationId: recurringDonation.id,
				fallbackDonationId: fallbackDonation.id,
				paymentId: paymentResponse.id,
			})

			return paymentResponse
		}, "RecurringDonationService.fallbackToOneTimePayment")()
	}
}
