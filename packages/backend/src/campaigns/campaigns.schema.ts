import { sql } from "drizzle-orm"
import {
	boolean,
	pgPolicy,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core"
import { t } from "elysia"
import {
	pgAdminRole,
	pgAnonymousRole,
	pgAuthenticatedRole,
	pgOrganizerRole,
	users,
} from "@/schemas"

// drizzle schema with RLS
export const campaigns = pgTable(
	"campaigns",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		organizerId: text("organizer_id")
			.references(() => users.id)
			.notNull(),
		name: text("name").notNull(),
		description: text("description"),
		slug: text("slug").notNull().unique(),
		isActive: boolean("is_active").default(true).notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(t) => [
		// Organizers can manage their own campaigns
		pgPolicy("campaigns_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`${t.organizerId} = current_setting('app.current_organizer_id', true) OR current_setting('app.current_user_role', true) = 'admin'`,
			withCheck: sql`${t.organizerId} = current_setting('app.current_organizer_id', true) OR current_setting('app.current_user_role', true) = 'admin'`,
		}),
		// Public read access for active campaigns (for donation pages)
		pgPolicy("campaigns_public_read", {
			for: "select",
			to: [pgAnonymousRole, pgAuthenticatedRole],
			using: sql`${t.isActive} = true AND current_setting('app.current_organizer_id', true) IS NULL`,
		}),
	],
)

export type Campaign = typeof campaigns.$inferSelect
export type CreateCampaign = typeof campaigns.$inferInsert

// Request schemas
export const createCampaignDto = t.Object({
	name: t.String({
		minLength: 1,
		maxLength: 255,
		errorMessages: {
			minLength: "Campaign name is required",
			maxLength: "Campaign name must be less than 255 characters",
		},
	}),
	description: t.Optional(t.String()),
	slug: t.String({
		minLength: 1,
		maxLength: 255,
		pattern: "^[a-z0-9-]+$", // Only lowercase letters, numbers, and hyphens
		errorMessages: {
			minLength: "Slug is required",
			maxLength: "Slug must be less than 100 characters",
			pattern: "Slug must contain only lowercase letters, numbers, and hyphens",
		},
	}),
})

export const campaignParamsDto = t.Object({
	slug: t.String({
		minLength: 1,
		errorMessages: {
			minLength: "Slug is required",
		},
	}),
})

export const campaignIdParamsDto = t.Object({
	id: t.String({
		format: "uuid",
		errorMessages: {
			format: "Invalid campaign ID format",
		},
	}),
})

export const updateCampaignDto = t.Object({
	name: t.String({
		minLength: 1,
		maxLength: 255,
		errorMessages: {
			minLength: "Campaign name is required",
			maxLength: "Campaign name must be less than 255 characters",
		},
	}),
	description: t.Optional(t.String()),
	isActive: t.Boolean({
		errorMessages: {
			type: "isActive must be a boolean value",
		},
	}),
})

export const statusToggleDto = t.Object({
	isActive: t.Boolean({
		errorMessages: {
			type: "isActive must be a boolean value",
		},
	}),
})

// Response schemas
export const campaignResponseDto = t.Object({
	id: t.String(),
	organizerId: t.String(),
	name: t.String(),
	description: t.Union([t.String(), t.Null()]),
	slug: t.String(),
	isActive: t.Boolean(),
	createdAt: t.Date(),
})

export const publicCampaignResponseDto = t.Object({
	id: t.String(),
	name: t.String(),
	description: t.Union([t.String(), t.Null()]),
	slug: t.String(),
	isActive: t.Boolean(),
	createdAt: t.Date(),
})

export const createCampaignResponseDto = t.Object({
	success: t.Boolean(),
	campaign: campaignResponseDto,
})

export const getCampaignsResponseDto = t.Object({
	success: t.Boolean(),
	campaigns: t.Array(campaignResponseDto),
})

export const getPublicCampaignResponseDto = t.Object({
	success: t.Boolean(),
	campaign: publicCampaignResponseDto,
})

export const updateCampaignResponseDto = t.Object({
	success: t.Boolean(),
	campaign: campaignResponseDto,
})

export const deleteCampaignResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})

export const statusToggleResponseDto = t.Object({
	success: t.Boolean(),
	campaign: campaignResponseDto,
})
