import { and, eq } from "drizzle-orm"
import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { withPublicContext } from "@/lib/db-context"
import { donations } from "@/schemas"
import { authController } from "../auth/auth.controller"
import {
	campaignIdParamsDto,
	campaignParamsDto,
	campaigns,
	createCampaignDto,
	createCampaignResponseDto,
	deleteCampaignResponseDto,
	getCampaignsResponseDto,
	getPublicCampaignResponseDto,
	statusToggleDto,
	statusToggleResponseDto,
	updateCampaignDto,
	updateCampaignResponseDto,
} from "./campaigns.schema"

export const campaignsController = new Elysia({ prefix: "/api/campaigns" })
	.use(authController)
	.post(
		"/",
		async ({ body, status, user }) => {
			try {
				// Check if slug is unique
				const existingCampaign = await db
					.select()
					.from(campaigns)
					.where(eq(campaigns.slug, body.slug))
					.limit(1)

				if (existingCampaign.length > 0) {
					return status(StatusCodes.CONFLICT, "Campaign slug already exists")
				}

				// Create campaign
				const [newCampaign] = await db
					.insert(campaigns)
					.values({
						organizerId: user.id,
						name: body.name,
						description: body.description,
						slug: body.slug,
						isActive: true,
					})
					.returning()

				return status(StatusCodes.CREATED, {
					success: true,
					campaign: newCampaign,
				})
			} catch (error) {
				console.error("Error creating campaign:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to create campaign",
				)
			}
		},
		{
			requirePermission: { campaign: ["create"] },
			body: createCampaignDto,
			response: {
				[StatusCodes.CREATED]: createCampaignResponseDto,
				[StatusCodes.UNAUTHORIZED]: t.String(),
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.BAD_REQUEST]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/my",
		async ({ status }) => {
			try {
				// RLS will automatically filter campaigns by current organizer
				const userCampaigns = await db
					.select()
					.from(campaigns)
					.orderBy(campaigns.createdAt)

				return {
					success: true,
					campaigns: userCampaigns,
				}
			} catch (error) {
				console.error("Error fetching campaigns:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch campaigns",
				)
			}
		},
		{
			requirePermission: { campaign: ["view"] },
			response: {
				[StatusCodes.OK]: getCampaignsResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/:id",
		async ({ params, status }) => {
			try {
				// RLS will automatically filter campaigns by current organizer
				const campaign = await db
					.select()
					.from(campaigns)
					.where(eq(campaigns.id, params.id))
					.limit(1)

				if (campaign.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Campaign not found")
				}

				return {
					success: true,
					campaign: campaign[0],
				}
			} catch (error) {
				console.error("Error fetching campaign:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch campaign",
				)
			}
		},
		{
			requirePermission: { campaign: ["view"] },
			params: campaignIdParamsDto,
			response: {
				[StatusCodes.OK]: createCampaignResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/public/:slug",
		async ({ params, status }) => {
			// Get public campaign info - use public context to bypass RLS
			try {
				const result = await withPublicContext(db, async () => {
					return await db
						.select()
						.from(campaigns)
						.where(eq(campaigns.slug, params.slug))
						.limit(1)
				})

				if (result.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Campaign not found")
				}

				const campaignData = result[0]

				// Only return public campaign info
				return {
					success: true,
					campaign: {
						id: campaignData.id,
						name: campaignData.name,
						description: campaignData.description,
						slug: campaignData.slug,
						isActive: campaignData.isActive,
						createdAt: campaignData.createdAt,
					},
				}
			} catch (error) {
				console.error("Error fetching campaign:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch campaign",
				)
			}
		},
		{
			params: campaignParamsDto,
			response: {
				[StatusCodes.OK]: getPublicCampaignResponseDto,
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.put(
		"/:id",
		async ({ params, body, status }) => {
			try {
				// RLS will ensure only campaigns owned by current organizer can be updated
				const result = await db
					.update(campaigns)
					.set({
						name: body.name,
						description: body.description,
						isActive: body.isActive,
					})
					.where(eq(campaigns.id, params.id))
					.returning()

				if (result.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Campaign not found")
				}

				const [updatedCampaign] = result

				return {
					success: true,
					campaign: updatedCampaign,
				}
			} catch (error) {
				console.error("Error updating campaign:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update campaign",
				)
			}
		},
		{
			requirePermission: { campaign: ["edit"] },
			params: campaignIdParamsDto,
			body: updateCampaignDto,
			response: {
				[StatusCodes.OK]: updateCampaignResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.delete(
		"/:id",
		async ({ params, status }) => {
			try {
				// RLS will ensure only campaigns owned by current organizer can be deleted
				const existingCampaign = await db
					.select()
					.from(campaigns)
					.where(and(eq(campaigns.id, params.id)))
					.limit(1)

				if (existingCampaign.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Campaign not found")
				}

				// Check if campaign has associated donations
				const associatedDonations = await db
					.select()
					.from(donations)
					.where(eq(donations.campaignId, params.id))
					.limit(1)

				if (associatedDonations.length > 0) {
					return status(
						StatusCodes.CONFLICT,
						"Cannot delete campaign with existing donations. Please deactivate the campaign instead.",
					)
				}

				// Delete campaign
				await db.delete(campaigns).where(eq(campaigns.id, params.id))

				return {
					success: true,
					message: "Campaign deleted successfully",
				}
			} catch (error) {
				console.error("Error deleting campaign:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to delete campaign",
				)
			}
		},
		{
			requirePermission: { campaign: ["delete"] },
			params: campaignIdParamsDto,
			response: {
				[StatusCodes.OK]: deleteCampaignResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.CONFLICT]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.patch(
		"/:id/status",
		async ({ params, body, status }) => {
			try {
				// RLS will ensure only campaigns owned by current organizer can be updated
				const existingCampaign = await db
					.select()
					.from(campaigns)
					.where(and(eq(campaigns.id, params.id)))
					.limit(1)

				if (existingCampaign.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Campaign not found")
				}

				// Update campaign status
				const [updatedCampaign] = await db
					.update(campaigns)
					.set({
						isActive: body.isActive,
					})
					.where(eq(campaigns.id, params.id))
					.returning()

				return {
					success: true,
					campaign: updatedCampaign,
				}
			} catch (error) {
				console.error("Error updating campaign status:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update campaign status",
				)
			}
		},
		{
			requirePermission: { campaign: ["edit"] },
			params: campaignIdParamsDto,
			body: statusToggleDto,
			response: {
				[StatusCodes.OK]: statusToggleResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)
