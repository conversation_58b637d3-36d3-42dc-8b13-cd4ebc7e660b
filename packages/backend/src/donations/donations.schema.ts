import { sql } from "drizzle-orm"
import {
	decimal,
	pgEnum,
	pgPolicy,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core"
import { t } from "elysia"
import { campaigns } from "@/schemas"
import {
	pgAdminRole,
	pgAuthenticatedRole,
	pgOrganizerRole,
	users,
} from "../users/users.schema"

// drizzle schema
export const donationStatusEnum = pgEnum("donation_status", [
	"pending",
	"completed",
	"failed",
	"canceled",
])
export const donations = pgTable(
	"donations",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		campaignId: uuid("campaign_id")
			.references(() => campaigns.id)
			.notNull(),
		donorId: text("donor_id")
			.references(() => users.id)
			.notNull(),
		amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
		currency: text("currency").default("MYR").notNull(),
		chipPaymentId: text("chip_payment_id"),
		status: donationStatusEnum("status").notNull(),
		donorName: text("donor_name").notNull(),
		donorEmail: text("donor_email").notNull(),
		donorPhone: text("donor_phone"),
		donorMessage: text("donor_message"),
		internalNotes: text("internal_notes"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(t) => [
		// Organizers can access donations for their campaigns
		pgPolicy("donations_organizer_access", {
			for: "all",
			to: [pgOrganizerRole, pgAdminRole],
			using: sql`EXISTS (
			SELECT 1 FROM campaigns c
			WHERE c.id = ${t.campaignId}
			AND (c.organizer_id = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin')
		)`,
			withCheck: sql`EXISTS (
			SELECT 1 FROM campaigns c
			WHERE c.id = ${t.campaignId}
			AND (c.organizer_id = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin')
		)`,
		}),
		// Donors can view their own donations
		pgPolicy("donations_donor_access", {
			for: "select",
			to: [pgAuthenticatedRole],
			using: sql`${t.donorId} = current_setting('app.current_user_id', true)
			AND current_setting('app.current_user_role', true) = 'user'`,
		}),
	],
)

export type Donation = typeof donations.$inferSelect
export type CreateDonation = typeof donations.$inferInsert

// Request schemas
export const initiateDonationDto = t.Object({
	campaignSlug: t.String({
		minLength: 1,
		error: "Campaign slug is required and cannot be empty",
	}),
	amount: t.String({
		pattern: "^\\d+(\\.\\d{1,2})?$",
		error:
			"Amount must be a valid number with up to 2 decimal places (e.g., 10.50)",
	}), // Decimal with up to 2 places
	donorName: t.String({
		minLength: 1,
		maxLength: 255,
		error: "Donor name is required and must be between 1-255 characters",
	}),
	donorEmail: t.String({
		format: "email",
		error: "Please provide a valid email address",
	}),
	donorPhone: t.Optional(
		t.String({
			pattern: "^\\+?[0-9]{10,15}$",
			error:
				"Phone number must be 10-15 digits and may include a + prefix (e.g., +60123456789)",
		}),
	),
	message: t.Optional(
		t.String({
			maxLength: 1000,
			error: "Message cannot exceed 1000 characters",
		}),
	),
})

export const chipWebhookDto = t.Object({
	id: t.String(),
	status: t.String(),
	amount: t.Number(),
	metadata: t.Optional(t.Any()),
})

export const updateDonationDto = t.Object({
	donorName: t.String({
		minLength: 1,
		maxLength: 255,
		error: "Donor name is required and must be between 1-255 characters",
	}),
	donorEmail: t.String({
		format: "email",
		error: "Please provide a valid email address",
	}),
	donorPhone: t.Optional(
		t.String({
			pattern: "^\\+?[0-9]{10,15}$",
			error:
				"Phone number must be 10-15 digits and may include a + prefix (e.g., +60123456789)",
		}),
	),
	internalNotes: t.Optional(
		t.String({
			maxLength: 2000,
			error: "Internal notes cannot exceed 2000 characters",
		}),
	),
})

export const updateStatusDto = t.Object({
	status: t.Union([
		t.Literal("pending"),
		t.Literal("completed"),
		t.Literal("failed"),
		t.Literal("canceled"),
	]),
	failureReason: t.Optional(t.String({ maxLength: 500 })),
})

export const donationFiltersDto = t.Object({
	search: t.Optional(t.String()),
	status: t.Optional(
		t.Union([
			t.Literal("all"),
			t.Literal("pending"),
			t.Literal("completed"),
			t.Literal("failed"),
			t.Literal("canceled"),
		]),
	),
	campaignId: t.Optional(t.String()),
	dateFrom: t.Optional(t.String({ format: "date" })),
	dateTo: t.Optional(t.String({ format: "date" })),
	sortBy: t.Optional(
		t.Union([
			t.Literal("amount"),
			t.Literal("createdAt"),
			t.Literal("donorName"),
		]),
	),
	sortOrder: t.Optional(t.Union([t.Literal("asc"), t.Literal("desc")])),
	page: t.Optional(t.Number({ minimum: 1 })),
	limit: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
})

export const donationParamsDto = t.Object({
	id: t.String({ format: "uuid" }),
})

// Response schemas
export const donationDto = t.Object({
	id: t.String(),
	campaignId: t.String(),
	donorId: t.String(),
	amount: t.String(),
	currency: t.String(),
	chipPaymentId: t.Union([t.String(), t.Null()]),
	status: t.Union([
		t.Literal("pending"),
		t.Literal("completed"),
		t.Literal("failed"),
		t.Literal("canceled"),
	]),
	donorName: t.String(),
	donorEmail: t.String(),
	donorPhone: t.Union([t.String(), t.Null()]),
	donorMessage: t.Union([t.String(), t.Null()]),
	internalNotes: t.Union([t.String(), t.Null()]),
	createdAt: t.Date(),
	updatedAt: t.Date(),
})

export const campaignInfoDto = t.Object({
	id: t.String(),
	name: t.String(),
	slug: t.String(),
	organizerId: t.String(),
})

export const donationWithCampaignDto = t.Intersect([
	donationDto,
	t.Object({
		campaign: campaignInfoDto,
	}),
])

export const initiateDonationResponseDto = t.Object({
	success: t.Boolean(),
	donation: t.Object({
		id: t.String(),
		amount: t.String(),
		currency: t.String(),
		status: t.String(),
	}),
	paymentUrl: t.String(),
})

export const donationResponseDto = t.Object({
	success: t.Boolean(),
	donation: donationWithCampaignDto,
})

export const donationsListResponseDto = t.Object({
	success: t.Boolean(),
	donations: t.Array(donationWithCampaignDto),
	pagination: t.Object({
		total: t.Number(),
		page: t.Number(),
		limit: t.Number(),
		totalPages: t.Number(),
	}),
})

export const analyticsResponseDto = t.Object({
	success: t.Boolean(),
	analytics: t.Object({
		totalAmount: t.String(),
		totalCount: t.Number(),
		averageAmount: t.String(),
		statusDistribution: t.Object({
			pending: t.Number(),
			completed: t.Number(),
			failed: t.Number(),
			canceled: t.Number(),
		}),
		topCampaigns: t.Array(
			t.Object({
				campaignId: t.String(),
				campaignName: t.String(),
				totalAmount: t.String(),
				donationCount: t.Number(),
			}),
		),
		monthlyTrends: t.Array(
			t.Object({
				month: t.String(),
				amount: t.String(),
				count: t.Number(),
			}),
		),
	}),
})

export const webhookResponseDto = t.Object({
	success: t.Boolean(),
})

export const deleteResponseDto = t.Object({
	success: t.Boolean(),
	message: t.String(),
})
