import crypto from "node:crypto"
import {
	and,
	count,
	desc,
	eq,
	gte,
	ilike,
	lte,
	or,
	sql,
	sum,
} from "drizzle-orm"
import { Elysia, t } from "elysia"
import { StatusCodes } from "http-status-codes"
import { db } from "@/db"
import { AppError, ErrorCode } from "@/lib/error-handler"
import { enhancedPaymentService } from "@/payments/payment.service"
import type { CreatePaymentParams } from "@/payments/payments.schema"
import { campaigns, errorResponseDto } from "@/schemas"
import { authController } from "../auth/auth.controller"
import { users } from "../users/users.schema"
import {
	analyticsResponseDto,
	deleteResponseDto,
	donationFiltersDto,
	donationParamsDto,
	donationResponseDto,
	donations,
	donationsListResponseDto,
	initiateDonationDto,
	initiateDonationResponseDto,
	updateDonationDto,
	updateStatusDto,
} from "./donations.schema"

export const donationsController = new Elysia({ prefix: "/api/donations" })
	.use(authController)
	.use(enhancedPaymentService)
	.post(
		"/initiate",
		async ({ body, createDonationPayment }) => {
			const requestId = `donation_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

			console.info("Initiating donation", {
				requestId,
				campaignSlug: body.campaignSlug,
				amount: body.amount,
				currency: "MYR",
				donorEmail: "[REDACTED]",
			})

			const donationAmount = parseFloat(body.amount)

			// Find campaign by slug
			const campaign = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.slug, body.campaignSlug))
				.limit(1)

			if (campaign.length === 0) {
				throw new AppError(
					"Campaign not found",
					ErrorCode.CAMPAIGN_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{ campaignSlug: body.campaignSlug, requestId },
				)
			}

			// Validate campaign status before payment creation
			if (!campaign[0].isActive) {
				throw new AppError(
					"This campaign is not currently accepting donations",
					ErrorCode.CAMPAIGN_INACTIVE,
					StatusCodes.BAD_REQUEST,
					{
						campaignId: campaign[0].id,
						campaignSlug: body.campaignSlug,
						campaignName: campaign[0].name,
						requestId,
					},
				)
			}

			// Check if donor exists, if not create one
			let donor = await db
				.select()
				.from(users)
				.where(eq(users.email, body.donorEmail))
				.limit(1)

			if (donor.length === 0) {
				try {
					// TODO: create user with better-auth magic link provider
					// Create new donor user
					const [newDonor] = await db
						.insert(users)
						.values({
							id: crypto.randomUUID(),
							email: body.donorEmail,
							name: body.donorName,
							role: "user",
							emailVerified: false,
						})
						.returning()

					donor = [newDonor]

					console.info("Created new donor user", {
						requestId,
						donorId: newDonor.id,
						donorEmail: "[REDACTED]",
					})
				} catch (dbError) {
					throw new AppError(
						"Failed to create donor record",
						ErrorCode.DATABASE_ERROR,
						StatusCodes.INTERNAL_SERVER_ERROR,
						{
							requestId,
							originalError:
								dbError instanceof Error ? dbError.message : String(dbError),
						},
					)
				}
			}

			// Create pending donation record
			let newDonation: typeof donations.$inferSelect
			try {
				const [createdDonation] = await db
					.insert(donations)
					.values({
						campaignId: campaign[0].id,
						donorId: donor[0].id,
						amount: body.amount,
						currency: "MYR",
						status: "pending",
						donorName: body.donorName,
						donorEmail: body.donorEmail,
						donorPhone: body.donorPhone || null,
						donorMessage: body.message || null,
					})
					.returning()

				newDonation = createdDonation

				console.info("Created donation record", {
					requestId,
					donationId: newDonation.id,
					campaignId: campaign[0].id,
					amount: body.amount,
				})
			} catch (dbError) {
				throw new AppError(
					"Failed to create donation record",
					ErrorCode.DONATION_CREATION_ERROR,
					StatusCodes.INTERNAL_SERVER_ERROR,
					{
						requestId,
						campaignId: campaign[0].id,
						originalError:
							dbError instanceof Error ? dbError.message : String(dbError),
					},
				)
			}

			// Create payment with campaign context
			// Note: Redirect URLs will be generated by paymentService.createDonationPayment
			const paymentParams: CreatePaymentParams = {
				amount: donationAmount,
				currency: "MYR",
				email: body.donorEmail,
				phone: body.donorPhone || "",
				fullName: body.donorName,
				products: [
					{
						name: `Donation to ${campaign[0].name}`,
						price: donationAmount,
						category: "donation",
						quantity: "1",
					},
				],
				successUrl: `${Bun.env.BACKEND_URL || "http://localhost:3000"}/api/webhooks/chip`,
				failureUrl: `${Bun.env.BACKEND_URL || "http://localhost:3000"}/api/webhooks/chip`,
				// These will be overridden by ChipService.createDonationPayment with proper context
				successRedirectUrl: "",
				failureRedirectUrl: "",
				cancelRedirectUrl: "",
				reference: newDonation.id,
				notes: body.message || `Donation to campaign: ${campaign[0].name}`,
			}

			// Create payment using paymentService with proper error handling
			const chipResponse = await createDonationPayment(
				newDonation,
				campaign[0],
				paymentParams,
			)

			console.info("Donation initiation completed successfully", {
				requestId,
				donationId: newDonation.id,
				chipPaymentId: chipResponse.id,
				hasCheckoutUrl: !!chipResponse.checkout_url,
			})

			if (!chipResponse.checkout_url) {
				throw new AppError(
					"Failed to create payment",
					ErrorCode.PAYMENT_GATEWAY_ERROR,
					StatusCodes.INTERNAL_SERVER_ERROR,
					{
						requestId,
						originalError: "Checkout URL not found in Chip response",
					},
				)
			}

			return {
				success: true,
				donation: {
					id: newDonation.id,
					amount: newDonation.amount,
					currency: newDonation.currency,
					status: newDonation.status,
				},
				paymentUrl: chipResponse.checkout_url,
			}
		},
		{
			body: initiateDonationDto,
			response: {
				200: initiateDonationResponseDto,
				400: errorResponseDto,
				404: errorResponseDto,
				500: errorResponseDto,
				502: errorResponseDto,
			},
		},
	)

	.get(
		"/",
		async ({ user, status, query }) => {
			try {
				const {
					search,
					status,
					campaignId,
					dateFrom,
					dateTo,
					sortBy = "createdAt",
					sortOrder = "desc",
					page = 1,
					limit = 50,
				} = query

				// Build where conditions
				const conditions = [eq(campaigns.organizerId, user.id)]

				if (search) {
					const searchCondition = or(
						ilike(donations.donorName, `%${search}%`),
						ilike(donations.donorEmail, `%${search}%`),
						ilike(campaigns.name, `%${search}%`),
					)
					if (searchCondition) {
						conditions.push(searchCondition)
					}
				}

				if (status && status !== "all") {
					conditions.push(eq(donations.status, status))
				}

				if (campaignId && campaignId !== "all") {
					conditions.push(eq(donations.campaignId, campaignId))
				}

				if (dateFrom) {
					conditions.push(gte(donations.createdAt, new Date(dateFrom)))
				}

				if (dateTo) {
					conditions.push(lte(donations.createdAt, new Date(dateTo)))
				}

				// Build order by
				const orderColumn =
					sortBy === "donorName"
						? donations.donorName
						: sortBy === "amount"
							? donations.amount
							: donations.createdAt
				const orderDirection =
					sortOrder === "asc" ? orderColumn : desc(orderColumn)

				// Get total count for pagination
				const totalResult = await db
					.select({ count: count() })
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))

				const total = totalResult[0]?.count || 0

				// Get donations with pagination
				const organizerDonations = await db
					.select({
						id: donations.id,
						campaignId: donations.campaignId,
						donorId: donations.donorId,
						amount: donations.amount,
						currency: donations.currency,
						chipPaymentId: donations.chipPaymentId,
						status: donations.status,
						donorName: donations.donorName,
						donorEmail: donations.donorEmail,
						donorPhone: donations.donorPhone,
						donorMessage: donations.donorMessage,
						internalNotes: donations.internalNotes,
						createdAt: donations.createdAt,
						updatedAt: donations.updatedAt,
						campaign: {
							id: campaigns.id,
							name: campaigns.name,
							slug: campaigns.slug,
							organizerId: campaigns.organizerId,
						},
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))
					.orderBy(orderDirection)
					.limit(limit)
					.offset((page - 1) * limit)

				return {
					success: true,
					donations: organizerDonations,
					pagination: {
						total,
						page,
						limit,
						totalPages: Math.ceil(total / limit),
					},
				}
			} catch (error) {
				console.error("Error fetching donations:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch donations",
				)
			}
		},
		{
			requirePermission: { donation: ["view"] },
			query: donationFiltersDto,
			response: {
				[StatusCodes.OK]: donationsListResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/:id",
		async ({ params, user, status }) => {
			// Get specific donation details (for receipts)
			try {
				const donation = await db
					.select({
						id: donations.id,
						campaignId: donations.campaignId,
						donorId: donations.donorId,
						amount: donations.amount,
						currency: donations.currency,
						chipPaymentId: donations.chipPaymentId,
						status: donations.status,
						donorName: donations.donorName,
						donorEmail: donations.donorEmail,
						donorPhone: donations.donorPhone,
						donorMessage: donations.donorMessage,
						internalNotes: donations.internalNotes,
						createdAt: donations.createdAt,
						updatedAt: donations.updatedAt,
						campaign: {
							id: campaigns.id,
							name: campaigns.name,
							slug: campaigns.slug,
							organizerId: campaigns.organizerId,
						},
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(donations.id, params.id))
					.limit(1)

				if (donation.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Donation not found")
				}

				const donationData = donation[0]

				// Allow access if: user is the donor, or user is the organizer of the campaign
				const hasAccess =
					user.id === donationData.donorId ||
					(user.role === "organizer" &&
						user.id === donationData.campaign?.organizerId)

				if (!hasAccess) {
					return status(StatusCodes.FORBIDDEN, "Access denied")
				}

				return {
					success: true,
					donation: donationData,
				}
			} catch (error) {
				console.error("Error fetching donation:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch donation",
				)
			}
		},
		{
			requirePermission: { donation: ["view"] },
			params: donationParamsDto,
			response: {
				[StatusCodes.OK]: donationResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.put(
		"/:id",
		async ({ params, body, user, status }) => {
			try {
				// Check if donation exists and belongs to organizer's campaign
				const existingDonation = await db
					.select({
						id: donations.id,
						campaignId: donations.campaignId,
						organizerId: campaigns.organizerId,
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(donations.id, params.id))
					.limit(1)

				if (existingDonation.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Donation not found")
				}

				if (existingDonation[0].organizerId !== user.id) {
					return status(StatusCodes.FORBIDDEN, "Access denied")
				}

				// Update donation
				const [_updatedDonation] = await db
					.update(donations)
					.set({
						donorName: body.donorName,
						donorEmail: body.donorEmail,
						donorPhone: body.donorPhone,
						internalNotes: body.internalNotes,
						updatedAt: new Date(),
					})
					.where(eq(donations.id, params.id))
					.returning()

				// Get updated donation with campaign info
				const donationWithCampaign = await db
					.select({
						id: donations.id,
						campaignId: donations.campaignId,
						donorId: donations.donorId,
						amount: donations.amount,
						currency: donations.currency,
						chipPaymentId: donations.chipPaymentId,
						status: donations.status,
						donorName: donations.donorName,
						donorEmail: donations.donorEmail,
						donorPhone: donations.donorPhone,
						donorMessage: donations.donorMessage,
						internalNotes: donations.internalNotes,
						createdAt: donations.createdAt,
						updatedAt: donations.updatedAt,
						campaign: {
							id: campaigns.id,
							name: campaigns.name,
							slug: campaigns.slug,
							organizerId: campaigns.organizerId,
						},
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(donations.id, params.id))
					.limit(1)

				return {
					success: true,
					donation: donationWithCampaign[0],
				}
			} catch (error) {
				console.error("Error updating donation:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update donation",
				)
			}
		},
		{
			requirePermission: { donation: ["edit"] },
			params: donationParamsDto,
			body: updateDonationDto,
			response: {
				[StatusCodes.OK]: donationResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.patch(
		"/:id/status",
		async ({ params, body, user, status }) => {
			try {
				// Check if donation exists and belongs to organizer's campaign
				const existingDonation = await db
					.select({
						id: donations.id,
						status: donations.status,
						internalNotes: donations.internalNotes,
						organizerId: campaigns.organizerId,
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(donations.id, params.id))
					.limit(1)

				if (existingDonation.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Donation not found")
				}

				if (existingDonation[0].organizerId !== user.id) {
					return status(StatusCodes.FORBIDDEN, "Access denied")
				}

				// Update donation status
				const updateData: Record<string, string | Date> = {
					status: body.status,
					updatedAt: new Date(),
				}

				// Add failure reason to internal notes if provided
				if (body.status === "failed" && body.failureReason) {
					const currentNotes = existingDonation[0].internalNotes || ""
					updateData.internalNotes = currentNotes
						? `${currentNotes}\n\nFailure reason: ${body.failureReason}`
						: `Failure reason: ${body.failureReason}`
				}

				await db
					.update(donations)
					.set(updateData)
					.where(eq(donations.id, params.id))

				// Get updated donation with campaign info
				const donationWithCampaign = await db
					.select({
						id: donations.id,
						campaignId: donations.campaignId,
						donorId: donations.donorId,
						amount: donations.amount,
						currency: donations.currency,
						chipPaymentId: donations.chipPaymentId,
						status: donations.status,
						donorName: donations.donorName,
						donorEmail: donations.donorEmail,
						donorMessage: donations.donorMessage,
						internalNotes: donations.internalNotes,
						createdAt: donations.createdAt,
						updatedAt: donations.updatedAt,
						campaign: {
							id: campaigns.id,
							name: campaigns.name,
							slug: campaigns.slug,
							organizerId: campaigns.organizerId,
						},
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(donations.id, params.id))
					.limit(1)

				return {
					success: true,
					donation: donationWithCampaign[0],
				}
			} catch (error) {
				console.error("Error updating donation status:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to update donation status",
				)
			}
		},
		{
			requirePermission: { donation: ["edit"] },
			params: donationParamsDto,
			body: updateStatusDto,
			response: {
				[StatusCodes.OK]: donationResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.delete(
		"/:id",
		async ({ params, user, status }) => {
			try {
				// Check if donation exists and belongs to organizer's campaign
				const existingDonation = await db
					.select({
						id: donations.id,
						status: donations.status,
						organizerId: campaigns.organizerId,
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(eq(donations.id, params.id))
					.limit(1)

				if (existingDonation.length === 0) {
					return status(StatusCodes.NOT_FOUND, "Donation not found")
				}

				if (existingDonation[0].organizerId !== user.id) {
					return status(StatusCodes.FORBIDDEN, "Access denied")
				}

				// Prevent deletion of completed donations
				if (existingDonation[0].status === "completed") {
					return status(
						StatusCodes.BAD_REQUEST,
						"Cannot delete completed donations",
					)
				}

				// Delete donation
				await db.delete(donations).where(eq(donations.id, params.id))

				return {
					success: true,
					message: "Donation deleted successfully",
				}
			} catch (error) {
				console.error("Error deleting donation:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to delete donation",
				)
			}
		},
		{
			requirePermission: { donation: ["delete"] },
			params: donationParamsDto,
			response: {
				[StatusCodes.OK]: deleteResponseDto,
				[StatusCodes.BAD_REQUEST]: t.String(),
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.NOT_FOUND]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/analytics",
		async ({ user, status, query }) => {
			try {
				const { status, campaignId, dateFrom, dateTo } = query

				// Build where conditions
				const conditions = [eq(campaigns.organizerId, user.id)]

				if (status && status !== "all") {
					conditions.push(eq(donations.status, status))
				}

				if (campaignId && campaignId !== "all") {
					conditions.push(eq(donations.campaignId, campaignId))
				}

				if (dateFrom) {
					conditions.push(gte(donations.createdAt, new Date(dateFrom)))
				}

				if (dateTo) {
					conditions.push(lte(donations.createdAt, new Date(dateTo)))
				}

				// Get total statistics
				const totalStats = await db
					.select({
						totalAmount: sum(donations.amount),
						totalCount: count(),
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))

				// Get status distribution
				const statusStats = await db
					.select({
						status: donations.status,
						count: count(),
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))
					.groupBy(donations.status)

				// Get top campaigns
				const topCampaigns = await db
					.select({
						campaignId: campaigns.id,
						campaignName: campaigns.name,
						totalAmount: sum(donations.amount),
						donationCount: count(),
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))
					.groupBy(campaigns.id, campaigns.name)
					.orderBy(desc(sum(donations.amount)))
					.limit(5)

				// Get monthly trends (last 12 months)
				const monthlyTrends = await db
					.select({
						month: sql<string>`TO_CHAR(${donations.createdAt}, 'YYYY-MM')`,
						amount: sum(donations.amount),
						count: count(),
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))
					.groupBy(sql`TO_CHAR(${donations.createdAt}, 'YYYY-MM')`)
					.orderBy(sql`TO_CHAR(${donations.createdAt}, 'YYYY-MM')`)

				const totalAmount = totalStats[0]?.totalAmount || "0"
				const totalCount = totalStats[0]?.totalCount || 0
				const averageAmount =
					totalCount > 0
						? (parseFloat(totalAmount) / totalCount).toFixed(2)
						: "0"

				// Build status distribution
				const statusDistribution = {
					pending: 0,
					completed: 0,
					failed: 0,
				}

				statusStats.forEach((stat) => {
					if (stat.status) {
						statusDistribution[stat.status] = stat.count
					}
				})

				return {
					success: true,
					analytics: {
						totalAmount,
						totalCount,
						averageAmount,
						statusDistribution,
						topCampaigns: topCampaigns.map((campaign) => ({
							campaignId: campaign.campaignId,
							campaignName: campaign.campaignName,
							totalAmount: campaign.totalAmount || "0",
							donationCount: campaign.donationCount,
						})),
						monthlyTrends: monthlyTrends.map((trend) => ({
							month: trend.month,
							amount: trend.amount || "0",
							count: trend.count,
						})),
					},
				}
			} catch (error) {
				console.error("Error fetching analytics:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to fetch analytics",
				)
			}
		},
		{
			requirePermission: { donation: ["view"] },
			query: donationFiltersDto,
			response: {
				[StatusCodes.OK]: analyticsResponseDto,
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)

	.get(
		"/export",
		async ({ user, status, query, set }) => {
			try {
				const {
					search,
					status,
					campaignId,
					dateFrom,
					dateTo,
					sortBy = "createdAt",
					sortOrder = "desc",
				} = query

				// Build where conditions (same as GET /)
				const conditions = [eq(campaigns.organizerId, user.id)]

				if (search) {
					const searchCondition = or(
						ilike(donations.donorName, `%${search}%`),
						ilike(donations.donorEmail, `%${search}%`),
						ilike(campaigns.name, `%${search}%`),
					)
					if (searchCondition) {
						conditions.push(searchCondition)
					}
				}

				if (status && status !== "all") {
					conditions.push(eq(donations.status, status))
				}

				if (campaignId && campaignId !== "all") {
					conditions.push(eq(donations.campaignId, campaignId))
				}

				if (dateFrom) {
					conditions.push(gte(donations.createdAt, new Date(dateFrom)))
				}

				if (dateTo) {
					conditions.push(lte(donations.createdAt, new Date(dateTo)))
				}

				// Build order by
				const orderColumn =
					sortBy === "donorName"
						? donations.donorName
						: sortBy === "amount"
							? donations.amount
							: donations.createdAt
				const orderDirection =
					sortOrder === "asc" ? orderColumn : desc(orderColumn)

				// Get all donations for export (no pagination)
				const exportDonations = await db
					.select({
						id: donations.id,
						campaignName: campaigns.name,
						donorName: donations.donorName,
						donorEmail: donations.donorEmail,
						amount: donations.amount,
						currency: donations.currency,
						status: donations.status,
						donorMessage: donations.donorMessage,
						internalNotes: donations.internalNotes,
						createdAt: donations.createdAt,
						updatedAt: donations.updatedAt,
					})
					.from(donations)
					.innerJoin(campaigns, eq(donations.campaignId, campaigns.id))
					.where(and(...conditions))
					.orderBy(orderDirection)

				// Generate CSV content
				const csvHeaders = [
					"ID",
					"Campaign",
					"Donor Name",
					"Donor Email",
					"Amount",
					"Currency",
					"Status",
					"Message",
					"Internal Notes",
					"Created At",
					"Updated At",
				]

				const csvRows = exportDonations.map((donation) => [
					donation.id,
					donation.campaignName || "",
					donation.donorName,
					donation.donorEmail,
					donation.amount,
					donation.currency,
					donation.status,
					donation.donorMessage || "",
					donation.internalNotes || "",
					donation.createdAt.toISOString(),
					donation.updatedAt.toISOString(),
				])

				const csvContent = [
					csvHeaders.join(","),
					...csvRows.map((row) =>
						row
							.map((field) =>
								typeof field === "string" && field.includes(",")
									? `"${field.replace(/"/g, '""')}"`
									: field,
							)
							.join(","),
					),
				].join("\n")

				// Set headers for file download
				set.headers["Content-Type"] = "text/csv"
				set.headers["Content-Disposition"] =
					`attachment; filename="donations-export-${new Date().toISOString().split("T")[0]}.csv"`

				return csvContent
			} catch (error) {
				console.error("Error exporting donations:", error)
				return status(
					StatusCodes.INTERNAL_SERVER_ERROR,
					"Failed to export donations",
				)
			}
		},
		{
			requirePermission: { donation: ["view"] },
			query: donationFiltersDto,
			response: {
				[StatusCodes.OK]: t.String(), // CSV content
				[StatusCodes.FORBIDDEN]: t.String(),
				[StatusCodes.INTERNAL_SERVER_ERROR]: t.String(),
			},
		},
	)
