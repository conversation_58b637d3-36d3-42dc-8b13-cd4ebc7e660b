import { eq } from "drizzle-orm"
import { StatusCodes } from "http-status-codes"
import { campaigns } from "@/campaigns/campaigns.schema"
import { db } from "@/db"
import { donations } from "@/donations/donations.schema"
import {
	AppError,
	ErrorCode,
	logError,
	withErrorHandling,
} from "@/lib/error-handler"
import type { ChipPaymentResponse } from "@/payments/payments.schema"

// TODO: remove withErrorHandling
// TODO: need to accept universal payment response
export abstract class DonationService {
	/**
	 * <PERSON>les completed donation payments by updating donation status
	 */
	static async handleDonationCompleted(
		data: ChipPaymentResponse, // TODO: this should be platform agnostic (chip, stripe, other gateways)
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing completed donation payment", {
				paymentId: data.id,
				reference: data.reference,
				status: data.status,
			})

			// Find donation by reference (donation ID) or chip payment ID
			const donationQuery = data.reference
				? eq(donations.id, data.reference)
				: eq(donations.chipPaymentId, data.id)

			const [donation] = await db
				.select()
				.from(donations)
				.where(donationQuery)
				.limit(1)

			if (!donation) {
				throw new AppError(
					"Donation not found for completed payment",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						paymentId: data.id,
						reference: data.reference,
						searchMethod: data.reference
							? "by_reference"
							: "by_chip_payment_id",
					},
				)
			}

			// Validate campaign exists and get campaign info
			const [campaign] = await db
				.select()
				.from(campaigns)
				.where(eq(campaigns.id, donation.campaignId))
				.limit(1)

			if (!campaign) {
				throw new AppError(
					"Campaign not found for donation",
					ErrorCode.CAMPAIGN_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						donationId: donation.id,
						campaignId: donation.campaignId,
						paymentId: data.id,
					},
				)
			}

			// Check if donation is already completed to avoid duplicate processing
			if (donation.status === "completed") {
				console.warn("Donation already marked as completed", {
					donationId: donation.id,
					paymentId: data.id,
					currentStatus: donation.status,
				})
				return
			}

			// Update donation status to completed
			await db
				.update(donations)
				.set({
					status: "completed",
					chipPaymentId: data.id,
					updatedAt: new Date(),
				})
				.where(eq(donations.id, donation.id))

			console.info("Donation successfully marked as completed", {
				donationId: donation.id,
				campaignId: campaign.id,
				campaignName: campaign.name,
				campaignSlug: campaign.slug,
				amount: donation.amount,
				currency: donation.currency,
				paymentId: data.id,
				donorEmail: "[REDACTED]",
				previousStatus: donation.status,
			})
		}, "DonationService.handleDonationCompleted")()
	}

	/**
	 * Handles failed donation payments by updating donation status and logging error
	 */
	static async handleDonationFailed(
		data: ChipPaymentResponse, // TODO: this should be platform agnostic (chip, stripe, other gateways)
	): Promise<void> {
		return withErrorHandling(async () => {
			logError(
				new AppError(
					"Processing failed donation payment",
					ErrorCode.WEBHOOK_PROCESSING_ERROR,
					StatusCodes.OK, // This is expected behavior, not an error response
				),
				{
					paymentId: data.id,
					reference: data.reference,
					status: data.status,
					chipErrorDetails:
						data.error_description || "No error description provided",
				},
				"warn",
			)

			// Find donation by reference (donation ID) or chip payment ID
			const donationQuery = data.reference
				? eq(donations.id, data.reference)
				: eq(donations.chipPaymentId, data.id)

			const [donation] = await db
				.select()
				.from(donations)
				.where(donationQuery)
				.limit(1)

			if (!donation) {
				throw new AppError(
					"Donation not found for failed payment",
					ErrorCode.DONATION_NOT_FOUND,
					StatusCodes.NOT_FOUND,
					{
						paymentId: data.id,
						reference: data.reference,
						searchMethod: data.reference
							? "by_reference"
							: "by_chip_payment_id",
					},
				)
			}

			// Check if donation is already failed to avoid duplicate processing
			if (donation.status === "failed") {
				console.warn("Donation already marked as failed", {
					donationId: donation.id,
					paymentId: data.id,
					currentStatus: donation.status,
				})
				return
			}

			// Update donation status to failed
			await db
				.update(donations)
				.set({
					status: "failed",
					chipPaymentId: data.id,
					updatedAt: new Date(),
				})
				.where(eq(donations.id, donation.id))

			logError(
				new AppError(
					"Donation payment failed",
					ErrorCode.WEBHOOK_PROCESSING_ERROR,
					StatusCodes.OK,
				),
				{
					donationId: donation.id,
					amount: donation.amount,
					currency: donation.currency,
					paymentId: data.id,
					donorEmail: "[REDACTED]",
					previousStatus: donation.status,
					chipErrorDetails: data.error_description,
				},
				"warn",
			)
		}, "DonationService.handleDonationFailed")()
	}

	/**
	 * Handles canceled donation payments by updating donation status
	 */
	static async handleDonationCanceled(
		data: ChipPaymentResponse, // TODO: this should be platform agnostic (chip, stripe, other gateways)
	): Promise<void> {
		return withErrorHandling(async () => {
			console.info("Processing canceled donation payment", {
				paymentId: data.id,
				reference: data.reference,
				status: data.status,
			})

			// Find donation by reference (donation ID) or chip payment ID
			const donationQuery = data.reference
				? eq(donations.id, data.reference)
				: eq(donations.chipPaymentId, data.id)

			const [donation] = await db
				.select()
				.from(donations)
				.where(donationQuery)
				.limit(1)

			if (!donation) {
				// For canceled payments, this might be expected if user cancels before donation is fully created
				console.warn("Donation not found for canceled payment", {
					paymentId: data.id,
					reference: data.reference,
					searchMethod: data.reference ? "by_reference" : "by_chip_payment_id",
				})
				return
			}

			// Check if donation is already canceled to avoid duplicate processing
			if (donation.status === "canceled") {
				console.warn("Donation already marked as canceled", {
					donationId: donation.id,
					paymentId: data.id,
					currentStatus: donation.status,
				})
				return
			}

			// Update donation status to canceled
			await db
				.update(donations)
				.set({
					status: "canceled",
					chipPaymentId: data.id,
					updatedAt: new Date(),
				})
				.where(eq(donations.id, donation.id))

			console.info("Donation successfully marked as canceled", {
				donationId: donation.id,
				amount: donation.amount,
				currency: donation.currency,
				paymentId: data.id,
				donorEmail: "[REDACTED]",
				previousStatus: donation.status,
			})
		}, "DonationService.handleDonationCanceled")()
	}
}
