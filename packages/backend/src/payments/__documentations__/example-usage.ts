import type { Campaign } from "../../campaigns/campaigns.schema"
import type { Donation } from "../../donations/donations.schema"
import type { RecurringDonation } from "../../recurring-donations/recurring-donations.schema"
import { PaymentGatewayFactory } from "../gateway"
import type { CreatePaymentParams } from "../payments.schema"

/**
 * Example usage of the new OOP PaymentGateway system
 *
 * This file demonstrates how to use the refactored payment system
 * which follows good OOP principles including:
 * - Abstract base class (PaymentGateway)
 * - Concrete implementation (ChipService)
 * - Factory pattern (PaymentGatewayFactory)
 * - Singleton pattern (ChipService instance management)
 * - Backward compatibility (ChipServiceCompat)
 */

// Example 1: Using the Factory Pattern (Recommended approach)
export async function createPaymentUsingFactory() {
	// Get payment gateway instance through factory
	const gateway = PaymentGatewayFactory.getGateway("chip")

	const paymentParams: CreatePaymentParams = {
		amount: 100.0,
		email: "<EMAIL>",
		fullName: "<PERSON> Doe",
		phone: "+60123456789",
		products: [
			{
				name: "Test Product",
				price: 100.0,
				category: "donation",
				quantity: "1",
			},
		],
		successRedirectUrl: "https://example.com/success",
		failureRedirectUrl: "https://example.com/failure",
		cancelRedirectUrl: "https://example.com/cancel",
	}

	try {
		const payment = await gateway.createPayment(paymentParams)
		console.log("Payment created:", payment.id)
		return payment
	} catch (error) {
		console.error("Payment creation failed:", error)
		throw error
	}
}

// Example 2: Using Direct Instance Access
export async function createPaymentUsingInstance() {
	// Get ChipService instance directly (Singleton pattern)
	const chipService = ChipService.getInstance()

	const paymentParams: CreatePaymentParams = {
		amount: 50.0,
		email: "<EMAIL>",
		fullName: "Jane Smith",
		products: [
			{
				name: "Another Product",
				price: 50.0,
				category: "donation",
				quantity: "1",
			},
		],
		successRedirectUrl: "https://example.com/success",
		failureRedirectUrl: "https://example.com/failure",
		cancelRedirectUrl: "https://example.com/cancel",
	}

	try {
		const payment = await chipService.createPayment(paymentParams)
		console.log("Payment created via instance:", payment.id)
		return payment
	} catch (error) {
		console.error("Payment creation failed:", error)
		throw error
	}
}

// Example 3: Creating Donation Payment (Template Method Pattern)
export async function createDonationPayment(
	donation: Donation,
	campaign: Campaign,
) {
	const gateway = PaymentGatewayFactory.getDefaultGateway()

	const paymentParams: CreatePaymentParams = {
		amount: parseFloat(donation.amount),
		email: donation.donorEmail,
		fullName: donation.donorName,
		phone: donation.donorPhone || undefined,
		products: [], // Will be populated by the template method
		notes: `Donation to ${campaign.name}`,
		// Redirect URLs will be generated automatically by the template method
		successRedirectUrl: "", // These will be overridden
		failureRedirectUrl: "",
		cancelRedirectUrl: "",
	}

	try {
		// This uses the template method pattern from PaymentGateway
		// which handles campaign validation, URL generation, and database updates
		const payment = await gateway.createDonationPayment(
			donation,
			campaign,
			paymentParams,
		)
		console.log("Donation payment created:", payment.id)
		return payment
	} catch (error) {
		console.error("Donation payment creation failed:", error)
		throw error
	}
}

// Example 4: Creating Recurring Donation Payment
export async function createRecurringDonationPayment(
	recurringDonation: RecurringDonation,
	campaign: Campaign,
) {
	const chipService = ChipService.getInstance()

	const paymentParams: CreatePaymentParams = {
		amount: parseFloat(recurringDonation.amount),
		email: recurringDonation.donorEmail,
		fullName: recurringDonation.donorName,
		phone: recurringDonation.donorPhone || undefined,
		products: [], // Will be populated by the implementation
		notes: `Recurring ${recurringDonation.frequency} donation`,
		isRecurringToken: true, // Enable token creation for future payments
		successRedirectUrl: "",
		failureRedirectUrl: "",
		cancelRedirectUrl: "",
	}

	try {
		const payment = await chipService.createRecurringDonationPayment(
			recurringDonation,
			campaign,
			paymentParams,
		)
		console.log("Recurring donation payment created:", payment.id)
		return payment
	} catch (error) {
		console.error("Recurring donation payment creation failed:", error)
		throw error
	}
}

// Example 5: Webhook Handling
export async function handleWebhook(payload: string, signature: string) {
	const gateway = PaymentGatewayFactory.getGateway("chip")

	// Verify webhook signature
	const verificationResult = await gateway.verifyWebhookSignature(
		payload,
		signature,
	)

	if (!verificationResult.isValid) {
		console.error(
			"Webhook signature verification failed:",
			verificationResult.error,
		)
		throw new Error("Invalid webhook signature")
	}

	// Parse webhook payload
	const webhookData = JSON.parse(payload)

	// Handle different webhook events
	switch (webhookData.event_type) {
		case "payment.completed":
			await gateway.handlePaymentCompleted(webhookData.data)
			break
		case "payment.failed":
			await gateway.handlePaymentFailed(webhookData.data)
			break
		case "payment.canceled":
			await gateway.handlePaymentCanceled(webhookData.data)
			break
		default:
			console.warn("Unknown webhook event type:", webhookData.event_type)
	}
}

// Example 6: Processing Scheduled Recurring Payments
export async function processScheduledPayments() {
	const chipService = ChipService.getInstance()

	try {
		// This method processes all due recurring payments
		await chipService.processScheduledRecurringPayments()
		console.log("Scheduled recurring payments processed successfully")
	} catch (error) {
		console.error("Failed to process scheduled payments:", error)
		throw error
	}
}

// Example 7: Using Backward Compatibility Layer
export async function useBackwardCompatibility() {
	// For existing code that uses static methods, use the compatibility layer
	const paymentParams: CreatePaymentParams = {
		amount: 25.0,
		email: "<EMAIL>",
		fullName: "Legacy User",
		products: [
			{
				name: "Legacy Product",
				price: 25.0,
				category: "donation",
				quantity: "1",
			},
		],
		successRedirectUrl: "https://example.com/success",
		failureRedirectUrl: "https://example.com/failure",
		cancelRedirectUrl: "https://example.com/cancel",
	}

	try {
		// Using the compatibility layer - behaves like the old static API
		const payment = await ChipServiceCompat.createPayment(paymentParams)
		console.log("Payment created via compatibility layer:", payment.id)
		return payment
	} catch (error) {
		console.error("Payment creation failed:", error)
		throw error
	}
}

// Example 8: Error Handling and Gateway Configuration
export function demonstrateErrorHandling() {
	try {
		// This will properly validate configuration on instantiation
		const gateway = PaymentGatewayFactory.getGateway("chip")
		console.log("Gateway initialized successfully:", gateway.gatewayName)

		// Gateway configuration is validated automatically
		console.log("Configuration is valid")
	} catch (error) {
		console.error("Gateway initialization failed:", error)
		// Handle configuration errors appropriately

		if (error instanceof Error) {
			if (error.message.includes("Base URL is required")) {
				console.error("Please set CHIP_BASE_URL environment variable")
			} else if (error.message.includes("Secret key is required")) {
				console.error("Please set CHIP_SECRET_KEY environment variable")
			} else if (error.message.includes("Brand ID is required")) {
				console.error("Please set CHIP_BRAND_ID environment variable")
			}
		}
	}
}

// Example 9: Extending the System (Future Gateway Implementation)
/*
To add a new payment gateway in the future:

1. Create a new class extending PaymentGateway:

export class StripeService extends PaymentGateway {
    readonly gatewayName = "Stripe"

    // Implement all abstract methods
    async createPayment(params: CreatePaymentParams): Promise<PaymentResponse> {
        // Stripe-specific implementation
    }

    // ... other methods
}

2. Update PaymentGatewayFactory:

static getGateway(gatewayName: "chip" | "stripe"): PaymentGateway {
    switch (gatewayName) {
        case "chip":
            return this.getChipInstance()
        case "stripe":
            return this.getStripeInstance()
        default:
            throw new Error(`Unknown payment gateway: ${gatewayName}`)
    }
}

This demonstrates the Open/Closed Principle - open for extension, closed for modification.
*/

export const PaymentGatewayExamples = {
	createPaymentUsingFactory,
	createPaymentUsingInstance,
	createDonationPayment,
	createRecurringDonationPayment,
	handleWebhook,
	processScheduledPayments,
	useBackwardCompatibility,
	demonstrateErrorHandling,
}
