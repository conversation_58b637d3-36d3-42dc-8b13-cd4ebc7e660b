import Elysia, { t } from "elysia"
import {
	enhancedPaymentService,
	type PaymentServiceContext,
	paymentRouteHelpers,
	paymentWebhook,
	type WebhookHandlerContext,
} from "../payment.service"
import type { CreatePaymentParams } from "../payments.schema"

/**
 * Example payment routes using the new Elysia plugin system
 *
 * This demonstrates how to use the payment service plugin with:
 * - Automatic error handling via .onError
 * - No manual withErrorHandling wrappers
 * - Type-safe payment operations
 * - Centralized error logging and response formatting
 */

// Payment creation and management routes
export const paymentRoutes = new Elysia({ prefix: "/api/payments" })
	.use(enhancedPaymentService)
	.post(
		"/create",
		async ({
			body,
			createPayment,
		}: PaymentServiceContext & { body: CreatePaymentParams }) => {
			// No need for withErrorHandling - errors bubble up to .onError handler
			const payment = await createPayment(body)

			return {
				success: true,
				message: "Payment created successfully",
				data: {
					id: payment.id,
					checkout_url: payment.checkout_url,
					status: payment.status,
				},
			}
		},
		{
			body: t.Object({
				amount: t.Number({ minimum: 0.01 }),
				email: t.String({ format: "email" }),
				fullName: t.String({ minLength: 1 }),
				phone: t.Optional(t.String()),
				products: t.Array(
					t.Object({
						name: t.String({ minLength: 1 }),
						price: t.Number({ minimum: 0.01 }),
						category: t.Optional(t.String()),
						quantity: t.Optional(t.String()),
					}),
					{ minItems: 1 },
				),
				successRedirectUrl: t.String({ format: "uri" }),
				failureRedirectUrl: t.String({ format: "uri" }),
				cancelRedirectUrl: t.String({ format: "uri" }),
				notes: t.Optional(t.String()),
				currency: t.Optional(t.String()),
				reference: t.Optional(t.String()),
				isRecurringToken: t.Optional(t.Boolean()),
			}),
			response: {
				200: t.Object({
					success: t.Boolean(),
					message: t.String(),
					data: t.Object({
						id: t.String(),
						checkout_url: t.Optional(t.String()),
						status: t.String(),
					}),
				}),
			},
		},
	)
	.get(
		"/:paymentId",
		async ({
			params,
			getPayment,
		}: PaymentServiceContext & { params: { paymentId: string } }) => {
			// Validate payment ID using helper
			const paymentId = paymentRouteHelpers.validatePaymentId(params.paymentId)

			// Get payment details - errors automatically handled by plugin
			const payment = await getPayment(paymentId)

			return {
				success: true,
				message: "Payment details retrieved successfully",
				data: payment,
			}
		},
		{
			params: t.Object({
				paymentId: t.String({ minLength: 1 }),
			}),
			response: {
				200: t.Object({
					success: t.Boolean(),
					message: t.String(),
					data: t.Any(), // Payment response structure
				}),
			},
		},
	)
	.get(
		"/",
		async ({
			query,
			getDefaultGateway,
		}: PaymentServiceContext & {
			query: { status?: string; limit?: number }
		}) => {
			// Example: List payments with filtering
			// In a real implementation, this would query the database

			console.info("Listing payments", {
				status: query.status,
				limit: query.limit || 10,
			})

			// For now, return a placeholder response
			return {
				success: true,
				message: "Payments retrieved successfully",
				data: {
					payments: [],
					total: 0,
					page: 1,
					limit: query.limit || 10,
				},
			}
		},
		{
			query: t.Object({
				status: t.Optional(t.String()),
				limit: t.Optional(t.Number({ minimum: 1, maximum: 100 })),
				page: t.Optional(t.Number({ minimum: 1 })),
			}),
		},
	)

// Donation-specific payment routes
export const donationPaymentRoutes = new Elysia({ prefix: "/api/donations" })
	.use(enhancedPaymentService)
	.post(
		"/:campaignSlug/donate",
		async ({
			params,
			body,
			createDonationPayment,
		}: PaymentServiceContext & {
			params: { campaignSlug: string }
			body: {
				amount: number
				donorName: string
				donorEmail: string
				donorPhone?: string
				message?: string
				anonymous?: boolean
			}
		}) => {
			// Validate campaign slug
			const campaignSlug = paymentRouteHelpers.validateCampaignSlug(
				params.campaignSlug,
			)

			// In a real implementation, you would:
			// 1. Find the campaign by slug
			// 2. Create a donation record
			// 3. Create the payment

			// For this example, we'll simulate the process
			console.info("Creating donation payment", {
				campaignSlug,
				amount: body.amount,
				donorEmail: "[REDACTED]",
			})

			// Mock campaign and donation objects
			const mockCampaign = {
				id: "camp_123",
				slug: campaignSlug,
				name: "Test Campaign",
				isActive: true,
			}

			const mockDonation = {
				id: "don_123",
				amount: body.amount.toString(),
				donorName: body.donorName,
				donorEmail: body.donorEmail,
				donorPhone: body.donorPhone,
				campaignId: mockCampaign.id,
			}

			const paymentParams: CreatePaymentParams = {
				amount: body.amount,
				email: body.donorEmail,
				fullName: body.donorName,
				phone: body.donorPhone,
				products: [], // Will be set by createDonationPayment
				notes: body.message,
				successRedirectUrl: "", // Will be generated
				failureRedirectUrl: "", // Will be generated
				cancelRedirectUrl: "", // Will be generated
			}

			// Create donation payment - uses template method pattern
			const payment = await createDonationPayment(
				mockDonation,
				mockCampaign,
				paymentParams,
			)

			return {
				success: true,
				message: "Donation payment created successfully",
				data: {
					donationId: mockDonation.id,
					paymentId: payment.id,
					checkout_url: payment.checkout_url,
					amount: body.amount,
					campaign: {
						slug: campaignSlug,
						name: mockCampaign.name,
					},
				},
			}
		},
		{
			params: t.Object({
				campaignSlug: t.String({ minLength: 1 }),
			}),
			body: t.Object({
				amount: t.Number({ minimum: 0.01 }),
				donorName: t.String({ minLength: 1 }),
				donorEmail: t.String({ format: "email" }),
				donorPhone: t.Optional(t.String()),
				message: t.Optional(t.String()),
				anonymous: t.Optional(t.Boolean()),
			}),
		},
	)

// Webhook handling routes
export const webhookRoutes = new Elysia({ prefix: "/api/webhooks" })
	.use(paymentWebhook)
	.post(
		"/payments/chip",
		async ({
			body,
			headers,
			handlePaymentWebhook,
		}: WebhookHandlerContext & {
			body: string
			headers: Headers
		}) => {
			// Validate webhook headers
			const { signature } = paymentRouteHelpers.validateWebhookHeaders(headers)

			// Handle the webhook - signature verification and routing is automatic
			const result = await handlePaymentWebhook(body, signature)

			return result
		},
		{
			// Accept raw body for signature verification
			body: t.String(),
			response: {
				200: t.Object({
					success: t.Boolean(),
					message: t.String(),
				}),
			},
		},
	)
	.get("/payments/test", async () => {
		// Test endpoint for webhook setup
		return {
			success: true,
			message: "Webhook endpoint is working",
			timestamp: new Date().toISOString(),
		}
	})

// Recurring payment routes
export const recurringPaymentRoutes = new Elysia({ prefix: "/api/recurring" })
	.use(enhancedPaymentService)
	.post(
		"/process-scheduled",
		async ({ chipService }: PaymentServiceContext) => {
			// Process all scheduled recurring payments
			await chipService.processScheduledRecurringPayments()

			return {
				success: true,
				message: "Scheduled recurring payments processed successfully",
				processedAt: new Date().toISOString(),
			}
		},
	)
	.post(
		"/:recurringId/retry",
		async ({
			params,
			chipService,
		}: PaymentServiceContext & {
			params: { recurringId: string }
		}) => {
			// Validate recurring donation ID
			if (!params.recurringId || params.recurringId.trim().length === 0) {
				throw new Error("Valid recurring donation ID is required")
			}

			// In a real implementation, you would:
			// 1. Find the recurring donation by ID
			// 2. Validate it can be retried
			// 3. Process the payment

			console.info("Retrying recurring payment", {
				recurringId: params.recurringId,
			})

			return {
				success: true,
				message: "Recurring payment retry initiated",
				recurringId: params.recurringId,
			}
		},
		{
			params: t.Object({
				recurringId: t.String({ minLength: 1 }),
			}),
		},
	)

// Health check and status routes
export const paymentHealthRoutes = new Elysia({
	prefix: "/api/payments/health",
})
	.use(enhancedPaymentService)
	.get("/", async ({ getDefaultGateway }: PaymentServiceContext) => {
		// Check payment gateway health
		const gateway = getDefaultGateway()

		// In a real implementation, you might ping the gateway API
		return {
			success: true,
			message: "Payment service is healthy",
			gateway: gateway.gatewayName,
			timestamp: new Date().toISOString(),
			status: "operational",
		}
	})
	.get("/config", async ({ chipService }: PaymentServiceContext) => {
		// Return configuration status (without sensitive data)
		return {
			success: true,
			message: "Payment configuration status",
			data: {
				gateway: chipService.gatewayName,
				hasBaseUrl: !!process.env.CHIP_BASE_URL,
				hasSecretKey: !!process.env.CHIP_SECRET_KEY,
				hasBrandId: !!process.env.CHIP_BRAND_ID,
				hasWebhookSecret: !!process.env.CHIP_WEBHOOK_SECRET,
				configurationValid: true, // Would be false if validation fails
			},
		}
	})

// Main payment app combining all routes
export const paymentApp = new Elysia()
	.use(paymentRoutes)
	.use(donationPaymentRoutes)
	.use(webhookRoutes)
	.use(recurringPaymentRoutes)
	.use(paymentHealthRoutes)
	.onError(({ error, code, set }) => {
		// Additional payment-specific error handling if needed
		// The plugin already handles most errors, but you can add specific logic here

		console.error("Payment app error", {
			error: error.message,
			code,
			stack: error.stack,
		})

		// Let the plugin handle the error
		throw error
	})

// Example usage in main server
/*
import { Elysia } from "elysia"
import { paymentApp } from "./payments/payment-routes.example"

const app = new Elysia()
	.use(paymentApp)
	.listen(3000)

console.log("Payment server running on http://localhost:3000")
*/

export default paymentApp
