# Migration Guide: From `withErrorHandling` to Elysia Error Handling

This guide explains how to migrate from manual `withErrorHandling` wrapper functions to Elysia's built-in `.onError` handling system for better error management and cleaner code.

## Overview

The refactoring moves error handling from individual method wrappers to centralized Elysia plugins, providing:

- ✅ **Cleaner Code**: No more wrapping every method call
- ✅ **Better Integration**: Native Elysia error handling
- ✅ **Centralized Logging**: All errors logged consistently
- ✅ **Type Safety**: Better TypeScript support
- ✅ **Automatic Response Formatting**: Consistent error responses

## Before and After Comparison

### Old Approach (withErrorHandling)

```typescript
// OLD: Manual error wrapping
import { withErrorHandling } from "../lib/error-handler"

export class PaymentService {
  static async createPayment(params: CreatePaymentParams) {
    return withErrorHandling(async () => {
      // Payment logic here
      const payment = await chipApi.createPayment(params)
      return payment
    }, "PaymentService.createPayment")()
  }

  static async getPayment(paymentId: string) {
    return withErrorHandling(async () => {
      // Get payment logic
      return await chipApi.getPayment(paymentId)
    }, "PaymentService.getPayment")()
  }
}

// Usage in routes
app.post("/payments", async ({ body }) => {
  try {
    const payment = await PaymentService.createPayment(body)
    return { success: true, data: payment }
  } catch (error) {
    // Manual error handling in each route
    return { success: false, error: error.message }
  }
})
```

### New Approach (Elysia Plugin)

```typescript
// NEW: Elysia plugin with centralized error handling
import { enhancedPaymentServicePlugin } from "./payment-service.plugin"

export class ChipService extends PaymentGateway {
  async createPayment(params: CreatePaymentParams): Promise<PaymentResponse> {
    // No withErrorHandling wrapper - just the business logic
    this.validateConfiguration()
    this.validatePaymentParams(params)

    const response = await fetch(/* ... */)
    // Errors bubble up automatically to Elysia's .onError
    return response.json()
  }

  async getPayment(paymentId: string): Promise<PaymentResponse> {
    // Direct implementation without wrappers
    const response = await fetch(`${this.config.baseUrl}/payments/${paymentId}`)
    return response.json()
  }
}

// Usage in routes - much cleaner!
app.use(enhancedPaymentServicePlugin)
  .post("/payments", async ({ body, createPayment }) => {
    // Errors automatically handled by plugin's .onError
    const payment = await createPayment(body)
    return { success: true, data: payment }
  })
```

## Step-by-Step Migration Process

### Step 1: Remove withErrorHandling Imports

**Before:**
```typescript
import {
  AppError,
  ErrorCode,
  logError,
  withErrorHandling, // ❌ Remove this
} from "../lib/error-handler"
```

**After:**
```typescript
import {
  AppError,
  ErrorCode,
  logError,
  // withErrorHandling removed ✅
} from "../lib/error-handler"
```

### Step 2: Convert Method Implementations

**Before:**
```typescript
static async createPayment(params: CreatePaymentParams) {
  return withErrorHandling(async () => {
    // Validation
    if (!params.amount) {
      throw new AppError("Amount required", ErrorCode.VALIDATION_ERROR, 400)
    }

    // API call
    const response = await fetch(apiUrl, { /* ... */ })
    if (!response.ok) {
      throw new AppError("API Error", ErrorCode.PAYMENT_GATEWAY_ERROR, 500)
    }

    return response.json()
  }, "createPayment")()
}
```

**After:**
```typescript
async createPayment(params: CreatePaymentParams): Promise<PaymentResponse> {
  // Direct implementation - errors bubble up naturally
  if (!params.amount) {
    throw new AppError("Amount required", ErrorCode.VALIDATION_ERROR, 400)
  }

  const response = await fetch(apiUrl, { /* ... */ })
  if (!response.ok) {
    throw new AppError("API Error", ErrorCode.PAYMENT_GATEWAY_ERROR, 500)
  }

  return response.json()
}
```

### Step 3: Add Elysia Plugin to Routes

**Before:**
```typescript
const paymentRoutes = new Elysia({ prefix: "/api/payments" })
  .post("/create", async ({ body }) => {
    try {
      const result = await PaymentService.createPayment(body)
      return { success: true, data: result }
    } catch (error) {
      console.error("Payment error:", error)
      return {
        success: false,
        error: error.message,
        status: error.statusCode || 500
      }
    }
  })
```

**After:**
```typescript
const paymentRoutes = new Elysia({ prefix: "/api/payments" })
  .use(enhancedPaymentServicePlugin) // ✅ Add plugin
  .post("/create", async ({ body, createPayment }) => {
    // No try-catch needed - plugin handles errors
    const result = await createPayment(body)
    return { success: true, data: result }
  })
```

### Step 4: Update Error Handling Strategy

The plugin automatically handles:
- **AppError instances**: Proper HTTP status codes and error responses
- **Validation errors**: Elysia schema validation failures
- **Parse errors**: JSON parsing issues
- **Unknown errors**: Converted to internal server errors

## Common Migration Patterns

### Pattern 1: Database Operations

**Before:**
```typescript
static async saveDonation(donation: Donation) {
  return withErrorHandling(async () => {
    const result = await db.insert(donations).values(donation)
    return result
  }, "saveDonation")()
}
```

**After:**
```typescript
async saveDonation(donation: Donation) {
  // Let database errors bubble up naturally
  const result = await db.insert(donations).values(donation)
  return result
}
```

### Pattern 2: External API Calls

**Before:**
```typescript
static async callExternalAPI(params: any) {
  return withErrorHandling(async () => {
    const response = await fetch(url, { body: JSON.stringify(params) })
    if (!response.ok) {
      throw new AppError("External API failed", ErrorCode.EXTERNAL_API_ERROR, 502)
    }
    return response.json()
  }, "callExternalAPI")()
}
```

**After:**
```typescript
async callExternalAPI(params: any) {
  const response = await fetch(url, { body: JSON.stringify(params) })
  if (!response.ok) {
    throw new AppError("External API failed", ErrorCode.EXTERNAL_API_ERROR, 502)
  }
  return response.json()
}
```

### Pattern 3: Validation Logic

**Before:**
```typescript
static async validatePayment(payment: PaymentData) {
  return withErrorHandling(async () => {
    if (!payment.amount || payment.amount <= 0) {
      throw new AppError("Invalid amount", ErrorCode.VALIDATION_ERROR, 400)
    }

    if (!payment.email?.includes("@")) {
      throw new AppError("Invalid email", ErrorCode.VALIDATION_ERROR, 400)
    }

    return true
  }, "validatePayment")()
}
```

**After:**
```typescript
validatePayment(payment: PaymentData): void {
  // Use Elysia schema validation when possible, or throw AppErrors directly
  if (!payment.amount || payment.amount <= 0) {
    throw new AppError("Invalid amount", ErrorCode.VALIDATION_ERROR, 400)
  }

  if (!payment.email?.includes("@")) {
    throw new AppError("Invalid email", ErrorCode.VALIDATION_ERROR, 400)
  }
}
```

## Error Context and Logging

### Maintaining Error Context

**Before:**
```typescript
return withErrorHandling(async () => {
  // The wrapper automatically logged context
  return await operation()
}, "operation-name")()
```

**After:**
```typescript
// The plugin automatically logs errors with request context
// You can add specific context by including it in the AppError
async operation() {
  try {
    return await someOperation()
  } catch (error) {
    throw new AppError(
      "Operation failed",
      ErrorCode.OPERATION_ERROR,
      500,
      {
        operationId: "op-123", // ✅ Add specific context
        originalError: error.message,
        timestamp: new Date().toISOString(),
      }
    )
  }
}
```

### Custom Logging

If you need additional logging:

```typescript
async operation() {
  this.log("info", "Starting operation", { operationId: "op-123" })

  const result = await someOperation()

  this.log("info", "Operation completed", {
    operationId: "op-123",
    resultSize: result.length
  })

  return result
}
```

## Benefits of the New Approach

### 1. Cleaner Service Code
- No wrapper functions cluttering business logic
- Methods focus on their core responsibility
- Easier to read and maintain

### 2. Centralized Error Handling
- All errors handled consistently
- Single place to modify error response format
- Better error monitoring and logging

### 3. Better Type Safety
- Elysia provides excellent TypeScript integration
- Route parameters and bodies are type-safe
- IDE support for autocompletion and error detection

### 4. Automatic Request Context
- Errors automatically include request information
- User agent, URL, method, timestamp
- No manual context passing required

### 5. Consistent API Responses
- All errors follow the same response format
- HTTP status codes are correctly set
- Client applications can rely on consistent error structure

## Testing Considerations

### Before (Hard to Test)
```typescript
// Hard to mock withErrorHandling
const mockWithErrorHandling = vi.fn()
vi.mock("../lib/error-handler", () => ({
  withErrorHandling: mockWithErrorHandling
}))
```

### After (Easy to Test)
```typescript
// Easy to test service methods directly
const chipService = ChipService.getInstance()

test("should create payment", async () => {
  const result = await chipService.createPayment(mockParams)
  expect(result.id).toBeDefined()
})

test("should throw AppError for invalid params", async () => {
  await expect(
    chipService.createPayment(invalidParams)
  ).rejects.toThrow(AppError)
})
```

## Backward Compatibility

For existing code that can't be migrated immediately, use the compatibility layer:

```typescript
import { ChipServiceCompat } from "./payments.service"

// Old code continues to work
const payment = await ChipServiceCompat.createPayment(params)
```

## Migration Checklist

- [ ] Remove `withErrorHandling` imports from service files
- [ ] Convert static methods to instance methods (if using OOP refactor)
- [ ] Remove `withErrorHandling` wrappers from method implementations
- [ ] Add payment service plugin to route files
- [ ] Update route handlers to use plugin context
- [ ] Remove manual try-catch blocks in routes (let plugin handle)
- [ ] Test error scenarios to ensure proper error responses
- [ ] Update error logging expectations in tests
- [ ] Document new error handling approach for team

## Common Pitfalls and Solutions

### Pitfall 1: Forgetting to Add Plugin
```typescript
// ❌ Missing plugin - errors won't be handled properly
const routes = new Elysia()
  .post("/payment", async ({ createPayment }) => { // createPayment undefined!
    return await createPayment(params)
  })

// ✅ Add the plugin
const routes = new Elysia()
  .use(enhancedPaymentServicePlugin) // Required!
  .post("/payment", async ({ createPayment }) => {
    return await createPayment(params)
  })
```

### Pitfall 2: Over-catching Errors
```typescript
// ❌ Don't catch errors unnecessarily
app.post("/payment", async ({ createPayment }) => {
  try {
    return await createPayment(params)
  } catch (error) {
    // Plugin already handles this!
    throw error
  }
})

// ✅ Let errors bubble up
app.post("/payment", async ({ createPayment }) => {
  return await createPayment(params) // Plugin handles errors
})
```

### Pitfall 3: Missing Error Context
```typescript
// ❌ Generic error without context
throw new Error("Something went wrong")

// ✅ Structured AppError with context
throw new AppError(
  "Payment gateway connection failed",
  ErrorCode.PAYMENT_GATEWAY_ERROR,
  502,
  {
    gateway: "chip",
    endpoint: "/payments",
    attempt: 1,
  }
)
```

## Conclusion

The migration from `withErrorHandling` to Elysia's error handling system provides:
- Cleaner, more maintainable code
- Better error handling and logging
- Improved type safety and developer experience
- Consistent API responses
- Easier testing

The change requires updating service methods and route handlers, but the result is a more robust and maintainable payment system that follows Elysia best practices.
