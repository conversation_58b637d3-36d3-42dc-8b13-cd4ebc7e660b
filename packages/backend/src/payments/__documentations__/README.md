# Payment Gateway System

This directory contains the refactored payment gateway system that follows object-oriented programming principles and design patterns for better maintainability, extensibility, and testability.

## Overview

The payment system has been refactored from a static service class to a proper OOP architecture using several design patterns:

- **Abstract Factory Pattern**: `PaymentGateway` abstract class defines the interface
- **Concrete Implementation**: `ChipService` implements Chip-In payment gateway
- **Factory Pattern**: `PaymentGatewayFactory` creates gateway instances
- **Singleton Pattern**: Ensures single instance of each gateway
- **Template Method Pattern**: Common payment flows with customizable steps

## Architecture

### Core Components

#### 1. `PaymentGateway` (Abstract Base Class)
- **File**: `payment-gateway.abstract.ts`
- **Purpose**: Defines the contract that all payment gateways must implement
- **Key Methods**:
  - `createPayment()` - Basic payment creation
  - `getPayment()` - Retrieve payment details
  - `verifyWebhookSignature()` - Webhook signature verification
  - `handlePaymentCompleted/Failed/Canceled()` - Webhook event handlers
  - `createDonationPayment()` - Template method for donation payments
  - `createRecurringDonationPayment()` - Recurring payment setup
  - `processRecurringPayment()` - Process recurring payments with stored tokens

#### 2. `ChipService` (Concrete Implementation)
- **File**: `payments.service.ts`
- **Purpose**: Chip-In payment gateway implementation
- **Features**:
  - Implements all abstract methods from `PaymentGateway`
  - Chip-specific configuration validation
  - Amount conversion to cents (Chip requirement)
  - Webhook signature verification using RSA
  - Comprehensive error handling and logging

#### 3. `PaymentGatewayFactory` (Factory)
- **File**: `payments.service.ts`
- **Purpose**: Creates and manages payment gateway instances
- **Methods**:
  - `getGateway(name)` - Get specific gateway instance
  - `getDefaultGateway()` - Get default gateway (Chip)

#### 4. `ChipServiceCompat` (Backward Compatibility)
- **File**: `payments.service.ts`
- **Purpose**: Provides static method compatibility for existing code
- **Usage**: Drop-in replacement for old static methods

## Design Principles Applied

### 1. Single Responsibility Principle (SRP)
- `PaymentGateway`: Defines payment gateway interface
- `ChipService`: Handles Chip-specific payment logic
- `PaymentGatewayFactory`: Manages gateway instances
- Each class has one reason to change

### 2. Open/Closed Principle (OCP)
- System is open for extension (new payment gateways)
- Closed for modification (existing code doesn't change)
- Adding new gateways requires no changes to existing code

### 3. Liskov Substitution Principle (LSP)
- Any `PaymentGateway` implementation can replace another
- Client code works with any gateway without modification
- `ChipService` is fully substitutable for `PaymentGateway`

### 4. Interface Segregation Principle (ISP)
- `PaymentGateway` defines focused, cohesive interface
- No unnecessary methods that implementations don't need
- Gateway-specific features are implemented separately

### 5. Dependency Inversion Principle (DIP)
- Client code depends on `PaymentGateway` abstraction
- Not dependent on concrete `ChipService` implementation
- Configuration is injected via constructor

## Usage Examples

### Basic Payment Creation

```typescript
// Using Factory Pattern (Recommended)
const gateway = PaymentGatewayFactory.getGateway("chip")
const payment = await gateway.createPayment(paymentParams)

// Using Direct Instance
const chipService = ChipService.getInstance()
const payment = await chipService.createPayment(paymentParams)
```

### Donation Payment (Template Method)

```typescript
const gateway = PaymentGatewayFactory.getDefaultGateway()
const payment = await gateway.createDonationPayment(donation, campaign, params)
```

### Webhook Handling

```typescript
const gateway = PaymentGatewayFactory.getGateway("chip")
const verification = await gateway.verifyWebhookSignature(payload, signature)

if (verification.isValid) {
    await gateway.handlePaymentCompleted(webhookData)
}
```

### Backward Compatibility

```typescript
// For existing code that uses static methods
const payment = await ChipServiceCompat.createPayment(params)
```

## Configuration

The `ChipService` requires the following environment variables:

```env
CHIP_BASE_URL=https://api.chip-in.asia/api/v1
CHIP_SECRET_KEY=your_secret_key
CHIP_BRAND_ID=your_brand_id
CHIP_WEBHOOK_SECRET=your_webhook_secret
```

Configuration is validated automatically on instance creation.

## Error Handling

The system uses comprehensive error handling with:

- **Custom Error Types**: `AppError` with specific error codes
- **Context Logging**: Detailed context for debugging
- **Graceful Degradation**: Non-critical errors don't stop payment flow
- **Sanitized Logging**: Sensitive data is redacted from logs

## Testing Strategy

### Unit Testing
- Test each gateway implementation independently
- Mock external API calls
- Test error conditions and edge cases

### Integration Testing
- Test webhook signature verification
- Test payment flow end-to-end
- Test recurring payment processing

### Contract Testing
- Ensure all implementations satisfy `PaymentGateway` contract
- Test substitutability of different gateways

## Adding New Payment Gateways

To add a new payment gateway (e.g., Stripe):

1. **Create Implementation**:
```typescript
export class StripeService extends PaymentGateway {
    readonly gatewayName = "Stripe"
    
    async createPayment(params: CreatePaymentParams): Promise<PaymentResponse> {
        // Stripe-specific implementation
    }
    
    // Implement all other abstract methods
}
```

2. **Update Factory**:
```typescript
static getGateway(gatewayName: "chip" | "stripe"): PaymentGateway {
    switch (gatewayName) {
        case "chip": return this.getChipInstance()
        case "stripe": return this.getStripeInstance()
        // ...
    }
}
```

3. **No changes required** in client code - it automatically works with new gateway

## Migration Guide

### From Static Methods to OOP

**Before**:
```typescript
const payment = await ChipService.createPayment(params)
```

**After**:
```typescript
// Option 1: Factory Pattern
const gateway = PaymentGatewayFactory.getGateway("chip")
const payment = await gateway.createPayment(params)

// Option 2: Backward Compatibility
const payment = await ChipServiceCompat.createPayment(params)
```

### Benefits of Migration

1. **Testability**: Easier to mock and test individual components
2. **Extensibility**: Simple to add new payment gateways
3. **Maintainability**: Clear separation of concerns
4. **Type Safety**: Better TypeScript support and IDE features
5. **Error Handling**: More comprehensive error management
6. **Configuration**: Proper configuration validation and management

## Files Structure

```
src/payments/
├── payment-gateway.abstract.ts    # Abstract base class and interfaces
├── payments.service.ts           # ChipService implementation and factory
├── payments.schema.ts           # Type definitions and schemas
├── example-usage.ts            # Usage examples and patterns
└── README.md                   # This documentation
```

## Best Practices

1. **Use Factory Pattern**: Always get gateways through `PaymentGatewayFactory`
2. **Handle Errors Gracefully**: Always wrap gateway calls in try-catch
3. **Validate Configuration**: Environment variables are checked automatically
4. **Log Appropriately**: Use gateway's built-in logging methods
5. **Type Safety**: Use TypeScript interfaces for better development experience
6. **Test Thoroughly**: Test both success and failure scenarios

## Future Enhancements

- **Retry Logic**: Automatic retry for failed payments
- **Circuit Breaker**: Prevent cascade failures
- **Metrics Collection**: Payment success/failure rates
- **A/B Testing**: Route payments to different gateways
- **Caching**: Cache payment gateway responses
- **Multi-Gateway Support**: Route payments based on criteria

This refactoring provides a solid foundation for a scalable, maintainable payment system that can easily accommodate future requirements and additional payment gateways.