import Elysia from "elysia"
import { StatusCodes } from "http-status-codes"
import {
	AppError,
	createErrorResponse,
	ErrorCode,
	logError,
} from "../lib/error-handler"
import { ChipService } from "./chip.service"
import { type PaymentGateway, PaymentGatewayFactory } from "./gateway"
import type { CreatePaymentParams } from "./payments.schema"

/**
 * Elysia plugin for payment services with integrated error handling
 *
 * This plugin provides:
 * - Payment gateway instances to route handlers
 * - Centralized error handling for payment operations
 * - Automatic error logging and response formatting
 * - Type-safe payment service access
 */
export const paymentService = new Elysia({ name: "payment-service" })
	.decorate("paymentGateway", PaymentGatewayFactory.getDefaultGateway())
	.decorate("chipService", ChipService.getInstance())
	.decorate(
		"getPaymentGateway",
		(gatewayName: "chip" = "chip"): PaymentGateway => {
			return PaymentGatewayFactory.getGateway(gatewayName)
		},
	)
	.decorate("getDefaultGateway", (): PaymentGateway => {
		return PaymentGatewayFactory.getDefaultGateway()
	})
	.onError(({ error, code, set, request }) => {
		// Get request context for logging
		const requestContext = {
			method: request.method,
			url: request.url,
			userAgent: request.headers.get("user-agent"),
			timestamp: new Date().toISOString(),
		}

		// Handle different error types
		if (error instanceof AppError) {
			// Payment-specific application errors
			logError(error, {
				...requestContext,
				errorCode: error.code,
				statusCode: error.statusCode,
				context: error.details,
			})

			set.status = error.statusCode
			return createErrorResponse(error)
		}

		// Handle validation errors (from Elysia)
		if (code === "VALIDATION") {
			const validationError = new AppError(
				"Request validation failed",
				ErrorCode.VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{ validationError: error.message },
			)

			logError(validationError, {
				...requestContext,
				originalError: error.message,
			})

			set.status = StatusCodes.BAD_REQUEST
			return createErrorResponse(validationError)
		}

		// Handle parse errors (malformed JSON, etc.)
		if (code === "PARSE") {
			const parseError = new AppError(
				"Request parsing failed",
				ErrorCode.VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{ parseError: error.message },
			)

			logError(parseError, {
				...requestContext,
				originalError: error.message,
			})

			set.status = StatusCodes.BAD_REQUEST
			return createErrorResponse(parseError)
		}

		// Handle not found errors
		if (code === "NOT_FOUND") {
			const notFoundError = new AppError(
				"Resource not found",
				ErrorCode.VALIDATION_ERROR,
				StatusCodes.NOT_FOUND,
				{ path: request.url },
			)

			logError(notFoundError, requestContext)

			set.status = StatusCodes.NOT_FOUND
			return createErrorResponse(notFoundError)
		}

		// Handle internal server errors and unexpected errors
		const internalError = new AppError(
			"Internal server error",
			ErrorCode.VALIDATION_ERROR,
			StatusCodes.INTERNAL_SERVER_ERROR,
			{
				originalError: error instanceof Error ? error.message : String(error),
				errorType: error.constructor.name,
			},
		)

		logError(
			internalError,
			{
				...requestContext,
				stackTrace: error instanceof Error ? error.stack : undefined,
			},
			"error",
		)

		set.status = StatusCodes.INTERNAL_SERVER_ERROR
		return createErrorResponse(internalError) // Don't expose internal details
	})
	.onBeforeHandle(({ request }) => {
		// Log incoming requests for payment endpoints
		if (
			request.url.includes("/payments") ||
			request.url.includes("/donations")
		) {
			console.info("Payment service request", {
				method: request.method,
				path: new URL(request.url).pathname,
				timestamp: new Date().toISOString(),
			})
		}
	})

/**
 * Enhanced payment service plugin with additional utilities
 */
export const enhancedPaymentService = paymentService
	.decorate(
		"executePaymentOperation",
		async <T>(operation: () => Promise<T>, context: string): Promise<T> => {
			try {
				return await operation()
			} catch (error) {
				// Log the error with context
				if (error instanceof AppError) {
					// Re-throw AppErrors as they will be handled by onError
					throw error
				}

				// Convert unknown errors to AppErrors
				const appError = new AppError(
					`Payment operation failed: ${context}`,
					ErrorCode.PAYMENT_GATEWAY_ERROR,
					StatusCodes.INTERNAL_SERVER_ERROR,
					{
						context,
						originalError:
							error instanceof Error ? error.message : String(error),
					},
				)

				throw appError
			}
		},
	)
	.decorate(
		"verifyWebhookSignature",
		async (payload: string, signature: string): Promise<boolean> => {
			try {
				const gateway = PaymentGatewayFactory.getDefaultGateway()
				const result = await gateway.verifyWebhookSignature(payload, signature)
				return result.isValid
			} catch (error) {
				throw new AppError(
					"Webhook signature verification failed",
					ErrorCode.WEBHOOK_SIGNATURE_INVALID,
					StatusCodes.UNAUTHORIZED,
					{
						hasPayload: !!payload,
						hasSignature: !!signature,
						originalError:
							error instanceof Error ? error.message : String(error),
					},
				)
			}
		},
	)
	.decorate("createPayment", async (params: CreatePaymentParams) => {
		const gateway = PaymentGatewayFactory.getDefaultGateway()
		return await gateway.createPayment(params)
	})
	.decorate("getPayment", async (paymentId: string) => {
		const gateway = PaymentGatewayFactory.getDefaultGateway()
		return await gateway.getPayment(paymentId)
	})
	.decorate(
		"createDonationPayment",
		async (donation: any, campaign: any, params: CreatePaymentParams) => {
			const gateway = PaymentGatewayFactory.getDefaultGateway()
			return await gateway.createDonationPayment(donation, campaign, params)
		},
	)

// Export types for better TypeScript support
export type PaymentServiceContext = {
	paymentGateway: PaymentGateway
	chipService: ChipService
	getPaymentGateway: (gatewayName?: "chip") => PaymentGateway
	getDefaultGateway: () => PaymentGateway
	executePaymentOperation: <T>(
		operation: () => Promise<T>,
		context: string,
	) => Promise<T>
	verifyWebhookSignature: (
		payload: string,
		signature: string,
	) => Promise<boolean>
	createPayment: (params: CreatePaymentParams) => Promise<unknown>
	getPayment: (paymentId: string) => Promise<unknown>
	createDonationPayment: (
		donation: any,
		campaign: any,
		params: CreatePaymentParams,
	) => Promise<unknown>
}
