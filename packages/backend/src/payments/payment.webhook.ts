import Elysia from "elysia"
import { StatusCodes } from "http-status-codes"
import { AppError, ErrorCode } from "../lib/error-handler"
import { PaymentGatewayFactory } from "./gateway"
import {
	enhancedPaymentService,
	type PaymentServiceContext,
} from "./payment.service"

/**
 * Webhook handling plugin specifically for payment webhooks
 */
export const paymentWebhook = new Elysia({ name: "payment-webhook" })
	.use(enhancedPaymentService)
	.decorate(
		"handlePaymentWebhook",
		async (payload: string, signature: string) => {
			// Verify signature first
			const paymentGateway = PaymentGatewayFactory.getDefaultGateway()
			const verificationResult = await paymentGateway.verifyWebhookSignature(
				payload,
				signature,
			)
			if (!verificationResult.isValid) {
				throw new AppError(
					"Invalid webhook signature",
					ErrorCode.WEBHOOK_SIGNATURE_INVALID,
					StatusCodes.UNAUTHORIZED,
				)
			}

			// Parse webhook payload
			// TODO: different payment gateway returns different event_type/status
			let webhookData: unknown
			try {
				webhookData = JSON.parse(payload)
			} catch (error) {
				throw new AppError(
					"Invalid webhook payload format",
					ErrorCode.WEBHOOK_PROCESSING_ERROR,
					StatusCodes.BAD_REQUEST,
					{
						parseError: error instanceof Error ? error.message : String(error),
					},
				)
			}

			// Route to appropriate handler based on event type
			const eventData = webhookData as any

			switch (eventData.event_type) {
				case "payment.completed":
				case "payment.success":
					await paymentGateway.handlePaymentCompleted(eventData.data)
					break
				case "payment.failed":
				case "payment.failure":
					await paymentGateway.handlePaymentFailed(eventData.data)
					break
				case "payment.canceled":
				case "payment.cancelled":
					await paymentGateway.handlePaymentCanceled(eventData.data)
					break
				default:
					console.warn("Unknown webhook event type received", {
						eventType: eventData.event_type,
						paymentId: eventData.data?.id,
					})
					// Don't throw error for unknown events, just log and continue
					break
			}

			return { success: true, message: "Webhook processed successfully" }
		},
	)

/**
 * Type-safe route helpers for common payment operations
 */
export const paymentRouteHelpers = {
	/**
	 * Extract payment ID from route params with validation
	 */
	validatePaymentId: (paymentId: string | undefined): string => {
		if (
			!paymentId ||
			typeof paymentId !== "string" ||
			paymentId.trim().length === 0
		) {
			throw new AppError(
				"Valid payment ID is required",
				ErrorCode.VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{ providedPaymentId: paymentId },
			)
		}
		return paymentId.trim()
	},

	/**
	 * Extract and validate donation ID from route params
	 */
	validateDonationId: (donationId: string | undefined): string => {
		if (
			!donationId ||
			typeof donationId !== "string" ||
			donationId.trim().length === 0
		) {
			throw new AppError(
				"Valid donation ID is required",
				ErrorCode.VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{ providedDonationId: donationId },
			)
		}
		return donationId.trim()
	},

	/**
	 * Extract and validate campaign slug from route params
	 */
	validateCampaignSlug: (campaignSlug: string | undefined): string => {
		if (
			!campaignSlug ||
			typeof campaignSlug !== "string" ||
			campaignSlug.trim().length === 0
		) {
			throw new AppError(
				"Valid campaign slug is required",
				ErrorCode.VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{ providedCampaignSlug: campaignSlug },
			)
		}
		return campaignSlug.trim()
	},

	/**
	 * Validate webhook headers
	 */
	validateWebhookHeaders: (
		headers: Record<string, string | undefined>,
	): { signature: string } => {
		const signature = headers?.["x-signature"] || headers?.["x-chip-signature"]

		if (!signature) {
			throw new AppError(
				"Webhook signature header is required",
				ErrorCode.WEBHOOK_SIGNATURE_INVALID,
				StatusCodes.BAD_REQUEST,
				// { availableHeaders: Array.from(headers) },
			)
		}

		return { signature }
	},
}

// Export types for better TypeScript support
export type WebhookHandlerContext = PaymentServiceContext & {
	handlePaymentWebhook: (
		payload: string,
		signature: string,
	) => Promise<{ success: boolean; message: string }>
}
