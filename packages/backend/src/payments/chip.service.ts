import crypto from "node:crypto"
import { and, eq, lte } from "drizzle-orm"
import { StatusCodes } from "http-status-codes"
import type { Campaign } from "@/campaigns/campaigns.schema"
import { db } from "@/db"
import type { Donation } from "@/donations/donations.schema"
import { donations } from "@/donations/donations.schema"
import { DonationService } from "@/donations/donations.service"
import {
	AppError,
	ErrorCode,
	logError,
	sanitizeForLogging,
	validateEnvironmentVariables,
	validatePaymentAmount,
} from "@/lib/error-handler"
import { type RedirectContext, RedirectUrlGenerator } from "@/lib/redirect-urls"
import {
	type RecurringDonation,
	recurringDonations,
	recurringPaymentAttempts,
} from "@/recurring-donations/recurring-donations.schema"
import { RecurringDonationService } from "@/recurring-donations/recurring-donations.service"
import {
	PaymentGateway,
	type PaymentGatewayConfig,
	type PaymentRedirectUrls,
	type PaymentResponse,
	type WebhookVerificationResult,
} from "./gateway"
import type {
	ChipPaymentResponse,
	CreatePaymentParams,
} from "./payments.schema"

/**
 * ChipService - Payment gateway implementation for Chip-In
 * Extends the abstract PaymentGateway class following OOP principles
 */
export class ChipService extends PaymentGateway {
	readonly gatewayName = "Chip"
	private static instance: ChipService | null = null

	private constructor(config: PaymentGatewayConfig) {
		super(config)
	}

	/**
	 * Factory method to create or get ChipService instance (Singleton pattern)
	 */
	static getInstance(): ChipService {
		if (!ChipService.instance) {
			const config: PaymentGatewayConfig = {
				baseUrl: Bun.env.CHIP_BASE_URL!,
				secretKey: Bun.env.CHIP_SECRET_KEY!,
				brandId: Bun.env.CHIP_BRAND_ID!,
				webhookSecret: Bun.env.CHIP_WEBHOOK_SECRET!,
				publicKey: Bun.env.CHIP_WEBHOOK_SECRET!, // Using webhook secret as public key for signature verification
			}
			ChipService.instance = new ChipService(config)
		}
		return ChipService.instance
	}

	/**
	 * Validates Chip-specific configuration
	 */
	protected validateConfiguration(): void {
		super.validateConfiguration()

		validateEnvironmentVariables([
			"CHIP_BASE_URL",
			"CHIP_SECRET_KEY",
			"CHIP_BRAND_ID",
			"CHIP_WEBHOOK_SECRET",
		])

		if (!this.config.brandId) {
			throw new Error(`${this.gatewayName}: Brand ID is required`)
		}
		if (!this.config.webhookSecret) {
			throw new Error(`${this.gatewayName}: Webhook secret is required`)
		}

		// Also validate redirect URL configuration
		RedirectUrlGenerator.validateEnvironmentConfiguration()
	}

	/**
	 * Verifies webhook signature from Chip
	 */
	async verifyWebhookSignature(
		payload: string,
		signature: string,
	): Promise<WebhookVerificationResult> {
		const context = {
			hasPayload: !!payload,
			hasSignature: !!signature,
			payloadLength: payload?.length || 0,
		}

		if (!payload || !signature) {
			const error =
				"Missing required parameters for webhook signature verification"
			logError(
				new AppError(
					error,
					ErrorCode.WEBHOOK_SIGNATURE_INVALID,
					StatusCodes.BAD_REQUEST,
					context,
				),
				context,
			)
			return { isValid: false, error }
		}

		try {
			this.validateConfiguration()
		} catch (error) {
			logError(error as AppError, context)
			return { isValid: false, error: (error as Error).message }
		}

		this.log("info", "Verifying webhook signature", {
			payloadLength: payload.length,
			signatureLength: signature.length,
		})

		try {
			const verifier = crypto.createVerify("sha256WithRSAEncryption")
			verifier.update(payload)

			// Compare the calculated signature with the provided one
			const result = verifier.verify(
				this.config.publicKey!,
				Buffer.from(signature, "base64"),
			)

			this.log("info", "Webhook signature verification completed", {
				result: result ? "valid" : "invalid",
				payloadLength: payload.length,
			})

			if (!result) {
				const error = "Webhook signature verification failed"
				logError(
					new AppError(
						error,
						ErrorCode.WEBHOOK_SIGNATURE_INVALID,
						StatusCodes.UNAUTHORIZED,
					),
					{ ...context, verificationResult: "failed" },
					"warn",
				)
				return { isValid: false, error }
			}

			return { isValid: true }
		} catch (error) {
			const errorMessage = "Error during webhook signature verification"
			logError(
				new AppError(
					errorMessage,
					ErrorCode.WEBHOOK_SIGNATURE_INVALID,
					StatusCodes.INTERNAL_SERVER_ERROR,
					{
						originalError:
							error instanceof Error ? error.message : String(error),
					},
				),
				context,
			)
			return { isValid: false, error: errorMessage }
		}
	}

	/**
	 * Creates a payment with Chip-In API
	 */
	async createPayment(params: CreatePaymentParams): Promise<PaymentResponse> {
		this.log("info", "Creating payment", {
			amount: params.amount,
			currency: params.currency || "MYR",
			reference: params.reference,
			productCount: params.products?.length || 0,
		})

		// Validate configuration first
		this.validateConfiguration()

		// Validate payment parameters
		this.validatePaymentParams(params)

		// Convert amount to gateway format (cents for Chip)
		const amountInCents = this.convertAmountToGatewayFormat(params.amount)

		// Convert product prices to cents
		const products = params.products.map((product) => ({
			...product,
			price: this.convertAmountToGatewayFormat(product.price),
		}))

		const requestPayload = {
			brand_id: this.config.brandId,
			purchase: {
				amount: amountInCents, // Amount in cents
				currency: params.currency || "MYR",
				products: products,
				notes: params.notes,
			},
			client: {
				email: params.email,
				phone: params.phone,
				full_name: params.fullName,
			},
			success_callback: params.successUrl,
			failure_callback: params.failureUrl,
			success_redirect: params.successRedirectUrl,
			failure_redirect: params.failureRedirectUrl,
			cancel_redirect: params.cancelRedirectUrl,
			platform: "web",
			payment_method_whitelist: params.paymentMethods,
			reference: params.reference, // Order reference for tracking
			due: params.due, // Optional due date
			is_recurring_token: params.isRecurringToken || false,
		}

		let response: Response
		try {
			response = await fetch(`${this.config.baseUrl}/purchases/`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${this.config.secretKey}`,
					"X-Brand-ID": this.config.brandId!,
				},
				body: JSON.stringify(requestPayload),
			})
		} catch (error) {
			throw new AppError(
				"Failed to connect to Chip payment gateway",
				ErrorCode.PAYMENT_GATEWAY_ERROR,
				StatusCodes.BAD_GATEWAY,
				{
					originalError: error instanceof Error ? error.message : String(error),
					chipBaseUrl: this.config.baseUrl,
				},
			)
		}

		if (!response.ok) {
			let errorData: unknown
			try {
				errorData = await response.json()
			} catch {
				errorData = await response.text()
			}

			logError(
				new AppError(
					"Chip payment creation failed",
					ErrorCode.PAYMENT_GATEWAY_ERROR,
					response.status,
					{
						httpStatus: response.status,
						httpStatusText: response.statusText,
						chipError: errorData,
						requestParams: sanitizeForLogging(
							params as unknown as Record<string, unknown>,
						),
					},
				),
				{
					amount: params.amount,
					reference: params.reference,
					httpStatus: response.status,
				},
			)

			throw new AppError(
				`Payment gateway error: ${response.statusText}`,
				ErrorCode.PAYMENT_GATEWAY_ERROR,
				StatusCodes.BAD_GATEWAY,
				{ chipError: errorData },
			)
		}

		const paymentResponse = await response.json()

		this.log("info", "Payment created successfully", {
			paymentId: paymentResponse.id,
			amount: params.amount,
			reference: params.reference,
			checkoutUrl: paymentResponse.checkout_url ? "present" : "missing",
		})

		return paymentResponse
	}

	/**
	 * Validates Chip-specific payment parameters
	 */
	protected validatePaymentParams(params: CreatePaymentParams): void {
		super.validatePaymentParams(params)
		// Validate amount with Chip-specific rules
		validatePaymentAmount(params.amount)

		// Validate redirect URLs
		const requiredUrls = {
			successRedirectUrl: params.successRedirectUrl,
			failureRedirectUrl: params.failureRedirectUrl,
			cancelRedirectUrl: params.cancelRedirectUrl,
		}

		for (const [urlName, url] of Object.entries(requiredUrls)) {
			if (!url || typeof url !== "string" || url.trim().length === 0) {
				throw new AppError(
					`${urlName} is required and cannot be empty`,
					ErrorCode.PAYMENT_VALIDATION_ERROR,
					StatusCodes.BAD_REQUEST,
					{ missingUrl: urlName },
				)
			}

			// Basic URL validation
			try {
				new URL(url)
			} catch {
				throw new AppError(
					`${urlName} must be a valid URL`,
					ErrorCode.PAYMENT_VALIDATION_ERROR,
					StatusCodes.BAD_REQUEST,
					{ invalidUrl: urlName, providedUrl: url },
				)
			}
		}
	}

	/**
	 * Updates donation record with payment ID
	 */
	protected async updateDonationWithPaymentId(
		donation: Donation,
		paymentResponse: PaymentResponse,
	): Promise<void> {
		try {
			await db
				.update(donations)
				.set({
					chipPaymentId: paymentResponse.id,
					updatedAt: new Date(),
				})
				.where(eq(donations.id, donation.id))

			this.log("info", "Donation updated with payment ID", {
				donationId: donation.id,
				chipPaymentId: paymentResponse.id,
			})
		} catch (dbError) {
			// Log database error but don't fail the payment creation
			logError(
				new AppError(
					"Failed to update donation with Chip payment ID",
					ErrorCode.DATABASE_ERROR,
					StatusCodes.INTERNAL_SERVER_ERROR,
					{
						donationId: donation.id,
						chipPaymentId: paymentResponse.id,
						originalError:
							dbError instanceof Error ? dbError.message : String(dbError),
					},
				),
				{
					donationId: donation.id,
					chipPaymentId: paymentResponse.id,
				},
				"warn",
			)
		}
	}

	/**
	 * Generates donation-specific redirect URLs
	 */
	protected generateDonationRedirectUrls(
		donation: Donation,
		campaign: Campaign,
		paymentId?: string,
	): PaymentRedirectUrls {
		const generator = RedirectUrlGenerator.fromEnvironment()

		const context: RedirectContext = {
			campaignId: campaign.id,
			campaignSlug: campaign.slug,
			donationId: donation.id,
			paymentId,
		}

		return generator.generateRedirectUrls(context)
	}

	/**
	 * Retrieves payment details by ID
	 */
	async getPayment(paymentId: string): Promise<PaymentResponse> {
		if (
			!paymentId ||
			typeof paymentId !== "string" ||
			paymentId.trim().length === 0
		) {
			throw new AppError(
				"Payment ID is required",
				ErrorCode.PAYMENT_VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{ providedPaymentId: paymentId },
			)
		}

		this.validateConfiguration()

		this.log("info", "Retrieving payment details", { paymentId })

		let response: Response
		try {
			response = await fetch(`${this.config.baseUrl}/purchases/${paymentId}`, {
				method: "GET",
				headers: {
					Authorization: `Bearer ${this.config.secretKey}`,
					"X-Brand-ID": this.config.brandId!,
				},
			})
		} catch (error) {
			throw new AppError(
				"Failed to connect to Chip payment gateway",
				ErrorCode.PAYMENT_GATEWAY_ERROR,
				StatusCodes.BAD_GATEWAY,
				{
					paymentId,
					originalError: error instanceof Error ? error.message : String(error),
				},
			)
		}

		if (!response.ok) {
			let errorData: unknown
			try {
				errorData = await response.json()
			} catch {
				errorData = await response.text()
			}

			throw new AppError(
				`Failed to retrieve payment details from Chip`,
				ErrorCode.PAYMENT_GATEWAY_ERROR,
				response.status,
				{
					paymentId,
					httpStatus: response.status,
					httpStatusText: response.statusText,
					chipError: errorData,
				},
			)
		}

		const paymentData = await response.json()
		this.log("info", "Payment details retrieved successfully", {
			paymentId,
			status: paymentData.status,
		})

		return paymentData
	}

	/**
	 * Handles successful payment webhook
	 */
	async handlePaymentCompleted(data: PaymentResponse): Promise<void> {
		if (!data.id) {
			console.warn("Payment completed missing ID")
			return
		}

		const product = data.purchase?.products?.[0]

		// determine the handler to use
		switch (product?.category) {
			case "donation":
				await DonationService.handleDonationCompleted(
					data as unknown as ChipPaymentResponse,
				)
				break
			case "recurring_donation":
				// Check if this is initial token setup or recurring payment
				if (data.is_recurring_token && data.recurring_token) {
					await RecurringDonationService.handleRecurringTokenSetup(
						data as unknown as ChipPaymentResponse,
					)
				} else {
					await this.handleRecurringDonationCompleted(
						data as unknown as ChipPaymentResponse,
					)
				}
				break
			default:
				console.warn("Unknown product category for completed payment", {
					paymentId: data.id,
					category: product?.category,
				})
				break
		}
	}

	/**
	 * Handles failed payment webhook
	 */
	async handlePaymentFailed(data: PaymentResponse): Promise<void> {
		if (!data.id) {
			console.warn("Payment failed missing ID")
			return
		}

		const product = data.purchase?.products?.[0]

		// determine the handler to use
		switch (product?.category) {
			case "donation":
				await this.handleDonationFailed(data)
				break
			case "recurring_donation":
				// Check if this is initial token setup failure or recurring payment failure
				if (data.is_recurring_token) {
					await this.handleRecurringTokenSetupFailed(data)
				} else {
					await this.handleRecurringDonationFailed(data)
				}
				break
			default:
				console.warn("Unknown product category for failed payment", {
					paymentId: data.id,
					category: product?.category,
				})
				break
		}
	}

	/**
	 * Handles canceled payment webhook
	 */
	async handlePaymentCanceled(data: PaymentResponse): Promise<void> {
		if (!data.id) {
			console.warn("Payment canceled missing ID")
			return
		}

		const product = data.purchase?.products?.[0]

		// determine the handler to use
		switch (product?.category) {
			case "donation":
				await DonationService.handleDonationCanceled(
					data as unknown as ChipPaymentResponse,
				)
				break
			case "recurring_donation":
				await RecurringDonationService.handleRecurringDonationCanceled(
					data as unknown as ChipPaymentResponse,
				)
				break
			default:
				console.warn("Unknown product category for canceled payment", {
					paymentId: data.id,
					category: product?.category,
				})
				break
		}
	}

	/**
	 * Generates recurring donation redirect URLs
	 */
	protected generateRecurringRedirectUrls(
		recurringDonation: RecurringDonation,
		campaign: Campaign,
		paymentId?: string,
	): PaymentRedirectUrls {
		const generator = RedirectUrlGenerator.fromEnvironment()

		const context: RedirectContext = {
			campaignId: campaign.id,
			campaignSlug: campaign.slug,
			donationId: recurringDonation.id, // Use recurring donation ID
			paymentId,
			isRecurring: true,
		}

		return generator.generateRedirectUrls(context)
	}

	/**
	 * Creates a recurring donation payment with token setup
	 */
	async createRecurringDonationPayment(
		recurringDonation: RecurringDonation,
		campaign: Campaign,
		params: CreatePaymentParams,
	): Promise<PaymentResponse> {
		this.log("info", "Creating recurring donation payment", {
			recurringDonationId: recurringDonation.id,
			campaignId: campaign.id,
			campaignName: campaign.name,
			amount: params.amount,
			frequency: recurringDonation.frequency,
			currency: params.currency || "MYR",
		})

		// Validate campaign is active
		this.validateCampaignForDonation(campaign)

		// Generate redirect URLs with recurring donation context
		const redirectUrls = this.generateRecurringRedirectUrls(
			recurringDonation,
			campaign,
		)

		// Create payment with recurring token flag enabled
		const paymentParams: CreatePaymentParams = {
			...params,
			products: [
				{
					name: `Recurring Donation to ${campaign.name} (${recurringDonation.frequency})`,
					price: params.amount,
					category: "recurring_donation",
					quantity: "1",
				},
			],
			reference: recurringDonation.id,
			notes:
				params.notes ||
				`Recurring ${recurringDonation.frequency} donation to campaign: ${campaign.name}`,
			isRecurringToken: true, // Enable recurring token creation
			// Override redirect URLs with generated ones
			successRedirectUrl: redirectUrls.successRedirectUrl,
			failureRedirectUrl: redirectUrls.failureRedirectUrl,
			cancelRedirectUrl: redirectUrls.cancelRedirectUrl,
		}

		const chipResponse = await this.createPayment(paymentParams)

		this.log("info", "Recurring donation payment created", {
			recurringDonationId: recurringDonation.id,
			chipPaymentId: chipResponse.id,
			campaignId: campaign.id,
			amount: params.amount,
			frequency: recurringDonation.frequency,
			isRecurringToken: chipResponse.is_recurring_token,
		})

		return chipResponse
	}

	/**
	 * Processes a recurring payment using stored token
	 */
	async processRecurringPayment(
		recurringDonation: RecurringDonation,
	): Promise<PaymentResponse> {
		this.log("info", "Processing recurring payment", {
			recurringDonationId: recurringDonation.id,
			amount: recurringDonation.amount,
			frequency: recurringDonation.frequency,
			nextPaymentDate: recurringDonation.nextPaymentDate,
		})

		// Validate recurring donation is active
		if (recurringDonation.status !== "active") {
			throw new AppError(
				"Recurring donation is not active",
				ErrorCode.PAYMENT_VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{
					recurringDonationId: recurringDonation.id,
					status: recurringDonation.status,
				},
			)
		}

		// Check if token is expired
		if (
			recurringDonation.chipTokenExpiresAt &&
			recurringDonation.chipTokenExpiresAt < new Date()
		) {
			throw new AppError(
				"Recurring payment token has expired",
				ErrorCode.PAYMENT_VALIDATION_ERROR,
				StatusCodes.BAD_REQUEST,
				{
					recurringDonationId: recurringDonation.id,
					tokenExpiresAt: recurringDonation.chipTokenExpiresAt,
				},
			)
		}

		this.validateConfiguration()

		// Create payment using recurring token
		const requestPayload = {
			brand_id: this.config.brandId,
			recurring_token: recurringDonation.chipRecurringToken,
			purchase: {
				amount: this.convertAmountToGatewayFormat(
					parseFloat(recurringDonation.amount),
				), // Convert to cents
				currency: recurringDonation.currency,
				products: [
					{
						name: `Recurring Donation (${recurringDonation.frequency})`,
						price: this.convertAmountToGatewayFormat(
							parseFloat(recurringDonation.amount),
						),
						category: "recurring_donation",
						quantity: "1",
					},
				],
				notes: `Recurring ${recurringDonation.frequency} donation`,
			},
			client: {
				email: recurringDonation.donorEmail,
				phone: recurringDonation.donorPhone || "",
				full_name: recurringDonation.donorName,
			},
			reference: `recurring_${recurringDonation.id}_${Date.now()}`,
			platform: "api",
		}

		let response: Response
		try {
			response = await fetch(`${this.config.baseUrl}/purchases/`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${this.config.secretKey}`,
					"X-Brand-ID": this.config.brandId!,
				},
				body: JSON.stringify(requestPayload),
			})
		} catch (error) {
			throw new AppError(
				"Failed to connect to Chip payment gateway for recurring payment",
				ErrorCode.PAYMENT_GATEWAY_ERROR,
				StatusCodes.BAD_GATEWAY,
				{
					recurringDonationId: recurringDonation.id,
					originalError: error instanceof Error ? error.message : String(error),
				},
			)
		}

		if (!response.ok) {
			let errorData: unknown
			try {
				errorData = await response.json()
			} catch {
				errorData = await response.text()
			}

			throw new AppError(
				"Recurring payment processing failed",
				ErrorCode.PAYMENT_GATEWAY_ERROR,
				response.status,
				{
					recurringDonationId: recurringDonation.id,
					httpStatus: response.status,
					httpStatusText: response.statusText,
					chipError: errorData,
				},
			)
		}

		const paymentResponse = await response.json()

		this.log("info", "Recurring payment processed successfully", {
			recurringDonationId: recurringDonation.id,
			paymentId: paymentResponse.id,
			amount: recurringDonation.amount,
			frequency: recurringDonation.frequency,
		})

		return paymentResponse
	}

	/**
	 * Handles token expiration and notifications
	 */
	async handleTokenExpiration(
		recurringDonation: RecurringDonation,
	): Promise<void> {
		this.log("info", "Handling token expiration for recurring donation", {
			recurringDonationId: recurringDonation.id,
			tokenExpiresAt: recurringDonation.chipTokenExpiresAt,
		})

		// Update recurring donation status to expired
		await db
			.update(recurringDonations)
			.set({
				status: "expired",
				updatedAt: new Date(),
			})
			.where(eq(recurringDonations.id, recurringDonation.id))

		// TODO: Send notification to donor about token expiration
		// This would typically involve sending an email with a link to update payment method

		this.log(
			"info",
			"Recurring donation marked as expired due to token expiration",
			{
				recurringDonationId: recurringDonation.id,
				donorEmail: "[REDACTED]",
				tokenExpiresAt: recurringDonation.chipTokenExpiresAt,
			},
		)
	}

	/**
	 * Processes recurring payments that are due
	 */
	async processScheduledRecurringPayments(): Promise<void> {
		const now = new Date()
		this.log("info", "Processing scheduled recurring payments", {
			currentTime: now,
		})

		// Find all active recurring donations that are due for payment
		const dueRecurringDonations = await db
			.select()
			.from(recurringDonations)
			.where(
				and(
					eq(recurringDonations.status, "active"),
					lte(recurringDonations.nextPaymentDate, now),
				),
			)

		this.log(
			"info",
			`Found ${dueRecurringDonations.length} recurring donations due for payment`,
		)

		for (const recurringDonation of dueRecurringDonations) {
			try {
				// Check if token is expired
				if (
					recurringDonation.chipTokenExpiresAt &&
					recurringDonation.chipTokenExpiresAt < now
				) {
					await this.handleTokenExpiration(recurringDonation)
					continue
				}

				// Create payment attempt record
				const [attemptRecord] = await db
					.insert(recurringPaymentAttempts)
					.values({
						recurringDonationId: recurringDonation.id,
						attemptNumber: recurringDonation.failedAttempts + 1,
						status: "pending",
					})
					.returning()

				// Process the recurring payment
				const paymentResponse =
					await this.processRecurringPayment(recurringDonation)

				this.log("info", "Recurring payment initiated successfully", {
					recurringDonationId: recurringDonation.id,
					paymentId: paymentResponse.id,
					attemptId: attemptRecord.id,
				})
			} catch (error) {
				logError(
					new AppError(
						"Failed to process scheduled recurring payment",
						ErrorCode.PAYMENT_GATEWAY_ERROR,
						StatusCodes.INTERNAL_SERVER_ERROR,
						{
							recurringDonationId: recurringDonation.id,
							originalError:
								error instanceof Error ? error.message : String(error),
						},
					),
					{ recurringDonationId: recurringDonation.id },
					"error",
				)

				// Increment failed attempts for this recurring donation
				await db
					.update(recurringDonations)
					.set({
						failedAttempts: recurringDonation.failedAttempts + 1,
						updatedAt: new Date(),
					})
					.where(eq(recurringDonations.id, recurringDonation.id))
			}
		}
	}

	// Helper methods for specific handlers
	// ===================================

	private async handleDonationFailed(data: PaymentResponse): Promise<void> {
		// Implementation would go here - delegate to DonationService
		this.log("info", "Handling donation payment failure", {
			paymentId: data.id,
		})
		// TODO: Implement donation failure handling
	}

	private async handleRecurringTokenSetupFailed(
		data: PaymentResponse,
	): Promise<void> {
		// Implementation would go here - delegate to RecurringDonationService
		this.log("info", "Handling recurring token setup failure", {
			paymentId: data.id,
		})
		// TODO: Implement recurring token setup failure handling
	}

	private async handleRecurringDonationFailed(
		data: PaymentResponse,
	): Promise<void> {
		// Implementation would go here - delegate to RecurringDonationService
		this.log("info", "Handling recurring donation failure", {
			paymentId: data.id,
		})
		// TODO: Implement recurring donation failure handling
	}

	private async handleRecurringDonationCompleted(
		data: PaymentResponse,
	): Promise<void> {
		// Implementation would go here - delegate to RecurringDonationService
		this.log("info", "Handling recurring donation completion", {
			paymentId: data.id,
		})
		// TODO: Implement recurring donation completion handling
	}
}
