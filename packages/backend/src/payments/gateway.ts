import type { Campaign } from "../campaigns/campaigns.schema"
import type { Donation } from "../donations/donations.schema"
import type { RecurringDonation } from "../recurring-donations/recurring-donations.schema"
import { ChipService } from "./chip.service"
import type { CreatePaymentParams } from "./payments.schema"

/**
 * Configuration interface for payment gateways
 */
export interface PaymentGatewayConfig {
	readonly baseUrl: string
	readonly secretKey: string
	readonly brandId?: string
	readonly webhookSecret?: string
	readonly publicKey?: string
}

/**
 * Standard payment response interface that all gateways should return
 */
export interface PaymentResponse {
	id: string
	status: string
	checkout_url?: string
	reference?: string
	created_at?: string
	is_recurring_token?: boolean
	recurring_token?: string | null
	purchase?: {
		amount?: number
		currency?: string
		products?: Array<{
			name: string
			price: number
			category?: string
			quantity?: string
		}>
	}
	[key: string]: any // Allow gateway-specific fields
}

/**
 * Webhook verification result
 */
export interface WebhookVerificationResult {
	isValid: boolean
	error?: string
}

/**
 * Redirect URL context for generating payment flow URLs
 */
export interface RedirectUrlContext {
	campaignId: string
	campaignSlug: string
	donationId: string
	paymentId?: string
	isRecurring?: boolean
}

/**
 * Generated redirect URLs for payment flows
 */
export interface PaymentRedirectUrls {
	successRedirectUrl: string
	failureRedirectUrl: string
	cancelRedirectUrl: string
}

/**
 * Abstract base class for payment gateway implementations
 *
 * This class defines the core interface and common behavior that all
 * payment gateways must implement, following the Template Method pattern
 * for complex operations while allowing gateway-specific customizations.
 */
export abstract class PaymentGateway {
	protected readonly config: PaymentGatewayConfig

	constructor(config: PaymentGatewayConfig) {
		this.config = config
		this.validateConfiguration()
	}

	/**
	 * Gateway name for identification and logging
	 */
	abstract readonly gatewayName: string

	// Core Payment Operations
	// =====================

	/**
	 * Creates a basic payment with the gateway
	 */
	abstract createPayment(params: CreatePaymentParams): Promise<PaymentResponse>

	/**
	 * Retrieves payment details by ID
	 */
	abstract getPayment(paymentId: string): Promise<PaymentResponse>

	/**
	 * Verifies webhook signature from the gateway
	 */
	abstract verifyWebhookSignature(
		payload: string,
		signature: string,
	): Promise<WebhookVerificationResult>

	// Donation-Specific Operations
	// ===========================

	/**
	 * Creates a donation payment with campaign context
	 * Template method that provides common donation payment flow
	 */
	async createDonationPayment(
		donation: Donation,
		campaign: Campaign,
		params: CreatePaymentParams,
	): Promise<PaymentResponse> {
		// Validate campaign is active
		this.validateCampaignForDonation(campaign)

		// Generate donation-specific redirect URLs
		const redirectUrls = this.generateDonationRedirectUrls(donation, campaign)

		// Prepare donation-specific payment parameters
		const donationParams = this.prepareDonationPaymentParams(
			donation,
			campaign,
			params,
			redirectUrls,
		)

		// Create the payment
		const paymentResponse = await this.createPayment(donationParams)

		// Update donation record with payment ID
		await this.updateDonationWithPaymentId(donation, paymentResponse)

		return paymentResponse
	}

	// Recurring Payment Operations
	// ===========================

	/**
	 * Creates a recurring donation payment with token setup
	 */
	abstract createRecurringDonationPayment(
		recurringDonation: RecurringDonation,
		campaign: Campaign,
		params: CreatePaymentParams,
	): Promise<PaymentResponse>

	/**
	 * Processes a recurring payment using stored token
	 */
	abstract processRecurringPayment(
		recurringDonation: RecurringDonation,
	): Promise<PaymentResponse>

	/**
	 * Processes all scheduled recurring payments
	 */
	abstract processScheduledRecurringPayments(): Promise<void>

	/**
	 * Handles token expiration for recurring payments
	 */
	abstract handleTokenExpiration(
		recurringDonation: RecurringDonation,
	): Promise<void>

	// Webhook Event Handlers
	// =====================

	/**
	 * Handles successful payment webhook
	 */
	abstract handlePaymentCompleted(data: PaymentResponse): Promise<void>

	/**
	 * Handles failed payment webhook
	 */
	abstract handlePaymentFailed(data: PaymentResponse): Promise<void>

	/**
	 * Handles canceled payment webhook
	 */
	abstract handlePaymentCanceled(data: PaymentResponse): Promise<void>

	// Configuration and Validation
	// ===========================

	/**
	 * Validates gateway configuration
	 * Each gateway can override to add specific validation
	 */
	protected validateConfiguration(): void {
		if (!this.config.baseUrl) {
			throw new Error(`${this.gatewayName}: Base URL is required`)
		}
		if (!this.config.secretKey) {
			throw new Error(`${this.gatewayName}: Secret key is required`)
		}
	}

	/**
	 * Validates payment parameters
	 * Can be overridden by gateways for specific validation rules
	 */
	protected validatePaymentParams(params: CreatePaymentParams): void {
		if (!params.amount || params.amount <= 0) {
			throw new Error("Payment amount must be greater than 0")
		}
		if (!params.email || !params.email.includes("@")) {
			throw new Error("Valid email address is required")
		}
		if (!params.fullName || params.fullName.trim().length === 0) {
			throw new Error("Full name is required")
		}
		if (!params.products || params.products.length === 0) {
			throw new Error("At least one product is required")
		}
	}

	/**
	 * Validates campaign can accept donations
	 */
	protected validateCampaignForDonation(campaign: Campaign): void {
		if (!campaign.isActive) {
			throw new Error(
				`Campaign "${campaign.name}" is not currently accepting donations`,
			)
		}
	}

	// URL Generation (Template Methods)
	// ================================

	/**
	 * Generates redirect URLs for donation payments
	 * Can be overridden by gateways for custom URL generation
	 */
	protected abstract generateDonationRedirectUrls(
		donation: Donation,
		campaign: Campaign,
		paymentId?: string,
	): PaymentRedirectUrls

	/**
	 * Generates redirect URLs for recurring donation payments
	 */
	protected abstract generateRecurringRedirectUrls(
		recurringDonation: RecurringDonation,
		campaign: Campaign,
		paymentId?: string,
	): PaymentRedirectUrls

	// Helper Methods (Template Methods)
	// ================================

	/**
	 * Prepares payment parameters specific to donations
	 * Template method that can be customized per gateway
	 */
	protected prepareDonationPaymentParams(
		donation: Donation,
		campaign: Campaign,
		params: CreatePaymentParams,
		redirectUrls: PaymentRedirectUrls,
	): CreatePaymentParams {
		return {
			...params,
			products: [
				{
					name: `Donation to ${campaign.name}`,
					price: params.amount,
					category: "donation",
					quantity: "1",
				},
			],
			reference: donation.id,
			notes: params.notes || `Donation to campaign: ${campaign.name}`,
			...redirectUrls,
		}
	}

	/**
	 * Updates donation record with payment ID
	 * Abstract method as implementation depends on database layer
	 */
	protected abstract updateDonationWithPaymentId(
		donation: Donation,
		paymentResponse: PaymentResponse,
	): Promise<void>

	// Utility Methods
	// ==============

	/**
	 * Converts amount to gateway-specific format (e.g., cents)
	 */
	protected convertAmountToGatewayFormat(amount: number): number {
		// Default implementation converts to cents
		// Gateways can override if they use different formats
		return Math.round(amount * 100)
	}

	/**
	 * Converts amount from gateway format to standard decimal
	 */
	protected convertAmountFromGatewayFormat(amount: number): number {
		// Default implementation converts from cents
		return amount / 100
	}

	/**
	 * Logs gateway-specific operations
	 */
	protected log(
		level: "info" | "warn" | "error",
		message: string,
		context?: any,
	): void {
		const logMessage = `[${this.gatewayName}] ${message}`
		switch (level) {
			case "info":
				console.info(logMessage, context)
				break
			case "warn":
				console.warn(logMessage, context)
				break
			case "error":
				console.error(logMessage, context)
				break
		}
	}
}

/**
 * Factory for creating payment gateway instances
 * Follows the Factory pattern for easy extensibility
 */
export class PaymentGatewayFactory {
	private static chipInstance: ChipService | null = null

	/**
	 * Gets a payment gateway instance by name
	 */
	static getGateway(gatewayName: "chip"): PaymentGateway {
		switch (gatewayName) {
			case "chip":
				if (!PaymentGatewayFactory.chipInstance) {
					PaymentGatewayFactory.chipInstance = ChipService.getInstance()
				}
				return PaymentGatewayFactory.chipInstance
			default:
				throw new Error(`Unknown payment gateway: ${gatewayName}`)
		}
	}

	/**
	 * Gets the default payment gateway (Chip)
	 */
	static getDefaultGateway(): PaymentGateway {
		return PaymentGatewayFactory.getGateway("chip")
	}
}
