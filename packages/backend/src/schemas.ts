// Re-export all schemas for external consumption

import { t } from "elysia"

export const errorResponseDto = t.Object({
	error: t.String(),
	details: t.Optional(t.Array(t.String())),
})

export * from "./campaigns/campaigns.schema"
export * from "./donations/donations.schema"
export * from "./donors/donors.schema"
export * from "./donors/donors-tags.schema"
export * from "./organizers/organizers.schema"
export * from "./recurring-donations/recurring-donations.schema"
export * from "./users/users.schema"
