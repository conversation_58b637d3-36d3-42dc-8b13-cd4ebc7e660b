#!/usr/bin/env bun

/**
 * Demo script showing end-to-end type safety with Eden
 * Run with: bun run demo-client.ts
 */

import { treaty } from "@elysiajs/eden"
import type { App } from "./src/index"

// Create type-safe client
const api = treaty<App>("http://localhost:3000")

async function demoTypesSafety() {
	console.log("🔒 DonorCare End-to-End Type Safety Demo")
	console.log("=".repeat(50))

	// 1. Health Check
	console.log("\n1. Health Check (Type-safe)")
	try {
		const { data, error } = await api.health.get()
		if (error) {
			console.log("❌ Health check failed:", error.value)
		} else {
			console.log("✅ Health check passed:", data)
		}
	} catch (err) {
		console.log("🔌 Server not running. Start with: bun dev")
	}

	// 2. Demonstrate type safety at compile time
	console.log("\n2. Compile-time Type Safety Examples")

	// This would cause a TypeScript error (uncomment to see):
	// const badRequest = await api.api.campaigns.post({
	//   name: 123, // ❌ Type error: should be string
	//   slug: "test"
	// })

	// This is type-safe:
	const goodRequest = {
		name: "My Campaign", // ✅ Correct type
		description: "A great cause",
		slug: "my-campaign-2024",
	}
	console.log("✅ Valid request structure:", goodRequest)

	// 3. Query parameter type safety
	console.log("\n3. Query Parameter Type Safety")
	const validQuery = {
		status: "completed" as const, // ✅ Must be one of the allowed values
		sortBy: "createdAt" as const, // ✅ Must be one of the allowed values
		sortOrder: "desc" as const, // ✅ Must be "asc" or "desc"
		page: 1, // ✅ Must be number
		limit: 10, // ✅ Must be number
	}
	console.log("✅ Valid query parameters:", validQuery)

	// This would cause TypeScript errors:
	// const badQuery = {
	//   status: "invalid", // ❌ Not in allowed union
	//   sortBy: "badField", // ❌ Not in allowed union
	//   page: "1" // ❌ Should be number, not string
	// }

	// 4. Response type safety
	console.log("\n4. Response Type Safety")
	console.log("✅ All API responses are fully typed")
	console.log("✅ Error responses include status codes and typed error objects")
	console.log("✅ Success responses match the defined schemas")

	// 5. Available endpoints
	console.log("\n5. Available Type-Safe Endpoints")
	console.log("📋 Campaigns:")
	console.log("  - POST /api/campaigns (create)")
	console.log("  - GET /api/campaigns/my (list organizer's campaigns)")
	console.log("  - GET /api/campaigns/:slug (public campaign)")
	console.log("  - PUT /api/campaigns/:id (update)")
	console.log("  - DELETE /api/campaigns/:id (delete)")
	console.log("  - PATCH /api/campaigns/:id/status (toggle status)")

	console.log("\n💰 Donations:")
	console.log("  - POST /api/donations/initiate (start donation)")
	console.log("  - GET /api/donations (list with filters)")
	console.log("  - GET /api/donations/:id (get specific)")
	console.log("  - PUT /api/donations/:id (update metadata)")
	console.log("  - PATCH /api/donations/:id/status (update status)")
	console.log("  - DELETE /api/donations/:id (delete)")
	console.log("  - GET /api/donations/analytics (get analytics)")
	console.log("  - GET /api/donations/export (export CSV)")

	console.log("\n👥 Users:")
	console.log("  - GET /users (list users)")
	console.log("  - GET /users/:id (get user)")
	console.log("  - POST /users (create user)")

	console.log("\n🔧 System:")
	console.log("  - GET /health (health check)")
	console.log("  - ALL /api/auth/* (authentication)")

	console.log("\n" + "=".repeat(50))
	console.log("🎉 All endpoints provide full type safety!")
	console.log("📚 See END_TO_END_TYPE_SAFETY.md for detailed documentation")
}

// Run the demo
demoTypesSafety().catch(console.error)
