// Debug script to check session data
const debugSession = async () => {
	try {
		// Sign out first
		await fetch("http://localhost:3000/api/auth/sign-out", {
			method: "POST",
		})

		// Sign in
		const signInResponse = await fetch(
			"http://localhost:3000/api/auth/sign-in/email",
			{
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					email: "<EMAIL>",
					password: "abcd1234",
				}),
			},
		)

		const cookies = signInResponse.headers.get("set-cookie")
		console.log("Cookies:", cookies)

		// Check session
		const sessionResponse = await fetch(
			"http://localhost:3000/api/auth/get-session",
			{
				method: "GET",
				headers: {
					Cookie: cookies || "",
				},
			},
		)

		const sessionData = await sessionResponse.json()
		console.log("Session data:", JSON.stringify(sessionData, null, 2))
	} catch (error) {
		console.error("Error:", error)
	}
}

debugSession()
