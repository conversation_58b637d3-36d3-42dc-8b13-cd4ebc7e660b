import { createAccessControl } from "better-auth/plugins/access"

// Define permission statements
export const statements = {
    donation: ["create", "view", "update", "delete"],
    profile: ["view", "update"],
    user: ["ban", "unban", "impersonate", "view", "create"],
    campaign: ["create", "view", "update", "delete"],
} as const

// Create access control instance
export const ac = createAccessControl(statements)

// Define roles with proper access control
export const adminRole = ac.newRole({
    donation: ["create", "view", "update", "delete"],
    profile: ["view", "update"],
    user: ["ban", "unban", "impersonate", "view", "create"],
    campaign: ["create", "view", "update", "delete"],
})

export const userRole = ac.newRole({
    donation: ["create", "view"],
    profile: ["view", "update"],
})

export const organizerRole = ac.newRole({
    donation: ["view"],
    profile: ["view", "update"],
    campaign: ["create", "view", "update", "delete"],
})