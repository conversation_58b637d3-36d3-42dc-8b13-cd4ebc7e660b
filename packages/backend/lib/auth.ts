import { betterAuth } from "better-auth"
import { drizzle<PERSON>dapter } from "better-auth/adapters/drizzle"
import { admin } from "better-auth/plugins"
import { db } from "@/db"
import { accounts, sessions, users, verifications } from "@/users/users.schema"
import { ac, adminRole, organizerRole, userRole } from "./permissions"

export const auth = betterAuth({
	baseURL: "http://localhost:3000",
	database: drizzleAdapter(db, {
		provider: "pg",
		usePlural: true,
		schema: {
			users: users,
			sessions: sessions,
			accounts: accounts,
			verifications: verifications,
		},
	}),
	emailAndPassword: {
		enabled: true,
	},
	session: {
		cookieCache: {
			enabled: true,
			maxAge: 60 * 60 * 24 * 7, // 7 days
		},
	},
	user: {
		additionalFields: {
			role: {
				type: "string",
				required: false,
			},
		},
	},
	trustedOrigins: [
		"http://localhost:3000",
		"http://localhost:3001",
		"http://localhost:3002",
	],
	plugins: [
		admin({
			// Define admin roles
			adminRoles: ["admin"],

			// Set default role for new users
			defaultRole: "user",

			// Configure impersonation settings
			impersonationSessionDuration: 60 * 60 * 24, // 24 hours

			// Custom message for banned users
			bannedUserMessage:
				"You have been banned from this application. Please contact support if you believe this is an error.",

			// Pass the access control instance
			ac,

			// Define roles with proper access control
			roles: {
				admin: adminRole,
				user: userRole,
				organizer: organizerRole,
			},
		}),
	],
})

export * from "./permissions"