/**
 * @lastModified 2025-02-04
 * @see https://elysiajs.com/recipe/drizzle.html#utility
 */

import { Kind, type TObject } from "@sinclair/typebox"
import type { Table } from "drizzle-orm"
import {
	type BuildSchema,
	createInsertSchema,
	createSelectSchema,
} from "drizzle-typebox"

type Spread<
	T extends TObject | Table,
	Mode extends "select" | "insert" | undefined,
> = T extends TObject<infer Fields>
	? {
			[K in keyof Fields]: Fields[K]
		}
	: T extends Table
		? Mode extends "select"
			? BuildSchema<"select", T["_"]["columns"], undefined>["properties"]
			: Mode extends "insert"
				? BuildSchema<"insert", T["_"]["columns"], undefined>["properties"]
				: {}
		: {}

/**
 * Spread a Drizzle schema into a plain object
 */
export const spread = <
	T extends TObject | Table,
	Mode extends "select" | "insert" | undefined,
>(
	schema: T,
	mode?: Mode,
): Spread<T, Mode> => {
	const newSchema: Record<string, unknown> = {}
	let table: any

	switch (mode) {
		case "insert":
		case "select":
			if (Kind in schema) {
				table = schema
				break
			}

			table =
				mode === "insert"
					? createInsertSchema(schema)
					: createSelectSchema(schema)

			break

		default:
			if (!(Kind in schema)) throw new Error("Expect a schema")
			table = schema
	}

	for (const key of Object.keys(table.properties))
		newSchema[key] = table.properties[key]

	return newSchema as any
}

/**
 * Spread a Drizzle Table into a plain object
 *
 * If `mode` is 'insert', the schema will be refined for insert
 * If `mode` is 'select', the schema will be refined for select
 * If `mode` is undefined, the schema will be spread as is, models will need to be refined manually
 */
export const spreads = <
	T extends Record<string, TObject | Table>,
	Mode extends "select" | "insert" | undefined,
>(
	models: T,
	mode?: Mode,
): {
	[K in keyof T]: Spread<T[K], Mode>
} => {
	const newSchema: Record<string, unknown> = {}
	const keys = Object.keys(models)

	for (const key of keys) newSchema[key] = spread(models[key], mode)

	return newSchema as any
}

// import { eq } from 'drizzle-orm'
// import { db } from '../database/model'
// import { table } from './schema'

// // Utility function to get all bookings with their related data
// export async function getBookingsWithDetails() {
// 	try {
// 		const bookings = await db.select.bookings.findMany({
// 			with: {
// 				containers: true,
// 				invoices: true,
// 				documents: true,
// 				parties: {
// 					with: {
// 						party: true,
// 					},
// 				},
// 			},
// 		})

// 		return bookings
// 	} catch (error) {
// 		console.error('Error fetching bookings with details:', error)
// 		throw error
// 	}
// }

// // Utility function to get a single booking with all related data
// export async function getBookingWithDetails(id: string) {
// 	try {
// 		const booking = await db.select.bookings.findFirst({
// 			where: eq(table.bookings.id, id),
// 			with: {
// 				containers: true,
// 				invoices: true,
// 				documents: true,
// 				parties: {
// 					with: {
// 						party: true,
// 					},
// 				},
// 			},
// 		})

// 		return booking
// 	} catch (error) {
// 		console.error('Error fetching booking with details:', error)
// 		throw error
// 	}
// }
