# DonorCare Backend API

A type-safe donation platform API built with Elysia, TypeScript, and PostgreSQL.

## Features

- 🔒 **End-to-End Type Safety** with Elysia and Eden
- 🗃️ **PostgreSQL** database with Drizzle ORM
- 🔐 **Authentication** with Better-Auth
- 📊 **Real-time Analytics** for donations and campaigns
- 💳 **Payment Integration** with CHIP gateway
- 🧪 **Comprehensive Testing** with Vitest
- 📝 **API Documentation** with Swagger

## Development

```bash
bun dev              # Start development server
bun test             # Run tests
bun db:migrate       # Run database migrations
bun db:seed          # Seed database with test data
```

## End-to-End Type Safety

This backend implements full end-to-end type safety using Elysia and Eden:

- **Runtime Validation**: TypeBox schemas validate all requests/responses
- **Compile-time Safety**: TypeScript ensures type consistency
- **Client Integration**: Eden provides type-safe API consumption
- **Auto-completion**: Full IDE support with type inference

### Quick Start with Type Safety

```typescript
import { treaty } from "@elysiajs/eden"
import type { App } from "@donorcare/backend"

// Create type-safe client
const api = treaty<App>("http://localhost:3000")

// All requests are fully typed
const { data, error } = await api.api.campaigns.post({
  name: "My Campaign",
  slug: "my-campaign",
})
```

### Demo

```bash
bun run demo-client.ts  # See type safety in action
```

📚 **[Complete Type Safety Documentation](./END_TO_END_TYPE_SAFETY.md)**

## API Endpoints

### Campaigns

- `POST /api/campaigns` - Create campaign
- `GET /api/campaigns/my` - Get organizer's campaigns
- `GET /api/campaigns/:slug` - Get public campaign
- `PUT /api/campaigns/:id` - Update campaign
- `DELETE /api/campaigns/:id` - Delete campaign
- `PATCH /api/campaigns/:id/status` - Toggle campaign status

### Donations

- `POST /api/donations/initiate` - Initiate donation
- `GET /api/donations` - List donations (with filters)
- `GET /api/donations/:id` - Get donation details
- `PUT /api/donations/:id` - Update donation metadata
- `PATCH /api/donations/:id/status` - Update donation status
- `DELETE /api/donations/:id` - Delete donation
- `GET /api/donations/analytics` - Get donation analytics
- `GET /api/donations/export` - Export donations as CSV

### Users

- `GET /users` - List users
- `GET /users/:id` - Get user by ID
- `POST /users` - Create user

### System

- `GET /health` - Health check
- `ALL /api/auth/*` - Authentication endpoints

## Architecture

```
src/
├── campaigns/           # Campaign management
│   ├── campaigns.controller.ts
│   └── campaigns.schema.ts
├── donations/           # Donation processing
│   ├── donations.controller.ts
│   └── donations.schema.ts
├── users/              # User management
│   ├── users.controller.ts
│   ├── users.service.ts
│   └── users.schema.ts
├── routes/             # Route definitions
├── __tests__/          # Test files
├── client.example.ts   # Type-safe client example
├── model.ts           # Unified type exports
└── index.ts           # Main server file
```

## Gotchas

- use relative imports `../..` instead of aliases `@/` if they will be consumed by other packages in the monorepo
- add errorMessages to schema dto for frontend validation with `createTypeboxResolver`
