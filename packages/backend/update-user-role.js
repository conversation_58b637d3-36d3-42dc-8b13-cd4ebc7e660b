import { db } from "./src/db.js"
import { users } from "./src/users/users.schema.js"
import { eq } from "drizzle-orm"

async function updateUserRole() {
	try {
		// Update the admin user to be an organizer
		const result = await db
			.update(users)
			.set({ role: "organizer" })
			.where(eq(users.email, "<EMAIL>"))
			.returning()

		if (result.length > 0) {
			console.log("Updated user role successfully:", result[0])
		} else {
			console.log("User not found")
		}

		process.exit(0)
	} catch (error) {
		console.error("Error updating user role:", error)
		process.exit(1)
	}
}

updateUserRole()
