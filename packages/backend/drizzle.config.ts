import "dotenv/config"
import { defineConfig } from "drizzle-kit"

export default defineConfig({
	out: "./drizzle/migrations",
	// in our codebase, 'schema' refers to TypeBox objects (abstraction of a JSON schema), while 'model' refers to database entities/tables, which is what drizzle refers to as 'schema'
	schema: "**/*.schema.ts",
	dialect: "postgresql",
	dbCredentials: {
		url: process.env.DATABASE_URL || "postgresql://localhost:5432/donorcare",
	},
	// Redefine default migrations table and schema for the sake of clarity
	migrations: {
		table: "__drizzle_migrations",
		schema: "drizzle",
	},
	entities: {
		roles: true,
	},
})
