// Simple script to create a test campaign
const createTestCampaign = async () => {
	try {
		// First, sign out to clear any existing session
		await fetch("http://localhost:3000/api/auth/sign-out", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
		})

		// Then sign in as the admin user
		const signInResponse = await fetch(
			"http://localhost:3000/api/auth/sign-in/email",
			{
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					email: "<EMAIL>",
					password: "abcd1234",
				}),
			},
		)

		if (!signInResponse.ok) {
			throw new Error(`Sign in failed: ${signInResponse.status}`)
		}

		const signInData = await signInResponse.json()
		console.log("Signed in successfully:", signInData.user.email)

		// Extract session cookies from the response
		const cookies = signInResponse.headers.get("set-cookie")

		// Create a campaign
		const campaignResponse = await fetch(
			"http://localhost:3000/api/campaigns",
			{
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Cookie: cookies || "",
				},
				body: JSON.stringify({
					name: "Help Local Community",
					description:
						"Support our local community with essential supplies and resources. Every donation makes a difference in helping families in need.",
					slug: "help-local-community",
				}),
			},
		)

		if (!campaignResponse.ok) {
			const errorText = await campaignResponse.text()
			throw new Error(
				`Campaign creation failed: ${campaignResponse.status} - ${errorText}`,
			)
		}

		const campaignData = await campaignResponse.json()
		console.log("Campaign created successfully:", campaignData)
	} catch (error) {
		console.error("Error creating test campaign:", error)
	}
}

createTestCampaign()
