{"id": "843f3748-d7c7-467e-849d-1b346cce04c9", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.campaigns": {"name": "campaigns", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organizer_id": {"name": "organizer_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"campaigns_organizer_id_users_id_fk": {"name": "campaigns_organizer_id_users_id_fk", "tableFrom": "campaigns", "tableTo": "users", "columnsFrom": ["organizer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"campaigns_slug_unique": {"name": "campaigns_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {"campaigns_organizer_access": {"name": "campaigns_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "\"campaigns\".\"organizer_id\" = current_setting('app.current_organizer_id', true) OR current_setting('app.current_user_role', true) = 'admin'", "withCheck": "\"campaigns\".\"organizer_id\" = current_setting('app.current_organizer_id', true) OR current_setting('app.current_user_role', true) = 'admin'"}, "campaigns_public_read": {"name": "campaigns_public_read", "as": "PERMISSIVE", "for": "SELECT", "to": ["anonymous", "authenticated"], "using": "\"campaigns\".\"is_active\" = true AND current_setting('app.current_organizer_id', true) IS NULL"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donations": {"name": "donations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "campaign_id": {"name": "campaign_id", "type": "uuid", "primaryKey": false, "notNull": true}, "donor_id": {"name": "donor_id", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'MYR'"}, "chip_payment_id": {"name": "chip_payment_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "donation_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "donor_name": {"name": "donor_name", "type": "text", "primaryKey": false, "notNull": true}, "donor_email": {"name": "donor_email", "type": "text", "primaryKey": false, "notNull": true}, "donor_phone": {"name": "donor_phone", "type": "text", "primaryKey": false, "notNull": false}, "donor_message": {"name": "donor_message", "type": "text", "primaryKey": false, "notNull": false}, "internal_notes": {"name": "internal_notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"donations_campaign_id_campaigns_id_fk": {"name": "donations_campaign_id_campaigns_id_fk", "tableFrom": "donations", "tableTo": "campaigns", "columnsFrom": ["campaign_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "donations_donor_id_users_id_fk": {"name": "donations_donor_id_users_id_fk", "tableFrom": "donations", "tableTo": "users", "columnsFrom": ["donor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"donations_organizer_access": {"name": "donations_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "EXISTS (\n\t\t\tSELECT 1 FROM campaigns c\n\t\t\tWHERE c.id = \"donations\".\"campaign_id\"\n\t\t\tAND (c.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t)", "withCheck": "EXISTS (\n\t\t\tSELECT 1 FROM campaigns c\n\t\t\tWHERE c.id = \"donations\".\"campaign_id\"\n\t\t\tAND (c.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t)"}, "donations_donor_access": {"name": "donations_donor_access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\"donations\".\"donor_id\" = current_setting('app.current_user_id', true)\n\t\t\tAND current_setting('app.current_user_role', true) = 'user'"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donor_notes": {"name": "donor_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "donor_id": {"name": "donor_id", "type": "text", "primaryKey": false, "notNull": true}, "organizer_id": {"name": "organizer_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"donor_notes_donor_id_users_id_fk": {"name": "donor_notes_donor_id_users_id_fk", "tableFrom": "donor_notes", "tableTo": "users", "columnsFrom": ["donor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "donor_notes_organizer_id_users_id_fk": {"name": "donor_notes_organizer_id_users_id_fk", "tableFrom": "donor_notes", "tableTo": "users", "columnsFrom": ["organizer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"donor_notes_organizer_access": {"name": "donor_notes_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "\"donor_notes\".\"organizer_id\" = current_setting('app.current_organizer_id', true)\n\t\t\tOR current_setting('app.current_user_role', true) = 'admin'", "withCheck": "\"donor_notes\".\"organizer_id\" = current_setting('app.current_organizer_id', true)\n\t\t\tOR current_setting('app.current_user_role', true) = 'admin'"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donor_tag_assignments": {"name": "donor_tag_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "donor_id": {"name": "donor_id", "type": "text", "primaryKey": false, "notNull": true}, "tag_id": {"name": "tag_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"unique_donor_tag": {"name": "unique_donor_tag", "columns": [{"expression": "donor_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tag_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"donor_tag_assignments_donor_id_users_id_fk": {"name": "donor_tag_assignments_donor_id_users_id_fk", "tableFrom": "donor_tag_assignments", "tableTo": "users", "columnsFrom": ["donor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "donor_tag_assignments_tag_id_donor_tags_id_fk": {"name": "donor_tag_assignments_tag_id_donor_tags_id_fk", "tableFrom": "donor_tag_assignments", "tableTo": "donor_tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"donor_tag_assignments_organizer_access": {"name": "donor_tag_assignments_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "EXISTS (\n\t\t\t\tSELECT 1 FROM donor_tags dt\n\t\t\t\tWHERE dt.id = \"donor_tag_assignments\".\"tag_id\"\n\t\t\t\tAND (dt.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t\t)", "withCheck": "EXISTS (\n\t\t\t\tSELECT 1 FROM donor_tags dt\n\t\t\t\tWHERE dt.id = \"donor_tag_assignments\".\"tag_id\"\n\t\t\t\tAND (dt.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t\t)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donor_tags": {"name": "donor_tags", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organizer_id": {"name": "organizer_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3b82f6'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"unique_organizer_tag": {"name": "unique_organizer_tag", "columns": [{"expression": "organizer_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"donor_tags_organizer_id_users_id_fk": {"name": "donor_tags_organizer_id_users_id_fk", "tableFrom": "donor_tags", "tableTo": "users", "columnsFrom": ["organizer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"donor_tags_organizer_access": {"name": "donor_tags_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "\"donor_tags\".\"organizer_id\" = current_setting('app.current_organizer_id', true)\n\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin'", "withCheck": "\"donor_tags\".\"organizer_id\" = current_setting('app.current_organizer_id', true)\n\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin'"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recurring_donations": {"name": "recurring_donations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "campaign_id": {"name": "campaign_id", "type": "uuid", "primaryKey": false, "notNull": true}, "donor_id": {"name": "donor_id", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'MYR'"}, "frequency": {"name": "frequency", "type": "recurring_frequency", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'monthly'"}, "status": {"name": "status", "type": "recurring_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "chip_recurring_token": {"name": "chip_recurring_token", "type": "text", "primaryKey": false, "notNull": true}, "chip_token_expires_at": {"name": "chip_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "donor_name": {"name": "donor_name", "type": "text", "primaryKey": false, "notNull": true}, "donor_email": {"name": "donor_email", "type": "text", "primaryKey": false, "notNull": true}, "donor_phone": {"name": "donor_phone", "type": "text", "primaryKey": false, "notNull": false}, "next_payment_date": {"name": "next_payment_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "last_payment_date": {"name": "last_payment_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "failed_attempts": {"name": "failed_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "max_retries": {"name": "max_retries", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"recurring_donations_campaign_id_campaigns_id_fk": {"name": "recurring_donations_campaign_id_campaigns_id_fk", "tableFrom": "recurring_donations", "tableTo": "campaigns", "columnsFrom": ["campaign_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "recurring_donations_donor_id_users_id_fk": {"name": "recurring_donations_donor_id_users_id_fk", "tableFrom": "recurring_donations", "tableTo": "users", "columnsFrom": ["donor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"recurring_donations_organizer_access": {"name": "recurring_donations_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "EXISTS (\n\t\t\t\tSELECT 1 FROM campaigns c\n\t\t\t\tWHERE c.id = \"recurring_donations\".\"campaign_id\"\n\t\t\t\tAND (c.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t\t)", "withCheck": "EXISTS (\n\t\t\t\tSELECT 1 FROM campaigns c\n\t\t\t\tWHERE c.id = \"recurring_donations\".\"campaign_id\"\n\t\t\t\tAND (c.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t\t)"}, "recurring_donations_donor_access": {"name": "recurring_donations_donor_access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "\"recurring_donations\".\"donor_id\" = current_setting('app.current_user_id', true)\n\t\t\t\tAND current_setting('app.current_user_role', true) = 'user'"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recurring_payment_attempts": {"name": "recurring_payment_attempts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "recurring_donation_id": {"name": "recurring_donation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "attempt_number": {"name": "attempt_number", "type": "integer", "primaryKey": false, "notNull": true}, "chip_payment_id": {"name": "chip_payment_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "attempted_at": {"name": "attempted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"recurring_payment_attempts_recurring_donation_id_recurring_donations_id_fk": {"name": "recurring_payment_attempts_recurring_donation_id_recurring_donations_id_fk", "tableFrom": "recurring_payment_attempts", "tableTo": "recurring_donations", "columnsFrom": ["recurring_donation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"recurring_payment_attempts_organizer_access": {"name": "recurring_payment_attempts_organizer_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin", "organizer"], "using": "EXISTS (\n\t\t\t\tSELECT 1 FROM recurring_donations rd\n\t\t\t\tJOIN campaigns c ON c.id = rd.campaign_id\n\t\t\t\tWHERE rd.id = \"recurring_payment_attempts\".\"recurring_donation_id\"\n\t\t\t\tAND (c.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t\t)", "withCheck": "EXISTS (\n\t\t\t\tSELECT 1 FROM recurring_donations rd\n\t\t\t\tJOIN campaigns c ON c.id = rd.campaign_id\n\t\t\t\tWHERE rd.id = \"recurring_payment_attempts\".\"recurring_donation_id\"\n\t\t\t\tAND (c.organizer_id = current_setting('app.current_organizer_id', true)\n\t\t\t\t\tOR current_setting('app.current_user_role', true) = 'admin')\n\t\t\t)"}, "recurring_payment_attempts_donor_access": {"name": "recurring_payment_attempts_donor_access", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "EXISTS (\n\t\t\t\tSELECT 1 FROM recurring_donations rd\n\t\t\t\tWHERE rd.id = \"recurring_payment_attempts\".\"recurring_donation_id\"\n\t\t\t\tAND rd.donor_id = current_setting('app.current_user_id', true)\n\t\t\t\tAND current_setting('app.current_user_role', true) = 'user'\n\t\t\t)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"accounts_admin_access": {"name": "accounts_admin_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin"], "using": "current_setting('app.current_user_role', true) = 'admin'", "withCheck": "current_setting('app.current_user_role', true) = 'admin'"}, "accounts_self_access": {"name": "accounts_self_access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\"accounts\".\"user_id\" = current_setting('app.current_user_id', true)", "withCheck": "\"accounts\".\"user_id\" = current_setting('app.current_user_id', true)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "impersonated_by": {"name": "impersonated_by", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "sessions_impersonated_by_users_id_fk": {"name": "sessions_impersonated_by_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["impersonated_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {"sessions_admin_access": {"name": "sessions_admin_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin"], "using": "current_setting('app.current_user_role', true) = 'admin'", "withCheck": "current_setting('app.current_user_role', true) = 'admin'"}, "sessions_self_access": {"name": "sessions_self_access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\"sessions\".\"user_id\" = current_setting('app.current_user_id', true)", "withCheck": "\"sessions\".\"user_id\" = current_setting('app.current_user_id', true)"}, "sessions_impersonation_access": {"name": "sessions_impersonation_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin"], "using": "\"sessions\".\"impersonated_by\" = current_setting('app.current_user_id', true)\n\t\t\t\tAND current_setting('app.current_user_role', true) = 'admin'", "withCheck": "\"sessions\".\"impersonated_by\" = current_setting('app.current_user_id', true)\n\t\t\t\tAND current_setting('app.current_user_role', true) = 'admin'"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "banned": {"name": "banned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "ban_reason": {"name": "ban_reason", "type": "text", "primaryKey": false, "notNull": false}, "ban_expires": {"name": "ban_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {"users_admin_access": {"name": "users_admin_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin"], "using": "current_setting('app.current_user_role', true) = 'admin'", "withCheck": "current_setting('app.current_user_role', true) = 'admin'"}, "users_organizer_read": {"name": "users_organizer_read", "as": "PERMISSIVE", "for": "SELECT", "to": ["organizer"], "using": "current_setting('app.current_user_role', true) = 'organizer'"}, "users_self_access": {"name": "users_self_access", "as": "PERMISSIVE", "for": "ALL", "to": ["authenticated"], "using": "\"users\".\"id\" = current_setting('app.current_user_id', true)", "withCheck": "\"users\".\"id\" = current_setting('app.current_user_id', true)"}}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"verifications_admin_access": {"name": "verifications_admin_access", "as": "PERMISSIVE", "for": "ALL", "to": ["admin"], "using": "current_setting('app.current_user_role', true) = 'admin'", "withCheck": "current_setting('app.current_user_role', true) = 'admin'"}, "verifications_system_access": {"name": "verifications_system_access", "as": "PERMISSIVE", "for": "ALL", "to": ["anonymous", "authenticated"], "using": "current_setting('app.current_user_id', true) IS NULL", "withCheck": "current_setting('app.current_user_id', true) IS NULL"}}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.donation_status": {"name": "donation_status", "schema": "public", "values": ["pending", "completed", "failed", "canceled"]}, "public.recurring_frequency": {"name": "recurring_frequency", "schema": "public", "values": ["monthly", "quarterly", "yearly"]}, "public.recurring_status": {"name": "recurring_status", "schema": "public", "values": ["active", "paused", "canceled", "expired"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["admin", "organizer", "user"]}}, "schemas": {}, "sequences": {}, "roles": {"admin": {"name": "admin", "createDb": false, "createRole": false, "inherit": true}, "anonymous": {"name": "anonymous", "createDb": false, "createRole": false, "inherit": true}, "authenticated": {"name": "authenticated", "createDb": false, "createRole": false, "inherit": true}, "organizer": {"name": "organizer", "createDb": false, "createRole": false, "inherit": true}}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}