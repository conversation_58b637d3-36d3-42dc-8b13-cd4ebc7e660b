CREATE TYPE "public"."donation_status" AS ENUM('pending', 'completed', 'failed', 'canceled');--> statement-breakpoint
CREATE TYPE "public"."recurring_frequency" AS ENUM('monthly', 'quarterly', 'yearly');--> statement-breakpoint
CREATE TYPE "public"."recurring_status" AS ENUM('active', 'paused', 'canceled', 'expired');--> statement-breakpoint
CREATE TYPE "public"."user_role" AS ENUM('admin', 'organizer', 'user');--> statement-breakpoint
CREATE ROLE "admin";--> statement-breakpoint
CREATE ROLE "anonymous";--> statement-breakpoint
CREATE ROLE "authenticated";--> statement-breakpoint
CREATE ROLE "organizer";--> statement-breakpoint
CREATE TABLE "campaigns" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"organizer_id" text NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"slug" text NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "campaigns_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "campaigns" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "donations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"campaign_id" uuid NOT NULL,
	"donor_id" text NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'MYR' NOT NULL,
	"chip_payment_id" text,
	"status" "donation_status" NOT NULL,
	"donor_name" text NOT NULL,
	"donor_email" text NOT NULL,
	"donor_phone" text,
	"donor_message" text,
	"internal_notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "donations" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "donor_notes" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"donor_id" text NOT NULL,
	"organizer_id" text NOT NULL,
	"content" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "donor_notes" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "donor_tag_assignments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"donor_id" text NOT NULL,
	"tag_id" uuid NOT NULL,
	"assigned_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "donor_tag_assignments" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "donor_tags" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"organizer_id" text NOT NULL,
	"name" text NOT NULL,
	"color" text DEFAULT '#3b82f6' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "donor_tags" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "recurring_donations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"campaign_id" uuid NOT NULL,
	"donor_id" text NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'MYR' NOT NULL,
	"frequency" "recurring_frequency" DEFAULT 'monthly' NOT NULL,
	"status" "recurring_status" DEFAULT 'active' NOT NULL,
	"chip_recurring_token" text NOT NULL,
	"chip_token_expires_at" timestamp,
	"donor_name" text NOT NULL,
	"donor_email" text NOT NULL,
	"donor_phone" text,
	"next_payment_date" timestamp NOT NULL,
	"last_payment_date" timestamp,
	"failed_attempts" integer DEFAULT 0 NOT NULL,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "recurring_donations" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "recurring_payment_attempts" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"recurring_donation_id" uuid NOT NULL,
	"attempt_number" integer NOT NULL,
	"chip_payment_id" text,
	"status" text NOT NULL,
	"error_message" text,
	"attempted_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "recurring_payment_attempts" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "accounts" (
	"id" text PRIMARY KEY NOT NULL,
	"account_id" text NOT NULL,
	"provider_id" text NOT NULL,
	"user_id" text NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"id_token" text,
	"access_token_expires_at" timestamp,
	"refresh_token_expires_at" timestamp,
	"scope" text,
	"password" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
ALTER TABLE "accounts" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "sessions" (
	"id" text PRIMARY KEY NOT NULL,
	"expires_at" timestamp NOT NULL,
	"token" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"ip_address" text,
	"user_agent" text,
	"user_id" text NOT NULL,
	"impersonated_by" text,
	CONSTRAINT "sessions_token_unique" UNIQUE("token")
);
--> statement-breakpoint
ALTER TABLE "sessions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "users" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean DEFAULT false NOT NULL,
	"image" text,
	"role" "user_role" DEFAULT 'user' NOT NULL,
	"banned" boolean DEFAULT false NOT NULL,
	"ban_reason" text,
	"ban_expires" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "users" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "verifications" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "verifications" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "campaigns" ADD CONSTRAINT "campaigns_organizer_id_users_id_fk" FOREIGN KEY ("organizer_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donations" ADD CONSTRAINT "donations_campaign_id_campaigns_id_fk" FOREIGN KEY ("campaign_id") REFERENCES "public"."campaigns"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donations" ADD CONSTRAINT "donations_donor_id_users_id_fk" FOREIGN KEY ("donor_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donor_notes" ADD CONSTRAINT "donor_notes_donor_id_users_id_fk" FOREIGN KEY ("donor_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donor_notes" ADD CONSTRAINT "donor_notes_organizer_id_users_id_fk" FOREIGN KEY ("organizer_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donor_tag_assignments" ADD CONSTRAINT "donor_tag_assignments_donor_id_users_id_fk" FOREIGN KEY ("donor_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donor_tag_assignments" ADD CONSTRAINT "donor_tag_assignments_tag_id_donor_tags_id_fk" FOREIGN KEY ("tag_id") REFERENCES "public"."donor_tags"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "donor_tags" ADD CONSTRAINT "donor_tags_organizer_id_users_id_fk" FOREIGN KEY ("organizer_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "recurring_donations" ADD CONSTRAINT "recurring_donations_campaign_id_campaigns_id_fk" FOREIGN KEY ("campaign_id") REFERENCES "public"."campaigns"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "recurring_donations" ADD CONSTRAINT "recurring_donations_donor_id_users_id_fk" FOREIGN KEY ("donor_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "recurring_payment_attempts" ADD CONSTRAINT "recurring_payment_attempts_recurring_donation_id_recurring_donations_id_fk" FOREIGN KEY ("recurring_donation_id") REFERENCES "public"."recurring_donations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_impersonated_by_users_id_fk" FOREIGN KEY ("impersonated_by") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "unique_donor_tag" ON "donor_tag_assignments" USING btree ("donor_id","tag_id");--> statement-breakpoint
CREATE UNIQUE INDEX "unique_organizer_tag" ON "donor_tags" USING btree ("organizer_id","name");--> statement-breakpoint
CREATE POLICY "campaigns_organizer_access" ON "campaigns" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING ("campaigns"."organizer_id" = current_setting('app.current_organizer_id', true) OR current_setting('app.current_user_role', true) = 'admin') WITH CHECK ("campaigns"."organizer_id" = current_setting('app.current_organizer_id', true) OR current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "campaigns_public_read" ON "campaigns" AS PERMISSIVE FOR SELECT TO "anonymous", "authenticated" USING ("campaigns"."is_active" = true AND current_setting('app.current_organizer_id', true) IS NULL);--> statement-breakpoint
CREATE POLICY "donations_organizer_access" ON "donations" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING (EXISTS (
			SELECT 1 FROM campaigns c
			WHERE c.id = "donations"."campaign_id"
			AND (c.organizer_id = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin')
		)) WITH CHECK (EXISTS (
			SELECT 1 FROM campaigns c
			WHERE c.id = "donations"."campaign_id"
			AND (c.organizer_id = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin')
		));--> statement-breakpoint
CREATE POLICY "donations_donor_access" ON "donations" AS PERMISSIVE FOR SELECT TO "authenticated" USING ("donations"."donor_id" = current_setting('app.current_user_id', true)
			AND current_setting('app.current_user_role', true) = 'user');--> statement-breakpoint
CREATE POLICY "donor_notes_organizer_access" ON "donor_notes" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING ("donor_notes"."organizer_id" = current_setting('app.current_organizer_id', true)
			OR current_setting('app.current_user_role', true) = 'admin') WITH CHECK ("donor_notes"."organizer_id" = current_setting('app.current_organizer_id', true)
			OR current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "donor_tag_assignments_organizer_access" ON "donor_tag_assignments" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING (EXISTS (
				SELECT 1 FROM donor_tags dt
				WHERE dt.id = "donor_tag_assignments"."tag_id"
				AND (dt.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)) WITH CHECK (EXISTS (
				SELECT 1 FROM donor_tags dt
				WHERE dt.id = "donor_tag_assignments"."tag_id"
				AND (dt.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			));--> statement-breakpoint
CREATE POLICY "donor_tags_organizer_access" ON "donor_tags" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING ("donor_tags"."organizer_id" = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin') WITH CHECK ("donor_tags"."organizer_id" = current_setting('app.current_organizer_id', true)
				OR current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "recurring_donations_organizer_access" ON "recurring_donations" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING (EXISTS (
				SELECT 1 FROM campaigns c
				WHERE c.id = "recurring_donations"."campaign_id"
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)) WITH CHECK (EXISTS (
				SELECT 1 FROM campaigns c
				WHERE c.id = "recurring_donations"."campaign_id"
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			));--> statement-breakpoint
CREATE POLICY "recurring_donations_donor_access" ON "recurring_donations" AS PERMISSIVE FOR SELECT TO "authenticated" USING ("recurring_donations"."donor_id" = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'user');--> statement-breakpoint
CREATE POLICY "recurring_payment_attempts_organizer_access" ON "recurring_payment_attempts" AS PERMISSIVE FOR ALL TO "admin", "organizer" USING (EXISTS (
				SELECT 1 FROM recurring_donations rd
				JOIN campaigns c ON c.id = rd.campaign_id
				WHERE rd.id = "recurring_payment_attempts"."recurring_donation_id"
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			)) WITH CHECK (EXISTS (
				SELECT 1 FROM recurring_donations rd
				JOIN campaigns c ON c.id = rd.campaign_id
				WHERE rd.id = "recurring_payment_attempts"."recurring_donation_id"
				AND (c.organizer_id = current_setting('app.current_organizer_id', true)
					OR current_setting('app.current_user_role', true) = 'admin')
			));--> statement-breakpoint
CREATE POLICY "recurring_payment_attempts_donor_access" ON "recurring_payment_attempts" AS PERMISSIVE FOR SELECT TO "authenticated" USING (EXISTS (
				SELECT 1 FROM recurring_donations rd
				WHERE rd.id = "recurring_payment_attempts"."recurring_donation_id"
				AND rd.donor_id = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'user'
			));--> statement-breakpoint
CREATE POLICY "accounts_admin_access" ON "accounts" AS PERMISSIVE FOR ALL TO "admin" USING (current_setting('app.current_user_role', true) = 'admin') WITH CHECK (current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "accounts_self_access" ON "accounts" AS PERMISSIVE FOR ALL TO "authenticated" USING ("accounts"."user_id" = current_setting('app.current_user_id', true)) WITH CHECK ("accounts"."user_id" = current_setting('app.current_user_id', true));--> statement-breakpoint
CREATE POLICY "sessions_admin_access" ON "sessions" AS PERMISSIVE FOR ALL TO "admin" USING (current_setting('app.current_user_role', true) = 'admin') WITH CHECK (current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "sessions_self_access" ON "sessions" AS PERMISSIVE FOR ALL TO "authenticated" USING ("sessions"."user_id" = current_setting('app.current_user_id', true)) WITH CHECK ("sessions"."user_id" = current_setting('app.current_user_id', true));--> statement-breakpoint
CREATE POLICY "sessions_impersonation_access" ON "sessions" AS PERMISSIVE FOR ALL TO "admin" USING ("sessions"."impersonated_by" = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'admin') WITH CHECK ("sessions"."impersonated_by" = current_setting('app.current_user_id', true)
				AND current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "users_admin_access" ON "users" AS PERMISSIVE FOR ALL TO "admin" USING (current_setting('app.current_user_role', true) = 'admin') WITH CHECK (current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "users_organizer_read" ON "users" AS PERMISSIVE FOR SELECT TO "organizer" USING (current_setting('app.current_user_role', true) = 'organizer');--> statement-breakpoint
CREATE POLICY "users_self_access" ON "users" AS PERMISSIVE FOR ALL TO "authenticated" USING ("users"."id" = current_setting('app.current_user_id', true)) WITH CHECK ("users"."id" = current_setting('app.current_user_id', true));--> statement-breakpoint
CREATE POLICY "verifications_admin_access" ON "verifications" AS PERMISSIVE FOR ALL TO "admin" USING (current_setting('app.current_user_role', true) = 'admin') WITH CHECK (current_setting('app.current_user_role', true) = 'admin');--> statement-breakpoint
CREATE POLICY "verifications_system_access" ON "verifications" AS PERMISSIVE FOR ALL TO "anonymous", "authenticated" USING (current_setting('app.current_user_id', true) IS NULL) WITH CHECK (current_setting('app.current_user_id', true) IS NULL);