import { db } from "../src/db"

// Helper function to wait for server to be ready
async function waitForServer(url: string, timeout = 10000): Promise<boolean> {
	const start = Date.now()
	while (Date.now() - start < timeout) {
		try {
			const response = await fetch(url)
			if (response.ok) {
				return true
			}
		} catch (_error) {
			// Server not ready yet
		}
		await new Promise((resolve) => setTimeout(resolve, 500))
	}
	return false
}

async function seedDatabase() {
	console.log("Seeding database...")

	// Clear auth tables to remove invalid data
	try {
		await db.execute(`DELETE FROM sessions`)
		await db.execute(`DELETE FROM accounts`)
		await db.execute(`DELETE FROM verifications`)
		await db.execute(`DELETE FROM users`)
		console.log("Cleared auth tables")
	} catch (_error) {
		console.log("Note: Auth tables may not exist yet, continuing...")
	}

	// Create sample users using Better Auth
	const sampleUsers = [
		{
			email: "<EMAIL>",
			password: "abcd1234",
			role: "admin",
			name: "Admin User",
		},
		{
			email: "<EMAIL>",
			password: "abcd1234",
			name: "Regular User",
		},
		{
			email: "<EMAIL>",
			password: "abcd1234",
			role: "organizer",
			name: "Sales User",
		},
	]

	console.log("Checking if server is running...")
	const serverReady = await waitForServer("http://localhost:3000")

	if (!serverReady) {
		console.error("Server is not running on http://localhost:3000!")
		console.log("Please start the server first with: bun run src/index.ts")
		console.log("Then run this seed script again.")
		console.log("Skipping user creation and continuing with other seed data...")
	} else {
		console.log("Server is ready! Creating users via better-auth API...")

		const createdUsers = []
		for (const userData of sampleUsers) {
			try {
				// Use Better Auth's sign-up API to create users properly
				const response = await fetch(
					"http://localhost:3000/api/auth/sign-up/email",
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							email: userData.email,
							password: userData.password,
							name: userData.name,
							role: userData.role || "user",
						}),
					},
				)

				if (response.ok) {
					const result = await response.json()
					console.log(`Created user: ${result.user.email}`)
					createdUsers.push(result.user)
				} else {
					const errorText = await response.text()
					console.error(
						`Error creating user ${userData.email}:`,
						response.status,
						errorText,
					)
				}
			} catch (error) {
				console.error(`Error creating user ${userData.email}:`, error)
			}
		}
	}

	console.log("Database seeding completed!")
}

// Run the seed function
seedDatabase().catch((error) => {
	console.error("Error seeding database:", error)
	process.exit(1)
})
