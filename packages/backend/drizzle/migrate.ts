import { drizzle } from "drizzle-orm/node-postgres"
import { migrate } from "drizzle-orm/node-postgres/migrator"
import { Pool } from "pg"
import "dotenv/config"

async function runMigrations() {
	const pool = new Pool({
		connectionString: process.env.DATABASE_URL,
	})

	const db = drizzle(pool)

	console.log("Running migrations...")

	try {
		await migrate(db, { migrationsFolder: "./drizzle/migrations" })
		console.log("Migrations completed successfully!")
	} catch (error) {
		console.error("Error running migrations:", error)
		process.exit(1)
	}

	await pool.end()
}

runMigrations()
