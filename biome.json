{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "formatter": {"enabled": true, "indentStyle": "tab", "lineWidth": 80}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noStaticOnlyClass": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "asNeeded"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}